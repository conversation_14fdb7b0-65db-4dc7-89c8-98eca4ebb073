{"extends": "../tsconfig.base.json", "compilerOptions": {"module": "esnext", "target": "es2023", "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "plugins": [{"name": "next"}], "noEmit": true, "incremental": true, "paths": {"~/*": ["./*"]}}, "include": ["next-env.d.ts", "../env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "tailwind.config.mjs"]}