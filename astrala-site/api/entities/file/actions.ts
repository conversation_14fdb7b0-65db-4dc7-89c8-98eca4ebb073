'use client';

import { v4 as uuidv4 } from 'uuid';

import { createClient } from '~/supabase/client';

export async function uploadFile(
  userId: string,
  file: File,
  bucket: 'cvs' | 'driving-licenses' | 'certifications' | 'passports',
): Promise<{ success: boolean; file_path: string | null; error?: string }> {
  const supabase = createClient();

  try {
    const fileExt = file.name.split('.').pop();
    const uniqueFileName = `${userId}_${uuidv4()}.${fileExt}`;
    const filePath = `private/${userId}/${uniqueFileName}`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (uploadError) {
      console.error(
        `Client: Error uploading file to ${bucket} storage:`,
        uploadError,
      );
      return {
        success: false,
        file_path: null,
        error: `Ошибка загрузки в хранилище: ${uploadError.message}`,
      };
    }

    // eslint-disable-next-line @typescript-eslint/naming-convention
    const file_path = uploadData.path;

    return {
      success: true,
      file_path,
    };
  } catch (error) {
    console.error('Client: Unexpected error during file upload:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown Error';
    return {
      success: false,
      file_path: null,
      error: errorMessage,
    };
  }
}
