'use client';

import { useCallback, useState } from 'react';
import _ from 'lodash';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  VacancyCertification,
  VacancyLanguage,
  VacancySkill,
} from '~/app/onboarding/employer/(schemas)/vacancy.schema';

import {
  createVacancy,
  getVacancyDetails,
  updateVacancy,
  updateVacancyStatus,
} from './actions';
import { companyKeys } from '~/api/features/company/queries.ts';
import { employerVacancyMatchesCountsKeys } from '~/api/features/matches/employer/queries.ts';
import { SUBSCRIPTION_QUERY_KEYS } from '~/api/features/subscription/queries';
import { useAuth } from '~/providers/AuthProvider';
import { createClient } from '~/supabase/client';

export const vacancyKeys = {
  all: ['vacancies'] as const,
  details: () => [...vacancyKeys.all, 'detail'] as const,
  detail: (id: number) => [...vacancyKeys.details(), id] as const,
};

export function useVacancyDetails(vacancyId: string) {
  return useQuery({
    queryKey: vacancyKeys.detail(Number(vacancyId)),
    queryFn: async () => {
      return await getVacancyDetails(Number(vacancyId));
    },
    enabled: Boolean(vacancyId),
  });
}

export function useCreateVacancy() {
  const queryClient = useQueryClient();
  const { employerProfile } = useAuth();

  return useMutation({
    mutationFn: async (params: {
      title: string;
      description: string;
      employer_id: number;
      company_id: number;
      country: string;
      city: string;
      experience_years_from: number;
      education_degree?: string;
      education_discipline?: string;
      driving_license: boolean;
      certifications: VacancyCertification[];
      languages: VacancyLanguage[];
      skills: VacancySkill[];
      salary_min: number;
      salary_max: number;
    }) => {
      return await createVacancy(params);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: companyKeys.vacancies(),
      });
      queryClient.refetchQueries({
        queryKey: companyKeys.vacancies(),
      });

      const companyId = variables.company_id || employerProfile?.company_id;
      if (companyId) {
        queryClient.invalidateQueries({
          queryKey: SUBSCRIPTION_QUERY_KEYS.usage(companyId),
        });
        queryClient.invalidateQueries({
          queryKey: SUBSCRIPTION_QUERY_KEYS.canCreateJob(companyId),
        });
      }
    },
  });
}

export function useUpdateVacancy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      id: string;
      title?: string;
      description?: string;
      employer_id?: number;
      company_id?: number;
      country?: string;
      city?: string;
      experience_years_from?: number;
      education_degree?: string;
      education_discipline?: string;
      driving_license?: boolean;
      skills?: VacancySkill[];
      certifications?: VacancyCertification[];
      languages?: VacancyLanguage[];
      salary_min?: number;
      salary_max?: number;
    }) => {
      return await updateVacancy({
        ...params,
        id: Number(params.id),
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: companyKeys.vacancies(),
      });
      queryClient.invalidateQueries({
        queryKey: vacancyKeys.detail(Number(variables.id)),
      });
    },
  });
}

const API = {
  searchSkills: async (query: string): Promise<VacancySkill[]> => {
    if (!query || query.length < 2) return [];

    const supabase = createClient();
    const { data, error } = await supabase
      .from('skills')
      .select('id, name')
      .ilike('name', `%${query}%`)
      .limit(10);

    if (error) {
      console.error('Error searching skills:', error);
      return [];
    }

    return data || [];
  },

  searchCertifications: async (
    query: string,
  ): Promise<VacancyCertification[]> => {
    if (!query || query.length < 2) return [];

    const supabase = createClient();
    const { data, error } = await supabase
      .from('certifications')
      .select('id, name')
      .ilike('name', `%${query}%`)
      .limit(10);

    if (error) {
      console.error('Error searching certifications:', error);
      return [];
    }

    return data || [];
  },
};

export function useCertificationSearch(initialSearchTerm = '') {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);

  const debouncedSetSearchQuery = useCallback(
    (query: string) => {
      const setSearchTermDebounced = _.debounce(q => {
        setSearchTerm(q);
      }, 300);
      setSearchTermDebounced(query);
    },
    [setSearchTerm],
  );

  const handleSearchInput = (query: string) => {
    debouncedSetSearchQuery(query);
  };

  const { data: searchResults = [], ...queryResults } = useQuery({
    queryKey: ['certifications', 'search', searchTerm],
    queryFn: () => API.searchCertifications(searchTerm),
    enabled: searchTerm.length >= 2,
  });

  const searchCertifications = async (
    query: string,
  ): Promise<VacancyCertification[]> => {
    handleSearchInput(query);
    return searchResults;
  };

  return {
    searchCertifications,
    searchTerm,
    searchResults,
    isSearching: queryResults.isLoading,
    ...queryResults,
  };
}

export function useUpdateVacancyStatus() {
  const queryClient = useQueryClient();
  const { employerProfile } = useAuth();

  return useMutation({
    mutationFn: async ({
      id,
      status,
    }: {
      id: string;
      status: 'OPENED' | 'CLOSED';
    }) => {
      return await updateVacancyStatus(Number(id), status);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: vacancyKeys.detail(Number(variables.id)),
      });
      queryClient.refetchQueries({
        queryKey: employerVacancyMatchesCountsKeys.all,
      });
      queryClient.refetchQueries({
        queryKey: companyKeys.vacancies(),
      });

      const companyId = employerProfile?.company_id;
      if (companyId) {
        queryClient.invalidateQueries({
          queryKey: SUBSCRIPTION_QUERY_KEYS.usage(companyId),
        });
        queryClient.invalidateQueries({
          queryKey: SUBSCRIPTION_QUERY_KEYS.canCreateJob(companyId),
        });
      }
    },
  });
}
