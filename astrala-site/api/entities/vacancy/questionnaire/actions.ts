'use server';

import { Database } from '~/shared/db/generated/types.ts';

import { getUserProfile } from '~/api/features/user/actions.ts';
import { requireVacancyQuestionnaireAccess } from '~/api/security/employer.ts';
import { createClient } from '~/supabase/server.ts';

export type Question =
  Database['public']['Tables']['vacancy_questionnaire_questions']['Row'];
export type QuestionGroup =
  Database['public']['Tables']['vacancy_questionnaire_groups']['Row'];

export type QuestionnaireAnswer = {
  question_id: number;
  value: number;
};
export type AnswerTag =
  Database['public']['Tables']['vacancy_answer_tags']['Row'];

export async function getVacancyQuestionGroups(): Promise<{
  groups: QuestionGroup[];
  questions: Question[];
  answerTags: AnswerTag[];
}> {
  const supabase = await createClient();

  const { data: groups, error: groupsError } = await supabase
    .from('vacancy_questionnaire_groups')
    .select('*')
    .order('id', { ascending: true });

  if (groupsError) {
    throw new Error(`Failed to fetch question groups: ${groupsError.message}`);
  }

  const { data: questions, error: questionsError } = await supabase
    .from('vacancy_questionnaire_questions')
    .select('*')
    .order('id', { ascending: true });

  if (questionsError) {
    throw new Error(`Failed to fetch questions: ${questionsError.message}`);
  }

  const { data: answerTags, error: answerTagsError } = await supabase
    .from('vacancy_answer_tags')
    .select('*')
    .order('question_id', { ascending: true })
    .order('value', { ascending: true });

  if (answerTagsError) {
    throw new Error(`Failed to fetch answer tags: ${answerTagsError.message}`);
  }

  return {
    groups,
    questions,
    answerTags: answerTags || [],
  };
}

export async function saveVacancyQuestionnaireAnswers(
  vacancyId: number,
  answers: QuestionnaireAnswer[],
): Promise<{ success: boolean }> {
  await requireVacancyQuestionnaireAccess(vacancyId);
  const supabase = await createClient();

  const { error: deleteError } = await supabase
    .from('vacancy_questionnaire_answers')
    .delete()
    .eq('vacancy_id', vacancyId);

  if (deleteError) {
    throw new Error(
      `Failed to delete existing vacancy answers: ${deleteError.message}`,
    );
  }

  await supabase
    .from('vacancies')
    .update({ status: 'OPENED' })
    .eq('id', vacancyId);

  const answersWithVacancyId = answers.map(answer => ({
    vacancy_id: vacancyId,
    question_id: answer.question_id,
    value: answer.value,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }));

  const { error: insertError } = await supabase
    .from('vacancy_questionnaire_answers')
    .insert(answersWithVacancyId);

  if (insertError) {
    throw new Error(`Failed to save vacancy answers: ${insertError.message}`);
  }

  return { success: true };
}

export async function getCompanyQuestionnaireTokens() {
  try {
    const supabase = await createClient();

    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return { success: true, data: [] };
    }

    const userProfile = await getUserProfile(user.id);

    if (
      !userProfile ||
      userProfile.role !== 'employer' ||
      !userProfile.profile
    ) {
      return { success: true, data: [] };
    }

    const employerProfile =
      userProfile.profile as Database['public']['Tables']['employers']['Row'];

    if (!employerProfile.company_id) {
      return { success: true, data: [] };
    }

    const { data, error } = await supabase
      .from('questionnaire_tokens')
      .select(
        `
        *,
        vacancies!inner(
          id,
          title,
          company_id
        )
      `,
      )
      .eq('vacancies.company_id', employerProfile.company_id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Database error:', error);
      throw new Error(`Database error: ${error.message}`);
    }

    return {
      success: true,
      data: data || [],
    };
  } catch (error) {
    console.error('💥 Error in getCompanyQuestionnaireTokens:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function getQuestionnaireTokenData(token: string) {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('questionnaire_tokens')
      .select(
        `
        *,
        vacancies!inner(
          id,
          title,
          description
        )
      `,
      )
      .eq('token', token)
      .single();

    if (error || !data) {
      return {
        success: false,
        error: 'Invalid or expired token',
      };
    }

    return {
      success: true,
      data: {
        ...data,
        vacancy: data.vacancies,
      },
    };
  } catch (error) {
    console.error('Error fetching token data:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function savePublicQuestionnaireAnswers(
  token: string,
  answers: { question_id: number; value: number }[],
) {
  try {
    const supabase = await createClient();

    const { data: tokenData, error: tokenError } = await supabase
      .from('questionnaire_tokens')
      .select('vacancy_id, completed_at')
      .eq('token', token)
      .single();

    if (tokenError || !tokenData) {
      return {
        success: false,
        error: 'Invalid token',
      };
    }

    if (tokenData.completed_at) {
      return {
        success: false,
        error: 'Questionnaire already completed',
      };
    }

    const vacancyId = tokenData.vacancy_id;

    await supabase
      .from('vacancy_questionnaire_answers')
      .delete()
      .eq('vacancy_id', vacancyId);

    const answersWithVacancyId = answers.map(answer => ({
      vacancy_id: vacancyId,
      question_id: answer.question_id,
      value: answer.value,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }));

    const { error: insertError } = await supabase
      .from('vacancy_questionnaire_answers')
      .insert(answersWithVacancyId);

    if (insertError) {
      throw new Error(`Failed to save answers: ${insertError.message}`);
    }

    await supabase
      .from('vacancies')
      .update({ status: 'OPENED' })
      .eq('id', vacancyId);

    await supabase
      .from('questionnaire_tokens')
      .update({ completed_at: new Date().toISOString() })
      .eq('token', token);

    return { success: true };
  } catch (error) {
    console.error('Error saving public answers:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
export async function getVacancyQuestionnaireAnswers(
  vacancyId: number,
): Promise<QuestionnaireAnswer[]> {
  await requireVacancyQuestionnaireAccess(vacancyId);
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('vacancy_questionnaire_answers')
    .select('question_id, value')
    .eq('vacancy_id', vacancyId)
    .order('question_id', { ascending: true });

  if (error) {
    throw new Error(`Failed to fetch vacancy answers: ${error.message}`);
  }

  return data || [];
}
