'use server';

import {
  VacancyCertification,
  VacancyLanguage,
  VacancySkill,
} from '~/app/onboarding/employer/(schemas)/vacancy.schema';

import { processSkills } from '../skill/actions';

import { processCertifications } from '~/api/entities/certification/actions.ts';
import { requireVacancyAccess } from '~/api/security/employer.ts';
import { createClient } from '~/supabase/client.ts';

export async function createVacancy(params: {
  title: string;
  description: string;
  employer_id: number;
  company_id: number;
  country: string;
  city: string;
  experience_years_from: number;
  education_degree?: string;
  education_discipline?: string;
  driving_license: boolean;
  skills: VacancySkill[];
  certifications: VacancyCertification[];
  languages: VacancyLanguage[];
  salary_min?: number;
  salary_max?: number;
}): Promise<{ success: boolean; id?: number; error?: unknown }> {
  await requireVacancyAccess(params.company_id);
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('vacancies')
      .insert({
        title: params.title,
        description: params.description,
        employer_id: params.employer_id,
        company_id: params.company_id,
        country: params.country,
        city: params.city,
        experience_years_from: params.experience_years_from,
        education_degree: params.education_degree,
        education_discipline: params.education_discipline,
        driving_license: params.driving_license,
        matches_last_calculated_at: null,
        salary_min: params.salary_min,
        salary_max: params.salary_max,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'CLOSED',
      })
      .select('id')
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    const vacancyId = data.id;

    if (params.skills && params.skills.length > 0) {
      try {
        await processSkills(
          {
            id: vacancyId,
            type: 'vacancy',
          },
          params.skills,
        );
      } catch (skillError) {
        console.error('Error processing skills:', skillError);
      }
    }

    if (params.certifications && params.certifications.length > 0) {
      try {
        await processCertifications(vacancyId, params.certifications);
      } catch (certError) {
        console.error('Error processing certifications:', certError);
      }
    }

    if (params.languages && params.languages.length > 0) {
      const languagesToInsert = params.languages.map(lang => ({
        vacancy_id: vacancyId,
        language: lang.language,
        level: lang.level,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      const { error: langsError } = await supabase
        .from('vacancy_languages')
        .insert(languagesToInsert);

      if (langsError) {
        console.error('Error inserting languages:', langsError);
      }
    }

    return { success: true, id: vacancyId };
  } catch (error: unknown) {
    return { success: false, error };
  }
}

export async function updateVacancy(params: {
  id: number;
  title?: string;
  description?: string;
  employer_id?: number;
  company_id?: number;
  country?: string;
  city?: string;
  experience_years_from?: number;
  education_degree?: string;
  education_discipline?: string;
  driving_license?: boolean;
  skills?: VacancySkill[];
  certifications?: VacancyCertification[];
  languages?: VacancyLanguage[];
  salary_min?: number;
  salary_max?: number;
}): Promise<{ success: boolean; error?: unknown }> {
  await requireVacancyAccess(params.id, 'write');
  const supabase = createClient();

  try {
    const updateData: Record<string, unknown> = {
      matches_last_calculated_at: null,
      updated_at: new Date().toISOString(),
    };

    if (params.title !== undefined) updateData.title = params.title;
    if (params.description !== undefined)
      updateData.description = params.description;
    if (params.employer_id !== undefined)
      updateData.employer_id = params.employer_id;
    if (params.company_id !== undefined)
      updateData.company_id = params.company_id;
    if (params.country !== undefined) updateData.country = params.country;
    if (params.city !== undefined) updateData.city = params.city;
    if (params.experience_years_from !== undefined)
      updateData.experience_years_from = params.experience_years_from;
    if (params.education_degree !== undefined)
      updateData.education_degree = params.education_degree;
    if (params.education_discipline !== undefined)
      updateData.education_discipline = params.education_discipline;
    if (params.driving_license !== undefined)
      updateData.driving_license = params.driving_license;
    if (params.salary_min !== undefined)
      updateData.salary_min = params.salary_min;
    if (params.salary_max !== undefined)
      updateData.salary_max = params.salary_max;

    const { error } = await supabase
      .from('vacancies')
      .update(updateData)
      .eq('id', params.id);

    if (error) {
      return { success: false, error: error.message };
    }

    if (params.skills !== undefined) {
      await supabase
        .from('vacancy_skills')
        .delete()
        .eq('vacancy_id', params.id);

      if (params.skills.length > 0) {
        await processSkills(
          {
            id: params.id,
            type: 'vacancy',
          },
          params.skills,
        );
      }
    }

    if (params.certifications !== undefined) {
      await supabase
        .from('vacancy_certifications')
        .delete()
        .eq('vacancy_id', params.id);

      if (params.certifications.length > 0) {
        await processCertifications(params.id, params.certifications);
      }
    }

    if (params.languages !== undefined) {
      await supabase
        .from('vacancy_languages')
        .delete()
        .eq('vacancy_id', params.id);

      if (params.languages.length > 0) {
        const languagesToInsert = params.languages.map(lang => ({
          vacancy_id: params.id,
          language: lang.language,
          level: lang.level,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }));

        const { error: langsError } = await supabase
          .from('vacancy_languages')
          .insert(languagesToInsert);

        if (langsError) {
          return { success: false, error: langsError.message };
        }
      }
    }

    return { success: true };
  } catch (error: unknown) {
    return { success: false, error };
  }
}

export async function getVacancyDetails(vacancyId: number) {
  await requireVacancyAccess(vacancyId, 'read');
  const supabase = createClient();

  try {
    const { data: vacancy, error } = await supabase
      .from('vacancies')
      .select('*')
      .eq('id', vacancyId)
      .single();

    if (error) {
      throw new Error(`Failed to fetch vacancy: ${error.message}`);
    }

    const { data: vacancySkills, error: skillsError } = await supabase
      .from('vacancy_skills')
      .select('skill_id, skills(id, name)')
      .eq('vacancy_id', vacancyId);

    if (skillsError) {
      console.error('Error fetching vacancy skills:', skillsError);
    }

    const { data: vacancyCerts, error: certsError } = await supabase
      .from('vacancy_certifications')
      .select('certification_id, certifications(id, name)')
      .eq('vacancy_id', vacancyId);

    if (certsError) {
      console.error('Error fetching vacancy certifications:', certsError);
    }

    const { data: vacancyLangs, error: langsError } = await supabase
      .from('vacancy_languages')
      .select('*')
      .eq('vacancy_id', vacancyId);

    if (langsError) {
      console.error('Error fetching vacancy languages:', langsError);
    }

    const skills = (vacancySkills || [])
      .map(item => ({
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        id: item.skills?.id,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        name: item.skills?.name,
      }))
      .filter(item => item.id && item.name);

    const certifications = (vacancyCerts || [])
      .map(item => ({
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        id: item.certifications?.id,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        name: item.certifications?.name,
      }))
      .filter(item => item.id && item.name);

    return {
      ...vacancy,
      skills,
      certifications,
      languages: vacancyLangs || [],
    };
  } catch (error) {
    console.error('Error in getVacancyDetails:', error);
    throw error;
  }
}

export async function updateVacancyStatus(
  id: number,
  status: 'OPENED' | 'CLOSED',
): Promise<{ success: boolean; error?: unknown }> {
  await requireVacancyAccess(id, 'write');
  const supabase = createClient();
  const now = new Date().toISOString();

  try {
    const { error } = await supabase
      .from('vacancies')
      .update({
        status,
        updated_at: now,
      })
      .eq('id', id);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: unknown) {
    return { success: false, error };
  }
}
