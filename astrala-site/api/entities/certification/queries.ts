import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';

import { Certification } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema';

import { getCertificateGroupsByType } from '~/api/entities/certification/actions.ts';

type CertType = 'professional' | 'membership' | 'health_safety';

type CertificationWithType = Certification & {
  certType?: CertType;
};

export const useCertificationsManager = () => {
  const [selectedCertifications, setSelectedCertifications] = useState<
    CertificationWithType[]
  >([]);
  const [isAddingCertification, setIsAddingCertification] = useState(false);
  const [isRemovingCertification, setIsRemovingCertification] = useState(false);
  const [isUploadingFile, setIsUploadingFile] = useState(false);
  const [nextNegativeId, setNextNegativeId] = useState(-1);

  const { data: professionalGroups = [] } = useQuery({
    queryKey: ['certificate-groups', 'professional'],
    queryFn: () => getCertificateGroupsByType('professional'),
  });

  const { data: membershipGroups = [] } = useQuery({
    queryKey: ['certificate-groups', 'membership'],
    queryFn: () => getCertificateGroupsByType('membership'),
  });

  const { data: healthSafetyGroups = [] } = useQuery({
    queryKey: ['certificate-groups', 'health_safety'],
    queryFn: () => getCertificateGroupsByType('health_safety'),
  });

  const getFilteredGroups = (type: CertType) => {
    let groups;

    // eslint-disable-next-line default-case
    switch (type) {
      case 'professional':
        groups = professionalGroups;
        break;
      case 'membership':
        groups = membershipGroups;
        break;
      case 'health_safety':
        groups = healthSafetyGroups;
        break;
    }

    return groups.map(group => ({
      ...group,
      certifications: group.certifications.filter(
        (cert: Certification) =>
          !selectedCertifications.some(selected => selected.id === cert.id),
      ),
    }));
  };

  const getCertificationsByType = (type: CertType) => {
    return selectedCertifications.filter(cert => cert.certType === type);
  };

  const addCertificationToList = async (
    certification: Certification,
    type: CertType,
  ) => {
    if (selectedCertifications.some(s => s.id === certification.id)) {
      return null;
    }

    try {
      setIsAddingCertification(true);
      const certWithType: CertificationWithType = {
        ...certification,
        certType: type,
      };
      setSelectedCertifications([...selectedCertifications, certWithType]);
      return certWithType;
    } catch (error) {
      console.error('Failed to add certification:', error);
      return null;
    } finally {
      setIsAddingCertification(false);
    }
  };

  const createAndAddNewCertification = async (
    certificationType: string,
    type: CertType,
  ) => {
    if (!certificationType.trim()) return null;

    try {
      setIsAddingCertification(true);

      if (
        selectedCertifications.some(
          cert => cert.name.toLowerCase() === certificationType.toLowerCase(),
        )
      ) {
        return null;
      }

      const tempId = nextNegativeId;
      setNextNegativeId(prev => prev - 1);

      const newCertification: CertificationWithType = {
        id: tempId,
        name: certificationType.trim(),
        certType: type,
      };

      setSelectedCertifications([...selectedCertifications, newCertification]);
      return newCertification;
    } catch (error) {
      console.error('Failed to create new certification:', error);
      return null;
    } finally {
      setIsAddingCertification(false);
    }
  };

  const removeCertification = async (certificationId: number) => {
    try {
      setIsRemovingCertification(true);
      const updatedCertifications = selectedCertifications.filter(
        certification => certification.id !== certificationId,
      );
      setSelectedCertifications(updatedCertifications);
      return true;
    } catch (error) {
      console.error('Failed to remove certification:', error);
      return false;
    } finally {
      setIsRemovingCertification(false);
    }
  };

  const uploadFile = async (certificationId: number, file: File) => {
    try {
      setIsUploadingFile(true);

      if (file.size > 2 * 1024 * 1024) {
        console.error('File size exceeds 2MB limit');
        return false;
      }

      setSelectedCertifications(
        selectedCertifications.map(cert =>
          cert.id === certificationId ? { ...cert, file } : cert,
        ),
      );

      return true;
    } catch (error) {
      console.error('Failed to attach file:', error);
      return false;
    } finally {
      setIsUploadingFile(false);
    }
  };

  return {
    getFilteredGroups,
    selectedCertifications,
    setSelectedCertifications,
    getCertificationsByType,
    addCertificationToList,
    createAndAddNewCertification,
    removeCertification,
    uploadFile,
    isAddingCertification,
    isRemovingCertification,
    isUploadingFile,
  };
};
