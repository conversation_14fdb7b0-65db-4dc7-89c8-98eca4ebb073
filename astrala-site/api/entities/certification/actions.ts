'use server';

import { VacancyCertification } from '~/app/onboarding/employer/(schemas)/vacancy.schema.ts';
import { Certification } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema.ts';

import { createClient } from '~/supabase/client.ts';

export async function getCertificateGroupsByType(
  type: 'professional' | 'membership' | 'health_safety',
) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('certificate_groups')
      .select(
        `
        id,
        name,
        certifications (id, name)
      `,
      )
      .eq('type', type);

    if (error) {
      console.error('Error fetching certificate groups:', error);
      return [];
    }

    return data;
  } catch (error) {
    console.error('Error in getCertificateGroupsByType:', error);
    return [];
  }
}

export async function searchCertifications(
  query: string,
): Promise<Certification[]> {
  const supabase = createClient();

  if (!query || query.trim().length < 2) {
    return [];
  }

  try {
    const { data, error } = await supabase
      .from('certifications')
      .select('id, name')
      .ilike('name', `%${query}%`)
      .limit(10);

    if (error) {
      console.error('Error searching certifications:', error);
      return [];
    }

    return data.map(item => ({
      id: item.id,
      name: item.name,
      file_path: null,
    })) as unknown as Certification[];
  } catch (error) {
    console.error('Error in searchCertifications:', error);
    return [];
  }
}

export async function processCertifications(
  entityId: number,
  certifications: VacancyCertification[],
): Promise<boolean> {
  if (!entityId) {
    console.error('Entity ID is required to process certifications');
    return false;
  }

  const supabase = createClient();

  try {
    const certsToCreate = certifications.filter(cert => cert.id < 0);
    const existingCerts = certifications.filter(cert => cert.id > 0);
    const createdCertsMap = new Map<number, number>();

    for (const cert of certsToCreate) {
      try {
        const { data: existingCert, error: checkError } = await supabase
          .from('certifications')
          .select('id')
          .eq('name', cert.name)
          .limit(1);

        if (checkError) {
          console.error(
            `Error checking for existing certification ${cert.name}:`,
            checkError,
          );
          continue;
        }

        let realCertId;

        if (existingCert && existingCert.length > 0) {
          realCertId = existingCert[0].id;
        } else {
          const { data: newCert, error: insertError } = await supabase
            .from('certifications')
            .insert({
              name: cert.name,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })
            .select('id')
            .single();

          if (insertError) {
            console.error(
              `Error creating certification ${cert.name}:`,
              insertError,
            );
            continue;
          }

          realCertId = newCert.id;
        }

        createdCertsMap.set(cert.id, realCertId);
      } catch (error) {
        console.error(`Error processing certification ${cert.name}:`, error);
      }
    }

    const vacancyCertifications = [];

    for (const cert of existingCerts) {
      vacancyCertifications.push({
        vacancy_id: entityId,
        certification_id: cert.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    }

    for (const cert of certsToCreate) {
      const realId = createdCertsMap.get(cert.id);
      if (realId) {
        vacancyCertifications.push({
          vacancy_id: entityId,
          certification_id: realId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      }
    }

    if (vacancyCertifications.length > 0) {
      const { error: insertError } = await supabase
        .from('vacancy_certifications')
        .insert(vacancyCertifications);

      if (insertError) {
        console.error('Error inserting vacancy certifications:', insertError);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error processing certifications:', error);
    return false;
  }
}

export async function createJobSeekerCertificationByName(
  jobSeekerId: number,
  certificationName: string,
): Promise<{
  success: boolean;
  jobSeekerCertificationId?: number;
  error?: string;
}> {
  const supabase = createClient();

  try {
    const { data: existingCert } = await supabase
      .from('certifications')
      .select('id')
      .eq('name', certificationName)
      .single();

    let certificationId;

    if (existingCert) {
      certificationId = existingCert.id;
    } else {
      const { data: newCert, error: createError } = await supabase
        .from('certifications')
        .insert({ name: certificationName })
        .select('id')
        .single();

      if (createError) {
        return {
          success: false,
          error: `Failed to create certification: ${createError.message}`,
        };
      }

      certificationId = newCert.id;
    }

    const { data, error } = await supabase
      .from('job_seeker_certifications')
      .insert({
        job_seeker_id: jobSeekerId,
        certification_id: certificationId,
        file_path: null,
      })
      .select('id')
      .single();

    if (error) {
      return {
        success: false,
        error: `Failed to create job seeker certification: ${error.message}`,
      };
    }

    return {
      success: true,
      jobSeekerCertificationId: data.id,
    };
  } catch (error) {
    return {
      success: false,
      error: String(error),
    };
  }
}

export async function createJobSeekerCertificationById(
  jobSeekerId: number,
  certificationId: number,
): Promise<{
  success: boolean;
  jobSeekerCertificationId?: number;
  error?: string;
}> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seeker_certifications')
      .insert({
        job_seeker_id: jobSeekerId,
        certification_id: certificationId,
        file_path: null,
      })
      .select('id')
      .single();

    if (error) {
      return {
        success: false,
        error: `Failed to create job seeker certification: ${error.message}`,
      };
    }

    return {
      success: true,
      jobSeekerCertificationId: data.id,
    };
  } catch (error) {
    return {
      success: false,
      error: String(error),
    };
  }
}
