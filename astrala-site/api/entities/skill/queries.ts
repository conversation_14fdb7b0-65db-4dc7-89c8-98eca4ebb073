import { useState } from 'react';
import { UseFormSetValue } from 'react-hook-form';
import { useQuery } from '@tanstack/react-query';

import { Skill } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema';

import { getAllSkillGroups } from '~/api/entities/skill/actions.ts';

export const useSkillsGroupManager = (
  formSkills: Skill[],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setValue: UseFormSetValue<any>,
) => {
  const [isAddingSkill, setIsAddingSkill] = useState(false);
  const [isRemovingSkill, setIsRemovingSkill] = useState(false);

  const { data: allGroups = [] } = useQuery({
    queryKey: ['skill-groups'],
    queryFn: () => getAllSkillGroups(),
  });

  const getFilteredGroups = () => {
    return allGroups.map(group => ({
      ...group,
      skills: group.skills.filter(
        (skill: Skill) =>
          !formSkills.some(selected => selected.id === skill.id),
      ),
    }));
  };

  const addSkillToList = async (skill: Skill) => {
    if (formSkills.some(s => s.id === skill.id)) {
      return null;
    }

    try {
      setIsAddingSkill(true);
      const updatedSkills = [...formSkills, skill];
      setValue('skills', updatedSkills, { shouldValidate: true });
      return skill;
    } catch (error) {
      console.error('Failed to add skill:', error);
      return null;
    } finally {
      setIsAddingSkill(false);
    }
  };

  const removeSkill = async (skillId: number) => {
    try {
      setIsRemovingSkill(true);
      const updatedSkills = formSkills.filter(skill => skill.id !== skillId);
      setValue('skills', updatedSkills, { shouldValidate: true });
      return true;
    } catch (error) {
      console.error('Failed to remove skill:', error);
      return false;
    } finally {
      setIsRemovingSkill(false);
    }
  };

  return {
    getFilteredGroups,
    addSkillToList,
    removeSkill,
    isAddingSkill,
    isRemovingSkill,
  };
};
