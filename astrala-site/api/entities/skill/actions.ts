'use server';

import { Skill } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema.ts';

import { createClient } from '~/supabase/client.ts';

export async function getAllSkillGroups() {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('skill_groups')
      .select(
        `
        id,
        name,
        skills:skills(id, name)
      `,
      )
      .order('name');

    if (error) {
      console.error('Error fetching skill groups:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getAllSkillGroups:', error);
    return [];
  }
}

export async function processSkills(
  entity: { id: number; type: 'jobSeeker' | 'vacancy' },
  skills: Skill[],
): Promise<boolean> {
  if (!entity.id) {
    console.error('Entity ID is required to process skills');
    return false;
  }

  const supabase = createClient();

  try {
    const tableName =
      entity.type === 'jobSeeker' ? 'job_seeker_skills' : 'vacancy_skills';
    const foreignKey =
      entity.type === 'jobSeeker' ? 'job_seeker_id' : 'vacancy_id';

    const skillAssociations = skills.map(skill => ({
      [foreignKey]: entity.id,
      skill_id: skill.id,
    }));

    if (skillAssociations.length > 0) {
      const { error } = await supabase
        .from(tableName)
        .insert(skillAssociations);

      if (error) {
        console.error('Error creating skill associations:', error);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error processing skills:', error);
    return false;
  }
}
