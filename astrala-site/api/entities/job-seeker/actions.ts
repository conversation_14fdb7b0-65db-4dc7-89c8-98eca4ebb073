'use server';

import { requireJobSeekerOwnership } from '~/api/security/job-seeker.ts';
import { createClient } from '~/supabase/client.ts';

async function updateJobSeekerMatchesTimestamp(
  jobSeekerId: number,
): Promise<{ success: boolean; error?: unknown }> {
  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('job_seekers')
      .update({
        matches_last_calculated_at: null,
      })
      .eq('id', jobSeekerId);

    if (error) {
      console.error('Error updating job seeker matches timestamp:', error);
      return { success: false, error };
    }

    return { success: true };
  } catch (error: unknown) {
    console.error(
      'Caught exception updating job seeker matches timestamp:',
      error,
    );
    return { success: false, error };
  }
}

export async function updateJobSeekerProfile(params: {
  jobSeekerId: number;
  fullName?: string;
  jobTitle?: string;
  phone?: string;
  country?: string;
  city?: string;
  salary_min?: number;
  salary_max?: number;
  has_driving_license?: boolean;
}): Promise<{ success: boolean; error?: unknown }> {
  await requireJobSeekerOwnership(params.jobSeekerId);
  const supabase = createClient();
  const now = new Date().toISOString();

  type ParamKeys = Exclude<keyof typeof params, 'jobSeekerId'>;
  type DBFields =
    | 'full_name'
    | 'job_title'
    | 'phone'
    | 'country'
    | 'city'
    | 'salary_min'
    | 'salary_max'
    | 'has_driving_license'
    | 'updated_at'
    | 'matches_last_calculated_at'
    | 'information_updated_at';

  const fieldMapping: Record<ParamKeys, DBFields> = {
    fullName: 'full_name',
    jobTitle: 'job_title',
    phone: 'phone',
    country: 'country',
    city: 'city',
    salary_min: 'salary_min',
    salary_max: 'salary_max',
    has_driving_license: 'has_driving_license',
  };

  type UpdateDataValue = string | number | boolean | null;
  const updateData: Record<string, UpdateDataValue> = {
    information_updated_at: now,
    matches_last_calculated_at: null,
  };

  (Object.entries(fieldMapping) as [ParamKeys, DBFields][]).forEach(
    ([paramName, dbField]) => {
      const value = params[paramName];
      if (value !== undefined) {
        updateData[dbField] = value;
      }
    },
  );

  try {
    const { error } = await supabase
      .from('job_seekers')
      .update(updateData)
      .eq('id', params.jobSeekerId);

    if (error) {
      console.error('Error updating job seeker profile:', error);
      return { success: false, error };
    }

    return { success: true };
  } catch (error: unknown) {
    console.error('Caught exception updating job seeker profile:', error);
    return { success: false, error };
  }
}

export async function addEducation(params: {
  jobSeekerId: number;
  type: string;
  institution: string;
  discipline: string;
  startYear: number;
  endYear: number;
}): Promise<{ success: boolean; id?: number; error?: unknown }> {
  await requireJobSeekerOwnership(params.jobSeekerId);
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seeker_educations')
      .insert({
        job_seeker_id: params.jobSeekerId,
        type: params.type,
        institution: params.institution,
        discipline: params.discipline,
        start_year: params.startYear,
        end_year: params.endYear,
      })
      .select('id')
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    await updateJobSeekerMatchesTimestamp(params.jobSeekerId);

    return { success: true, id: data.id };
  } catch (error: unknown) {
    return { success: false, error };
  }
}

export async function addWorkExperience(params: {
  jobSeekerId: number;
  companyName: string;
  jobTitle: string;
  startMonth: number;
  startYear: number;
  endMonth?: number | null;
  endYear?: number | null;
  comment: string;
}): Promise<{ success: boolean; id?: number; error?: unknown }> {
  await requireJobSeekerOwnership(params.jobSeekerId);
  const supabase = createClient();

  try {
    const startDate = `${params.startYear}-${String(params.startMonth).padStart(2, '0')}-01`;
    const endDate = params.endYear
      ? `${params.endYear}-${String(params.endMonth).padStart(2, '0')}-01`
      : null;

    const insertData = {
      job_seeker_id: params.jobSeekerId,
      company_name: params.companyName,
      job_title: params.jobTitle,
      start_date: startDate,
      end_date: endDate,
      comment: params.comment,
    };

    const { data, error } = await supabase
      .from('job_seeker_work_experience')
      .insert(insertData)
      .select('id')
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    await updateJobSeekerMatchesTimestamp(params.jobSeekerId);

    return { success: true, id: data.id };
  } catch (error: unknown) {
    return { success: false, error };
  }
}

export async function addLanguage(params: {
  jobSeekerId: number;
  language: string;
  level: string;
}): Promise<{ success: boolean; id?: number; error?: unknown }> {
  await requireJobSeekerOwnership(params.jobSeekerId);
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seeker_languages')
      .insert({
        job_seeker_id: params.jobSeekerId,
        language: params.language,
        level: params.level,
      })
      .select('id')
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    await updateJobSeekerMatchesTimestamp(params.jobSeekerId);

    return { success: true, id: data.id };
  } catch (error: unknown) {
    return { success: false, error };
  }
}

export async function updateJobSeekerWorkStyle(
  jobSeekerId: number,
): Promise<{ success: boolean; error?: unknown }> {
  await requireJobSeekerOwnership(jobSeekerId);
  const supabase = createClient();
  const now = new Date().toISOString();

  try {
    const { error } = await supabase
      .from('job_seekers')
      .update({
        work_style_updated_at: now,
        matches_last_calculated_at: null,
      })
      .eq('id', jobSeekerId);

    if (error) {
      console.error('Error updating job seeker work style timestamp:', error);
      return { success: false, error };
    }

    return { success: true };
  } catch (error: unknown) {
    console.error('Caught exception updating job seeker work style:', error);
    return { success: false, error };
  }
}

export async function getJobSeekerCanEditInfo(
  jobSeekerId: number,
): Promise<{ canEdit: boolean; error?: unknown }> {
  await requireJobSeekerOwnership(jobSeekerId);
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seekers')
      .select('information_updated_at')
      .eq('id', jobSeekerId)
      .single();

    if (error) {
      console.error('Error checking if job seeker can edit info:', error);
      return { canEdit: false, error };
    }

    if (!data.information_updated_at) {
      return { canEdit: true };
    }

    const lastUpdate = new Date(data.information_updated_at);
    const now = new Date();
    const minutesSinceUpdate =
      (now.getTime() - lastUpdate.getTime()) / (1000 * 60);

    return { canEdit: minutesSinceUpdate >= 5 };
  } catch (error: unknown) {
    console.error(
      'Caught exception checking if job seeker can edit info:',
      error,
    );
    return { canEdit: false, error };
  }
}

export async function getJobSeekerCanEditWorkStyle(
  jobSeekerId: number,
): Promise<{ canEdit: boolean; error?: unknown }> {
  await requireJobSeekerOwnership(jobSeekerId);
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seekers')
      .select('work_style_updated_at')
      .eq('id', jobSeekerId)
      .single();

    if (error) {
      console.error('Error checking if job seeker can edit work style:', error);
      return { canEdit: false, error };
    }

    if (!data.work_style_updated_at) {
      return { canEdit: true };
    }

    const lastUpdate = new Date(data.work_style_updated_at);
    const now = new Date();
    const minutesSinceUpdate =
      (now.getTime() - lastUpdate.getTime()) / (1000 * 60);

    return { canEdit: minutesSinceUpdate >= 5 };
  } catch (error: unknown) {
    console.error(
      'Caught exception checking if job seeker can edit work style:',
      error,
    );
    return { canEdit: false, error };
  }
}

export async function getJobSeekerEducations(jobSeekerId: number) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seeker_educations')
      .select('*')
      .eq('job_seeker_id', jobSeekerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching job seeker educations:', error);
      return { success: false, error, data: [] };
    }

    return {
      success: true,
      data: data.map(item => ({
        type: item.type,
        institution: item.institution,
        discipline: item.discipline,
        startYear: item.start_year,
        endYear: item.end_year,
        id: item.id,
      })),
    };
  } catch (error) {
    console.error('Caught exception fetching job seeker educations:', error);
    return { success: false, error, data: [] };
  }
}

export async function getJobSeekerWorkExperiences(jobSeekerId: number) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seeker_work_experience')
      .select('*')
      .eq('job_seeker_id', jobSeekerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching job seeker work experiences:', error);
      return { success: false, error, data: [] };
    }

    return {
      success: true,
      data: data.map(item => {
        const startDateParts = item.start_date
          ? item.start_date.split('-')
          : null;
        const endDateParts = item.end_date ? item.end_date.split('-') : null;

        return {
          companyName: item.company_name,
          jobTitle: item.job_title,
          startMonth: startDateParts
            ? Number.parseInt(startDateParts[1], 10)
            : 1,
          startYear: startDateParts
            ? Number.parseInt(startDateParts[0], 10)
            : new Date().getFullYear() - 1,
          endMonth: endDateParts ? Number.parseInt(endDateParts[1], 10) : 1,
          endYear: endDateParts
            ? Number.parseInt(endDateParts[0], 10)
            : new Date().getFullYear(),
          comment: item.comment || '',
          id: item.id,
        };
      }),
    };
  } catch (error) {
    console.error(
      'Caught exception fetching job seeker work experiences:',
      error,
    );
    return { success: false, error, data: [] };
  }
}

export async function getJobSeekerLanguages(jobSeekerId: number) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seeker_languages')
      .select('*')
      .eq('job_seeker_id', jobSeekerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching job seeker languages:', error);
      return { success: false, error, data: [] };
    }

    return {
      success: true,
      data: data.map(item => ({
        language: item.language,
        level: item.level,
        id: item.id,
      })),
    };
  } catch (error) {
    console.error('Caught exception fetching job seeker languages:', error);
    return { success: false, error, data: [] };
  }
}

export async function getJobSeekerSkills(jobSeekerId: number) {
  const supabase = createClient();

  try {
    const { data: skillRelations, error: skillRelationsError } = await supabase
      .from('job_seeker_skills')
      .select('skill_id')
      .eq('job_seeker_id', jobSeekerId);

    if (skillRelationsError) {
      console.error(
        'Error fetching job seeker skill relations:',
        skillRelationsError,
      );
      return { success: false, error: skillRelationsError, data: [] };
    }

    if (skillRelations.length === 0) {
      return { success: true, data: [] };
    }

    const skillIds = skillRelations.map(relation => relation.skill_id);

    const { data: skills, error: skillsError } = await supabase
      .from('skills')
      .select('*')
      .in('id', skillIds);

    if (skillsError) {
      console.error('Error fetching job seeker skills:', skillsError);
      return { success: false, error: skillsError, data: [] };
    }

    return {
      success: true,
      data: skills.map(skill => ({
        id: skill.id,
        name: skill.name,
      })),
    };
  } catch (error) {
    console.error('Caught exception fetching job seeker skills:', error);
    return { success: false, error, data: [] };
  }
}

export async function getJobSeekerCertifications(jobSeekerId: number) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_seeker_certifications')
      .select(
        `
        *,
        certifications:certification_id (
          name
        )
      `,
      )
      .eq('job_seeker_id', jobSeekerId);

    if (error) {
      console.error('Error fetching job seeker certifications:', error);
      return { success: false, error, data: [] };
    }

    return {
      success: true,
      data: data.map(item => ({
        id: item.id,
        name: item.certifications?.name || 'Unknown Certification',
        file_path: item.file_path,
      })),
    };
  } catch (error) {
    console.error(
      'Caught exception fetching job seeker certifications:',
      error,
    );
    return { success: false, error, data: [] };
  }
}

export async function getJobSeekerFullProfile(jobSeekerId: number) {
  try {
    const [
      educationsResponse,
      workExperiencesResponse,
      languagesResponse,
      skillsResponse,
      certificationsResponse,
    ] = await Promise.all([
      getJobSeekerEducations(jobSeekerId),
      getJobSeekerWorkExperiences(jobSeekerId),
      getJobSeekerLanguages(jobSeekerId),
      getJobSeekerSkills(jobSeekerId),
      getJobSeekerCertifications(jobSeekerId),
    ]);

    return {
      success: true,
      data: {
        educations: educationsResponse.success ? educationsResponse.data : [],
        workExperiences: workExperiencesResponse.success
          ? workExperiencesResponse.data
          : [],
        languages: languagesResponse.success ? languagesResponse.data : [],
        skills: skillsResponse.success ? skillsResponse.data : [],
        certifications: certificationsResponse.success
          ? certificationsResponse.data
          : [],
      },
    };
  } catch (error) {
    console.error('Caught exception fetching full job seeker profile:', error);
    return {
      success: false,
      error,
      data: {
        educations: [],
        workExperiences: [],
        languages: [],
        skills: [],
        certifications: [],
      },
    };
  }
}
