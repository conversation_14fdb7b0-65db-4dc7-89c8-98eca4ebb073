'use server';

type ParsedResumeData = {
  fullName?: string;
  jobTitle?: string;
  phone?: string;
  email?: string;
  location?: {
    country?: string;
    city?: string;
  };
  skills?: string[];
  education?: Array<{
    type?: string;
    institution?: string;
    major?: string;
    startYear?: number;
    endYear?: number;
  }>;
  workExperience?: Array<{
    companyName?: string;
    jobTitle?: string;
    startYear?: number;
    endYear?: number;
    description?: string;
  }>;
  languages?: Array<{
    language?: string;
    level?: string;
  }>;
};

export async function parseResume(fileUrl: string): Promise<{
  success: boolean;
  data?: ParsedResumeData;
  error?: string;
}> {
  try {
    const apiUrl = `http://${process.env.PARSER_SERVICE_HOST}:${process.env.PARSER_SERVICE_PORT}`;
    const apiToken =
      process.env.PARSING_API_TOKEN || 'J4s9XvB2LmQyLT2AY3NWB8CA74B1GD0x';

    const response = await fetch(
      `${apiUrl}/process-cv?link=${encodeURIComponent(fileUrl)}`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiToken}`,
          'Content-Type': 'application/json',
        },
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `API responded with status ${response.status}: ${errorText}`,
      );
    }

    const data = await response.json();

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('Resume parsing error:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error during resume parsing',
    };
  }
}
