'use server';

import { randomUUID } from 'crypto';
import { z } from 'zod';

import { createClient } from '~/supabase/server';

const CompanyInterestedSchema = z.object({
  jobSeekerEmail: z.string().email('Invalid email address'),
  jobSeekerName: z.string().min(1, 'Job seeker name is required'),
  companyName: z.string().min(1, 'Company name is required'),
  appUrl: z.string().url('Invalid APP_URL environment variable'),
});
type CompanyInterestedInput = z.infer<typeof CompanyInterestedSchema>;

export async function sendCompanyInterestedEmail(
  input: CompanyInterestedInput,
) {
  try {
    const validatedInput = CompanyInterestedSchema.parse(input);
    const actionUrl = `${validatedInput.appUrl}/dashboard/job-seeker/pending/company-interested`;
    const textBody = `
      Hello, ${validatedInput.jobSeekerName}!

      Great news! ${validatedInput.companyName} has shown an interest in your profile.

      Check them out and decide if you're interested too!

      Warm Regards,
      AstralaNexus
    `.trim();
    const htmlBody = composeEmailFromTemplate({
      heading: `${validatedInput.companyName} is interested in your profile!`,
      body: textBody,
      cta: 'View Interested Company',
      ctaLink: actionUrl,
    });

    return await sendEmail({
      from: '<EMAIL>',
      to: validatedInput.jobSeekerEmail,
      subject: `${validatedInput.companyName} is interested in your profile!`,
      htmlBody,
      textBody,
      tag: 'CompanyInterested',
    });
  } catch (error) {
    console.error('Error sending company interested email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

const SendQuestionnaireSchema = z.object({
  vacancyId: z.number(),
  hiringManagerName: z.string().min(1),
  hiringManagerEmail: z.string().email(),
  note: z.string().optional(),
  vacancyTitle: z.string().min(1),
});

export type SendQuestionnaireRequest = z.infer<typeof SendQuestionnaireSchema>;

export async function sendQuestionnaireEmail(data: SendQuestionnaireRequest) {
  try {
    const validated = SendQuestionnaireSchema.parse(data);
    const supabase = await createClient();

    const token = randomUUID().replace(/-/g, '');

    const { error: insertError } = await supabase
      .from('questionnaire_tokens')
      .insert({
        vacancy_id: validated.vacancyId,
        token,
        hiring_manager_email: validated.hiringManagerEmail,
        hiring_manager_name: validated.hiringManagerName,
        note: validated.note || null,
      });

    if (insertError) {
      throw new Error(`Database error: ${insertError.message}`);
    }

    const questionnaireUrl = `${process.env.APP_URL}/questionnaire/${token}`;
    const textBody = `
      Hi ${validated.hiringManagerName}!

      Please complete the Behavioural Preferences for your ${validated.vacancyTitle} role.

      These preferences reveal the core traits that shape how success will look in this role.

      Your preferences will be automatically linked to this vacancy and guide smarter, insight-led matching.${validated.note ? `\n\nNote: ${validated.note}\n\n` : ''}

      Warm regards,
      AstralaNexus
    `.trim();
    const htmlBody = composeEmailFromTemplate({
      heading: `Complete Behavioural Preferences for ${validated.vacancyTitle} role`,
      body: textBody,
      cta: 'Define Behavioural Preferences',
      ctaLink: questionnaireUrl,
    });

    const emailResult = await sendEmail({
      from: '<EMAIL>',
      to: validated.hiringManagerEmail,
      subject: `Complete Behavioural Preferences for ${validated.vacancyTitle} role`,
      htmlBody,
      textBody,
      tag: 'Questionnaire',
    });

    if (!emailResult.success) {
      throw new Error(emailResult.error || 'Failed to send email');
    }

    return {
      success: true,
      token,
      messageId: emailResult.messageId,
    };
  } catch (error) {
    console.error('Error sending questionnaire email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

const SendInvitationSchema = z.object({
  recipientEmail: z.string().email('Invalid email address'),
  inviteSenderName: z.string().min(1, 'Sender name is required'),
  companyName: z.string().min(1, 'Company name is required'),
  actionUrl: z.string().url('Invalid action URL'),
});
type SendInvitationInput = z.infer<typeof SendInvitationSchema>;

export async function sendInvitationEmail(input: SendInvitationInput) {
  try {
    const postmarkServerToken = process.env.POSTMARK_SERVER_API;

    if (!postmarkServerToken) {
      throw new Error(
        'Postmark server token is not configured in environment variables',
      );
    }

    const validatedInput = SendInvitationSchema.parse(input);

    const textBody = `
      Hi!

      ${validatedInput.inviteSenderName} from ${validatedInput.companyName} has invited you to use Astrala Nexus to collaborate with them.

      Warm regards,
      AstralaNexus
    `.trim();

    const htmlBody = composeEmailFromTemplate({
      heading: 'Great News! You Have a Match!',
      body: textBody,
      cta: 'Accept Invitation',
      ctaLink: validatedInput.actionUrl,
    });

    const emailData = {
      From: '<EMAIL>',
      To: validatedInput.recipientEmail,
      Subject: `${validatedInput.inviteSenderName} invited you to Astrala Nexus`,
      HtmlBody: htmlBody,
      TextBody: textBody,
      MessageStream: 'outbound',
      Tag: 'Invitation',
    };

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': postmarkServerToken,
      },
      body: JSON.stringify(emailData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `Postmark API error: ${errorData.Message || 'Unknown error'}`,
      );
    }

    const result = await response.json();
    return { success: true, messageId: result.MessageID };
  } catch (error) {
    console.error('Error sending invitation email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

const MatchNotificationSchema = z.object({
  recipientEmail: z.string().email('Invalid email address'),
  recipientName: z.string().min(1, 'Recipient name is required'),
  matchName: z.string().min(1, 'Match name is required'),
  role: z.enum(['jobseeker', 'employer'], {
    errorMap: () => ({ message: 'Role must be either jobseeker or employer' }),
  }),
  appUrl: z.string().url('Invalid APP_URL environment variable'),
});
type MatchNotificationInput = z.infer<typeof MatchNotificationSchema>;

export async function sendMatchNotificationEmail(
  input: MatchNotificationInput,
) {
  try {
    const validatedInput = MatchNotificationSchema.parse(input);

    const actionUrl =
      validatedInput.role === 'jobseeker'
        ? `${validatedInput.appUrl}/dashboard/job-seeker/pending/company-interested`
        : `${validatedInput.appUrl}/dashboard/employer/pending/paid`;

    const buttonText =
      validatedInput.role === 'jobseeker'
        ? 'View Interested Company'
        : 'View Matched Candidate';

    const textBody = `
      Hello, ${validatedInput.recipientName}!

      ${
        validatedInput.role === 'jobseeker'
          ? `Great news! ${validatedInput.matchName} is also interested in your profile.`
          : `Great news! ${validatedInput.matchName} is also interested in your job opportunity.`
      }

      This is a great match. Take the next step and connect now!

      Warm Regards,
      AstralaNexus
    `.trim();

    const htmlBody = composeEmailFromTemplate({
      heading: 'Great News! You Have a Match!',
      body: textBody,
      cta: buttonText,
      ctaLink: actionUrl,
    });

    return await sendEmail({
      from: '<EMAIL>',
      to: validatedInput.recipientEmail,
      subject: 'Great News! You Have a Match!',
      htmlBody,
      textBody,
      tag: 'Match',
    });
  } catch (error) {
    console.error('Error sending match notification email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

// IMPORTANT! Don't forget to update the duplicate at astrala-cron module
function composeEmailFromTemplate(mail: {
  heading: string;
  body: string;
  cta: string;
  ctaLink: string;
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 40px; background-color: #171C18; color: #fff">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 302 52" style="width: 185px; text-align: center; margin: 0 auto; display: block;"><g fill="currentColor" clip-path="url(#logo_svg__a)"><path d="M54.908 13.194 53.417 9.66a79 79 0 0 0-5.674 2.658C44.13 5.028 36.611 0 27.94 0S11.74 5.028 8.129 12.322a76 76 0 0 0-5.674-2.658L.964 13.197c2.091.88 4.005 1.79 5.764 2.718a22 22 0 0 0-.887 6.18c0 11.755 9.23 21.393 20.823 22.053L27.992 52l1.258-7.856c11.57-.683 20.777-10.31 20.777-22.05a22 22 0 0 0-.887-6.179 73 73 0 0 1 5.764-2.718zM27.94 3.835c7.236 0 13.503 4.232 16.455 10.348-9.596 5.726-14.243 12.064-16.455 17.304-2.212-5.24-6.863-11.578-16.455-17.304 2.952-6.12 9.218-10.351 16.455-10.351zM9.68 22.091c0-1.45.174-2.861.495-4.213 13.609 8.377 15.666 17.754 15.847 21.903q-.01.242-.011.468c-9.166-.967-16.33-8.74-16.33-18.158m36.515 0c0 9.418-7.164 17.191-16.33 18.158q0-.221-.011-.457c.177-4.145 2.227-13.53 15.847-21.918.32 1.356.498 2.764.498 4.217zM62.755 39.418l11.577-29.305h5.338l11.62 29.305h-4.402l-2.861-7.263h-14.27l-2.82 7.263h-4.186zm8.331-10.72h11.578l-3.545-9.185a41 41 0 0 0-.385-1.068q-.255-.686-.555-1.518t-.596-1.71q-.3-.876-.555-1.6h-.257c-.2.57-.426 1.222-.683 1.966q-.385 1.112-.77 2.156c-.257.698-.468 1.287-.642 1.774zM101.542 39.931q-2.306-.002-4.058-.468-1.75-.47-2.906-1.366a6 6 0 0 1-1.752-2.137c-.4-.827-.596-1.767-.596-2.82v-.404q.001-.192.041-.32h3.718a1.2 1.2 0 0 0-.041.298v.256q.04 1.497.789 2.348.747.856 2.072 1.197t2.948.343q1.41 0 2.605-.362t1.903-1.11q.704-.747.706-1.902 0-1.41-.918-2.156-.917-.747-2.412-1.174t-3.118-.898a50 50 0 0 1-2.733-.853 11.8 11.8 0 0 1-2.458-1.155 5.36 5.36 0 0 1-1.751-1.775c-.442-.724-.66-1.646-.66-2.755q0-1.495.618-2.65t1.775-1.967c.77-.54 1.702-.948 2.797-1.216q1.643-.408 3.654-.408c1.423 0 2.669.16 3.737.469q1.604.47 2.669 1.325c.71.57 1.238 1.215 1.582 1.944a5.4 5.4 0 0 1 .513 2.329v.49q-.001.24-.041.363h-3.673v-.468q0-.854-.45-1.646-.448-.791-1.536-1.283-1.087-.492-3.012-.491-1.239 0-2.137.192-.895.194-1.495.597-.599.408-.898.94-.3.534-.299 1.215 0 1.11.725 1.71.726.598 1.944.982t2.628.853q1.495.43 3.054.876 1.558.447 2.861 1.132a5.6 5.6 0 0 1 2.091 1.857c.529.786.789 1.816.789 3.096q.001 1.794-.683 3.118a5.8 5.8 0 0 1-1.944 2.178q-1.262.855-2.948 1.261-1.687.408-3.696.408zM119.013 39.932c-1.166 0-2.08-.223-2.733-.661a3.68 3.68 0 0 1-1.408-1.752 6.3 6.3 0 0 1-.427-2.329V20.11h-2.906v-3.16h2.989l.684-6.278h2.989v6.278h4.228v3.16h-4.228v14.522q0 1.069.385 1.6.384.532 1.454.532h2.393v2.477a4.3 4.3 0 0 1-1.027.343q-.6.129-1.215.234a7 7 0 0 1-1.174.106zM125.589 39.418V16.95h3.032l.343 3.718h.298q.34-1.067.94-2.03a4.8 4.8 0 0 1 1.624-1.582q1.023-.619 2.521-.62.641 0 1.174.106.533.109.789.234v3.462h-1.408c-.97 0-1.8.162-2.499.49a4.56 4.56 0 0 0-1.729 1.39q-.683.895-1.004 2.137-.322 1.24-.321 2.65v12.517h-3.76zM143.701 39.932q-1.241-.002-2.476-.299a7.5 7.5 0 0 1-2.307-.981c-.713-.457-1.28-1.091-1.71-1.903q-.64-1.218-.642-3.053 0-2.306 1.11-3.802 1.112-1.495 3.096-2.329 1.988-.832 4.718-1.155 2.735-.322 5.938-.321v-2.563q0-1.239-.426-2.137c-.287-.596-.789-1.06-1.518-1.39-.724-.327-1.759-.49-3.095-.49s-2.299.163-3.054.49q-1.134.493-1.582 1.262a3.35 3.35 0 0 0-.449 1.71v.642h-3.632a2 2 0 0 1-.041-.427v-.513q0-2.05 1.132-3.44 1.132-1.387 3.183-2.094 2.05-.705 4.7-.706 2.82 0 4.718.811c1.269.54 2.22 1.31 2.862 2.307q.962 1.495.962 3.586V35.31q0 .81.385 1.133.385.321.94.32h1.665v2.477q-.6.255-1.325.45a6.5 6.5 0 0 1-1.665.192q-1.109 0-1.838-.45a2.9 2.9 0 0 1-1.11-1.238 5.7 5.7 0 0 1-.513-1.774h-.299a7.5 7.5 0 0 1-1.944 1.857 9.5 9.5 0 0 1-2.627 1.22q-1.455.425-3.16.426zm.94-3.16q1.367 0 2.586-.426a6.8 6.8 0 0 0 2.155-1.22 5.85 5.85 0 0 0 1.495-1.902q.555-1.11.555-2.435v-1.88q-3.46 0-5.896.385-2.435.386-3.738 1.366-1.302.985-1.302 2.779-.001 1.155.514 1.88c.343.483.826.85 1.453 1.09q.939.363 2.178.363M160.492 39.418V8.535h3.76v30.887h-3.76zM174.033 39.932q-1.241-.002-2.476-.299a7.5 7.5 0 0 1-2.307-.981c-.713-.457-1.279-1.091-1.71-1.903q-.64-1.218-.642-3.053 0-2.306 1.11-3.802c.74-.996 1.775-1.774 3.096-2.329q1.988-.832 4.718-1.155 2.735-.322 5.938-.321v-2.563q0-1.239-.426-2.137c-.287-.596-.789-1.06-1.518-1.39-.724-.327-1.759-.49-3.095-.49s-2.299.163-3.054.49q-1.134.493-1.582 1.262a3.35 3.35 0 0 0-.449 1.71v.642h-3.632a2 2 0 0 1-.041-.427v-.513q0-2.05 1.132-3.44 1.132-1.387 3.183-2.094 2.05-.705 4.7-.706 2.82 0 4.718.811c1.269.54 2.22 1.31 2.862 2.307q.962 1.495.962 3.586V35.31q0 .81.385 1.133.385.321.94.32h1.665v2.477q-.6.255-1.325.45a6.5 6.5 0 0 1-1.665.192q-1.109 0-1.838-.45a2.9 2.9 0 0 1-1.11-1.238 5.7 5.7 0 0 1-.513-1.774h-.299a7.5 7.5 0 0 1-1.944 1.857 9.5 9.5 0 0 1-2.627 1.22q-1.455.425-3.16.426zm.94-3.16q1.366 0 2.586-.426a6.8 6.8 0 0 0 2.155-1.22 5.85 5.85 0 0 0 1.495-1.902q.555-1.11.555-2.435v-1.88q-3.46 0-5.896.385-2.435.386-3.737 1.366-1.303.985-1.303 2.779-.001 1.155.514 1.88c.343.483.826.85 1.453 1.09q.939.363 2.178.363M190.692 39.418V10.113h3.93l14.224 19.31q.214.254.555.77c.226.343.457.675.683 1.003q.34.493.555.876h.17V10.113h3.971v29.305h-3.714L196.668 19.77q-.3-.47-.876-1.325a33 33 0 0 0-.963-1.367h-.215v22.34h-3.929zM227.814 39.932c-2.219 0-4.096-.42-5.617-1.261-1.525-.838-2.676-2.13-3.461-3.865-.786-1.737-1.174-3.945-1.174-6.622s.392-4.919 1.174-6.644c.781-1.721 1.944-3.005 3.48-3.843s3.462-1.26 5.768-1.26q3.161-.002 5.296 1.215t3.224 3.632q1.09 2.413 1.091 6.043v1.793h-16.149c.057 1.793.321 3.27.789 4.42.468 1.156 1.174 1.994 2.114 2.523.94.528 2.121.788 3.545.788q1.45 0 2.54-.362 1.089-.361 1.839-1.046a4.5 4.5 0 0 0 1.132-1.623q.386-.94.427-2.05h3.673q-.045 1.795-.684 3.311a6.9 6.9 0 0 1-1.88 2.586q-1.24 1.07-3.031 1.665c-1.197.4-2.563.596-4.099.596zm-6.277-13.628h12.174q-.001-1.88-.427-3.16-.429-1.279-1.219-2.09a4.7 4.7 0 0 0-1.839-1.175q-1.046-.362-2.37-.362-1.964 0-3.333.725c-.914.483-1.609 1.223-2.096 2.22-.483.996-.785 2.28-.898 3.842zM238.192 39.418l8.075-11.491-7.69-10.978h4.53l5.466 8.03h.216l5.466-8.03h4.273l-7.69 10.85 8.117 11.619h-4.485l-5.897-8.671h-.215l-5.896 8.67h-4.274zM268.565 39.932q-3.246 0-5.213-1.665-1.966-1.665-1.967-5.81V16.95h3.76v14.994q-.001 1.41.344 2.307.34.896.981 1.408.64.515 1.518.724a8 8 0 0 0 1.902.216 6 6 0 0 0 2.99-.77q1.365-.77 2.242-2.243t.876-3.439v-13.2h3.76v22.468h-3.031l-.344-3.375h-.298q-.897 1.37-2.031 2.22a7.8 7.8 0 0 1-2.522 1.26q-1.387.408-2.967.408zM291.763 39.931q-2.307-.002-4.059-.468-1.75-.47-2.906-1.366a6 6 0 0 1-1.752-2.137q-.598-1.24-.596-2.82v-.404q.001-.192.041-.32h3.719a1.1 1.1 0 0 0-.042.298v.256q.04 1.497.789 2.348.747.856 2.072 1.197t2.949.343q1.41 0 2.604-.362t1.903-1.11q.704-.747.706-1.902 0-1.41-.917-2.156-.918-.747-2.413-1.174-1.493-.427-3.118-.898a50 50 0 0 1-2.733-.853 11.8 11.8 0 0 1-2.457-1.155 5.36 5.36 0 0 1-1.752-1.775c-.442-.724-.66-1.646-.66-2.755q0-1.495.619-2.65.618-1.154 1.774-1.967c.77-.54 1.702-.948 2.797-1.216q1.643-.408 3.654-.408c1.423 0 2.669.16 3.737.469q1.605.47 2.669 1.325c.71.57 1.239 1.215 1.582 1.944q.514 1.087.513 2.329v.49q-.001.24-.041.363h-3.673v-.468q-.001-.854-.449-1.646-.449-.791-1.537-1.283t-3.012-.491q-1.239 0-2.137.192-.894.194-1.495.597-.599.408-.898.94-.299.534-.298 1.215 0 1.11.724 1.71.726.598 1.945.982 1.217.384 2.627.853l3.054.876q1.558.447 2.861 1.132a5.6 5.6 0 0 1 2.092 1.857q.79 1.177.789 3.096 0 1.794-.684 3.118a5.8 5.8 0 0 1-1.944 2.178q-1.262.855-2.948 1.261-1.687.408-3.696.408z"></path></g><defs><clipPath id="logo_svg__a"><path fill="currentColor" d="M.968 0h300.064v52H.968z"></path></clipPath></defs></svg>
      <h1 style="font-size: 20px; line-height: 1.4; text-align: center; margin-top: 32px;">${mail.heading}</h1>
      <p style="font-size: 16px; line-height: 1.4;">
        ${mail.body.replace(/\n/g, '<br>')}
      </p>
      <div style="text-align: center; margin: 32px 0;">
        <a href="${mail.ctaLink}" style="font-size: 14px; line-height: 24px; font-weight: bold; background: #1A4E48; color: #fff; padding: 8px 16px; text-decoration: none; border-radius: 6px; display: inline-block;">
          ${mail.cta}
        </a>
      </div>
      <div style="font-size: 14px; color: #aaa; padding: 24px; text-align: center">
        Astrala © ${new Date().getFullYear()}
      </div>
    </div>
  `;
}

type EmailData = {
  from: string;
  to: string;
  subject: string;
  htmlBody: string;
  textBody: string;
  messageStream?: string;
  tag?: string;
};

async function sendEmail(emailData: EmailData) {
  try {
    const postmarkServerToken = process.env.POSTMARK_SERVER_API;

    if (!postmarkServerToken) {
      throw new Error(
        'Postmark server token is not configured in environment variables',
      );
    }

    const postmarkData = {
      From: emailData.from,
      To: emailData.to,
      Subject: emailData.subject,
      HtmlBody: emailData.htmlBody,
      TextBody: emailData.textBody,
      MessageStream: emailData.messageStream || 'outbound',
      Tag: emailData.tag,
    };

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': postmarkServerToken,
      },
      body: JSON.stringify(postmarkData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `Postmark API error: ${errorData.Message || 'Unknown error'}`,
      );
    }

    const result = await response.json();
    return { success: true, messageId: result.MessageID };
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
