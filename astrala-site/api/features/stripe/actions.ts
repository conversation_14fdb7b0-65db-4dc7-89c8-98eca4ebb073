'use server';

import Strip<PERSON> from 'stripe';

import { createClient } from '~/supabase/server.ts';

let stripe: Stripe | null = null;
if (process.env.STRIPE_SECRET_KEY) {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    apiVersion: '2023-10-16',
  });
}

export type PayerRole = 'employer' | 'job_seeker';

export type CreateCheckoutSessionInput = {
  matchId: number;
  payerId: number;
  payerRole: PayerRole;
  returnUrl?: string;
};

export type StripePrices = {
  employer: number;
  jobSeeker: number;
  currency: string;
};

const stripePrices: StripePrices = {
  employer: Number(process.env.NEXT_PUBLIC_STRIPE_EMPLOYER_PRICE || 0),
  jobSeeker: Number(process.env.NEXT_PUBLIC_STRIPE_JOB_SEEKER_PRICE || 0),
  currency: process.env.STRIPE_CURRENCY || 'GBP',
};

async function createTransaction(
  payerId: number,
  payerRole: PayerRole,
  matchId: number,
  amount: number,
  currency: string,
): Promise<number> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('transactions')
    .insert({
      payer_id: payerId,
      payer_role: payerRole,
      vacancy_match_id: matchId,
      amount,
      currency,
      status: 'pending',
    })
    .select('id')
    .single();

  if (error) {
    console.error('Error creating transaction:', error);
    throw new Error('Failed to create transaction record');
  }

  return data.id;
}

async function updateTransactionStatus(
  transactionId: number,
  status: 'pending' | 'completed' | 'failed' | 'cancelled',
  completedAt?: Date,
): Promise<void> {
  const supabase = await createClient();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const updateData: any = {
    status,
    updated_at: new Date().toISOString(),
  };

  if (completedAt) {
    updateData.completed_at = completedAt.toISOString();
  }

  const { error } = await supabase
    .from('transactions')
    .update(updateData)
    .eq('id', transactionId);

  if (error) {
    console.error('Error updating transaction:', error);
    throw new Error('Failed to update transaction status');
  }
}

async function updateMatchStatusToPaid(
  matchId: number,
  transactionId?: number,
): Promise<void> {
  const supabase = await createClient();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const updateData: any = {
    employer_status: 'PAID',
    job_seeker_status: 'PAID',
    updated_at: new Date().toISOString(),
  };

  if (transactionId) {
    updateData.employer_transaction_id = transactionId;
  }

  const { error } = await supabase
    .from('vacancy_matches')
    .update(updateData)
    .eq('id', matchId);

  if (error) {
    console.error('Error updating match status to PAID:', error);
    throw new Error('Failed to update match status to PAID');
  }
}

export async function createCheckoutSession({
  matchId,
  payerId,
  payerRole,
}: CreateCheckoutSessionInput): Promise<string> {
  if (!matchId || !payerId) {
    throw new Error('Match ID and payer ID are required');
  }

  if (!stripe) {
    throw new Error('Stripe is not initialized');
  }

  const amount = stripePrices.employer;
  const currency = stripePrices.currency;

  const transactionId = await createTransaction(
    payerId,
    payerRole,
    matchId,
    amount,
    currency,
  );

  const baseUrl = process.env.APP_URL;
  const successUrl = `${baseUrl}/payment/success?session_id={CHECKOUT_SESSION_ID}&transaction_id=${transactionId}`;
  const cancelUrl = `${baseUrl}/payment/cancel?transaction_id=${transactionId}`;

  try {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: currency.toLowerCase(),
            product_data: {
              name: 'Contact Information Access',
              description: 'Access to candidate contact information',
            },
            unit_amount: amount * 100,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        match_id: matchId.toString(),
        transaction_id: transactionId.toString(),
        payer_id: payerId.toString(),
        payer_role: payerRole,
      },
    });

    if (!session.url) {
      throw new Error('Failed to create checkout session');
    }

    return session.url;
  } catch (error) {
    console.error('Stripe error:', error);
    await updateTransactionStatus(transactionId, 'failed');
    throw new Error('Failed to create payment session');
  }
}

export async function handleSuccessfulPayment(
  sessionId: string,
  transactionId: number,
): Promise<{
  success: boolean;
  dashboardUrl: string;
  error?: string;
}> {
  if (!sessionId || !transactionId) {
    return {
      success: false,
      // eslint-disable-next-line sonarjs/no-duplicate-string
      dashboardUrl: '/dashboard',
      error: 'Session ID and transaction ID are required',
    };
  }

  try {
    if (!stripe) {
      throw new Error('Stripe is not initialized');
    }

    const session = await stripe.checkout.sessions.retrieve(sessionId);

    if (session.payment_status !== 'paid') {
      return {
        success: false,
        dashboardUrl: '/dashboard',
        error: 'Payment not completed',
      };
    }

    const matchId = Number(session.metadata?.match_id);

    await updateTransactionStatus(transactionId, 'completed', new Date());

    await updateMatchStatusToPaid(matchId, transactionId);

    const dashboardUrl = '/dashboard/employer/matches/paid';

    return {
      success: true,
      dashboardUrl,
    };
  } catch (error) {
    console.error('Error handling successful payment:', error);
    return {
      success: false,
      dashboardUrl: '/dashboard',
      error: 'Failed to process payment completion',
    };
  }
}

export async function handleCancelledPayment(transactionId: number): Promise<{
  success: boolean;
  dashboardUrl: string;
  error?: string;
}> {
  if (!transactionId) {
    return {
      success: false,
      dashboardUrl: '/dashboard',
      error: 'Transaction ID is required',
    };
  }

  try {
    await updateTransactionStatus(transactionId, 'cancelled');

    const supabase = await createClient();
    const { data, error } = await supabase
      .from('transactions')
      .select('payer_role')
      .eq('id', transactionId)
      .single();

    if (error || !data) {
      return {
        success: false,
        dashboardUrl: '/dashboard',
        error: 'Failed to get transaction information',
      };
    }

    const dashboardUrl = '/dashboard/employer/pending/waiting-for-payment';

    return {
      success: true,
      dashboardUrl,
    };
  } catch (error) {
    console.error('Error handling cancelled payment:', error);
    return {
      success: false,
      dashboardUrl: '/dashboard',
      error: 'Failed to process payment cancellation',
    };
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function processStripeWebhook(event: any): Promise<void> {
  try {
    const eventType = event.type;

    if (eventType === 'checkout.session.completed') {
      const session = event.data.object;
      const transactionId = Number(session.metadata.transaction_id);
      const matchId = Number(session.metadata.match_id);

      await updateTransactionStatus(transactionId, 'completed', new Date());

      await updateMatchStatusToPaid(matchId, transactionId);
    }
  } catch (error) {
    console.error('Error processing webhook:', error);
  }
}
