'use client';

import { useQuery } from '@tanstack/react-query';

import { MatchData } from '~/app/dashboard/(components)/MatchCard.tsx';

import { getMatchById } from './actions';

export const matchKeys = {
  all: ['matches'] as const,
  match: (vacancyMatchId: number, userType: 'employer' | 'job_seeker') =>
    [...matchKeys.all, 'single', vacancyMatchId, userType] as const,
};

export function useGetMatchById(
  vacancyMatchId: number | undefined,
  userType: 'employer' | 'job_seeker',
  enabled = true,
) {
  return useQuery<MatchData | null>({
    queryKey: vacancyMatchId
      ? matchKeys.match(vacancyMatchId, userType)
      : ['matches', 'single', null, userType],
    queryFn: () => getMatchById(vacancyMatchId as number, userType),
    enabled: Boolean(vacancyMatchId) && enabled,
  });
}
