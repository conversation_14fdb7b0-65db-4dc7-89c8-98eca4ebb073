import { MatchData } from '~/app/dashboard/(components)/MatchCard.tsx';

import { createClient } from '~/supabase/client.ts';

function maskName(fullName: string): string {
  if (!fullName) return '';
  return fullName
    .split(' ')
    .map(namePart => {
      if (!namePart) return '';
      return namePart[0] + '*'.repeat(Math.max(namePart.length - 1, 0));
    })
    .join(' ');
}

export async function getMatchById(
  vacancyMatchId: number,
  userType: 'employer' | 'job_seeker',
): Promise<MatchData | null> {
  const supabase = createClient();

  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('Not authenticated');
      return null;
    }

    let userId: number;
    if (userType === 'employer') {
      const { data: employerData } = await supabase
        .from('employers')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (!employerData) {
        console.error('Employer not found');
        return null;
      }
      userId = employerData.id;
    } else {
      const { data: jobSeekerData } = await supabase
        .from('job_seekers')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (!jobSeekerData) {
        console.error('Job seeker not found');
        return null;
      }
      userId = jobSeekerData.id;
    }

    const { data: matchData, error: matchError } = await supabase
      .from('vacancy_matches')
      .select(
        `
        *,
        vacancy:vacancy_id (
          *,
          company:company_id (name),
          employer:employer_id (full_name, email)
        )
      `,
      )
      .eq('id', vacancyMatchId)
      .single();

    if (matchError || !matchData) {
      console.error('Error fetching match:', matchError);
      return null;
    }

    if (userType === 'job_seeker') {
      if (matchData.job_seeker_id !== userId) {
        console.error('Access denied: job seeker mismatch');
        return null;
      }
      // eslint-disable-next-line sonarjs/no-collapsible-if
    } else if (userType === 'employer') {
      if (
        !matchData.vacancy?.employer_id ||
        matchData.vacancy.employer_id !== userId
      ) {
        console.error('Access denied: employer mismatch');
        return null;
      }
    }

    const enhancedMatch = { ...matchData };

    if (matchData.job_seeker_id) {
      const { data: jobSeekerData, error: jobSeekerError } = await supabase
        .from('job_seekers')
        .select('*')
        .eq('id', matchData.job_seeker_id)
        .single();

      if (!jobSeekerError && jobSeekerData) {
        const { data: workExperienceData } = await supabase
          .from('job_seeker_work_experience')
          .select('*')
          .eq('job_seeker_id', matchData.job_seeker_id)
          .order('end_date', { ascending: false });

        const { data: educationData } = await supabase
          .from('job_seeker_educations')
          .select('*')
          .eq('job_seeker_id', matchData.job_seeker_id)
          .order('end_year', { ascending: false });

        const isMatched =
          matchData.employer_status === 'INTERESTED' &&
          matchData.job_seeker_status === 'INTERESTED';

        const jobSeekerName = isMatched
          ? jobSeekerData.full_name
          : maskName(jobSeekerData.full_name);

        enhancedMatch.jobSeeker = {
          ...jobSeekerData,
          full_name: jobSeekerName,
          work_experience: workExperienceData || [],
          education: educationData || [],
        };

        if (!isMatched) {
          delete enhancedMatch.jobSeeker.phone;
          delete enhancedMatch.jobSeeker.email;
        }
      }
    }

    const { data: matchDescData } = await supabase
      .from('vacancy_match_descriptions')
      .select('*')
      .eq('vacancy_match_id', vacancyMatchId)
      .single();

    if (matchDescData) {
      enhancedMatch.match_description = matchDescData;
    }

    return enhancedMatch;
  } catch (error) {
    console.error('Exception fetching match:', error);
    return null;
  }
}
