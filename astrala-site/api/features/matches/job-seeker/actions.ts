'use server';

import { MatchData } from '~/app/dashboard/(components)/MatchCard.tsx';

import { sendMatchNotificationEmail } from '../../mailing/actions';

import { canCreateMatch } from '~/api/features/subscription/helpers.ts';
import { createClient } from '~/supabase/server.ts';

export type VacancyMatchStatus = 'PENDING' | 'INTERESTED' | 'REJECTED';

export type VacancyMatch = {
  id: number;
  created_at: string;
  updated_at: string;
  job_seeker_id: number;
  job_seeker_status: string;
  employer_status: string;
  ai_match_score: number;
  vacancy_id: number;
  employer_rejected_at?: string;
  job_seeker_rejected_at?: string;

  jobSeeker?: {
    id: number;
    full_name: string;
    job_title: string;
    country?: string;
    city?: string;
    salary_min?: number;
    salary_max?: number;
    has_driving_license?: boolean;
    resume_path?: string;
    work_experience?: Array<{
      id: number;
      company_name: string;
      job_title: string;
      start_date: string;
      end_date?: string;
      comment?: string;
    }>;
    education?: Array<{
      id: number;
      type: string;
      institution: string;
      discipline: string;
      start_year: number;
      end_year?: number;
    }>;
  };

  vacancy?: {
    id: number;
    title: string;
    description: string;
    salary_min?: number;
    salary_max?: number;
    employer_id: number;
    company_id?: number;
    driving_license?: boolean;
    education_degree?: string;
    education_discipline?: string;
    country?: string;
    city?: string;
    experience_years_from?: number;
    company?: {
      id: number;
      name: string;
    };
    employer?: {
      id: number;
      full_name: string;
      company_id: number;
      email?: string;
    };
  };

  match_description?: {
    vacancy_match_id: number;
    driving_license_match: boolean;
    education_match: boolean;
    location_match: boolean;
    salary_match: boolean;
    work_experience_match: boolean;
    work_experience_years?: number;
    lacking_skills?: string | string[];
    matching_skills?: string | string[];
    matching_behavioural_tags?: string | string[];
    lacking_behavioural_tags?: string | string[];
    ai_summary?: string;
  };
};

export type PaginatedResponse<T> = {
  data: T[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type JobSeekerStatus =
  | 'PENDING'
  | 'INTERESTED'
  | 'NOT_INTERESTED'
  | 'REJECTED';

export type MatchesCountsResponse = {
  suggestions: number;
  interested: number;
  rejected: number;
  companyInterested: number;
  companyRejected: number;
  matches: number;
};

function maskName(name: string): string {
  if (!name) return '';

  return name
    .split(' ')
    .map(part => {
      if (!part) return '';
      return part[0] + '*'.repeat(Math.max(part.length - 1, 0));
    })
    .join(' ');
}

async function enhanceMatchData(
  match: MatchData,
  jobSeekerId: number,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  supabase: any,
): Promise<VacancyMatch> {
  const { data: jobSeekerData, error: jobSeekerError } = await supabase
    .from('job_seekers')
    .select('*')
    .eq('id', jobSeekerId)
    .single();

  if (!jobSeekerError && jobSeekerData) {
    const { data: workExperienceData, error: workExperienceError } =
      await supabase
        .from('job_seeker_work_experience')
        .select('*')
        .eq('job_seeker_id', jobSeekerId)
        .order('end_year', { ascending: false });

    const { data: educationData, error: educationError } = await supabase
      .from('job_seeker_educations')
      .select('*')
      .eq('job_seeker_id', jobSeekerId)
      .order('end_year', { ascending: false });

    const isMatched =
      match.employer_status === 'INTERESTED' &&
      match.job_seeker_status === 'INTERESTED';

    const jobSeekerWithoutEmail = { ...jobSeekerData };
    if (!isMatched) {
      delete jobSeekerWithoutEmail.email;
    }

    match.jobSeeker = {
      ...jobSeekerWithoutEmail,
      work_experience:
        !workExperienceError && workExperienceData ? workExperienceData : [],
      education: !educationError && educationData ? educationData : [],
    };
  }

  if (match.vacancy) {
    if (match.vacancy.company_id) {
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select('*')
        .eq('id', match.vacancy.company_id)
        .single();

      if (!companyError && companyData) {
        const companyName =
          match.employer_status === 'INTERESTED' &&
          match.job_seeker_status === 'INTERESTED'
            ? companyData.name
            : maskName(companyData.name);

        const maskedCompanyData = {
          ...companyData,
          name: companyName,
        };

        match.vacancy.company = maskedCompanyData;
      }
    }

    if (match.vacancy.employer_id) {
      const { data: employerData, error: employerError } = await supabase
        .from('employers')
        .select('*')
        .eq('id', match.vacancy.employer_id)
        .single();

      if (!employerError && employerData) {
        const isMatched =
          match.employer_status === 'INTERESTED' &&
          match.job_seeker_status === 'INTERESTED';

        const employerName =
          match.employer_status === 'INTERESTED' &&
          match.job_seeker_status === 'INTERESTED'
            ? employerData.full_name
            : maskName(employerData.full_name);

        const maskedEmployerData = {
          ...employerData,
          full_name: employerName,
        };

        if (!isMatched) {
          delete maskedEmployerData.email;
        }

        match.vacancy.employer = maskedEmployerData;
      }
    }
  }

  const { data: matchDescData, error: matchDescError } = await supabase
    .from('vacancy_match_descriptions')
    .select(
      '*, matching_behavioural_tags, lacking_behavioural_tags, ai_summary',
    )
    .eq('vacancy_match_id', match.id)
    .single();

  if (!matchDescError && matchDescData) {
    // eslint-disable-next-line require-atomic-updates
    match.match_description = matchDescData;
  }

  return match;
}

async function getVacancyMatchesByStatusPaginated(
  jobSeekerId: number,
  jobSeekerStatus: VacancyMatchStatus,
  employerStatus: VacancyMatchStatus,
  page = 1,
  pageSize = 5,
  filterByCompanyLimit = false,
): Promise<PaginatedResponse<VacancyMatch>> {
  if (!jobSeekerId) {
    return {
      data: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }

  const supabase = await createClient();

  try {
    let countQuery = supabase
      .from('vacancy_matches')
      .select(
        `
        *,
        vacancy:vacancy_id!inner (
          *,
          company:company_id!inner (
            id,
            subscription_status,
            subscription_plan
          )
        )
      `,
        { count: 'exact' },
      )
      .eq('job_seeker_id', jobSeekerId)
      .eq('job_seeker_status', jobSeekerStatus)
      .eq('employer_status', employerStatus);

    if (filterByCompanyLimit) {
      countQuery = countQuery.in('vacancy.company.subscription_status', [
        'trial',
        'active',
      ]);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error counting vacancy matches:', countError);
      return {
        data: [],
        totalCount: 0,
        currentPage: page,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      };
    }

    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let matchesQuery = supabase
      .from('vacancy_matches')
      .select(
        `
        *,
        vacancy:vacancy_id!inner (
          *,
          company:company_id!inner (*)
        )
      `,
      )
      .eq('job_seeker_id', jobSeekerId)
      .eq('job_seeker_status', jobSeekerStatus)
      .eq('employer_status', employerStatus)
      .order('created_at', { ascending: false })
      .range(from, to);

    if (filterByCompanyLimit) {
      matchesQuery = matchesQuery.in('vacancy.company.subscription_status', [
        'trial',
        'active',
      ]);
    }

    const { data: matchesData, error: matchesError } = await matchesQuery;

    if (matchesError) {
      console.error('Error fetching vacancy matches:', matchesError);
      return {
        data: [],
        totalCount,
        currentPage: page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };
    }

    if (!matchesData || matchesData.length === 0) {
      return {
        data: [],
        totalCount,
        currentPage: page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };
    }

    const filteredMatches = [];

    if (filterByCompanyLimit) {
      for (const match of matchesData) {
        const companyId = match.vacancy?.company?.id;

        if (companyId) {
          const canMatch = await canCreateMatch(companyId);

          if (canMatch) {
            filteredMatches.push(match);
          }
        }
      }
    } else {
      filteredMatches.push(...matchesData);
    }

    const enhancedMatches = await Promise.all(
      filteredMatches.map((match: MatchData) =>
        enhanceMatchData(match, jobSeekerId, supabase),
      ),
    );

    return {
      data: enhancedMatches,
      totalCount: filteredMatches.length,
      currentPage: page,
      totalPages: Math.ceil(filteredMatches.length / pageSize),
      hasNextPage: page < Math.ceil(filteredMatches.length / pageSize),
      hasPreviousPage: page > 1,
    };
  } catch (error) {
    console.error('Exception fetching vacancy matches:', error);
    return {
      data: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }
}
export async function getSuggestions(
  jobSeekerId: number,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<VacancyMatch>> {
  return getVacancyMatchesByStatusPaginated(
    jobSeekerId,
    'PENDING',
    'PENDING',
    page,
    pageSize,
  );
}

export async function getJobSeekerInterested(
  jobSeekerId: number,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<VacancyMatch>> {
  return getVacancyMatchesByStatusPaginated(
    jobSeekerId,
    'INTERESTED',
    'PENDING',
    page,
    pageSize,
  );
}

export async function getCompanyInterested(
  jobSeekerId: number,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<VacancyMatch>> {
  return getVacancyMatchesByStatusPaginated(
    jobSeekerId,
    'PENDING',
    'INTERESTED',
    page,
    pageSize,
    true,
  );
}

export async function getCompanyRejected(
  jobSeekerId: number,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<VacancyMatch>> {
  return getVacancyMatchesByStatusPaginated(
    jobSeekerId,
    'INTERESTED',
    'REJECTED',
    page,
    pageSize,
  );
}

export async function getMatches(
  jobSeekerId: number,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<VacancyMatch>> {
  return getVacancyMatchesByStatusPaginated(
    jobSeekerId,
    'INTERESTED',
    'INTERESTED',
    page,
    pageSize,
  );
}

export async function getJobSeekerRejected(
  jobSeekerId: number,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<VacancyMatch>> {
  if (!jobSeekerId) {
    return {
      data: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }

  const rejectedPendingCount = await getVacancyMatchesByStatusPaginated(
    jobSeekerId,
    'REJECTED',
    'PENDING',
    1,
    1,
  );

  const rejectedInterestedCount = await getVacancyMatchesByStatusPaginated(
    jobSeekerId,
    'REJECTED',
    'INTERESTED',
    1,
    1,
  );

  const totalCount =
    rejectedPendingCount.totalCount + rejectedInterestedCount.totalCount;
  const totalPages = Math.ceil(totalCount / pageSize);

  const pendingOffset = (page - 1) * pageSize;
  const interestedOffset = Math.max(
    0,
    pendingOffset - rejectedPendingCount.totalCount,
  );

  let rejectedPending: PaginatedResponse<VacancyMatch> = {
    data: [],
    totalCount: rejectedPendingCount.totalCount,
    currentPage: page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };

  if (pendingOffset < rejectedPendingCount.totalCount) {
    rejectedPending = await getVacancyMatchesByStatusPaginated(
      jobSeekerId,
      'REJECTED',
      'PENDING',
      Math.floor(pendingOffset / pageSize) + 1,
      Math.min(pageSize, rejectedPendingCount.totalCount - pendingOffset),
    );
  }

  let rejectedInterested: PaginatedResponse<VacancyMatch> = {
    data: [],
    totalCount: rejectedInterestedCount.totalCount,
    currentPage: 1,
    totalPages: Math.ceil(rejectedInterestedCount.totalCount / pageSize),
    hasNextPage: false,
    hasPreviousPage: false,
  };

  if (interestedOffset >= 0 && rejectedPending.data.length < pageSize) {
    const remainingItems = pageSize - rejectedPending.data.length;

    rejectedInterested = await getVacancyMatchesByStatusPaginated(
      jobSeekerId,
      'REJECTED',
      'INTERESTED',
      Math.floor(interestedOffset / pageSize) + 1,
      remainingItems,
    );
  }

  const combinedData = [...rejectedPending.data, ...rejectedInterested.data];

  return {
    data: combinedData,
    totalCount,
    currentPage: page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };
}

export async function updateJobSeekerStatus(
  matchId: number,
  status: JobSeekerStatus,
): Promise<{ success: boolean; error?: string; decisionDeadline?: string }> {
  if (!matchId) return { success: false, error: 'No match ID provided' };

  const supabase = await createClient();

  try {
    const { data: matchData, error: fetchError } = await supabase
      .from('vacancy_matches')
      .select(
        `
        *,
        vacancy:vacancy_id (
          *,
          employer:employer_id (
            *,
            company:company_id (*)
          )
        ),
        job_seeker:job_seeker_id (*)
      `,
      )
      .eq('id', matchId)
      .single();

    if (fetchError) {
      console.error('Error fetching match data:', fetchError);
      return { success: false, error: fetchError.message };
    }

    // eslint-disable-next-line sonarjs/no-collapsible-if
    if (status === 'INTERESTED' && matchData.job_seeker_status === 'REJECTED') {
      if (matchData.job_seeker_rejected_at) {
        const rejectedAt = new Date(matchData.job_seeker_rejected_at);
        const now = new Date();
        const hoursSinceRejection =
          (now.getTime() - rejectedAt.getTime()) / (1000 * 60 * 60);

        if (hoursSinceRejection > 24) {
          return {
            success: false,
            error:
              'You can only change your decision within 24 hours of rejection',
          };
        }
      }
    }

    if (
      status === 'INTERESTED' &&
      matchData.job_seeker_status !== 'INTERESTED'
    ) {
      const companyId = matchData.vacancy?.employer?.company?.id;

      if (!companyId) {
        return { success: false, error: 'Company not found' };
      }

      const canMatch = await canCreateMatch(companyId);

      if (!canMatch) {
        return {
          success: false,
          error: 'Company has reached their match limit.',
        };
      }

      if (matchData.employer_status === 'INTERESTED') {
        const { data: currentUsage } = await supabase
          .from('company_match_usage')
          .select('matches_used')
          .eq('company_id', companyId)
          .single();

        await supabase
          .from('company_match_usage')
          .update({
            matches_used: (currentUsage?.matches_used || 0) + 1,
            updated_at: new Date().toISOString(),
          })
          .eq('company_id', companyId);
      }
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updateData: Record<string, any> = {
      job_seeker_status: status,
    };

    if (status === 'REJECTED' && matchData.job_seeker_status !== 'REJECTED') {
      updateData.job_seeker_rejected_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('vacancy_matches')
      .update(updateData)
      .eq('id', matchId);

    if (error) {
      console.error('Error updating job seeker status:', error);
      return { success: false, error: error.message };
    }

    let decisionDeadline;
    if (status === 'REJECTED') {
      const deadline = new Date();
      deadline.setHours(deadline.getHours() + 24);
      decisionDeadline = deadline.toISOString();
    }

    // eslint-disable-next-line sonarjs/no-collapsible-if
    if (status === 'INTERESTED' && matchData.employer_status === 'INTERESTED') {
      const appUrl = process.env.APP_URL || 'https://astralanexus.ai';

      const { data: vacancyData, error: vacancyError } = await supabase
        .from('vacancies')
        .select('employer_id')
        .eq('id', matchData.vacancy_id)
        .single();

      if (vacancyError || !vacancyData) {
        console.error('Error fetching vacancy data:', vacancyError);
        return { success: true };
      }

      const { data: employerData, error: employerError } = await supabase
        .from('employers')
        .select('email, full_name')
        .eq('id', vacancyData.employer_id)
        .single();

      if (employerError || !employerData) {
        console.error('Error fetching employer data:', employerError);
        return { success: true };
      }

      const { data: jobSeekerData } = await supabase
        .from('job_seekers')
        .select('full_name')
        .eq('id', matchData.job_seeker_id)
        .single();

      const jobSeekerName = jobSeekerData?.full_name || 'Job Seeker';

      await sendMatchNotificationEmail({
        recipientEmail: employerData.email,
        recipientName: employerData.full_name,
        matchName: jobSeekerName,
        role: 'employer',
        appUrl,
      });
    }

    return {
      success: true,
      decisionDeadline,
    };
  } catch (error) {
    console.error('Exception updating job seeker status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function canJobSeekerChangeRejection(
  matchId: number,
): Promise<{ canChange: boolean; deadline?: string; error?: string }> {
  if (!matchId) return { canChange: false, error: 'No match ID provided' };

  const supabase = await createClient();

  try {
    const { data: matchData, error: fetchError } = await supabase
      .from('vacancy_matches')
      .select('*, job_seeker_rejected_at')
      .eq('id', matchId)
      .single();

    if (fetchError) {
      console.error('Error fetching match data:', fetchError);
      return { canChange: false, error: fetchError.message };
    }

    if (
      matchData.job_seeker_status !== 'REJECTED' ||
      !matchData.job_seeker_rejected_at
    ) {
      return { canChange: false };
    }

    const rejectedAt = new Date(matchData.job_seeker_rejected_at);
    const now = new Date();
    const hoursSinceRejection =
      (now.getTime() - rejectedAt.getTime()) / (1000 * 60 * 60);

    if (hoursSinceRejection <= 24) {
      const deadline = new Date(rejectedAt);
      deadline.setHours(deadline.getHours() + 24);

      return {
        canChange: true,
        deadline: deadline.toISOString(),
      };
    }

    return { canChange: false };
  } catch (error) {
    console.error(
      'Exception checking if job seeker can change rejection:',
      error,
    );
    return {
      canChange: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function getVacancyMatchesCounts(
  jobSeekerId: number,
): Promise<MatchesCountsResponse> {
  if (!jobSeekerId) {
    return {
      suggestions: 0,
      interested: 0,
      rejected: 0,
      companyInterested: 0,
      companyRejected: 0,
      matches: 0,
    };
  }

  const supabase = await createClient();

  try {
    const getCount = async (
      jobSeekerStatus: VacancyMatchStatus,
      employerStatus: VacancyMatchStatus,
    ) => {
      const { count, error } = await supabase
        .from('vacancy_matches')
        .select('*', { count: 'exact' })
        .eq('job_seeker_id', jobSeekerId)
        .eq('job_seeker_status', jobSeekerStatus)
        .eq('employer_status', employerStatus);

      if (error) {
        console.error(
          `Error counting matches (${jobSeekerStatus}, ${employerStatus}):`,
          error,
        );
        return 0;
      }

      return count || 0;
    };

    const suggestionsCount = await getCount('PENDING', 'PENDING');
    const interestedCount = await getCount('INTERESTED', 'PENDING');

    const rejectedPendingCount = await getCount('REJECTED', 'PENDING');
    const rejectedInterestedCount = await getCount('REJECTED', 'INTERESTED');
    const rejectedCount = rejectedPendingCount + rejectedInterestedCount;

    const companyInterestedCount = await getCount('PENDING', 'INTERESTED');
    const companyRejectedCount = await getCount('INTERESTED', 'REJECTED');

    const matchesCount = await getCount('INTERESTED', 'INTERESTED');

    return {
      suggestions: suggestionsCount,
      interested: interestedCount,
      rejected: rejectedCount,
      companyInterested: companyInterestedCount,
      companyRejected: companyRejectedCount,
      matches: matchesCount,
    };
  } catch (error) {
    console.error('Exception counting vacancy matches:', error);
    return {
      suggestions: 0,
      interested: 0,
      rejected: 0,
      companyInterested: 0,
      companyRejected: 0,
      matches: 0,
    };
  }
}
