'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  canJobSeekerChangeRejection,
  getCompanyInterested,
  getCompanyRejected,
  getJobSeekerInterested,
  getJobSeekerRejected,
  getMatches,
  getSuggestions,
  getVacancyMatchesCounts,
  JobSeekerStatus,
  MatchesCountsResponse,
  PaginatedResponse,
  updateJobSeekerStatus,
  VacancyMatch,
} from '~/api/features/matches/job-seeker/actions';

export const vacancyMatchesKeys = {
  all: ['vacancyMatches'] as const,
  suggestions: (jobSeekerId: number, page: number, pageSize: number) =>
    [
      ...vacancyMatchesKeys.all,
      'suggestions',
      jobSeekerId,
      page,
      pageSize,
    ] as const,
  interested: (jobSeekerId: number, page: number, pageSize: number) =>
    [
      ...vacancyMatchesKeys.all,
      'interested',
      jobSeekerId,
      page,
      pageSize,
    ] as const,
  rejected: (jobSeekerId: number, page: number, pageSize: number) =>
    [
      ...vacancyMatchesKeys.all,
      'rejected',
      jobSeekerId,
      page,
      pageSize,
    ] as const,
  companyInterested: (jobSeekerId: number, page: number, pageSize: number) =>
    [
      ...vacancyMatchesKeys.all,
      'companyInterested',
      jobSeekerId,
      page,
      pageSize,
    ] as const,
  companyRejected: (jobSeekerId: number, page: number, pageSize: number) =>
    [
      ...vacancyMatchesKeys.all,
      'companyRejected',
      jobSeekerId,
      page,
      pageSize,
    ] as const,
  matches: (jobSeekerId: number, page: number, pageSize: number) =>
    [
      ...vacancyMatchesKeys.all,
      'matches',
      jobSeekerId,
      page,
      pageSize,
    ] as const,
  canJobSeekerChangeRejection: (matchId: number) =>
    [
      ...vacancyMatchesKeys.all,
      'canJobSeekerChangeRejection',
      matchId,
    ] as const,
};

export const vacancyMatchesCountsKeys = {
  all: ['vacancyMatchesCounts'] as const,
  byJobSeekerId: (jobSeekerId: number) =>
    [...vacancyMatchesCountsKeys.all, jobSeekerId] as const,
};

export function useGetSuggestions(
  jobSeekerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  return useQuery<PaginatedResponse<VacancyMatch>>({
    queryKey: jobSeekerId
      ? vacancyMatchesKeys.suggestions(jobSeekerId, page, pageSize)
      : ['vacancyMatches', 'suggestions', null, page, pageSize],
    queryFn: () => getSuggestions(jobSeekerId as number, page, pageSize),
    enabled: Boolean(jobSeekerId),
  });
}

export function useGetJobSeekerInterested(
  jobSeekerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  return useQuery<PaginatedResponse<VacancyMatch>>({
    queryKey: jobSeekerId
      ? vacancyMatchesKeys.interested(jobSeekerId, page, pageSize)
      : ['vacancyMatches', 'interested', null, page, pageSize],
    queryFn: () =>
      getJobSeekerInterested(jobSeekerId as number, page, pageSize),
    enabled: Boolean(jobSeekerId),
  });
}

export function useGetJobSeekerRejected(
  jobSeekerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  return useQuery<PaginatedResponse<VacancyMatch>>({
    queryKey: jobSeekerId
      ? vacancyMatchesKeys.rejected(jobSeekerId, page, pageSize)
      : ['vacancyMatches', 'rejected', null, page, pageSize],
    queryFn: () => getJobSeekerRejected(jobSeekerId as number, page, pageSize),
    enabled: Boolean(jobSeekerId),
  });
}

export function useGetCompanyInterested(
  jobSeekerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  return useQuery<PaginatedResponse<VacancyMatch>>({
    queryKey: jobSeekerId
      ? vacancyMatchesKeys.companyInterested(jobSeekerId, page, pageSize)
      : ['vacancyMatches', 'companyInterested', null, page, pageSize],
    queryFn: () => getCompanyInterested(jobSeekerId as number, page, pageSize),
    enabled: Boolean(jobSeekerId),
  });
}

export function useGetCompanyRejected(
  jobSeekerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  return useQuery<PaginatedResponse<VacancyMatch>>({
    queryKey: jobSeekerId
      ? vacancyMatchesKeys.companyRejected(jobSeekerId, page, pageSize)
      : ['vacancyMatches', 'companyRejected', null, page, pageSize],
    queryFn: () => getCompanyRejected(jobSeekerId as number, page, pageSize),
    enabled: Boolean(jobSeekerId),
  });
}

export function useGetMatches(
  jobSeekerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  return useQuery<PaginatedResponse<VacancyMatch>>({
    queryKey: jobSeekerId
      ? vacancyMatchesKeys.matches(jobSeekerId, page, pageSize)
      : ['vacancyMatches', 'matches', null, page, pageSize],
    queryFn: () => getMatches(jobSeekerId as number, page, pageSize),
    enabled: Boolean(jobSeekerId),
  });
}

export function useUpdateJobSeekerStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      matchId,
      status,
    }: {
      matchId: number;
      status: JobSeekerStatus;
    }) => updateJobSeekerStatus(matchId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: vacancyMatchesKeys.all });
      queryClient.invalidateQueries({ queryKey: vacancyMatchesCountsKeys.all });
    },
  });
}

export function useMarkJobSeekerInterested() {
  const updateStatus = useUpdateJobSeekerStatus();

  return {
    markAsInterested: (matchId: number) =>
      updateStatus.mutateAsync({ matchId, status: 'INTERESTED' }),
    isLoading: updateStatus.isPending,
    isError: updateStatus.isError,
    error: updateStatus.error,
  };
}

export function useMarkJobSeekerRejected() {
  const updateStatus = useUpdateJobSeekerStatus();

  return {
    markAsRejected: (matchId: number) =>
      updateStatus.mutateAsync({ matchId, status: 'REJECTED' }),
    isLoading: updateStatus.isPending,
    isError: updateStatus.isError,
    error: updateStatus.error,
  };
}

export function useCanJobSeekerChangeRejection(matchId: number | undefined) {
  return useQuery({
    queryKey: matchId
      ? vacancyMatchesKeys.canJobSeekerChangeRejection(matchId)
      : ['vacancyMatches', 'canJobSeekerChangeRejection', null],
    queryFn: () => canJobSeekerChangeRejection(matchId as number),
    enabled: Boolean(matchId),
    refetchInterval: 60 * 1000,
  });
}

export function useVacancyMatchesCounts(jobSeekerId: number | undefined) {
  return useQuery<MatchesCountsResponse>({
    queryKey: jobSeekerId
      ? vacancyMatchesCountsKeys.byJobSeekerId(jobSeekerId)
      : ['vacancyMatchesCounts', null],
    queryFn: () => getVacancyMatchesCounts(jobSeekerId as number),
    enabled: Boolean(jobSeekerId),
    refetchOnMount: 'always',
  });
}
