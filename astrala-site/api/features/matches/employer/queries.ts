'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  canEmployerChangeRejection,
  EmployerMatchesCountsResponse,
  EmployerMatchStatus,
  EmployerVacancyMatch,
  getCompanyInterested,
  getCompanyRejected,
  getEmployerVacancyMatchesCounts,
  getJobSeekerInterested,
  getJobSeekerRejected,
  getMatches,
  getSuggestions,
  PaginatedResponse,
  updateEmployerStatus,
} from '~/api/features/matches/employer/actions';
import { useEmployerVacancyStore } from '~/store/employer-vacancies-store.ts';

export const employerVacancyMatchesKeys = {
  all: ['employerVacancyMatches'] as const,
  suggestions: (
    employerId: number,
    vacancyId: number | null,
    page: number,
    pageSize: number,
  ) =>
    [
      ...employerVacancyMatchesKeys.all,
      'suggestions',
      employerId,
      vacancyId,
      page,
      pageSize,
    ] as const,
  companyInterested: (
    employerId: number,
    vacancyId: number | null,
    page: number,
    pageSize: number,
  ) =>
    [
      ...employerVacancyMatchesKeys.all,
      'companyInterested',
      employerId,
      vacancyId,
      page,
      pageSize,
    ] as const,
  companyRejected: (
    employerId: number,
    vacancyId: number | null,
    page: number,
    pageSize: number,
  ) =>
    [
      ...employerVacancyMatchesKeys.all,
      'companyRejected',
      employerId,
      vacancyId,
      page,
      pageSize,
    ] as const,
  jobSeekerInterested: (
    employerId: number,
    vacancyId: number | null,
    page: number,
    pageSize: number,
  ) =>
    [
      ...employerVacancyMatchesKeys.all,
      'jobSeekerInterested',
      employerId,
      vacancyId,
      page,
      pageSize,
    ] as const,
  jobSeekerRejected: (
    employerId: number,
    vacancyId: number | null,
    page: number,
    pageSize: number,
  ) =>
    [
      ...employerVacancyMatchesKeys.all,
      'jobSeekerRejected',
      employerId,
      vacancyId,
      page,
      pageSize,
    ] as const,
  matches: (
    employerId: number,
    vacancyId: number | null,
    page: number,
    pageSize: number,
  ) =>
    [
      ...employerVacancyMatchesKeys.all,
      'matches',
      employerId,
      vacancyId,
      page,
      pageSize,
    ] as const,
  canEmployerChangeRejection: (matchId: number) =>
    [
      ...employerVacancyMatchesKeys.all,
      'canEmployerChangeRejection',
      matchId,
    ] as const,
};

export const employerVacancyMatchesCountsKeys = {
  all: ['employerVacancyMatchesCounts'] as const,
  byEmployerId: (employerId: number, vacancyId: number | null) =>
    [...employerVacancyMatchesCountsKeys.all, employerId, vacancyId] as const,
};

export function useGetSuggestions(
  employerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  const { selectedVacancyId, vacancies } = useEmployerVacancyStore();

  const emptyResponse: PaginatedResponse<EmployerVacancyMatch> = {
    data: [],
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  const isVacancyOpen =
    selectedVacancyId && vacancies.some(v => v.id === selectedVacancyId);

  return useQuery<PaginatedResponse<EmployerVacancyMatch>>({
    queryKey: employerId
      ? employerVacancyMatchesKeys.suggestions(
          employerId,
          selectedVacancyId,
          page,
          pageSize,
        )
      : ['employerVacancyMatches', 'suggestions', null, null, page, pageSize],
    queryFn: () => {
      if (!isVacancyOpen) return emptyResponse;
      return getSuggestions(
        employerId as number,
        selectedVacancyId,
        page,
        pageSize,
      );
    },
    enabled: Boolean(employerId),
  });
}

export function useGetMatches(
  employerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  const { selectedVacancyId, vacancies } = useEmployerVacancyStore();

  const emptyResponse: PaginatedResponse<EmployerVacancyMatch> = {
    data: [],
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  const isVacancyOpen =
    selectedVacancyId && vacancies.some(v => v.id === selectedVacancyId);

  return useQuery<PaginatedResponse<EmployerVacancyMatch>>({
    queryKey: employerId
      ? employerVacancyMatchesKeys.matches(
          employerId,
          selectedVacancyId,
          page,
          pageSize,
        )
      : ['employerVacancyMatches', 'matches', null, null, page, pageSize],
    queryFn: () => {
      if (!isVacancyOpen) return emptyResponse;
      return getMatches(
        employerId as number,
        selectedVacancyId,
        page,
        pageSize,
      );
    },
    enabled: Boolean(employerId),
  });
}

export function useGetCompanyInterested(
  employerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  const { selectedVacancyId, vacancies } = useEmployerVacancyStore();

  const emptyResponse: PaginatedResponse<EmployerVacancyMatch> = {
    data: [],
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  const isVacancyOpen =
    selectedVacancyId && vacancies.some(v => v.id === selectedVacancyId);

  return useQuery<PaginatedResponse<EmployerVacancyMatch>>({
    queryKey: employerId
      ? employerVacancyMatchesKeys.companyInterested(
          employerId,
          selectedVacancyId,
          page,
          pageSize,
        )
      : [
          'employerVacancyMatches',
          'companyInterested',
          null,
          null,
          page,
          pageSize,
        ],
    queryFn: () => {
      if (!isVacancyOpen) return emptyResponse;
      return getCompanyInterested(
        employerId as number,
        selectedVacancyId,
        page,
        pageSize,
      );
    },
    enabled: Boolean(employerId),
  });
}

export function useGetCompanyRejected(
  employerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  const { selectedVacancyId, vacancies } = useEmployerVacancyStore();

  const emptyResponse: PaginatedResponse<EmployerVacancyMatch> = {
    data: [],
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  const isVacancyOpen =
    selectedVacancyId && vacancies.some(v => v.id === selectedVacancyId);

  return useQuery<PaginatedResponse<EmployerVacancyMatch>>({
    queryKey: employerId
      ? employerVacancyMatchesKeys.companyRejected(
          employerId,
          selectedVacancyId,
          page,
          pageSize,
        )
      : [
          'employerVacancyMatches',
          'companyRejected',
          null,
          null,
          page,
          pageSize,
        ],
    queryFn: () => {
      if (!isVacancyOpen) return emptyResponse;
      return getCompanyRejected(
        employerId as number,
        selectedVacancyId,
        page,
        pageSize,
      );
    },
    enabled: Boolean(employerId),
  });
}

export function useGetJobSeekerInterested(
  employerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  const { selectedVacancyId, vacancies } = useEmployerVacancyStore();

  const emptyResponse: PaginatedResponse<EmployerVacancyMatch> = {
    data: [],
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  const isVacancyOpen =
    selectedVacancyId && vacancies.some(v => v.id === selectedVacancyId);

  return useQuery<PaginatedResponse<EmployerVacancyMatch>>({
    queryKey: employerId
      ? employerVacancyMatchesKeys.jobSeekerInterested(
          employerId,
          selectedVacancyId,
          page,
          pageSize,
        )
      : [
          'employerVacancyMatches',
          'jobSeekerInterested',
          null,
          null,
          page,
          pageSize,
        ],
    queryFn: () => {
      if (!isVacancyOpen) return emptyResponse;
      return getJobSeekerInterested(
        employerId as number,
        selectedVacancyId,
        page,
        pageSize,
      );
    },
    enabled: Boolean(employerId),
  });
}

export function useGetJobSeekerRejected(
  employerId: number | undefined,
  page = 1,
  pageSize = 5,
) {
  const { selectedVacancyId, vacancies } = useEmployerVacancyStore();

  const emptyResponse: PaginatedResponse<EmployerVacancyMatch> = {
    data: [],
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  const isVacancyOpen =
    selectedVacancyId && vacancies.some(v => v.id === selectedVacancyId);

  return useQuery<PaginatedResponse<EmployerVacancyMatch>>({
    queryKey: employerId
      ? employerVacancyMatchesKeys.jobSeekerRejected(
          employerId,
          selectedVacancyId,
          page,
          pageSize,
        )
      : [
          'employerVacancyMatches',
          'jobSeekerRejected',
          null,
          null,
          page,
          pageSize,
        ],
    queryFn: () => {
      if (!isVacancyOpen) return emptyResponse;
      return getJobSeekerRejected(
        employerId as number,
        selectedVacancyId,
        page,
        pageSize,
      );
    },
    enabled: Boolean(employerId),
  });
}

export function useUpdateEmployerStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      matchId,
      status,
    }: {
      matchId: number;
      status: EmployerMatchStatus;
    }) => updateEmployerStatus(matchId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: employerVacancyMatchesKeys.all,
      });
      queryClient.invalidateQueries({
        queryKey: employerVacancyMatchesCountsKeys.all,
      });
    },
  });
}

export function useMarkEmployerInterested() {
  const updateStatus = useUpdateEmployerStatus();

  return {
    markAsInterested: (matchId: number) =>
      updateStatus.mutateAsync({ matchId, status: 'INTERESTED' }),
    isLoading: updateStatus.isPending,
    isError: updateStatus.isError,
    error: updateStatus.error,
  };
}

export function useMarkEmployerRejected() {
  const updateStatus = useUpdateEmployerStatus();

  return {
    markAsRejected: (matchId: number) =>
      updateStatus.mutateAsync({ matchId, status: 'REJECTED' }),
    isLoading: updateStatus.isPending,
    isError: updateStatus.isError,
    error: updateStatus.error,
  };
}

export function useCanEmployerChangeRejection(matchId: number | undefined) {
  return useQuery({
    queryKey: matchId
      ? employerVacancyMatchesKeys.canEmployerChangeRejection(matchId)
      : ['employerVacancyMatches', 'canEmployerChangeRejection', null],
    queryFn: () => canEmployerChangeRejection(matchId as number),
    enabled: Boolean(matchId),
    refetchInterval: 60 * 1000,
  });
}

export function useEmployerVacancyMatchesCounts(
  employerId: number | undefined,
) {
  const { selectedVacancyId, vacancies } = useEmployerVacancyStore();

  const emptyResponse: EmployerMatchesCountsResponse = {
    suggestions: 0,
    companyInterested: 0,
    companyRejected: 0,
    matches: 0,
    jobSeekerInterested: 0,
    jobSeekerRejected: 0,
  };

  const isVacancyOpen =
    selectedVacancyId && vacancies.some(v => v.id === selectedVacancyId);

  return useQuery<EmployerMatchesCountsResponse>({
    queryKey: employerId
      ? employerVacancyMatchesCountsKeys.byEmployerId(
          employerId,
          selectedVacancyId,
        )
      : ['employerVacancyMatchesCounts', null, null],
    queryFn: () => {
      if (!isVacancyOpen) return emptyResponse;

      return getEmployerVacancyMatchesCounts(
        employerId as number,
        selectedVacancyId,
      );
    },
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
  });
}
