'use server';

import { requireAuth, requireChatAccess } from '~/api/security/index.ts';
import { createClient } from '~/supabase/server.ts';

export type ChatMessage = {
  id: number | string;
  chat_id: number;
  employer_id?: number;
  job_seeker_id?: number;
  message_text: string;
  created_at: string;
  read_at?: string;
  edited_at?: string;
  is_edited: boolean;

  sender?: {
    id: number | string;
    full_name: string;
    type: 'employer' | 'job_seeker';
  };
};

export type Chat = {
  id: number;
  vacancy_match_id: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;

  vacancy?: {
    id: number;
    title: string;
    company?: {
      name: string;
    };
    employer?: {
      full_name: string;
    };
  };
  job_seeker?: {
    id: number;
    full_name: string;
  };
  last_message?: {
    message_text: string;
    created_at: string;
    sender_type: 'employer' | 'job_seeker';
  };
  unread_count?: number;
};

export type CreateChatResponse = {
  success: boolean;
  chat_id?: number;
  error?: string;
};

export type SendMessageResponse = {
  success: boolean;
  message_id?: number;
  error?: string;
};

export async function createChat(
  vacancyMatchId: number,
): Promise<CreateChatResponse> {
  const supabase = await createClient();

  try {
    const { data: existingChat, error: checkError } = await supabase
      .from('chats')
      .select('id')
      .eq('vacancy_match_id', vacancyMatchId)
      .eq('is_active', true)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing chat:', checkError);
      return { success: false, error: checkError.message };
    }

    if (existingChat) {
      return { success: true, chat_id: existingChat.id };
    }

    const { data: newChat, error: createError } = await supabase
      .from('chats')
      .insert({
        vacancy_match_id: vacancyMatchId,
        is_active: true,
      })
      .select('id')
      .single();

    if (createError) {
      console.error('Error creating chat:', createError);
      return { success: false, error: createError.message };
    }

    return { success: true, chat_id: newChat.id };
  } catch (error) {
    console.error('Exception creating chat:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function getChatsForUser(
  userId: number,
  userType: 'employer' | 'job_seeker',
): Promise<Chat[]> {
  const authContext = await requireAuth();

  if (userType === 'employer' && authContext.employerId !== userId) {
    throw new Error('Forbidden: Can only access your own chats');
  }
  if (userType === 'job_seeker' && authContext.jobSeekerId !== userId) {
    throw new Error('Forbidden: Can only access your own chats');
  }
  const supabase = await createClient();

  try {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    let vacancy_match_query = supabase.from('vacancy_matches').select(`
        id,
        vacancy:vacancy_id (
          id,
          title,
          company:company_id (name),
          employer:employer_id (full_name)
        ),
        job_seeker:job_seeker_id (id, full_name)
      `);

    if (userType === 'employer') {
      const { data: vacancies } = await supabase
        .from('vacancies')
        .select('id')
        .eq('employer_id', userId);

      if (!vacancies || vacancies.length === 0) return [];

      const vacancyIds = vacancies.map(v => v.id);
      vacancy_match_query = vacancy_match_query.in('vacancy_id', vacancyIds);
    } else {
      vacancy_match_query = vacancy_match_query.eq('job_seeker_id', userId);
    }

    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { data: vacancy_matches, error: matchError } =
      await vacancy_match_query;

    if (matchError) {
      console.error('Error fetching vacancy matches:', matchError);
      return [];
    }

    if (!vacancy_matches || vacancy_matches.length === 0) return [];

    // eslint-disable-next-line @typescript-eslint/naming-convention
    const vacancy_match_ids = vacancy_matches.map(vm => vm.id);

    const { data: chats, error: chatsError } = await supabase
      .from('chats')
      .select('*')
      .in('vacancy_match_id', vacancy_match_ids)
      .eq('is_active', true)
      .order('updated_at', { ascending: false });

    if (chatsError) {
      console.error('Error fetching chats:', chatsError);
      return [];
    }

    if (!chats || chats.length === 0) return [];

    const enhancedChats = await Promise.all(
      chats.map(async chat => {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const vacancy_match = vacancy_matches.find(
          vm => vm.id === chat.vacancy_match_id,
        );

        const { data: lastMessage } = await supabase
          .from('chat_messages')
          .select('message_text, created_at, employer_id, job_seeker_id')
          .eq('chat_id', chat.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        // eslint-disable-next-line @typescript-eslint/naming-convention
        let unread_count = 0;
        if (userType === 'employer') {
          const { count } = await supabase
            .from('chat_messages')
            .select('*', { count: 'exact' })
            .eq('chat_id', chat.id)
            .not('job_seeker_id', 'is', null)
            .is('read_at', null);
          unread_count = count || 0;
        } else {
          const { count } = await supabase
            .from('chat_messages')
            .select('*', { count: 'exact' })
            .eq('chat_id', chat.id)
            .not('employer_id', 'is', null)
            .is('read_at', null);
          unread_count = count || 0;
        }

        return {
          ...chat,
          vacancy: vacancy_match?.vacancy,
          job_seeker: vacancy_match?.job_seeker,
          last_message: lastMessage
            ? {
                message_text: lastMessage.message_text,
                created_at: lastMessage.created_at,
                sender_type: lastMessage.employer_id
                  ? 'employer'
                  : 'job_seeker',
              }
            : null,
          unread_count,
        };
      }),
    );

    return enhancedChats;
  } catch (error) {
    console.error('Exception fetching chats for user:', error);
    return [];
  }
}

export async function getChatMessages(chatId: number): Promise<ChatMessage[]> {
  await requireChatAccess(chatId);
  const supabase = await createClient();

  try {
    const { data: messages, error } = await supabase
      .from('chat_messages')
      .select(
        `
        *,
        employer:employer_id (full_name),
        job_seeker:job_seeker_id (full_name)
      `,
      )
      .eq('chat_id', chatId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching chat messages:', error);
      return [];
    }

    if (!messages) return [];

    return messages.map(message => ({
      ...message,
      sender: message.employer_id
        ? {
            id: message.employer_id,
            full_name: message.employer?.full_name || 'Unknown Employer',
            type: 'employer' as const,
          }
        : {
            id: message.job_seeker_id!,
            full_name: message.job_seeker?.full_name || 'Unknown Job Seeker',
            type: 'job_seeker' as const,
          },
    }));
  } catch (error) {
    console.error('Exception fetching chat messages:', error);
    return [];
  }
}

export async function sendMessage(
  chatId: number,
  messageText: string,
  senderId: number,
  senderType: 'employer' | 'job_seeker',
): Promise<SendMessageResponse> {
  const authContext = await requireChatAccess(chatId);

  if (senderType === 'employer' && authContext.employerId !== senderId) {
    throw new Error('Forbidden: Can only send from your account');
  }
  if (senderType === 'job_seeker' && authContext.jobSeekerId !== senderId) {
    throw new Error('Forbidden: Can only send from your account');
  }
  const supabase = await createClient();

  try {
    const messageData = {
      chat_id: chatId,
      message_text: messageText,
      ...(senderType === 'employer'
        ? { employer_id: senderId, job_seeker_id: null }
        : { job_seeker_id: senderId, employer_id: null }),
    };

    const { data: newMessage, error: messageError } = await supabase
      .from('chat_messages')
      .insert(messageData)
      .select('id')
      .single();

    if (messageError) {
      console.error('Error sending message:', messageError);
      return { success: false, error: messageError.message };
    }

    const { error: updateError } = await supabase
      .from('chats')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', chatId);

    if (updateError) {
      console.error('Error updating chat timestamp:', updateError);
    }

    return { success: true, message_id: newMessage.id };
  } catch (error) {
    console.error('Exception sending message:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function markMessagesAsRead(
  chatId: number,
  userId: number,
  userType: 'employer' | 'job_seeker',
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    let query = supabase
      .from('chat_messages')
      .update({ read_at: new Date().toISOString() })
      .eq('chat_id', chatId)
      .is('read_at', null);

    if (userType === 'employer') {
      query = query.not('job_seeker_id', 'is', null);
    } else {
      query = query.not('employer_id', 'is', null);
    }

    const { error } = await query.select();

    if (error) {
      console.error('Error marking messages as read:', error);
      return { success: false, error: error.message };
    }
    return { success: true };
  } catch (error) {
    console.error('Exception marking messages as read:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export type UnreadChatsCountResponse = {
  unreadChats: number;
};

export async function getUnreadChatsCount(
  userId: number,
  userType: 'employer' | 'job_seeker',
): Promise<UnreadChatsCountResponse> {
  const supabase = await createClient();

  try {
    let chatsQuery;

    if (userType === 'employer') {
      chatsQuery = supabase
        .from('chats')
        .select(
          `
          id,
          vacancy_match_id,
          vacancy_matches!inner(
            vacancy_id,
            vacancies!inner(employer_id)
          )
        `,
        )
        .eq('vacancy_matches.vacancies.employer_id', userId)
        .eq('is_active', true);
    } else {
      chatsQuery = supabase
        .from('chats')
        .select(
          `
          id,
          vacancy_match_id,
          vacancy_matches!inner(job_seeker_id)
        `,
        )
        .eq('vacancy_matches.job_seeker_id', userId)
        .eq('is_active', true);
    }

    const { data: userChats, error: chatsError } = await chatsQuery;

    if (chatsError || !userChats?.length) {
      return { unreadChats: 0 };
    }

    const chatIds = userChats.map(chat => chat.id);

    const { data: unreadMessages, error: messagesError } = await supabase
      .from('chat_messages')
      .select('chat_id, employer_id, job_seeker_id')
      .in('chat_id', chatIds)
      .is('read_at', null);

    if (messagesError) {
      return { unreadChats: 0 };
    }

    const filteredMessages =
      unreadMessages?.filter(msg => {
        if (userType === 'employer') {
          return msg.employer_id !== userId;
        }
        return msg.job_seeker_id !== userId;
      }) || [];

    const unreadChatIds = new Set(filteredMessages.map(msg => msg.chat_id));

    return { unreadChats: unreadChatIds.size };
  } catch (error) {
    return { unreadChats: 0 };
  }
}
