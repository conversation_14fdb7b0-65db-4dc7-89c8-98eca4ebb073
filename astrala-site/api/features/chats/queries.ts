'use client';

import { useEffect, useRef } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  Chat,
  ChatMessage,
  createChat,
  getChatMessages,
  getChatsForUser,
  getUnreadChatsCount,
  markMessagesAsRead,
  sendMessage,
  UnreadChatsCountResponse,
} from '~/api/features/chats/actions';
import { usePageTitle } from '~/hooks/usePageTitle.tsx';
import { createClient } from '~/supabase/client';
import { soundManager } from '~/utils/sound';

export const chatKeys = {
  all: ['chats'] as const,
  userChats: (userId: number, userType: 'employer' | 'job_seeker') =>
    [...chatKeys.all, 'user', userId, userType] as const,
  chatMessages: (chatId: number) =>
    [...chatKeys.all, 'messages', chatId] as const,
};

export function useGetChatsForUser(
  userId: number | undefined,
  userType: 'employer' | 'job_seeker',
) {
  return useQuery<Chat[]>({
    queryKey: userId
      ? chatKeys.userChats(userId, userType)
      : ['chats', 'user', null, userType],
    queryFn: () => getChatsForUser(userId as number, userType),
    enabled: Boolean(userId),
    refetchOnWindowFocus: true,
    staleTime: 30 * 1000,
  });
}

export function useGetChatMessages(chatId: number | undefined) {
  return useQuery<ChatMessage[]>({
    queryKey: chatId
      ? chatKeys.chatMessages(chatId)
      : ['chats', 'messages', null],
    queryFn: () => getChatMessages(chatId as number),
    enabled: Boolean(chatId),
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateChat() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (vacancyMatchId: number) => {
      return await createChat(vacancyMatchId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: chatKeys.all,
      });
    },
  });
}

const generateTempId = () => `temp_${Date.now()}_${Math.random()}`;

export function useSendMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      chatId,
      messageText,
      senderId,
      senderType,
    }: {
      chatId: number;
      messageText: string;
      senderId: number;
      senderType: 'employer' | 'job_seeker';
    }) => {
      return await sendMessage(chatId, messageText, senderId, senderType);
    },

    onMutate: async ({ chatId, messageText, senderId, senderType }) => {
      await queryClient.cancelQueries({
        queryKey: chatKeys.chatMessages(chatId),
      });

      const previousMessages = queryClient.getQueryData<ChatMessage[]>(
        chatKeys.chatMessages(chatId),
      );

      const optimisticMessage: ChatMessage & { _isOptimistic?: boolean } = {
        id: generateTempId(),
        chat_id: chatId,
        message_text: messageText,
        created_at: new Date().toISOString(),
        read_at: undefined,
        employer_id: senderType === 'employer' ? senderId : undefined,
        job_seeker_id: senderType === 'job_seeker' ? senderId : undefined,
        sender: undefined,
        is_edited: false,
        _isOptimistic: true,
      };

      queryClient.setQueryData<ChatMessage[]>(
        chatKeys.chatMessages(chatId),
        old => (old ? [...old, optimisticMessage] : [optimisticMessage]),
      );

      return { previousMessages, optimisticMessage };
    },

    onSuccess: (data, variables, context) => {
      if (data.success && data.message_id) {
        queryClient.setQueryData<(ChatMessage & { _isOptimistic?: boolean })[]>(
          chatKeys.chatMessages(variables.chatId),
          old => {
            if (!old) return [];

            return old.map(msg => {
              if (msg.id === context?.optimisticMessage.id) {
                return {
                  ...msg,
                  id: data.message_id as number,
                  _isOptimistic: false,
                };
              }
              return msg;
            });
          },
        );

        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: chatKeys.userChats(
              variables.senderId,
              variables.senderType,
            ),
          });
        }, 100);
      } else {
        console.error('Server response missing message data:', data);
        queryClient.invalidateQueries({
          queryKey: chatKeys.chatMessages(variables.chatId),
        });
      }
    },

    onError: (error, variables, context) => {
      console.error('Failed to send message:', error);

      if (context?.previousMessages) {
        queryClient.setQueryData(
          chatKeys.chatMessages(variables.chatId),
          context.previousMessages,
        );
      }
    },
  });
}

export function useMarkMessagesAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      chatId,
      userId,
      userType,
    }: {
      chatId: number;
      userId: number;
      userType: 'employer' | 'job_seeker';
    }) => {
      return await markMessagesAsRead(chatId, userId, userType);
    },
    onSuccess: (data, variables) => {
      if (data.success) {
        queryClient.invalidateQueries({
          queryKey: chatKeys.chatMessages(variables.chatId),
        });
        queryClient.invalidateQueries({
          queryKey: chatKeys.userChats(variables.userId, variables.userType),
        });
        queryClient.invalidateQueries({
          queryKey: ['unreadChats', variables.userId, variables.userType],
        });
      }
    },
  });
}

export function useAutoMarkAsRead(
  chatId: number | undefined,
  userId: number | undefined,
  userType: 'employer' | 'job_seeker' | undefined,
) {
  const markAsReadMutation = useMarkMessagesAsRead();
  const markAsReadTimeoutRef = useRef<NodeJS.Timeout>();

  const markMessagesAsReadDelayed = () => {
    if (!chatId || !userId || !userType) return;

    if (markAsReadTimeoutRef.current) {
      clearTimeout(markAsReadTimeoutRef.current);
    }

    markAsReadTimeoutRef.current = setTimeout(() => {
      if (!document.hidden) {
        markAsReadMutation.mutate({
          chatId,
          userId,
          userType,
        });
      }
    }, 1000);
  };

  useEffect(() => {
    return () => {
      if (markAsReadTimeoutRef.current) {
        clearTimeout(markAsReadTimeoutRef.current);
      }
    };
  }, []);

  return markMessagesAsReadDelayed;
}

export function useChatRealtime(
  chatId: number | undefined,
  userId: number | undefined,
  userType: 'employer' | 'job_seeker' | undefined,
) {
  const queryClient = useQueryClient();
  const { showNewMessageIndicator } = usePageTitle();
  const markMessagesAsReadDelayed = useAutoMarkAsRead(chatId, userId, userType);

  useEffect(() => {
    if (!chatId || !userId || !userType) return;

    const supabase = createClient();

    const messagesChannel = supabase
      .channel(`chat-${chatId}-${Date.now()}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
        },
        async payload => {
          if (payload.new && Number(payload.new.chat_id) === Number(chatId)) {
            const isFromOtherUser =
              userType === 'employer'
                ? payload.new.job_seeker_id !== null
                : payload.new.employer_id !== null;

            queryClient.invalidateQueries({
              queryKey: chatKeys.chatMessages(chatId),
            });

            queryClient.invalidateQueries({
              queryKey: chatKeys.userChats(userId, userType),
            });

            if (isFromOtherUser) {
              if (document.hidden) {
                soundManager.playNotification();
                showNewMessageIndicator();
              } else {
                markMessagesAsReadDelayed();
              }
            }
          }
        },
      )
      .subscribe();

    return () => {
      supabase.removeChannel(messagesChannel);
    };
  }, [
    chatId,
    userId,
    userType,
    queryClient,
    showNewMessageIndicator,
    markMessagesAsReadDelayed,
  ]);
}

export function useChatsShortPolling(
  userId: number | undefined,
  userType: 'employer' | 'job_seeker' | undefined,
  activeChatId: number | undefined,
) {
  const queryClient = useQueryClient();
  const prevUnreadCountsRef = useRef<{ [chatId: number]: number }>({});
  const { showNewMessageIndicator } = usePageTitle();

  useEffect(() => {
    if (!userId || !userType) return;

    const interval = setInterval(() => {
      queryClient.invalidateQueries({
        queryKey: chatKeys.userChats(userId, userType),
      });

      setTimeout(() => {
        const chats = queryClient.getQueryData<Chat[]>(
          chatKeys.userChats(userId, userType),
        );
        if (chats) {
          chats.forEach(chat => {
            const prevCount = prevUnreadCountsRef.current[chat.id] || 0;
            const currentCount = chat.unread_count || 0;

            if (currentCount > prevCount && chat.id !== activeChatId) {
              soundManager.playNotification();
              showNewMessageIndicator();
            }

            prevUnreadCountsRef.current[chat.id] = currentCount;
          });
        }
      }, 100);
    }, 10000);

    return () => clearInterval(interval);
  }, [userId, userType, activeChatId, queryClient, showNewMessageIndicator]);
}

export function useMarkInitialAsRead(
  chatId: number | undefined,
  userId: number | undefined,
  userType: 'employer' | 'job_seeker' | undefined,
  messages: ChatMessage[] | undefined,
) {
  const markAsReadMutation = useMarkMessagesAsRead();
  const hasMarkedInitialRef = useRef<number | null>(null);

  useEffect(() => {
    if (!chatId || !userId || !userType || !messages) return;

    if (hasMarkedInitialRef.current === chatId) return;

    const hasUnreadMessages = messages.some(msg => {
      const isFromOtherUser =
        userType === 'employer'
          ? msg.job_seeker_id !== null
          : msg.employer_id !== null;
      return isFromOtherUser && !msg.read_at;
    });

    if (hasUnreadMessages && !document.hidden) {
      setTimeout(() => {
        markAsReadMutation.mutate({
          chatId,
          userId,
          userType,
        });
        hasMarkedInitialRef.current = chatId;
      }, 500);
    }
  }, [chatId, userId, userType, messages, markAsReadMutation]);

  useEffect(() => {
    hasMarkedInitialRef.current = null;
  }, [chatId]);
}

export function useMarkAsReadOnFocus(
  chatId: number | undefined,
  userId: number | undefined,
  userType: 'employer' | 'job_seeker' | undefined,
) {
  const markMessagesAsReadDelayed = useAutoMarkAsRead(chatId, userId, userType);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && chatId && userId && userType) {
        markMessagesAsReadDelayed();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
    };
  }, [chatId, userId, userType, markMessagesAsReadDelayed]);
}

export function useChatPage(
  userId: number | undefined,
  userType: 'employer' | 'job_seeker' | undefined,
  activeChatId: number | undefined,
) {
  const chatsQuery = useGetChatsForUser(userId, userType!);
  const messagesQuery = useGetChatMessages(activeChatId);

  const messages = (messagesQuery.data || []) as (ChatMessage & {
    _isOptimistic?: boolean;
  })[];

  useChatRealtime(activeChatId, userId, userType);
  useChatsShortPolling(userId, userType, activeChatId);

  useMarkInitialAsRead(activeChatId, userId, userType, messages);
  useMarkAsReadOnFocus(activeChatId, userId, userType);

  return {
    chats: chatsQuery.data || [],
    messages,
    isLoadingChats: chatsQuery.isLoading,
    isLoadingMessages: messagesQuery.isLoading,
    chatsError: chatsQuery.error,
    messagesError: messagesQuery.error,
  };
}

export function useUnreadChatsCount(
  userId: number | undefined,
  userType: 'employer' | 'job_seeker',
) {
  return useQuery<UnreadChatsCountResponse>({
    queryKey: userId
      ? ['unreadChats', userId, userType]
      : ['unreadChats', null, userType],
    queryFn: () => getUnreadChatsCount(userId as number, userType),
    enabled: Boolean(userId),
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
  });
}
