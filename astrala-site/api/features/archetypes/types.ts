export type Dimension = {
  id: number;
  name: string;
  left_label: string;
  right_label: string;
};

export type DimensionWithScore = Dimension & {
  score: number;
  icon: React.ComponentType;
};

export type TopArchetype = {
  id: number;
  name: string;
  description: string;
  metaphor: string;
  dimensions_intro: string;
  wheel_description: string;
  role_alignments: string[];
  role_description: string;
  about_profile: string;
};

export type UserDimensionScore = {
  dimensionId: number;
  score: number;
};

export type ArchetypeScore = {
  id: number;
  name: string;
  description: string;
  distance: number;
  percentage: number;
};

export type ArchetypeHistoryItem = {
  archetype_id: number;
  created_at: string;
  archetypes: TopArchetype;
};
