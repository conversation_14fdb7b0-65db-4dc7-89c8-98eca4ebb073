'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  getArchetypeById,
  getArchetypeHistory,
  getArchetypePinnedStatus,
  getDimensions,
  getTopArchetype,
  getUserArchetypeScores,
  getUserDimensionScores,
  toggleArchetypePinned,
} from './actions.ts';
import type { Dimension } from './types.ts';

export const archetypeKeys = {
  all: ['archetypes'] as const,
  userScores: (jobSeekerId: number) =>
    [...archetypeKeys.all, 'scores', jobSeekerId] as const,
  dimensions: () => [...archetypeKeys.all, 'dimensions'] as const,
  detail: (archetypeId: number) =>
    [...archetypeKeys.all, 'detail', archetypeId] as const,
  pinnedStatus: (jobSeekerId: number) =>
    [...archetypeKeys.all, 'pinned', jobSeekerId] as const,
  dimensionScores: (jobSeekerId: number) =>
    [...archetypeKeys.all, 'dimension-scores', jobSeekerId] as const,
  topArchetype: (jobSeekerId: number) =>
    [...archetypeKeys.all, 'top-archetype', jobSeekerId] as const,
  history: (jobSeekerId: number) =>
    [...archetypeKeys.all, 'history', jobSeekerId] as const,
};

export function useGetUserArchetypeScores(jobSeekerId: number | undefined) {
  return useQuery({
    queryKey: jobSeekerId
      ? archetypeKeys.userScores(jobSeekerId)
      : [...archetypeKeys.all, 'scores', null],
    queryFn: () => getUserArchetypeScores(jobSeekerId as number),
  });
}

export function useGetDimensions() {
  const result = useQuery<Dimension[]>({
    queryKey: archetypeKeys.dimensions(),
    queryFn: () => {
      return getDimensions();
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });

  return result;
}

export function useGetArchetypeById(archetypeId: number | undefined) {
  return useQuery({
    queryKey: archetypeId
      ? archetypeKeys.detail(archetypeId)
      : [...archetypeKeys.all, 'detail', null],
    queryFn: () => getArchetypeById(archetypeId as number),
    enabled: Boolean(archetypeId),
  });
}

export function useGetArchetypePinnedStatus(jobSeekerId: number | undefined) {
  return useQuery({
    queryKey: jobSeekerId
      ? archetypeKeys.pinnedStatus(jobSeekerId)
      : [...archetypeKeys.all, 'pinned', null],
    queryFn: () => getArchetypePinnedStatus(jobSeekerId as number),
    enabled: Boolean(jobSeekerId),
  });
}

export function useToggleArchetypePinned() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobSeekerId: number) => toggleArchetypePinned(jobSeekerId),
    onSuccess: (_, jobSeekerId) => {
      queryClient.invalidateQueries({
        queryKey: archetypeKeys.pinnedStatus(jobSeekerId),
      });
    },
  });
}

export function useGetUserDimensionScores(jobSeekerId: number | undefined) {
  return useQuery({
    queryKey: jobSeekerId
      ? archetypeKeys.dimensionScores(jobSeekerId)
      : [...archetypeKeys.all, 'dimension-scores', null],
    queryFn: () => getUserDimensionScores(jobSeekerId as number),
    enabled: Boolean(jobSeekerId),
  });
}

export function useGetTopArchetype(jobSeekerId: number | undefined) {
  return useQuery({
    queryKey: jobSeekerId
      ? archetypeKeys.topArchetype(jobSeekerId)
      : [...archetypeKeys.all, 'top-archetype', null],
    queryFn: () => getTopArchetype(jobSeekerId as number),
    enabled: Boolean(jobSeekerId),
  });
}

export function useGetArchetypeHistory(jobSeekerId: number | undefined) {
  return useQuery({
    queryKey: jobSeekerId
      ? archetypeKeys.history(jobSeekerId)
      : [...archetypeKeys.all, 'history', null],
    queryFn: () => getArchetypeHistory(jobSeekerId as number),
    enabled: Boolean(jobSeekerId),
  });
}
