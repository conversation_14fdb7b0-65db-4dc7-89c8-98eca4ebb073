'use client';

import { useRouter } from 'next/navigation';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { getSession, signIn, signOut, signUp } from './actions.ts';
import type { UserRole } from '~/types/auth.types.ts';

export type { SignInParams, SignUpParams } from './actions.ts';

export const authKeys = {
  session: ['auth', 'session'] as const,
};

export function useSignUp() {
  const router = useRouter();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      email: string;
      password: string;
      role: UserRole;
      fullName?: string;
      jobRole?: string;
      companyId?: number;
      invitationToken?: string;
    }) => {
      const result = await signUp(params);

      if (result.error) {
        throw new Error(result.error);
      }

      return result.data;
    },
    onSuccess: data => {
      if (data) {
        if (data.role === 'job_seeker') {
          router.push('/onboarding/job-seeker');
        } else {
          router.push('/onboarding/employer');
        }
      }

      queryClient.invalidateQueries({
        queryKey: authKeys.session,
      });
    },
  });
}

export function useSignIn() {
  const router = useRouter();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: { email: string; password: string }) => {
      const result = await signIn(params);

      if (result.error) {
        throw new Error(result.error);
      }
      return result.data;
    },
    onSuccess: data => {
      if (!data?.role) {
        router.push('/');
      } else if (data.role === 'job_seeker') {
        router.push('/dashboard/job-seeker/suggestions');
      } else {
        router.push('/dashboard/employer/suggestions');
      }
      queryClient.invalidateQueries({
        queryKey: authKeys.session,
      });
    },
  });
}

export function useSignOut() {
  const router = useRouter();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const result = await signOut();

      if (result.error) {
        throw new Error(result.error);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: authKeys.session,
      });
      router.refresh();
      router.push('/signin');
    },
  });
}

export function useSession() {
  return useQuery({
    queryKey: authKeys.session,
    queryFn: async () => {
      const result = await getSession();

      if (result.error) {
        throw new Error(result.error);
      }

      return result.data;
    },
    refetchOnWindowFocus: true,
  });
}
