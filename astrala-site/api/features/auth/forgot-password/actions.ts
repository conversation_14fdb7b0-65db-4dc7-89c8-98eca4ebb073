'use server';

import { createClient } from '~/supabase/server.ts';

export type RequestPasswordResetParams = {
  email: string;
};

export async function requestPasswordReset({
  email,
}: RequestPasswordResetParams) {
  try {
    const redirectUrl = `${process.env.APP_URL}/auth/confirm?next=/update-password`;
    const supabase = await createClient();

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl,
    });

    if (error) {
      console.error('Error requesting password reset:', error);
      return { error: null, data: { success: true } };
    }

    return { error: null, data: { success: true } };
  } catch (error) {
    console.error('Password reset error:', error);
    return { error: null, data: { success: true } };
  }
}

export type UpdatePasswordParams = {
  password: string;
};

export async function updatePassword({ password }: UpdatePasswordParams) {
  try {
    const supabase = await createClient();

    const { error } = await supabase.auth.updateUser({
      password,
    });

    if (error) {
      return { error: error.message, data: null };
    }

    return { error: null, data: { success: true } };
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred',
      data: null,
    };
  }
}
