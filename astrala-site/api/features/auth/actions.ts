'use server';

import {
  markInvitationTokenAsUsed,
  validateInvitationToken,
} from '~/api/features/company/actions';
import { createClient } from '~/supabase/server.ts';
import type { UserRole } from '~/types/auth.types.ts';

export type SignUpParams = {
  email: string;
  password: string;
  role: UserRole;
  fullName?: string;
  jobRole?: string;
  companyId?: number;
  invitationToken?: string;
};

export type SignInParams = {
  email: string;
  password: string;
};

export async function signUp({
  email,
  password,
  role,
  fullName,
  jobRole,
  companyId,
  invitationToken,
}: SignUpParams) {
  const supabase = await createClient();

  if (invitationToken) {
    const tokenValidation = await validateInvitationToken(invitationToken);
    if (!tokenValidation.valid) {
      return {
        error: tokenValidation.error || 'Invalid invitation token',
        data: null,
        errorType: tokenValidation.errorType,
      };
    }

    const supabase = await createClient();
    const { data: tokenData, error: tokenError } = await supabase
      .from('invitation_tokens')
      .select('is_used')
      .eq('token', invitationToken)
      .single();

    if (tokenError || tokenData?.is_used) {
      return {
        error: 'This invitation has already been used',
        data: null,
        errorType: 'already_used',
      };
    }

    if (!companyId && tokenValidation.companyId) {
      companyId = tokenValidation.companyId;
    }
  }
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
  });

  if (authError) {
    return { error: authError.message, data: null };
  }

  if (!authData.user) {
    return { error: 'Failed to create user account', data: null };
  }

  const userData = {
    user_id: authData.user.id,
    role,
    updated_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
  };

  const { error: userError } = await supabase
    .from('users')
    .insert(userData)
    .select();

  if (userError) {
    return {
      error: `Failed to create user record: ${userError.message}`,
      data: null,
    };
  }

  if (role === 'job_seeker') {
    const { error: seekerError } = await supabase.from('job_seekers').insert({
      user_id: authData.user.id,
      email,
      created_at: new Date().toISOString(),
    });

    if (seekerError) {
      return {
        error: `Failed to create job seeker profile: ${seekerError.message}`,
        data: null,
      };
    }
  } else if (role === 'employer') {
    if (companyId && fullName && jobRole) {
      const { data: employerData, error: employerError } = await supabase
        .from('employers')
        .insert({
          user_id: authData.user.id,
          company_id: companyId,
          full_name: fullName,
          role: jobRole,
          email,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (employerError) {
        return {
          error: `Failed to create employer profile: ${employerError.message}`,
          data: null,
        };
      }

      if (invitationToken && employerData) {
        await markInvitationTokenAsUsed(invitationToken, employerData.id);
      }

      return {
        error: null,
        data: {
          user: authData.user,
          role,
          completedOnboarding: true,
        },
      };
    }

    const { error: employerError } = await supabase.from('employers').insert({
      user_id: authData.user.id,
      email,
      created_at: new Date().toISOString(),
    });

    if (employerError) {
      return {
        error: `Failed to create employer profile: ${employerError.message}`,
        data: null,
      };
    }
  }

  return { error: null, data: { user: authData.user, role } };
}

export async function signIn({ email, password }: SignInParams) {
  const supabase = await createClient();

  const { data: authData, error: authError } =
    await supabase.auth.signInWithPassword({
      email,
      password,
    });

  if (authError) {
    return { error: authError.message, data: null };
  }

  if (!authData.user) {
    return { error: 'Failed to sign in', data: null };
  }

  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('role')
    .eq('user_id', authData.user.id)
    .single();

  if (userError) {
    if (userError.code === 'PGRST116') {
      return { error: null, data: { user: authData.user, role: null } };
    }
    return {
      error: `Failed to get user role: ${userError.message}`,
      data: null,
    };
  }

  return {
    error: null,
    data: { user: authData.user, role: userData.role as UserRole },
  };
}

export async function signOut() {
  const supabase = await createClient();

  const { error } = await supabase.auth.signOut();

  if (error) {
    return { error: error.message };
  }

  return { error: null };
}

export async function getSession() {
  const supabase = await createClient();

  const { data: sessionData, error: sessionError } =
    await supabase.auth.getSession();

  if (sessionError) {
    return { error: sessionError.message, data: null };
  }

  if (!sessionData.session) {
    return { error: null, data: null };
  }

  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('role')
    .eq('user_id', sessionData.session.user.id)
    .single();

  if (userError && userError.code !== 'PGRST116') {
    return { error: userError.message, data: null };
  }

  const sessionWithRole = {
    ...sessionData.session,
    role: userData?.role as UserRole | undefined,
  };

  return { error: null, data: sessionWithRole };
}
