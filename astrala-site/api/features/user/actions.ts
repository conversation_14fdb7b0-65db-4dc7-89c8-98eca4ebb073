'use server';

import { Database } from '~/shared/db/generated/types.ts';

import { createClient } from '~/supabase/server.ts';

type UserProfile = {
  id: string;
  role: 'job_seeker' | 'employer';
  profile:
    | Database['public']['Tables']['job_seekers']['Row']
    | Database['public']['Tables']['employers']['Row']
    | null;
};

export async function getUserProfile(
  authUserId: string,
): Promise<UserProfile | null> {
  if (!authUserId) return null;

  const supabase = await createClient();

  try {
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('user_id', authUserId)
      .maybeSingle();

    if (userError) {
      return null;
    }

    if (!userData?.role) {
      return null;
    }

    let profileData = null;
    if (userData.role === 'job_seeker') {
      const { data, error } = await supabase
        .from('job_seekers')
        .select('*')
        .eq('user_id', authUserId);

      if (error) {
        console.error(error);
      } else if (data && data.length > 0) {
        profileData = data[0];
      }
    } else if (userData.role === 'employer') {
      const { data, error } = await supabase
        .from('employers')
        .select('*')
        .eq('user_id', authUserId);

      if (error) {
        console.error(error);
      } else if (data && data.length > 0) {
        profileData = data[0];
      }
    }

    return {
      id: authUserId,
      role: userData.role,
      profile: profileData,
    };
  } catch (error) {
    return null;
  }
}
