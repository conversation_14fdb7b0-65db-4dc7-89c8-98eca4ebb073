'use client';

import { useQuery } from '@tanstack/react-query';

import { getUserProfile } from './actions.ts';

export const userKeys = {
  all: ['users'] as const,
  profile: (userId: string) => [...userKeys.all, 'profile', userId] as const,
  jobSeekerProfile: (userId: string) =>
    [...userKeys.all, 'jobSeekerProfile', userId] as const,
  employerProfile: (userId: string) =>
    [...userKeys.all, 'employerProfile', userId] as const,
};

export function useGetUserProfile(userId: string | undefined) {
  return useQuery({
    queryKey: userId ? userKeys.profile(userId) : ['users', 'profile', null],
    queryFn: () => getUserProfile(userId as string),
    enabled: Boolean(userId),
  });
}
