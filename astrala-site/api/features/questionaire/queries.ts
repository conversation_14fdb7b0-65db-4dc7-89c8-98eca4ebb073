'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  getQuestionGroups,
  getQuestionnaireAnswers,
  QuestionnaireAnswer,
  saveQuestionnaireAnswers,
} from './actions';
import { archetypeKeys } from '~/api/features/archetypes/queries.ts';
import {
  vacancyMatchesCountsKeys,
  vacancyMatchesKeys,
} from '~/api/features/matches/job-seeker/queries.ts';

export const questionnaireKeys = {
  all: ['questionnaire'] as const,
  groups: () => [...questionnaireKeys.all, 'groups'] as const,
  answers: () => [...questionnaireKeys.all, 'answers'] as const,
  answersById: (jobSeekerId: number) =>
    [...questionnaireKeys.answers(), jobSeekerId] as const,
};

export function useQuestionGroups() {
  return useQuery({
    queryKey: questionnaireKeys.groups(),
    queryFn: async () => {
      try {
        return await getQuestionGroups();
      } catch (error) {
        console.error('Error in useQuestionGroups:', error);
        throw error;
      }
    },
  });
}

export function useQuestionnaireAnswers(jobSeekerId?: number) {
  return useQuery({
    queryKey: jobSeekerId
      ? questionnaireKeys.answersById(jobSeekerId)
      : questionnaireKeys.answers(),
    queryFn: async () => {
      try {
        return await getQuestionnaireAnswers(jobSeekerId);
      } catch (error) {
        console.error('Error in useQuestionnaireAnswers:', error);
        throw error;
      }
    },
    enabled: Boolean(jobSeekerId),
  });
}

export function useSaveQuestionnaireAnswers() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      jobSeekerId,
      answers,
    }: {
      jobSeekerId: number;
      answers: QuestionnaireAnswer[];
    }) => {
      return await saveQuestionnaireAnswers(jobSeekerId, answers);
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: questionnaireKeys.answersById(variables.jobSeekerId),
      });

      queryClient.invalidateQueries({
        queryKey: ['user'],
      });

      queryClient.invalidateQueries({ queryKey: vacancyMatchesKeys.all });

      queryClient.invalidateQueries({ queryKey: vacancyMatchesCountsKeys.all });

      queryClient.invalidateQueries({ queryKey: archetypeKeys.all });
    },
  });
}
