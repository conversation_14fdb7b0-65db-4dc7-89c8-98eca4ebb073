'use client';

import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { createEmployerCompany } from './actions.ts';
import { authKeys } from '~/api/features/auth/queries.ts';
import { userKeys } from '~/api/features/user/queries.ts';

export function useCreateEmployerCompany() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: async ({
      userId,
      companyName,
      fullName,
      role,
    }: {
      userId: string;
      companyName: string;
      fullName: string;
      role: string;
    }) => {
      const result = await createEmployerCompany({
        userId,
        companyName,
        fullName,
        role,
      });

      if (result.error) {
        throw new Error(String(result.error));
      }

      return result.data;
    },
    onSuccess: async (_, variables) => {
      await queryClient.invalidateQueries({
        queryKey: authKeys.session,
      });

      await queryClient.refetchQueries({
        queryKey: userKeys.profile(variables.userId),
      });

      router.push('/onboarding/employer');
    },
  });
}
