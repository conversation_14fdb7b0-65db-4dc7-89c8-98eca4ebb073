import {
  BillingPeriod,
  SubscriptionPlan,
  SubscriptionStatus,
} from '~/constants/subscription.ts';

export type CompanySubscription = {
  id: number;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  subscription_status: SubscriptionStatus;
  subscription_plan: SubscriptionPlan | null;
  billing_period: BillingPeriod | null;
  current_period_start: string | null;
  current_period_end: string | null;
  canceled_at: string | null;
};

export type SubscriptionLimits = {
  seats: number;
  jobs: number;
  matches: number;
};

export type SubscriptionUsage = {
  seats: number;
  jobs: number;
  matches: number;
};
