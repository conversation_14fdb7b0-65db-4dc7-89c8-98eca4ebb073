'use server';

import Strip<PERSON> from 'stripe';

import { createClient } from '~/supabase/server';

const stripe = process.env.STRIPE_SECRET_KEY
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-03-31.basil',
    })
  : null;

type CompanyUpdateData = {
  subscription_status?: string;
  subscription_plan?: string;
  billing_period?: string;
  current_period_start?: string;
  current_period_end?: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  canceled_at?: string | null;
  updated_at: string;
};

type StripeInvoiceWithParent = Stripe.Invoice & {
  parent?: {
    subscription_details?: {
      subscription: string;
      metadata: Record<string, string>;
    };
  };
};

export async function handleSubscriptionWebhook(
  event: Stripe.Event,
): Promise<void> {
  const supabase = await createClient();

  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;

        if (session.mode !== 'subscription') break;

        const companyId = Number(session.metadata?.company_id);
        const plan = session.metadata?.plan;
        const period = session.metadata?.period;

        if (!companyId || !plan || !period) {
          console.error('[Webhook] Missing metadata in checkout session', {
            companyId,
            plan,
            period,
          });
          break;
        }

        const customerId = session.customer as string;
        const subscriptionId = session.subscription as string;

        const { error } = await supabase
          .from('companies')
          .update({
            stripe_customer_id: customerId,
            stripe_subscription_id: subscriptionId,
          })
          .eq('id', companyId);

        if (error) {
          console.error('[Webhook] Error updating company Stripe IDs:', error);
        }

        break;
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;

        const firstItem = subscription.items?.data?.[0];
        const periodStart = firstItem?.current_period_start;
        const periodEnd = firstItem?.current_period_end;

        const companyId = Number(subscription.metadata?.company_id);
        const plan = subscription.metadata?.plan;
        const period = subscription.metadata?.period;

        if (!companyId) {
          console.error(
            '[Webhook] Missing company_id in subscription metadata',
          );
          break;
        }

        if (!periodStart || !periodEnd) {
          console.error(
            '[Webhook] Missing period timestamps in subscription items',
          );
          break;
        }

        const updateData: CompanyUpdateData = {
          subscription_status:
            subscription.status === 'active' ? 'active' : subscription.status,
          subscription_plan: plan,
          billing_period: period,
          current_period_start: new Date(periodStart * 1000).toISOString(),
          current_period_end: new Date(periodEnd * 1000).toISOString(),
          stripe_subscription_id: subscription.id,
          updated_at: new Date().toISOString(),
        };

        if (subscription.status === 'active') {
          updateData.canceled_at = null;
          await supabase
            .from('company_trials')
            .delete()
            .eq('company_id', companyId);
        }

        if (subscription.cancel_at_period_end) {
          updateData.subscription_status = 'canceled';
          updateData.canceled_at = new Date().toISOString();
        }

        const { error } = await supabase
          .from('companies')
          .update(updateData)
          .eq('id', companyId);

        if (error) {
          console.error(
            '[Webhook] Error updating company subscription:',
            error,
          );
          break;
        }

        if (subscription.status === 'active') {
          const { data: existing } = await supabase
            .from('company_match_usage')
            .select('id')
            .eq('company_id', companyId)
            .single();

          if (existing) {
            const { error: usageError } = await supabase
              .from('company_match_usage')
              .update({
                period_start: new Date(periodStart * 1000).toISOString(),
                period_end: new Date(periodEnd * 1000).toISOString(),
                matches_used: 0,
                updated_at: new Date().toISOString(),
              })
              .eq('company_id', companyId);

            if (usageError) {
              console.error(
                '[Webhook] Error updating match usage:',
                usageError,
              );
            }
          } else {
            const { error: insertError } = await supabase
              .from('company_match_usage')
              .insert({
                company_id: companyId,
                period_start: new Date(periodStart * 1000).toISOString(),
                period_end: new Date(periodEnd * 1000).toISOString(),
                matches_used: 0,
              });

            if (insertError) {
              console.error(
                '[Webhook] Error creating match usage:',
                insertError,
              );
            }
          }
        }

        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        const companyId = Number(subscription.metadata?.company_id);

        if (!companyId) break;

        const { error } = await supabase
          .from('companies')
          .update({
            subscription_status: 'expired',
            updated_at: new Date().toISOString(),
          })
          .eq('id', companyId);

        if (error) {
          console.error(
            '[Webhook] Error marking subscription as expired:',
            error,
          );
        }

        break;
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as StripeInvoiceWithParent;

        const subscriptionId =
          invoice.parent?.subscription_details?.subscription;

        if (!subscriptionId) {
          break;
        }

        const subscription =
          await stripe!.subscriptions.retrieve(subscriptionId);

        const firstItem = subscription.items?.data?.[0];
        const periodStart = firstItem?.current_period_start;
        const periodEnd = firstItem?.current_period_end;

        const companyId = Number(subscription.metadata?.company_id);

        if (!companyId) {
          console.error('[Webhook] Missing company_id in subscription');
          break;
        }

        if (!periodStart || !periodEnd) {
          console.error(
            '[Webhook] Missing period timestamps in subscription items',
          );
          break;
        }

        const { error: companyError } = await supabase
          .from('companies')
          .update({
            current_period_start: new Date(periodStart * 1000).toISOString(),
            current_period_end: new Date(periodEnd * 1000).toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq('id', companyId);

        if (companyError) {
          console.error(
            '[Webhook] Error updating company periods:',
            companyError,
          );
        }

        const { error: usageError } = await supabase
          .from('company_match_usage')
          .update({
            period_start: new Date(periodStart * 1000).toISOString(),
            period_end: new Date(periodEnd * 1000).toISOString(),
            matches_used: 0,
            updated_at: new Date().toISOString(),
          })
          .eq('company_id', companyId);

        if (usageError) {
          console.error(
            '[Webhook] Error updating match usage periods:',
            usageError,
          );
        }

        break;
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as StripeInvoiceWithParent;

        const subscriptionId =
          invoice.parent?.subscription_details?.subscription;

        if (!subscriptionId) {
          break;
        }

        const subscription =
          await stripe!.subscriptions.retrieve(subscriptionId);

        const companyId = Number(subscription.metadata?.company_id);

        if (!companyId) {
          console.error('[Webhook] Missing company_id in subscription');
          break;
        }

        const { error } = await supabase
          .from('companies')
          .update({
            subscription_status: 'past_due',
            updated_at: new Date().toISOString(),
          })
          .eq('id', companyId);

        if (error) {
          console.error(
            '[Webhook] Error marking subscription as past_due:',
            error,
          );
        }

        break;
      }

      default:
        break;
    }
  } catch (error) {
    console.error('[Webhook] Error processing subscription webhook:', error);
    throw error;
  }
}
