'use server';

import <PERSON><PERSON> from 'stripe';

import {
  BillingPeriod,
  getStripePriceId,
  SubscriptionPlan,
} from '~/constants/subscription.ts';
import { createClient } from '~/supabase/server';

const stripe = process.env.STRIPE_SECRET_KEY
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-03-31.basil',
    })
  : null;

export async function createSubscriptionCheckout(
  companyId: number,
  plan: SubscriptionPlan,
  period: BillingPeriod,
): Promise<{ url: string } | { error: string }> {
  try {
    const supabase = await createClient();

    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('id, name, stripe_customer_id')
      .eq('id', companyId)
      .single();

    if (companyError || !company) {
      return { error: 'Company not found' };
    }

    const priceId = getStripePriceId(plan, period);
    const baseUrl = process.env.APP_URL || 'http://localhost:3000';

    const sessionParams: Stripe.Checkout.SessionCreateParams = {
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${baseUrl}/dashboard/employer/subscription?success=true`,
      cancel_url: `${baseUrl}/dashboard/employer/subscription?canceled=true`,
      metadata: {
        company_id: companyId.toString(),
        plan,
        period,
      },
      subscription_data: {
        metadata: {
          company_id: companyId.toString(),
          plan,
          period,
        },
      },
      automatic_tax: {
        enabled: true,
      },
    };

    if (company.stripe_customer_id) {
      sessionParams.customer = company.stripe_customer_id;
    } else {
      sessionParams.customer_email = undefined;
    }

    const session = await stripe!.checkout.sessions.create(sessionParams);

    if (!session.url) {
      return { error: 'Failed to create checkout session' };
    }

    return { url: session.url };
  } catch (error) {
    console.error('Error creating subscription checkout:', error);
    return { error: 'Failed to create checkout session' };
  }
}

export async function createBillingPortalSession(
  companyId: number,
): Promise<{ url: string } | { error: string }> {
  try {
    const supabase = await createClient();

    const { data: company } = await supabase
      .from('companies')
      .select('stripe_customer_id')
      .eq('id', companyId)
      .single();

    if (!company?.stripe_customer_id) {
      return { error: 'No active subscription found' };
    }

    const baseUrl = process.env.APP_URL || 'http://localhost:3000';

    const session = await stripe!.billingPortal.sessions.create({
      customer: company.stripe_customer_id,
      return_url: `${baseUrl}/dashboard/employer/subscription`,
    });

    return { url: session.url };
  } catch (error) {
    console.error('Error creating portal session:', error);
    return { error: 'Failed to create billing portal session' };
  }
}
