import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  createBillingPortalSession,
  createSubscriptionCheckout,
} from './actions';
import {
  canAddEmployer,
  canCreateJob,
  getCompanySubscription,
  getCompanyTrial,
  getCompanyUsage,
} from './helpers';
import { BillingPeriod, SubscriptionPlan } from '~/constants/subscription.ts';

export const SUBSCRIPTION_QUERY_KEYS = {
  subscription: (companyId: number) => ['subscription', companyId] as const,
  usage: (companyId: number) => ['subscription', 'usage', companyId] as const,
  limits: (plan: SubscriptionPlan) => ['subscription', 'limits', plan] as const,
  canCreateJob: (companyId: number) =>
    ['subscription', 'canCreateJob', companyId] as const,
  canCreateMatch: (companyId: number) =>
    ['subscription', 'canCreateMatch', companyId] as const,
  canAddEmployer: (companyId: number) =>
    ['subscription', 'canAddEmployer', companyId] as const,
};

export function useCompanySubscription(companyId: number | undefined) {
  return useQuery({
    queryKey: SUBSCRIPTION_QUERY_KEYS.subscription(companyId!),
    queryFn: () => getCompanySubscription(companyId!),
    enabled: Boolean(companyId),
    staleTime: 1000 * 60,
  });
}

export function useCompanyUsage(companyId: number | undefined) {
  return useQuery({
    queryKey: SUBSCRIPTION_QUERY_KEYS.usage(companyId!),
    queryFn: () => getCompanyUsage(companyId!),
    enabled: Boolean(companyId),
    refetchInterval: 1000 * 30,
  });
}

export function useCanCreateJob(companyId: number | undefined) {
  return useQuery({
    queryKey: SUBSCRIPTION_QUERY_KEYS.canCreateJob(companyId!),
    queryFn: () => canCreateJob(companyId!),
    enabled: Boolean(companyId),
  });
}

export function useCanAddEmployer(companyId: number | undefined) {
  return useQuery({
    queryKey: SUBSCRIPTION_QUERY_KEYS.canAddEmployer(companyId!),
    queryFn: () => canAddEmployer(companyId!),
    enabled: Boolean(companyId),
  });
}

export function useCreateSubscriptionCheckout() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      companyId,
      plan,
      period,
    }: {
      companyId: number;
      plan: SubscriptionPlan;
      period: BillingPeriod;
    }) => createSubscriptionCheckout(companyId, plan, period),
    onSuccess: (data, variables) => {
      if ('url' in data) {
        window.location.href = data.url;
      }
      queryClient.invalidateQueries({
        queryKey: SUBSCRIPTION_QUERY_KEYS.subscription(variables.companyId),
      });
    },
  });
}

export function useCompanyTrial(companyId: number | undefined) {
  return useQuery({
    queryKey: ['subscription', 'trial', companyId] as const,
    queryFn: () => getCompanyTrial(companyId!),
    enabled: Boolean(companyId),
  });
}

export function useCreateBillingPortalSession() {
  return useMutation({
    mutationFn: ({ companyId }: { companyId: number }) =>
      createBillingPortalSession(companyId),
  });
}
