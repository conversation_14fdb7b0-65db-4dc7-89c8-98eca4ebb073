'use server';

import type {
  CompanySubscription,
  SubscriptionLimits,
  SubscriptionUsage,
} from './types';
import {
  SUBSCRIPTION_PLANS,
  SubscriptionPlan,
} from '~/constants/subscription.ts';
import { createClient } from '~/supabase/server';

export async function getCompanySubscription(
  companyId: number,
): Promise<CompanySubscription | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('companies')
    .select(
      'id, stripe_customer_id, stripe_subscription_id, subscription_status, subscription_plan, billing_period, current_period_start, current_period_end, canceled_at',
    )
    .eq('id', companyId)
    .single();

  if (error || !data) {
    console.error('Error fetching company subscription:', error);
    return null;
  }

  return data as CompanySubscription;
}

export async function getPlanLimits(
  plan: SubscriptionPlan,
): Promise<SubscriptionLimits> {
  return SUBSCRIPTION_PLANS[plan!];
}

export async function getCompanyUsage(
  companyId: number,
): Promise<SubscriptionUsage> {
  const supabase = await createClient();

  const { count: seatsCount } = await supabase
    .from('employers')
    .select('*', { count: 'exact', head: true })
    .eq('company_id', companyId);

  const { count: jobsCount } = await supabase
    .from('vacancies')
    .select('*', { count: 'exact', head: true })
    .eq('company_id', companyId)
    .eq('status', 'OPENED');

  const { data: matchUsage } = await supabase
    .from('company_match_usage')
    .select('matches_used')
    .eq('company_id', companyId)
    .single();

  return {
    seats: seatsCount || 0,
    jobs: jobsCount || 0,
    matches: matchUsage?.matches_used || 0,
  };
}

export async function canCreateJob(companyId: number): Promise<boolean> {
  const subscription = await getCompanySubscription(companyId);
  if (!subscription) return false;

  if (subscription.subscription_status === 'trial') {
    const supabase = await createClient();
    const { data: trial } = await supabase
      .from('company_trials')
      .select('ends_at')
      .eq('company_id', companyId)
      .single();

    if (trial && new Date(trial.ends_at) < new Date()) {
      return false;
    }
  }

  if (subscription.subscription_status === 'expired') {
    return false;
  }

  if (!subscription.subscription_plan) return false;

  const limits = await getPlanLimits(subscription.subscription_plan);
  const usage = await getCompanyUsage(companyId);

  return usage.jobs < limits.jobs;
}

export async function canCreateMatch(companyId: number): Promise<boolean> {
  const subscription = await getCompanySubscription(companyId);

  if (!subscription) {
    return false;
  }

  if (subscription.subscription_status === 'trial') {
    const supabase = await createClient();
    const { data: trial } = await supabase
      .from('company_trials')
      .select('ends_at')
      .eq('company_id', companyId)
      .single();

    if (trial && new Date(trial.ends_at) < new Date()) {
      return false;
    }
  }

  if (subscription.subscription_status === 'expired') {
    return false;
  }

  if (!subscription.subscription_plan) {
    return false;
  }

  const limits = await getPlanLimits(subscription.subscription_plan);

  const usage = await getCompanyUsage(companyId);

  return usage.matches < limits.matches;
}

export async function canAddEmployer(companyId: number): Promise<boolean> {
  const subscription = await getCompanySubscription(companyId);
  if (!subscription) return false;

  if (subscription.subscription_status === 'trial') {
    const supabase = await createClient();
    const { data: trial } = await supabase
      .from('company_trials')
      .select('ends_at')
      .eq('company_id', companyId)
      .single();

    if (trial && new Date(trial.ends_at) < new Date()) {
      return false;
    }
  }

  if (subscription.subscription_status === 'expired') {
    return false;
  }

  if (!subscription.subscription_plan) return false;

  const limits = await getPlanLimits(subscription.subscription_plan);
  const usage = await getCompanyUsage(companyId);

  return usage.seats < limits.seats;
}

export async function getCompanyTrial(
  companyId: number,
): Promise<{ ends_at: string } | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('company_trials')
    .select('ends_at')
    .eq('company_id', companyId)
    .single();

  if (error || !data) {
    return null;
  }

  return data;
}
