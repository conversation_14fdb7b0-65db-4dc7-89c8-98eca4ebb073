'use server';

import { createClient } from '~/supabase/server.ts';

export async function completeJobSeekerOnboarding(
  jobSeekerId: number,
): Promise<{ success: boolean }> {
  const supabase = await createClient();

  const { error } = await supabase
    .from('job_seekers')
    .update({ onboarding_completed: true })
    .eq('id', jobSeekerId);

  if (error) {
    console.error('Failed to complete job seeker onboarding:', error);
    return { success: false };
  }

  return { success: true };
}

export async function completeEmployerOnboarding(
  employerId: number,
): Promise<{ success: boolean }> {
  const supabase = await createClient();

  const { error } = await supabase
    .from('employers')
    .update({ onboarding_completed: true })
    .eq('id', employerId);

  if (error) {
    console.error('Failed to complete employer onboarding:', error);
    return { success: false };
  }

  return { success: true };
}
