import { useMutation, useQueryClient } from '@tanstack/react-query';

import { archetypeKeys } from '~/api/features/archetypes/queries.ts';
import {
  vacancyMatchesCountsKeys,
  vacancyMatchesKeys,
} from '~/api/features/matches/job-seeker/queries.ts';
import {
  completeEmployerOnboarding,
  completeJobSeekerOnboarding,
} from '~/api/features/onboarding/actions.ts';

export function useCompleteJobSeekerOnboarding() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobSeekerId: number) =>
      completeJobSeekerOnboarding(jobSeekerId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['user'],
      });

      queryClient.invalidateQueries({
        queryKey: vacancyMatchesKeys.all,
      });

      queryClient.invalidateQueries({
        queryKey: vacancyMatchesCountsKeys.all,
      });

      queryClient.invalidateQueries({
        queryKey: archetypeKeys.all,
      });
    },
  });
}

export function useCompleteEmployerOnboarding() {
  return useMutation({
    mutationFn: (employerId: number) => completeEmployerOnboarding(employerId),
  });
}
