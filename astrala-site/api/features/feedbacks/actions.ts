'use server';

import { createClient } from '~/supabase/server';

export type FeedbackType = 'like' | 'dislike';

export type MatchFeedbackData = {
  id: number;
  vacancyMatchId: number;
  jobSeekerFeedbackType?: FeedbackType;
  jobSeekerFeedbackText?: string;
  jobSeekerFeedbackAt?: string;
  employerFeedbackType?: FeedbackType;
  employerFeedbackText?: string;
  employerFeedbackAt?: string;
  createdAt: string;
  updatedAt: string;
};

export type SubmitFeedbackRequest = {
  matchId: number;
  feedbackType: FeedbackType;
  feedbackText?: string;
  viewMode: 'jobSeeker' | 'employer';
};

export async function submitMatchFeedback({
  matchId,
  feedbackType,
  feedbackText,
  viewMode,
}: SubmitFeedbackRequest): Promise<{
  success: boolean;
  data?: MatchFeedbackData;
  error?: string;
}> {
  if (!matchId) {
    return { success: false, error: 'Match ID is required' };
  }

  if (!feedbackType) {
    return { success: false, error: 'Feedback type is required' };
  }

  const supabase = await createClient();

  try {
    const { data: matchExists, error: matchError } = await supabase
      .from('vacancy_matches')
      .select('id')
      .eq('id', matchId)
      .single();

    if (matchError || !matchExists) {
      return { success: false, error: 'Match not found' };
    }

    const timestamp = new Date().toISOString();
    const updateFields =
      viewMode === 'jobSeeker'
        ? {
            job_seeker_feedback_type: feedbackType,
            job_seeker_feedback_text: feedbackText || null,
            job_seeker_feedback_at: timestamp,
          }
        : {
            employer_feedback_type: feedbackType,
            employer_feedback_text: feedbackText || null,
            employer_feedback_at: timestamp,
          };

    const { data: existingFeedback, error: existingError } = await supabase
      .from('match_feedbacks')
      .select('*')
      .eq('vacancy_match_id', matchId)
      .single();

    let result;

    if (existingError && existingError.code === 'PGRST116') {
      const insertData = {
        vacancy_match_id: matchId,
        ...updateFields,
        created_at: timestamp,
        updated_at: timestamp,
      };

      const { data, error } = await supabase
        .from('match_feedbacks')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Error creating feedback:', error);
        return { success: false, error: error.message };
      }

      result = data;
    } else if (existingFeedback) {
      const { data, error } = await supabase
        .from('match_feedbacks')
        .update({
          ...updateFields,
          updated_at: timestamp,
        })
        .eq('vacancy_match_id', matchId)
        .select()
        .single();

      if (error) {
        console.error('Error updating feedback:', error);
        return { success: false, error: error.message };
      }

      result = data;
    } else {
      console.error('Error checking existing feedback:', existingError);
      return { success: false, error: 'Failed to check existing feedback' };
    }

    const feedbackData: MatchFeedbackData = {
      id: result.id,
      vacancyMatchId: result.vacancy_match_id,
      jobSeekerFeedbackType: result.job_seeker_feedback_type,
      jobSeekerFeedbackText: result.job_seeker_feedback_text,
      jobSeekerFeedbackAt: result.job_seeker_feedback_at,
      employerFeedbackType: result.employer_feedback_type,
      employerFeedbackText: result.employer_feedback_text,
      employerFeedbackAt: result.employer_feedback_at,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    };

    return { success: true, data: feedbackData };
  } catch (error) {
    console.error('Exception submitting feedback:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function getMatchFeedback(matchId: number): Promise<{
  success: boolean;
  data?: MatchFeedbackData;
  error?: string;
}> {
  if (!matchId) {
    return { success: false, error: 'Match ID is required' };
  }

  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('match_feedbacks')
      .select('*')
      .eq('vacancy_match_id', matchId)
      .single();

    if (error && error.code === 'PGRST116') {
      return { success: true, data: undefined };
    }

    if (error) {
      console.error('Error fetching feedback:', error);
      return { success: false, error: error.message };
    }

    if (!data) {
      return { success: true, data: undefined };
    }

    const feedbackData: MatchFeedbackData = {
      id: data.id,
      vacancyMatchId: data.vacancy_match_id,
      jobSeekerFeedbackType: data.job_seeker_feedback_type,
      jobSeekerFeedbackText: data.job_seeker_feedback_text,
      jobSeekerFeedbackAt: data.job_seeker_feedback_at,
      employerFeedbackType: data.employer_feedback_type,
      employerFeedbackText: data.employer_feedback_text,
      employerFeedbackAt: data.employer_feedback_at,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };

    return { success: true, data: feedbackData };
  } catch (error) {
    console.error('Exception fetching feedback:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function getMatchFeedbacks(matchIds: number[]): Promise<{
  success: boolean;
  data?: MatchFeedbackData[];
  error?: string;
}> {
  if (matchIds.length === 0) {
    return { success: true, data: [] };
  }

  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('match_feedbacks')
      .select('*')
      .in('vacancy_match_id', matchIds);

    if (error) {
      console.error('Error fetching feedbacks:', error);
      return { success: false, error: error.message };
    }

    const feedbacksData: MatchFeedbackData[] = (data || []).map(item => ({
      id: item.id,
      vacancyMatchId: item.vacancy_match_id,
      jobSeekerFeedbackType: item.job_seeker_feedback_type,
      jobSeekerFeedbackText: item.job_seeker_feedback_text,
      jobSeekerFeedbackAt: item.job_seeker_feedback_at,
      employerFeedbackType: item.employer_feedback_type,
      employerFeedbackText: item.employer_feedback_text,
      employerFeedbackAt: item.employer_feedback_at,
      createdAt: item.created_at,
      updatedAt: item.updated_at,
    }));

    return { success: true, data: feedbacksData };
  } catch (error) {
    console.error('Exception fetching feedbacks:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
