'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  getMatchFeedback,
  getMatchFeedbacks,
  SubmitFeedbackRequest,
  submitMatchFeedback,
} from '~/api/features/feedbacks/actions';

export const feedbacksKeys = {
  all: ['feedbacks'] as const,
  feedback: (matchId: number) =>
    [...feedbacksKeys.all, 'feedback', matchId] as const,
  feedbacks: (matchIds: number[]) =>
    [...feedbacksKeys.all, 'feedbacks', matchIds] as const,
};

export function useGetMatchFeedback(matchId: number | undefined) {
  return useQuery({
    queryKey: matchId
      ? feedbacksKeys.feedback(matchId)
      : ['feedbacks', 'feedback', null],
    queryFn: () => getMatchFeedback(matchId as number),
    enabled: Boolean(matchId),
    staleTime: 5 * 60 * 1000,
  });
}

export function useGetMatchFeedbacks(matchIds: number[]) {
  return useQuery({
    queryKey: feedbacksKeys.feedbacks(matchIds),
    queryFn: () => getMatchFeedbacks(matchIds),
    enabled: matchIds.length > 0,
    staleTime: 5 * 60 * 1000,
  });
}

export function useSubmitMatchFeedback() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (request: SubmitFeedbackRequest) => {
      const result = await submitMatchFeedback(request);
      if (!result.success) {
        throw new Error(result.error || 'Failed to submit feedback');
      }
      return result.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: feedbacksKeys.feedback(variables.matchId),
      });

      if (data) {
        queryClient.setQueryData(feedbacksKeys.feedback(variables.matchId), {
          success: true,
          data,
        });
      }

      queryClient.invalidateQueries({
        queryKey: feedbacksKeys.all,
        predicate: query => {
          const queryKey = query.queryKey;
          if (
            queryKey[0] === 'feedbacks' &&
            queryKey[1] === 'feedbacks' &&
            Array.isArray(queryKey[2])
          ) {
            const matchIds = queryKey[2] as number[];
            return matchIds.includes(variables.matchId);
          }
          return false;
        },
      });
    },
    onError: error => {
      console.error('Failed to submit feedback:', error);
    },
  });
}
