'use client';

import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  Company,
  CompanyEmployee,
  getCompanyEmployees,
  getCompanyInfo,
  getCompanyInvitations,
  getCompanyVacancies,
  InvitationTokenWithStatus,
  PaginatedResponse,
  sendEmployeeInvitation,
} from '~/api/features/company/actions';
import { SUBSCRIPTION_QUERY_KEYS } from '~/api/features/subscription/queries.ts';
import { useAuth } from '~/providers/AuthProvider';

export const companyKeys = {
  all: ['company'] as const,
  info: () => [...companyKeys.all, 'info'] as const,
  vacancies: () => [...companyKeys.all, 'vacancies'] as const,
  employees: () => [...companyKeys.all, 'employees'] as const,
  invitations: () => [...companyKeys.all, 'invitations'] as const,
  paginatedEmployees: (page: number, limit: number) =>
    [...companyKeys.employees(), { page, limit }] as const,
  paginatedInvitations: (page: number, limit: number) =>
    [...companyKeys.invitations(), { page, limit }] as const,
};

export function useCompanyInfo() {
  const { employerProfile } = useAuth();
  const companyId = employerProfile?.company_id;

  return useQuery<Company | null>({
    queryKey: companyKeys.info(),
    queryFn: () => getCompanyInfo(companyId as number),
    enabled: Boolean(companyId),
  });
}

export function useCompanyVacancies() {
  const { employerProfile } = useAuth();
  const companyId = employerProfile?.company_id;

  return useQuery({
    queryKey: companyKeys.vacancies(),
    queryFn: () => getCompanyVacancies(companyId as number),
    enabled: Boolean(companyId),
  });
}

export function useCompanyEmployees(page = 1, limit = 10) {
  const { employerProfile } = useAuth();
  const companyId = employerProfile?.company_id;

  return useQuery<PaginatedResponse<CompanyEmployee>>({
    queryKey: companyKeys.paginatedEmployees(page, limit),
    queryFn: () => getCompanyEmployees(companyId as number, page, limit),
    enabled: Boolean(companyId),
  });
}

export function useCompanyInvitations(page = 1, limit = 10) {
  const { employerProfile } = useAuth();
  const companyId = employerProfile?.company_id;

  return useQuery<PaginatedResponse<InvitationTokenWithStatus>>({
    queryKey: companyKeys.paginatedInvitations(page, limit),
    queryFn: () => getCompanyInvitations(companyId as number, page, limit),
    enabled: Boolean(companyId),
  });
}

export function usePagination<T>(initialData?: PaginatedResponse<T>) {
  const [page, setPage] = useState(initialData?.page || 1);
  const [limit, setLimit] = useState(initialData?.limit || 10);

  const totalPages = initialData?.totalPages || 0;
  const total = initialData?.total || 0;

  const hasPreviousPage = page > 1;
  const hasNextPage = page < totalPages;

  const goToPage = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  const goToNextPage = () => {
    if (hasNextPage) {
      setPage(prev => prev + 1);
    }
  };

  const goToPreviousPage = () => {
    if (hasPreviousPage) {
      setPage(prev => prev - 1);
    }
  };

  const changeLimit = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
  };

  const getPageNumbers = () => {
    const visiblePages = 5;
    const pageNumbers: (number | string)[] = [];

    if (totalPages <= visiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      pageNumbers.push(1);

      if (page <= 3) {
        pageNumbers.push(2, 3);
        pageNumbers.push('...');
      } else if (page >= totalPages - 2) {
        pageNumbers.push('...');
        pageNumbers.push(totalPages - 2, totalPages - 1);
      } else {
        pageNumbers.push('...');
        pageNumbers.push(page - 1, page, page + 1);
        pageNumbers.push('...');
      }

      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers;
  };

  return {
    page,
    limit,
    total,
    totalPages,
    hasPreviousPage,
    hasNextPage,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    changeLimit,
    pageNumbers: getPageNumbers(),
  };
}

export function useSendInvitation() {
  const queryClient = useQueryClient();
  const { employerProfile, userProfile } = useAuth();
  const { data: companyInfo } = useCompanyInfo();

  return useMutation({
    mutationFn: async ({ email }: { email: string }) => {
      if (!employerProfile?.company_id || !employerProfile?.id) {
        throw new Error('Missing company or employer information');
      }

      return sendEmployeeInvitation({
        email,
        companyId: employerProfile.company_id,
        createdByEmployerId: employerProfile.id,
        senderName: userProfile?.full_name || 'A team member',
        companyName: companyInfo?.name || 'Your company',
      });
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: companyKeys.invitations(),
      });

      const companyId = employerProfile?.company_id;
      if (companyId) {
        await queryClient.invalidateQueries({
          queryKey: SUBSCRIPTION_QUERY_KEYS.usage(companyId),
        });
        await queryClient.invalidateQueries({
          queryKey: SUBSCRIPTION_QUERY_KEYS.canAddEmployer(companyId),
        });
      }
    },
  });
}
