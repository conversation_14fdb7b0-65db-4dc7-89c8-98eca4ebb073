'use server';

import { cache } from 'react';

import { createClient } from '~/supabase/server.ts';

type ChatWithVacancyMatch = {
  id: number;
  vacancy_match: {
    id: number;
    job_seeker_id: number;
    vacancy: {
      id: number;
      company_id: number;
    };
  };
};

export type UserRole = 'employer' | 'job_seeker';

export type AuthContext = {
  userId: string;
  userType: UserRole;
  employerId?: number;
  companyId?: number;
  jobSeekerId?: number;
};

export const getAuthContext = cache(async (): Promise<AuthContext | null> => {
  const supabase = await createClient();

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return null;
  }

  const [employerResult, jobSeekerResult] = await Promise.allSettled([
    supabase
      .from('employers')
      .select('id, company_id')
      .eq('user_id', user.id)
      .single(),
    supabase.from('job_seekers').select('id').eq('user_id', user.id).single(),
  ]);

  if (employerResult.status === 'fulfilled' && employerResult.value.data) {
    const employer = employerResult.value.data;
    return {
      userId: user.id,
      userType: 'employer',
      employerId: employer.id,
      companyId: employer.company_id,
    };
  }

  if (jobSeekerResult.status === 'fulfilled' && jobSeekerResult.value.data) {
    const jobSeeker = jobSeekerResult.value.data;
    return {
      userId: user.id,
      userType: 'job_seeker',
      jobSeekerId: jobSeeker.id,
    };
  }

  return null;
});

export async function requireAuth(): Promise<AuthContext> {
  const authContext = await getAuthContext();

  if (!authContext) {
    throw new Error('Unauthorized: Please log in');
  }

  return authContext;
}

export const canAccessChat = cache(
  async (chatId: number, authContext: AuthContext): Promise<boolean> => {
    const supabase = await createClient();

    const { data: chatWithDetails } = await supabase
      .from('chats')
      .select(
        `
        *,
        vacancy_match:vacancy_match_id (
          id,
          job_seeker_id,
          vacancy:vacancy_id (
            id,
            company_id
          )
        )
      `,
      )
      .eq('id', chatId)
      .single<ChatWithVacancyMatch>();

    if (!chatWithDetails?.vacancy_match) {
      return false;
    }

    const match = chatWithDetails.vacancy_match;

    if (authContext.userType === 'job_seeker') {
      return match.job_seeker_id === authContext.jobSeekerId;
    }

    if (authContext.userType === 'employer') {
      return match.vacancy?.company_id === authContext.companyId;
    }

    return false;
  },
);

export async function requireChatAccess(chatId: number): Promise<AuthContext> {
  const authContext = await requireAuth();

  const hasAccess = await canAccessChat(chatId, authContext);

  if (!hasAccess) {
    throw new Error(`Forbidden: No access to this chat`);
  }

  return authContext;
}
