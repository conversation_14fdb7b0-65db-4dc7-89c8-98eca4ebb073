'use server';

import { cache } from 'react';

import { AuthContext, requireAuth } from '.';
import { createClient } from '~/supabase/server.ts';

export const canAccessVacancy = cache(
  async (
    vacancyId: number,
    authContext: AuthContext,
    operation: 'read' | 'write' = 'read',
  ): Promise<boolean> => {
    const supabase = await createClient();

    const { data: vacancy } = await supabase
      .from('vacancies')
      .select('company_id, employer_id')
      .eq('id', vacancyId)
      .single();

    if (!vacancy) {
      return false;
    }

    if (operation === 'write') {
      if (authContext.userType !== 'employer') {
        return false;
      }
      return vacancy.company_id === authContext.companyId;
    }

    return true;
  },
);

export async function canAccessCompany(
  companyId: number,
  authContext: AuthContext,
): Promise<boolean> {
  if (authContext.userType !== 'employer') {
    return false;
  }

  return authContext.companyId === companyId;
}

export async function canAccessVacancyQuestionnaire(
  vacancyId: number,
  authContext: AuthContext,
  operation: 'read' | 'write' = 'read',
): Promise<boolean> {
  return canAccessVacancy(vacancyId, authContext, operation);
}

export async function requireVacancyAccess(
  vacancyId: number,
  operation: 'read' | 'write' = 'read',
): Promise<AuthContext> {
  const authContext = await requireAuth();

  const hasAccess = await canAccessVacancy(vacancyId, authContext, operation);

  if (!hasAccess) {
    throw new Error(`Forbidden: No ${operation} access to this vacancy`);
  }

  return authContext;
}

export async function requireCompanyAccess(
  companyId: number,
): Promise<AuthContext> {
  const authContext = await requireAuth();

  const hasAccess = await canAccessCompany(companyId, authContext);

  if (!hasAccess) {
    throw new Error(`Forbidden: No access to this company`);
  }

  return authContext;
}

export async function requireVacancyQuestionnaireAccess(
  vacancyId: number,
  operation: 'read' | 'write' = 'read',
): Promise<AuthContext> {
  const authContext = await requireAuth();

  const hasAccess = await canAccessVacancyQuestionnaire(
    vacancyId,
    authContext,
    operation,
  );

  if (!hasAccess) {
    throw new Error(
      `Forbidden: No ${operation} access to this vacancy questionnaire`,
    );
  }

  return authContext;
}
