import { AuthContext, requireAuth } from '.';

export async function requireJobSeekerOwnership(
  jobSeekerId: number,
): Promise<AuthContext & { jobSeekerId: number }> {
  const authContext = await requireJobSeeker();

  if (authContext.jobSeekerId !== jobSeekerId) {
    throw new Error('Forbidden: Can only access your own data');
  }

  return authContext;
}

export async function requireJobSeeker(): Promise<
  AuthContext & { jobSeekerId: number }
> {
  const authContext = await requireAuth();

  if (authContext.userType !== 'job_seeker' || !authContext.jobSeekerId) {
    throw new Error('Forbidden: Job seeker access required');
  }

  return authContext as AuthContext & { jobSeekerId: number };
}
