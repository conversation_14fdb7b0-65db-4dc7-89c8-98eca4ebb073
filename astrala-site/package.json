{"name": "@astrala/site", "version": "1.0.0", "description": "", "private": true, "author": "", "license": "ISC", "scripts": {"dev": "dotenv -e ../.env.local -- next dev", "build": "dotenv -e ../.env.local -- next build", "start": "dotenv -e ../.env.local -- next start", "gen:types": "source ../.env.local && supabase gen types typescript --project-id $SUPABASE_PROJECT_ID > shared/db/generated/types.ts", "db:stop": "dotenv -e ../.env.local -- supabase stop", "db:start": "dotenv -e ../.env.local -- supabase start", "typecheck": "tsc", "lint": "npm run typecheck && next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-query": "^5.66.9", "@tanstack/react-query-devtools": "^5.66.9", "@tanstack/react-table": "^8.21.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.6.3", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "next": "^14.2.15", "postmark": "^4.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-phone-number-input": "^3.4.12", "stripe": "^18.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "whatwg-url": "^14.2.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/lodash": "^4.17.16", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "supabase": "^2.0.0", "tailwindcss": "^3.4.13", "uuid": "^11.1.0"}}