'use client';
import { FC } from 'react';
import { useRouter } from 'next/navigation';

import { Button } from '~/components/ui/button';

const Home: FC = () => {
  const router = useRouter();

  return (
    <main className="h-screen flex flex-col justify-center items-center gap-4">
      <Button size="lg" onClick={() => router.push('/signin')}>
        Sign In
      </Button>
      <Button size="lg" onClick={() => router.push('/signup')}>
        Sign Up
      </Button>
    </main>
  );
};

export default Home;
