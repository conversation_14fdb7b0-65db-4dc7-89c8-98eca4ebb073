import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

import { handleSubscriptionWebhook } from '~/api/features/subscription/webhook';

const stripe = process.env.STRIPE_SECRET_KEY
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-03-31.basil',
    })
  : null;

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || '';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature') || '';

    let event: Stripe.Event;

    try {
      event = stripe!.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (error) {
      console.error(
        `[Stripe Webhook] Signature verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    const subscriptionEvents = [
      'checkout.session.completed',
      'customer.subscription.created',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'invoice.payment_succeeded',
      'invoice.payment_failed',
    ];

    if (subscriptionEvents.includes(event.type)) {
      try {
        await handleSubscriptionWebhook(event);
      } catch (error) {
        console.error(
          `[Stripe Webhook] Error processing ${event.type}:`,
          error instanceof Error ? error.message : error,
        );
        throw error;
      }
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error(
      '[Stripe Webhook] Fatal error processing webhook:',
      error instanceof Error ? error.message : error,
      error instanceof Error ? error.stack : '',
    );
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
