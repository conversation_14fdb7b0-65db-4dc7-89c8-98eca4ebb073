'use client';

import { useEffect } from 'react';
import { format } from 'date-fns';
import { useSearchParams } from 'next/navigation';

export default function PrintArchetypeContent() {
  const searchParams = useSearchParams();

  const archetypeName = searchParams.get('archetypeName') || '';
  const description = searchParams.get('description') || '';
  const dimensionsIntro = searchParams.get('dimensionsIntro') || '';
  const wheelDescription = searchParams.get('wheelDescription') || '';
  const roleAlignments = searchParams.get('roleAlignments')?.split('|||') || [];
  const roleDescription = searchParams.get('roleDescription') || '';
  const aboutProfile = searchParams.get('aboutProfile') || '';

  useEffect(() => {
    if (archetypeName) {
      setTimeout(() => window.print(), 300);
    }
  }, [archetypeName]);

  if (!archetypeName) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>No data available</p>
      </div>
    );
  }

  const currentDate = format(new Date(), 'dd/MM/yyyy');
  const profileId = `JS-${Date.now().toString(36).toUpperCase()}`;

  return (
    <>
      {/* eslint-disable-next-line react/no-unknown-property */}
      <style jsx>{`
        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          @page {
            margin: 0;
          }
        }
        .print-content {
          max-width: 800px;
          margin: 0 auto;
          padding: 40px;
          font-family:
            system-ui,
            -apple-system,
            sans-serif;
        }
      `}</style>

      <div className="print-content bg-white text-black">
        <div className="mb-8">
          <p className="text-sm text-gray-600 mb-4">
            Symbolic Behavioural Profile
          </p>
          <h1 className="text-4xl font-bold mb-3">{archetypeName}</h1>
          <p className="text-base text-gray-700">{description}</p>
        </div>

        {dimensionsIntro && (
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-3">
              You Sit Across Behavioural Dimensions
            </h2>
            <p className="text-base text-gray-700 leading-relaxed whitespace-pre-line">
              {dimensionsIntro}
            </p>
          </div>
        )}

        {wheelDescription && (
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-3">Symbolic Insight Wheel</h2>
            <p className="text-base text-gray-700 leading-relaxed">
              {wheelDescription}
            </p>
          </div>
        )}

        {roleAlignments.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-3">Typical Role Alignments</h2>
            <p className="text-base text-gray-700 mb-3">
              Based on your behavioural insight profile, you may thrive in roles
              such as:
            </p>
            <ul className="list-disc list-inside mb-3 text-gray-700">
              {roleAlignments.map((role, idx) => (
                // eslint-disable-next-line react/no-array-index-key
                <li key={idx} className="ml-4">
                  {role}
                </li>
              ))}
            </ul>
            {roleDescription && (
              <p className="text-base text-gray-700 leading-relaxed">
                {roleDescription}
              </p>
            )}
          </div>
        )}

        {aboutProfile && (
          <div className="mb-12">
            <h2 className="text-xl font-bold mb-3">About This Profile</h2>
            <p className="text-base text-gray-700 leading-relaxed whitespace-pre-line">
              {aboutProfile}
            </p>
          </div>
        )}

        <div className="mt-16 pt-6 border-t border-gray-300">
          <div className="text-sm text-gray-600 space-y-1">
            <p>Brownlee Cale Connect | Profile Generated by AstralaNexus</p>
            <p>Profile ID: {profileId}</p>
            <p>Date Generated: {currentDate}</p>
          </div>
        </div>
      </div>
    </>
  );
}
