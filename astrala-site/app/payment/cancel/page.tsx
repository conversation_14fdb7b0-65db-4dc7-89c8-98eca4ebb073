import { Suspense } from 'react';

import { CancelPageContent } from './(components)/CancelPageContent';

export default function PaymentCancelPage() {
  return (
    <Suspense
      fallback={
        <div className="container flex flex-col items-center justify-center gap-4 py-10">
          <h1 className="text-2xl font-bold">Loading Payment Information</h1>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
        </div>
      }
    >
      <CancelPageContent />
    </Suspense>
  );
}
