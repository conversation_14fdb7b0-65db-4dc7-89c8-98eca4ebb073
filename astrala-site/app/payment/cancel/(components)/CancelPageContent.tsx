'use client';

import { useEffect, useState } from 'react';
import { AlertCircle, XCircle } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import { handleCancelledPayment } from '~/api/features/stripe/actions';
import { Button } from '~/components/ui/button';

export const CancelPageContent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'processing' | 'cancelled' | 'error'>(
    'processing',
  );
  const [error, setError] = useState<string | null>(null);
  const [dashboardUrl, setDashboardUrl] = useState('/dashboard');

  useEffect(() => {
    const transactionId = searchParams.get('transaction_id');

    if (!transactionId) {
      setStatus('error');
      setError('Missing transaction information');
      return;
    }
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const processCancel = async () => {
      try {
        const result = await handleCancelledPayment(Number(transactionId));

        if (result.success) {
          setStatus('cancelled');
          setDashboardUrl(result.dashboardUrl);

          const timer = setTimeout(() => {
            router.push(result.dashboardUrl);
          }, 5000);

          return () => clearTimeout(timer);
        }
        setStatus('error');
        setError(result.error || 'Failed to process cancellation');
      } catch (error_) {
        setStatus('error');
        setError('An unexpected error occurred');
        console.error('Payment cancellation error:', error_);
      }
    };

    processCancel();
  }, [searchParams, router]);

  const handleGoToDashboard = () => {
    router.push(dashboardUrl);
  };

  if (status === 'processing') {
    return (
      <div className="container flex flex-col items-center justify-center gap-4 py-10">
        <h1 className="text-2xl font-bold">Processing Cancellation</h1>
        <p className="text-center text-muted-foreground mb-6">
          Your payment cancellation is being processed. Please wait...
        </p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="container flex flex-col items-center justify-center gap-4 py-10">
        <div className="flex items-center gap-2 text-destructive">
          <AlertCircle size={24} />
          <h1 className="text-2xl font-bold">Cancellation Error</h1>
        </div>
        <p className="text-center text-muted-foreground mb-6">
          {error || 'An error occurred while processing your cancellation'}
        </p>
        <Button onClick={handleGoToDashboard}>Back to Dashboard</Button>
      </div>
    );
  }

  return (
    <div className="container flex flex-col items-center justify-center gap-4 py-10">
      <div className="flex items-center gap-2 text-yellow-600">
        <XCircle size={24} />
        <h1 className="text-2xl font-bold">Payment Cancelled</h1>
      </div>
      <p className="text-center text-muted-foreground mb-6">
        Your payment has been cancelled. You will be redirected to the dashboard
        in a few seconds.
      </p>
      <div className="flex items-center gap-2">
        <span>Redirecting...</span>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900" />
      </div>
      <Button onClick={handleGoToDashboard} className="mt-4">
        Go to Dashboard Now
      </Button>
    </div>
  );
};
