import { Suspense } from 'react';

import { SuccessPageContent } from './(components)/SuccessPageContent';

export default function PaymentSuccessPage() {
  return (
    <Suspense
      fallback={
        <div className="w-full flex flex-col items-center justify-center gap-4 min-h-screen py-10">
          <h1 className="text-2xl font-bold">Loading Payment Information</h1>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
        </div>
      }
    >
      <SuccessPageContent />
    </Suspense>
  );
}
