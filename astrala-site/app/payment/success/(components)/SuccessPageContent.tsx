'use client';

import { useEffect, useState } from 'react';
import { AlertCircle, CheckCircle } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import { handleSuccessfulPayment } from '~/api/features/stripe/actions';
import { Button } from '~/components/ui/button';

export const SuccessPageContent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>(
    'processing',
  );
  const [error, setError] = useState<string | null>(null);
  const [dashboardUrl, setDashboardUrl] = useState('/dashboard');

  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    const transactionId = searchParams.get('transaction_id');

    if (!sessionId || !transactionId) {
      setStatus('error');
      setError('Missing payment information');
      return;
    }

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const processPayment = async () => {
      try {
        const result = await handleSuccessfulPayment(
          sessionId,
          Number(transactionId),
        );

        if (result.success) {
          setStatus('success');
          setDashboardUrl(result.dashboardUrl);

          const timer = setTimeout(() => {
            router.push(result.dashboardUrl);
          }, 5000);

          return () => clearTimeout(timer);
        }
        setStatus('error');
        setError(result.error || 'Failed to process payment');
      } catch (error_) {
        setStatus('error');
        setError('An unexpected error occurred');
        console.error('Payment processing error:', error_);
      }
    };

    processPayment();
  }, [searchParams, router]);

  const handleGoToDashboard = () => {
    router.push(dashboardUrl);
  };

  if (status === 'processing') {
    return (
      <div className="w-full flex flex-col items-center justify-center gap-4 min-h-screen py-10">
        <h1 className="text-2xl font-bold">Processing Payment</h1>
        <p className="text-center text-muted-foreground mb-6">
          Your payment is being processed. Please wait...
        </p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="w-full flex flex-col items-center justify-center gap-4 min-h-screen py-10">
        <div className="flex items-center gap-2 text-destructive">
          <AlertCircle size={24} />
          <h1 className="text-2xl font-bold">Payment Error</h1>
        </div>
        <p className="text-center text-muted-foreground mb-6">
          {error || 'An error occurred while processing your payment'}
        </p>
        <Button onClick={handleGoToDashboard}>Back to Dashboard</Button>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col items-center justify-center gap-4 min-h-screen py-10">
      <div className="flex items-center gap-2 text-green">
        <CheckCircle size={24} />
        <h1 className="text-2xl font-bold">Payment Successful!</h1>
      </div>
      <p className="text-center text-muted-foreground mb-6">
        Your payment has been processed successfully. You will be redirected to
        the dashboard in a few seconds.
      </p>
      <div className="flex items-center gap-2">
        <span>Redirecting...</span>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900" />
      </div>
      <Button onClick={handleGoToDashboard} className="mt-4">
        Go to Dashboard Now
      </Button>
    </div>
  );
};
