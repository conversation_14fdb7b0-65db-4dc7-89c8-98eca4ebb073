import { FC, PropsWithChildren } from 'react';
import { Archivo } from 'next/font/google';

import './_styles/globals.css';

import { Toaster } from '~/components/ui/toaster';
import { AuthProvider } from '~/providers/AuthProvider';
import { OnboardingProvider } from '~/providers/OnboardingProvider';
import ReactQueryProvider from '~/providers/ReactQueryProvider';
import { RouteGuardProvider } from '~/providers/RouteGuardProvider.tsx';

const roboto = Archivo({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto',
});

export const metadata = {
  title: 'Astrala',
  description: 'Generated by Next.js',
};

const RootLayout: FC<PropsWithChildren> = ({ children }) => {
  return (
    <html lang="en" className={roboto.className}>
      <body className="bg-background text-foreground">
        <ReactQueryProvider>
          <AuthProvider>
            <OnboardingProvider>
              <RouteGuardProvider>{children}</RouteGuardProvider>
            </OnboardingProvider>
          </AuthProvider>
        </ReactQueryProvider>
        <Toaster />
      </body>
    </html>
  );
};

export default RootLayout;
