import { NextRequest, NextResponse } from 'next/server';

import { validateInvitationToken } from '~/api/features/company/actions';
import { canAddEmployer } from '~/api/features/subscription/helpers';

export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } },
) {
  const token = params.token;
  const APP_URL = process.env.APP_URL;

  const supabase = await createClient();
  const { data: sessionData } = await supabase.auth.getSession();

  if (sessionData.session) {
    return NextResponse.redirect(
      new URL('/invitation-error?type=already_logged_in', APP_URL),
    );
  }

  const validation = await validateInvitationToken(token);

  if (!validation.valid) {
    let errorType = 'invalid';

    const { data: tokenData } = await supabase
      .from('invitation_tokens')
      .select('is_used')
      .eq('token', token)
      .single();

    if (tokenData?.is_used) {
      errorType = 'already_used';
    }

    return NextResponse.redirect(
      new URL(`/invitation-error?type=${errorType}`, APP_URL),
    );
  }

  if (validation.companyId) {
    const canAdd = await canAddEmployer(validation.companyId);

    if (!canAdd) {
      return NextResponse.redirect(
        new URL('/invitation-error?type=seat_limit_reached', APP_URL),
      );
    }
  }

  let companyName = 'a company';

  if (validation.companyId) {
    try {
      const { data: company } = await supabase
        .from('companies')
        .select('name')
        .eq('id', validation.companyId)
        .single();

      if (company) {
        companyName = company.name;
      }
    } catch (error) {
      console.error('Error fetching company name:', error);
    }
  }

  const signupUrl = new URL('/signup', APP_URL);
  signupUrl.searchParams.append('token', token);
  signupUrl.searchParams.append(
    'company_id',
    validation.companyId?.toString() || '',
  );
  signupUrl.searchParams.append(
    'company_name',
    encodeURIComponent(companyName),
  );

  return NextResponse.redirect(signupUrl);
}

async function createClient() {
  const { createClient } = await import('~/supabase/server');
  return createClient();
}
