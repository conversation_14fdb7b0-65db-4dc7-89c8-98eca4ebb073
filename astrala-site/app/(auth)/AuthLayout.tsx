'use client';

import { ReactNode } from 'react';
import Image, { StaticImageData } from 'next/image';

import Logo from '~/assets/logo.svg';

type AuthLayoutProps = {
  sideImage: StaticImageData;
  children: ReactNode;
};

export default function AuthLayout({ sideImage, children }: AuthLayoutProps) {
  return (
    <div className="flex min-h-screen w-full bg-black">
      <div className="relative hidden lg:block w-[35%] min-h-screen">
        <Image
          src={sideImage}
          alt="Authentication"
          className="object-cover"
          style={{ filter: 'brightness(0.9)' }}
          fill
          priority
        />
      </div>

      <div className="w-full lg:w-[65%] flex flex-col items-center justify-between px-6 min-h-screen py-4">
        <div className="flex-grow" />

        <div className="w-full max-w-md flex flex-col items-center pt-4">
          <div className="flex justify-center mb-6 items-center">
            <Logo className="w-full" />
          </div>

          {children}
        </div>

        <div className="flex-grow" />

        <div className="text-center text-xs text-gray-500 w-full py-4">
          Astrala © {new Date().getFullYear()}
        </div>
      </div>
    </div>
  );
}
