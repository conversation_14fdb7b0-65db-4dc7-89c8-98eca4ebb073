'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import AuthLayout from '../AuthLayout';

import { updatePassword } from '~/api/features/auth/forgot-password/actions.ts';
import SignInImage from '~/assets/images/auth/signin.jpg';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input.tsx';
import { useAuth } from '~/providers/AuthProvider';

const updatePasswordSchema = z
  .object({
    password: z.string().min(6, 'Password must be at least 6 characters'),
    confirmPassword: z.string().min(1, 'Password confirmation is required'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords must match',
    path: ['confirmPassword'],
  });

type UpdatePasswordFormValues = z.infer<typeof updatePasswordSchema>;

export default function UpdatePassword() {
  const router = useRouter();
  const { userRole, isLoading } = useAuth();
  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [dashboardPath, setDashboardPath] = useState('/');

  useEffect(() => {
    if (!isLoading && userRole) {
      const path =
        userRole === 'job_seeker'
          ? '/dashboard/job-seeker/suggestions'
          : '/dashboard/employer/suggestions';
      setDashboardPath(path);
    }
  }, [isLoading, userRole]);

  const form = useForm<UpdatePasswordFormValues>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: UpdatePasswordFormValues) => {
    setIsPending(true);
    setError(null);

    try {
      const result = await updatePassword({
        password: data.password,
      });

      if (result.error) {
        setError(result.error);
      } else {
        setIsSuccess(true);
        setTimeout(() => {
          router.push(dashboardPath);
        }, 2000);
      }
    } catch (error_) {
      console.error('Error updating password:', error_);
      setError('An unexpected error occurred');
    } finally {
      setIsPending(false);
    }
  };

  if (isSuccess) {
    return (
      <AuthLayout sideImage={SignInImage}>
        <div className="w-full">
          <h2 className="text-xl font-semibold text-center">
            Password Updated Successfully
          </h2>

          <Alert className="mt-6 bg-primary/10 border-primary/20">
            <AlertDescription>
              Your password has been successfully updated. You will be
              redirected to dashboard.
            </AlertDescription>
          </Alert>

          <div className="text-center mt-5">
            <Link href={dashboardPath} className="text-primary hover:underline">
              Go to Dashboard now
            </Link>
          </div>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout sideImage={SignInImage}>
      <div className="w-full">
        <h2 className="text-xl font-semibold text-center">
          Create New Password
        </h2>
        <p className="text-center text-muted-foreground text-sm mt-1 mb-6">
          Enter your new password below
        </p>

        {error && (
          <Alert className="border-destructive/20 mb-4">
            <AlertDescription className="text-destructive">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="space-y-1.5">
                  <FormLabel className="text-foreground text-sm">
                    New Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="New Password"
                      type="password"
                      className="w-full h-12 px-3 rounded-md border"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-destructive text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem className="space-y-1.5">
                  <FormLabel className="text-foreground text-sm">
                    Confirm New Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Confirm New Password"
                      type="password"
                      className="w-full h-12 px-3 rounded-md border"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-destructive text-sm" />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              disabled={isPending}
              className="w-full"
              size="lg"
            >
              {isPending ? 'Updating...' : 'Update Password'}
            </Button>
          </form>
        </Form>

        <div className="text-center mt-5 text-sm">
          Remember your password?{' '}
          <Link href="/signin" className="text-primary hover:underline">
            Sign In
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
