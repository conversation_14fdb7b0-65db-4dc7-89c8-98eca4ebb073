'use client';

import { Suspense } from 'react';

import AuthLayout from '~/app/(auth)/AuthLayout.tsx';
import SignUpContent from '~/app/(auth)/signup/(components)/SignUpContent.tsx';

import SignUpImage from '~/assets/images/auth/signup.jpg';

export default function SignUp() {
  return (
    <AuthLayout sideImage={SignUpImage}>
      <Suspense
        fallback={<div className="w-full text-center p-8">Loading...</div>}
      >
        <SignUpContent />
      </Suspense>
    </AuthLayout>
  );
}
