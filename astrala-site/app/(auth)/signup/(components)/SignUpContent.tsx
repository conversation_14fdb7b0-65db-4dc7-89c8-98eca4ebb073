'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import { useSignUp } from '~/api/features/auth/queries';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import { Checkbox } from '~/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input.tsx';
import { Label } from '~/components/ui/label';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';

const createSignUpSchema = (isInvited: boolean) => {
  return z
    .object({
      email: z
        .string()
        .min(1, 'Email is required')
        .email('Invalid email address'),
      password: z.string().min(6, 'Password must be at least 6 characters'),
      confirmPassword: z.string().min(1, 'Password confirmation is required'),
      role: z.enum(['job_seeker', 'employer'], {
        required_error: 'Please select a role',
      }),
      fullName: isInvited
        ? z.string().min(1, 'Full name is required')
        : z.string().optional(),
      jobRole: isInvited
        ? z.string().min(1, 'Role in company is required')
        : z.string().optional(),
      agreeToTerms: z.boolean().refine(val => val === true, {
        message: 'You must agree to the Terms & Conditions',
      }),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: 'Passwords must match',
      path: ['confirmPassword'],
    });
};

type SignUpFormValues = z.infer<ReturnType<typeof createSignUpSchema>>;

export default function SignUpContent() {
  const searchParams = useSearchParams();
  const [isInvited, setIsInvited] = useState(false);
  const [invitationToken, setInvitationToken] = useState<string | null>(null);
  const [companyId, setCompanyId] = useState<number | null>(null);
  const [companyName, setCompanyName] = useState<string | null>(null);

  const { mutate: signUp, isPending, error, isError } = useSignUp();

  const [formSchema, setFormSchema] = useState(() => createSignUpSchema(false));

  const form = useForm<SignUpFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      role: undefined,
      fullName: '',
      jobRole: '',
      agreeToTerms: false,
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    const token = searchParams.get('token');
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const company_id = searchParams.get('company_id');
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const company_name = searchParams.get('company_name');

    if (token && company_id) {
      setIsInvited(true);
      setInvitationToken(token);
      setCompanyId(Number(company_id));
      setCompanyName(company_name ? decodeURIComponent(company_name) : null);

      form.setValue('role', 'employer');

      setFormSchema(createSignUpSchema(true));
    }
  }, [searchParams, form]);

  const onSubmit = (data: SignUpFormValues) => {
    const { confirmPassword, agreeToTerms, ...submitData } = data;

    if (isInvited && invitationToken && companyId) {
      signUp({
        ...submitData,
        fullName: data.fullName || '',
        jobRole: data.jobRole || '',
        companyId,
        invitationToken,
      });
    } else {
      signUp(submitData);
    }
  };

  const renderButtonText = () => {
    if (isPending) {
      return 'Loading...';
    }
    if (isInvited) {
      return 'Join Company';
    }
    return 'Sign Up';
  };

  return (
    <div className="w-full">
      <h2 className="text-xl font-semibold text-center">Sign Up</h2>

      {isInvited && companyName && (
        <Alert className="mt-4 mb-2 text-foreground border">
          <AlertDescription className="text-foreground">
            You've been invited to join{' '}
            <span className="font-medium">{companyName}</span>. Complete your
            registration to accept the invitation.
          </AlertDescription>
        </Alert>
      )}

      <p className="text-center text-foreground text-sm mt-1 mb-6">
        {!isInvited && 'Welcome! Begin by creating your account.'}
      </p>

      {isError && (
        <div className="text-destructive text-sm mb-4">{error.message}</div>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="space-y-1.5">
                <FormLabel className="text-sm">Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Email"
                    type="email"
                    className="w-full h-12 px-3 rounded-md border"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive text-sm" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="space-y-1.5">
                <FormLabel className="text-sm">Password</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Password"
                    type="password"
                    className="w-full h-12 px-3 rounded-md border"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive text-sm" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem className="space-y-1.5">
                <FormLabel className="text-sm">Confirm Password</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Password"
                    type="password"
                    className="w-full h-12 px-3 rounded-md border"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive text-sm" />
              </FormItem>
            )}
          />

          {!isInvited && (
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem className="space-y-1.5">
                  <FormLabel className="text-sm">I am:</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                      className="flex flex-col space-y-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="job_seeker" id="job_seeker" />
                        <Label
                          htmlFor="job_seeker"
                          className="text-sm font-normal"
                        >
                          Looking for a job
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="employer" id="employer" />
                        <Label
                          htmlFor="employer"
                          className="text-sm font-normal"
                        >
                          Looking for an employee
                        </Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage className="text-destructive text-sm" />
                </FormItem>
              )}
            />
          )}

          {isInvited && (
            <>
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel className="text-sm">Full Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Your full name"
                        className="w-full h-12 px-3 rounded-md border"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-destructive text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="jobRole"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel className="text-sm">
                      Your Role in Company
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="HR Manager, Recruiter, etc."
                        className="w-full h-12 px-3 rounded-md border"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-destructive text-sm" />
                  </FormItem>
                )}
              />
            </>
          )}

          <FormField
            control={form.control}
            name="agreeToTerms"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    id="agreeToTerms"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <Label
                    htmlFor="agreeToTerms"
                    className="text-sm cursor-pointer"
                  >
                    I agree to the{' '}
                    <Link
                      href="/terms-and-conditions"
                      className="text-primary hover:underline underline"
                      target="_blank"
                    >
                      Terms & Conditions
                    </Link>
                  </Label>
                  <FormMessage className="text-destructive text-sm" />
                </div>
              </FormItem>
            )}
          />

          <Button
            variant="default"
            type="submit"
            disabled={isPending}
            className="w-full"
            size="lg"
          >
            {renderButtonText()}
          </Button>
        </form>
      </Form>

      <div className="text-center mt-5 text-sm">
        Already have an account?{' '}
        <Link href="/signin" className="text-primary hover:underline">
          Sign In
        </Link>
      </div>

      <p className="text-xs text-muted-foreground text-center mt-8 max-w-md mx-auto">
        By submitting your details, you agree to Astral a Nexus processing your
        personal data in accordance with our{' '}
        <Link
          href="/terms-and-conditions"
          className="text-primary hover:underline"
        >
          Terms & Conditions
        </Link>
        . Your information will be securely stored and used solely for the
        purpose of providing our services, matching talent with opportunities,
        and improving user experience. You have the right to access, modify, or
        request deletion of your data at any time in compliance with GDPR
        regulations.
      </p>
      <p className="text-xs text-muted-foreground text-center mt-2">
        If you have any further questions please do not hesitate to call.
      </p>
    </div>
  );
}
