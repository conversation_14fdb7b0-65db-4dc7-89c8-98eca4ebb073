'use client';

import { useForm } from 'react-hook-form';
import Link from 'next/link';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import AuthLayout from '../AuthLayout';

import { useSignIn } from '~/api/features/auth/queries';
import SignInImage from '~/assets/images/auth/signin.jpg';
import { Button } from '~/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input.tsx';

const signInSchema = z.object({
  email: z.string().min(1, 'Email is required').email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});
type SignInFormValues = z.infer<typeof signInSchema>;

export default function SignIn() {
  const { mutate: signIn, isPending, error, isError } = useSignIn();

  const form = useForm<SignInFormValues>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = (data: SignInFormValues) => {
    signIn(data);
  };

  return (
    <AuthLayout sideImage={SignInImage}>
      <div className="w-full">
        <h2 className="text-xl font-semibold text-center">Sign In</h2>
        <p className="text-center text-muted-foreground text-sm mt-1 mb-6">
          Welcome back!
        </p>

        {isError && (
          <div className="text-destructive text-sm mb-4">{error.message}</div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="space-y-1.5">
                  <FormLabel className="text-foreground text-sm">
                    Email
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Email"
                      type="email"
                      className="w-full h-12 px-3 rounded-md border"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-destructive text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="space-y-1.5">
                  <FormLabel className="text-foreground text-sm">
                    Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Password"
                      type="password"
                      className="w-full h-12 px-3 rounded-md border"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-destructive text-sm" />
                </FormItem>
              )}
            />

            <div className="flex justify-center mt-1">
              <Link
                href="/forgot-password"
                className="text-sm text-primary hover:underline"
              >
                Forgot your password?
              </Link>
            </div>

            <Button
              variant="default"
              size="lg"
              type="submit"
              disabled={isPending}
              className="w-full"
            >
              {isPending ? 'Loading...' : 'Sign In'}
            </Button>
          </form>
        </Form>

        <div className="text-center mt-5 text-sm">
          Don't have an account?{' '}
          <Link href="/signup" className="text-primary hover:underline">
            Sign Up
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
