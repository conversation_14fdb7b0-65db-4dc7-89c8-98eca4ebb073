'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import AuthLayout from '../AuthLayout';

import { requestPasswordReset } from '~/api/features/auth/forgot-password/actions.ts';
import SignInImage from '~/assets/images/auth/signin.jpg';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input.tsx';

const forgotPasswordSchema = z.object({
  email: z.string().min(1, 'Email is required').email('Invalid email address'),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPassword() {
  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: ForgotPasswordFormValues) => {
    setIsPending(true);
    setError(null);

    try {
      const result = await requestPasswordReset({
        email: data.email,
      });

      if (result.error) {
        setError(result.error);
      } else {
        setIsSuccess(true);
        form.reset();
      }
    } catch (error_) {
      console.error('Error requesting password reset:', error_);
      setError('An unexpected error occurred');
    } finally {
      setIsPending(false);
    }
  };

  return (
    <AuthLayout sideImage={SignInImage}>
      <div className="w-full">
        <h2 className="text-xl font-semibold text-center">Reset Password</h2>
        <p className="text-center text-muted-foreground text-sm mt-1 mb-6">
          Enter your email and we'll send you instructions to reset your
          password
        </p>

        {isSuccess ? (
          <div className="space-y-4">
            <Alert className="bg-primary/10 border-primary/20">
              <AlertDescription>
                If an account exists with this email, we've sent you
                instructions to reset your password. Please check your inbox and
                spam folder.
              </AlertDescription>
            </Alert>
            <div className="text-center mt-4">
              <Link href="/signin" className="text-primary hover:underline">
                Return to Sign In
              </Link>
            </div>
          </div>
        ) : (
          <>
            {error && (
              <Alert className="bg-destructive/10 border-destructive/20 mb-4">
                <AlertDescription className="text-destructive">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-5"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel className="text-foreground text-sm">
                        Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Email"
                          type="email"
                          className="w-full h-12 px-3 rounded-md border"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-destructive text-sm" />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={isPending}
                  className="w-full"
                  size="lg"
                >
                  {isPending ? 'Sending...' : 'Send Reset Instructions'}
                </Button>
              </form>
            </Form>

            <div className="text-center mt-5 text-sm">
              Remember your password?{' '}
              <Link href="/signin" className="text-primary hover:underline">
                Sign In
              </Link>
            </div>
          </>
        )}
      </div>
    </AuthLayout>
  );
}
