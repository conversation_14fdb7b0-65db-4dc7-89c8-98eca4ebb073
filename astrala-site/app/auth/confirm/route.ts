import { NextRequest, NextResponse } from 'next/server';
import { type EmailOtpType } from '@supabase/supabase-js';

import { createClient } from '~/supabase/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const token_hash = searchParams.get('token_hash');
  const type = searchParams.get('type') as EmailOtpType | null;
  const next = searchParams.get('next') ?? '/';

  const baseUrl = process.env.APP_URL || 'https://dev.astralanexus.ai';

  const redirectUrl = new URL(next, baseUrl);

  if (token_hash) {
    redirectUrl.searchParams.set('token_hash', token_hash);
  }
  if (type) {
    redirectUrl.searchParams.set('type', type);
  }

  if (token_hash && type) {
    const supabase = await createClient();

    try {
      const { error } = await supabase.auth.verifyOtp({
        type,
        token_hash,
      });

      if (!error) {
        return NextResponse.redirect(redirectUrl.toString());
      }
    } catch (error) {}
  }

  const errorUrl = new URL('/auth/auth-code-error', baseUrl);
  return NextResponse.redirect(errorUrl.toString());
}
