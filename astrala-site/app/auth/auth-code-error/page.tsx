'use client';

import Link from 'next/link';

import AuthLayout from '~/app/(auth)/AuthLayout';

import SignInImage from '~/assets/images/auth/signin.jpg';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { But<PERSON> } from '~/components/ui/button';

export default function AuthCodeError() {
  return (
    <AuthLayout sideImage={SignInImage}>
      <div className="w-full">
        <h2 className="text-xl font-semibold text-center">
          Authentication Error
        </h2>

        <Alert className="mt-6 bg-red-50 border-red-200">
          <AlertDescription>
            <p className="mb-2">
              The authentication link is invalid or has expired.
            </p>
            <p>
              Please request a new password reset link or contact support if you
              continue to experience issues.
            </p>
          </AlertDescription>
        </Alert>

        <div className="mt-6 flex justify-center">
          <Button asChild>
            <Link href="/forgot-password">Request New Reset Link</Link>
          </Button>
        </div>

        <div className="text-center mt-5 text-sm">
          Remember your password?{' '}
          <Link href="/signin" className="text-blue hover:underline">
            Sign In
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
