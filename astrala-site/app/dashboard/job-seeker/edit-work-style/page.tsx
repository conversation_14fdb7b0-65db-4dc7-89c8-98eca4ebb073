'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import EditWorkStyleStep from '../(components)/EditWorkStyleStep';

import {
  useJobSeekerCanEditWorkStyle,
  useUpdateJobSeekerWorkStyle,
} from '~/api/entities/job-seeker/queries';
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { useToast } from '~/hooks/use-toast.ts';
import { useAuth } from '~/providers/AuthProvider';

export default function EditJobSeekerWorkStyle() {
  const { toast } = useToast();
  const router = useRouter();
  const { jobSeekerProfile, isLoading } = useAuth();
  const updateWorkStyleMutation = useUpdateJobSeekerWorkStyle();

  const { data: canEditData } = useJobSeekerCanEditWorkStyle(
    jobSeekerProfile?.id ? Number(jobSeekerProfile.id) : undefined,
  );

  useEffect(() => {
    if (!isLoading && canEditData && !canEditData.canEdit) {
      toast({
        title: 'Editing Restricted',
        description:
          'You can only edit your Behavioural Profile preferences once every 24 hours.',
        variant: 'destructive',
      });
      router.push('/dashboard/job-seeker/dashboard');
    }
  }, [canEditData, isLoading, router, toast]);

  const handleComplete = async () => {
    if (jobSeekerProfile?.id) {
      try {
        await updateWorkStyleMutation.mutateAsync(Number(jobSeekerProfile.id));

        toast({
          title: 'Behavioural Profile Updated',
          description:
            'Your Behavioural Profile preferences have been successfully updated',
        });

        router.push('/dashboard/job-seeker/dashboard');
      } catch (error) {
        console.error('Error updating Behavioural Profile timestamp:', error);
        toast({
          title: 'Error',
          description: 'Failed to complete the Behavioural Profile update',
          variant: 'destructive',
        });
      }
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/job-seeker/dashboard');
  };

  if (isLoading || !jobSeekerProfile) {
    return <div className="p-6">Loading...</div>;
  }

  return (
    <div className="container w-full">
      <div>
        <CardHeader>
          <CardTitle className="text-xl">
            Edit Your Behavioural Profile
          </CardTitle>
        </CardHeader>
        <CardContent>
          <EditWorkStyleStep
            onComplete={handleComplete}
            onCancel={handleCancel}
            jobSeekerId={Number(jobSeekerProfile.id)}
          />
        </CardContent>
      </div>
    </div>
  );
}
