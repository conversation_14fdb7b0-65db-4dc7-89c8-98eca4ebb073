'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList';
import { MatchFeedback } from '~/app/dashboard/(components)/MatchFeedback.tsx';
import { MatchesActionButtons } from '~/app/dashboard/job-seeker/(components)/ActionButtons';

import { useGetMatches } from '~/api/features/matches/job-seeker/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function MatchesPage() {
  const [page, setPage] = useState(1);
  const pageSize = 5;

  const { jobSeekerProfile, isLoading: isLoadingJobSeeker } = useAuth();

  const jobSeekerId = jobSeekerProfile?.id as number | undefined;

  const {
    data: matches,
    isLoading: isMatchesLoading,
    error,
  } = useGetMatches(jobSeekerId, page, pageSize);

  const isLoading = isLoadingJobSeeker || isMatchesLoading;

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={matches || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="jobSeeker"
        useAccordion={true}
        emptyTitle="Your matches are on the way!"
        emptyMessage="When the right opportunity aligns with who you are - and a company feels the same - it will appear here."
        showActions={true}
        onPageChange={setPage}
      >
        {match => (
          <div className="space-y-3">
            <MatchFeedback matchId={match.id} viewMode="jobSeeker" />

            <MatchesActionButtons match={match} />
          </div>
        )}
      </MatchesList>
    </div>
  );
}
