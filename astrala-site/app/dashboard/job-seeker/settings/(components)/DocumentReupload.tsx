'use client';
import React, { useRef, useState } from 'react';
import { Check, Loader2 } from 'lucide-react';

import { Button } from '~/components/ui/button';

export type DocumentType = 'resume' | 'drivingLicense' | 'passport';

type DocumentReuploadProps = {
  documentType: DocumentType;
  hasDocument: boolean;
  onFileChange: (file: File | null) => void;
  isUploading: boolean;
  acceptedFileTypes?: string;
};

export const DocumentReupload: React.FC<DocumentReuploadProps> = ({
  documentType,
  hasDocument,
  onFileChange,
  isUploading,
  acceptedFileTypes = '.pdf,.doc,.docx,.jpg,.jpeg,.png',
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const documentLabels: Record<DocumentType, string> = {
    resume: 'Resume',
    drivingLicense: 'Driving License',
    passport: 'Passport',
  };

  const documentLabel = documentLabels[documentType];

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange(file);
  };

  const handleReuploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-4">
      <input
        type="file"
        className="hidden"
        ref={fileInputRef}
        onChange={handleFileInputChange}
        accept={acceptedFileTypes}
      />

      {(hasDocument || selectedFile) && (
        <div className="flex items-center justify-between rounded-md border border-input bg-dark p-2 px-3 py-4 bg-background">
          <div className="flex items-center gap-2">
            <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center text-foreground">
              <Check className="h-3.5 w-3.5" />
            </div>
            <span className="text-sm font-medium">
              {documentLabel} Uploaded
            </span>
          </div>
        </div>
      )}

      <Button
        variant="default"
        type="button"
        className="w-full"
        onClick={handleReuploadClick}
        disabled={isUploading}
      >
        {isUploading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Uploading...
          </>
        ) : (
          `${hasDocument ? 'Reupload' : 'Upload'} ${documentLabel}`
        )}
      </Button>
    </div>
  );
};
