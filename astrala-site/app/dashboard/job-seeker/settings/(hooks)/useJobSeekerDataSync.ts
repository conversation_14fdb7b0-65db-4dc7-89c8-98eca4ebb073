'use client';

import { useEffect, useState } from 'react';
import { UseFormReset, UseFormSetValue } from 'react-hook-form';

import { MainInformationFormSchema } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema.ts';

import { Database } from '~/shared/db/generated/types.ts';

import { useJobSeekerFullProfile } from '~/api/entities/job-seeker/queries.ts';

type JobSeekerProfile = Database['public']['Tables']['job_seekers']['Row'];

export function useJobSeekerDataSync(
  jobSeekerProfile: JobSeekerProfile | undefined | null,
  setValue: UseFormSetValue<MainInformationFormSchema>,
  reset: UseFormReset<MainInformationFormSchema>,
) {
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [syncError, setSyncError] = useState<string | null>(null);
  const [formDataInitialized, setFormDataInitialized] = useState(false);

  const { data: fullProfileData, isLoading: isFullProfileLoading } =
    useJobSeekerFullProfile(jobSeekerProfile?.id, jobSeekerProfile?.user_id);

  useEffect(() => {
    if (jobSeekerProfile && fullProfileData?.success && !formDataInitialized) {
      try {
        setIsDataLoading(true);

        const formValues: Partial<MainInformationFormSchema> = {
          personalInfo: {
            fullName: jobSeekerProfile.full_name || '',
            jobTitle: jobSeekerProfile.job_title || '',
            phone: jobSeekerProfile.phone || '',
            country: jobSeekerProfile.country || '',
            city: jobSeekerProfile.city || '',
          },
          salary_min: jobSeekerProfile.salary_min || 0,
          salary_max: jobSeekerProfile.salary_max || 0,
          has_driving_license: jobSeekerProfile.has_driving_license || null,
          education: [],
          workExperience: [],
          languages: [],
          skills: [],
          certifications: [],
        };

        const profileData = fullProfileData.data;

        if (profileData.educations.length > 0) {
          formValues.education = profileData.educations.map(edu => ({
            type: edu.type,
            institution: edu.institution,
            discipline: edu.discipline,
            startYear: edu.startYear,
            endYear: edu.endYear,
          }));
        }

        if (profileData.workExperiences.length > 0) {
          formValues.workExperience = profileData.workExperiences.map(exp => ({
            companyName: exp.companyName,
            jobTitle: exp.jobTitle,
            startMonth: exp.startMonth,
            startYear: exp.startYear,
            endMonth: exp.endMonth,
            endYear: exp.endYear,
            comment: exp.comment || '',
          }));
        } else {
          const currentDate = new Date();
          formValues.workExperience = [
            {
              companyName: '',
              jobTitle: '',
              startMonth: currentDate.getMonth() + 1,
              startYear: currentDate.getFullYear() - 1,
              endMonth: currentDate.getMonth() + 1,
              endYear: currentDate.getFullYear(),
              comment: '',
            },
          ];
        }

        if (profileData.languages.length > 0) {
          formValues.languages = profileData.languages.map(lang => ({
            language: lang.language,
            level: lang.level,
          }));
        }

        if (profileData.skills.length > 0) {
          formValues.skills = profileData.skills.map(skill => ({
            id: skill.id,
            name: skill.name,
          }));
        }

        if (profileData.certifications.length > 0) {
          formValues.certifications = profileData.certifications.map(cert => ({
            id: cert.id,
            name: cert.name,
            file_path: cert.file_path,
          }));
        }

        reset(formValues as MainInformationFormSchema);

        setFormDataInitialized(true);
        setIsDataLoading(false);
        setSyncError(null);
      } catch (error) {
        console.error('Error loading job seeker data:', error);
        setSyncError('Failed to load existing profile data');
        setIsDataLoading(false);
      }
    } else if (
      !isFullProfileLoading &&
      !fullProfileData?.success &&
      !formDataInitialized
    ) {
      setSyncError('Failed to load profile data from server');
      setIsDataLoading(false);
    }
  }, [
    jobSeekerProfile,
    fullProfileData,
    setValue,
    reset,
    isFullProfileLoading,
    formDataInitialized,
  ]);

  return {
    isDataLoading: isDataLoading || isFullProfileLoading,
    syncError,
    profileData: fullProfileData?.data,
    formDataInitialized,
  };
}
