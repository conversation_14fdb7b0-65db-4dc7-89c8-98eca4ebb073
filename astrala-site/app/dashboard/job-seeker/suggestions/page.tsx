'use client';

import { useCallback, useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

import { MatchData } from '~/app/dashboard/(components)/MatchCard';
import { SuggestionsBase } from '~/app/dashboard/(components)/SuggestionsBase';
import { SuggestionsActionButtons } from '~/app/dashboard/job-seeker/(components)/ActionButtons';

import {
  useGetSuggestions,
  useMarkJobSeekerInterested,
  useMarkJobSeekerRejected,
  vacancyMatchesCountsKeys,
} from '~/api/features/matches/job-seeker/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function JobSeekerSuggestionsPage() {
  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const previousSuggestionRef = useRef<MatchData | null>(null);
  const { jobSeekerProfile, isLoading: isLoadingJobSeeker } = useAuth();
  const jobSeekerId = jobSeekerProfile?.id as number | undefined;

  const {
    data: matchesResponse,
    isLoading: isSuggestionLoading,
    error,
    refetch: refetchMatches,
  } = useGetSuggestions(jobSeekerId, 1, 1);

  const currentSuggestion = (matchesResponse?.data?.[0] ||
    null) as MatchData | null;

  const { markAsInterested, isLoading: isMarkingInterested } =
    useMarkJobSeekerInterested();
  const { markAsRejected, isLoading: isMarkingRejected } =
    useMarkJobSeekerRejected();

  const isGeneratingSuggestions =
    !isLoadingJobSeeker && !isSuggestionLoading && !currentSuggestion && !error;

  const invalidateCounts = useCallback(() => {
    if (jobSeekerId) {
      queryClient.invalidateQueries({
        queryKey: vacancyMatchesCountsKeys.all,
      });
    }
  }, [queryClient, jobSeekerId]);

  const handleRefetchWithCountsInvalidation = useCallback(async () => {
    invalidateCounts();
    return refetchMatches();
  }, [invalidateCounts, refetchMatches]);

  useEffect(() => {
    if (!previousSuggestionRef.current && currentSuggestion) {
      invalidateCounts();
    }
    previousSuggestionRef.current = currentSuggestion;
  }, [currentSuggestion, invalidateCounts]);

  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (
      !currentSuggestion &&
      !isLoadingJobSeeker &&
      !isSuggestionLoading &&
      !error
    ) {
      intervalRef.current = setInterval(() => {
        handleRefetchWithCountsInvalidation();
      }, 5000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [
    currentSuggestion,
    isLoadingJobSeeker,
    isSuggestionLoading,
    error,
    handleRefetchWithCountsInvalidation,
  ]);

  useEffect(() => {
    if (jobSeekerId) {
      invalidateCounts();
    }
  }, [jobSeekerId, invalidateCounts]);

  return (
    <SuggestionsBase
      viewMode="jobSeeker"
      isProfileLoading={isLoadingJobSeeker}
      currentSuggestion={currentSuggestion}
      isSuggestionLoading={isSuggestionLoading}
      error={error as Error | null}
      refetchMatches={handleRefetchWithCountsInvalidation}
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      markAsInterested={markAsInterested}
      isMarkingInterested={isMarkingInterested}
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      markAsRejected={markAsRejected}
      isMarkingRejected={isMarkingRejected}
      ActionButtons={SuggestionsActionButtons}
      emptyStateTitle="No more suggestions available"
      emptyStateDescription="We'll notify you when new job matches become available"
      interestedToastMessage="You've expressed interest in this position."
      rejectedToastMessage="You've rejected this position."
      isGeneratingSuggestions={isGeneratingSuggestions}
      generatingSuggestionsMessage="We're generating job suggestions for you..."
      showRefreshButton={!currentSuggestion}
      refreshButtonLabel="Check for new jobs"
    />
  );
}
