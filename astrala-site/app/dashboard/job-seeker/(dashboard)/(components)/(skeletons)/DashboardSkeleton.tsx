import { Card } from '~/components/ui/card.tsx';
import { Skeleton } from '~/components/ui/skeleton';

function DashboardSkeleton() {
  return (
    <div className="h-screen bg-[#101010] p-8 text-foreground font-medium w-full">
      <Skeleton className="h-5 w-32 mb-2" />
      <Skeleton className="h-9 w-48 mb-4" />
      <div className="w-full flex gap-8">
        <div className="w-[75%]">
          <div className="flex items-center gap-4 mb-6 w-full flex-1">
            <Card className="w-1/2 p-6">
              <Skeleton className="h-4 w-32 mb-2" />
              <div className="flex gap-2 items-center">
                <Skeleton className="h-9 w-40" />
                <Skeleton className="h-7 w-10 rounded-full" />
              </div>
            </Card>
            <Card className="w-1/2 p-6">
              <Skeleton className="h-4 w-24 mb-2" />
              <div className="flex gap-2 items-center">
                <Skeleton className="h-9 w-24" />
                <Skeleton className="h-7 w-10 rounded-full" />
              </div>
            </Card>
          </div>
          <Card className="flex items-center justify-center">
            <div className="py-12">
              <Skeleton className="w-[800px] h-[800px] rounded-full" />
            </div>
          </Card>
        </div>
        <div className="w-[30%] space-y-6">
          {[...new Array(6)].map((_, i) => (
            // eslint-disable-next-line react/no-array-index-key
            <Card key={i} className="p-6">
              <div className="flex items-center mb-3 gap-2">
                <Skeleton className="h-8 w-32" />
                <Skeleton className="h-7 w-12 rounded-md" />
              </div>
              <Skeleton className="w-full h-4 rounded-full" />
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}

export default DashboardSkeleton;
