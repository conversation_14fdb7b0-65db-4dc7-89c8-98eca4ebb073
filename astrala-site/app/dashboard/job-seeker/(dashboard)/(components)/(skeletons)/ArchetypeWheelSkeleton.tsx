import React from 'react';

import { Skeleton } from '~/components/ui/skeleton.tsx';

function ArchetypeWheelSkeleton() {
  return (
    <div className="flex items-center justify-center py-12 w-full">
      <div className="relative w-full max-w-[600px] aspect-square">
        <Skeleton className="w-full h-full rounded-full" />
        <div
          className="absolute flex items-center justify-center"
          style={{
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
          }}
        >
          <div className="w-full flex flex-col items-center">
            <Skeleton className="w-14 h-14 rounded-full mb-4" />
            <Skeleton className="h-5 w-48 mb-2" />
            <Skeleton className="h-7 w-32" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default ArchetypeWheelSkeleton;
