import React, { useState } from 'react';

import ArchetypeWheelSkeleton from '~/app/dashboard/job-seeker/(dashboard)/(components)/(skeletons)/ArchetypeWheelSkeleton.tsx';

import { ARCHETYPE_CONFIG, DIMENSION_CONFIG } from './constants.ts';
import {
  useGetTopArchetype,
  useGetUserDimensionScores,
} from '~/api/features/archetypes/queries.ts';
import { DimensionWithScore } from '~/api/features/archetypes/types.ts';
import { useAuth } from '~/providers/AuthProvider.tsx';

const colors = {
  primary: '#4B9C88',
  primaryDark: '#181D19',
  secondary: '#1f2937',
  secondaryLight: '#374151',
  background: '#101010',
  text: '#ffffff',
  textSecondary: '#9ca3af',
  textMuted: '#6b7280',
  border: 'rgba(75, 85, 99, 0.5)',
  glow: 'rgba(75, 156, 136, 0.6)',
  glowBright: 'rgba(75, 156, 136, 0.8)',
  gradientStart: 'rgba(75, 156, 136, 0.1)',
  gradientEnd: 'rgba(75, 156, 136, 0.05)',
};

const createRoundedRingSectorPath = (
  centerX: number,
  centerY: number,
  innerR: number,
  outerR: number,
  startAngle: number,
  endAngle: number,
): string => {
  const startAngleRad = (startAngle * Math.PI) / 180;
  const endAngleRad = (endAngle * Math.PI) / 180;
  const cornerRadius = 15;

  const outerX1 = centerX + outerR * Math.cos(startAngleRad);
  const outerY1 = centerY + outerR * Math.sin(startAngleRad);
  const outerX2 = centerX + outerR * Math.cos(endAngleRad);
  const outerY2 = centerY + outerR * Math.sin(endAngleRad);

  const innerX1 = centerX + innerR * Math.cos(startAngleRad);
  const innerY1 = centerY + innerR * Math.sin(startAngleRad);
  const innerX2 = centerX + innerR * Math.cos(endAngleRad);
  const innerY2 = centerY + innerR * Math.sin(endAngleRad);

  const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';

  const angleOffset = cornerRadius / outerR;
  const innerAngleOffset = cornerRadius / innerR;

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const outerX1_start =
    centerX + outerR * Math.cos(startAngleRad + angleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const outerY1_start =
    centerY + outerR * Math.sin(startAngleRad + angleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const outerX2_end = centerX + outerR * Math.cos(endAngleRad - angleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const outerY2_end = centerY + outerR * Math.sin(endAngleRad - angleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const innerX1_start =
    centerX + innerR * Math.cos(startAngleRad + innerAngleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const innerY1_start =
    centerY + innerR * Math.sin(startAngleRad + innerAngleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const innerX2_end =
    centerX + innerR * Math.cos(endAngleRad - innerAngleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const innerY2_end =
    centerY + innerR * Math.sin(endAngleRad - innerAngleOffset);
  const midX1 = centerX + (outerR - cornerRadius) * Math.cos(startAngleRad);
  const midY1 = centerY + (outerR - cornerRadius) * Math.sin(startAngleRad);
  const midX2 = centerX + (outerR - cornerRadius) * Math.cos(endAngleRad);
  const midY2 = centerY + (outerR - cornerRadius) * Math.sin(endAngleRad);

  const innerMidX1 =
    centerX + (innerR + cornerRadius) * Math.cos(startAngleRad);
  const innerMidY1 =
    centerY + (innerR + cornerRadius) * Math.sin(startAngleRad);
  const innerMidX2 = centerX + (innerR + cornerRadius) * Math.cos(endAngleRad);
  const innerMidY2 = centerY + (innerR + cornerRadius) * Math.sin(endAngleRad);

  return `
    M ${outerX1_start} ${outerY1_start}
    A ${outerR} ${outerR} 0 ${largeArcFlag} 1 ${outerX2_end} ${outerY2_end}
    Q ${outerX2} ${outerY2} ${midX2} ${midY2}
    L ${innerMidX2} ${innerMidY2}
    Q ${innerX2} ${innerY2} ${innerX2_end} ${innerY2_end}
    A ${innerR} ${innerR} 0 ${largeArcFlag} 0 ${innerX1_start} ${innerY1_start}
    Q ${innerX1} ${innerY1} ${innerMidX1} ${innerMidY1}
    L ${midX1} ${midY1}
    Q ${outerX1} ${outerY1} ${outerX1_start} ${outerY1_start}
    Z
  `;
};

const ArchetypeWheel: React.FC = () => {
  const { jobSeekerProfile } = useAuth();
  const jobSeekerId = jobSeekerProfile?.id;

  const { data: dimensionScores, isLoading: isLoadingScores } =
    useGetUserDimensionScores(jobSeekerId);
  const { data: topArchetype, isLoading: isLoadingArchetype } =
    useGetTopArchetype(jobSeekerId);

  const [selectedDimension, setSelectedDimension] = useState<number | null>(
    null,
  );
  const [hoveredDimension, setHoveredDimension] = useState<number | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  const dimensionsWithConfig: DimensionWithScore[] = React.useMemo(() => {
    if (!dimensionScores) return [];

    return dimensionScores.map(ds => {
      const dimension = Array.isArray(ds.dimensions)
        ? ds.dimensions[0]
        : ds.dimensions;
      const config = DIMENSION_CONFIG[dimension.name];

      return {
        id: dimension.id,
        name: dimension.name,
        left_label: dimension.left_label,
        right_label: dimension.right_label,
        score: ds.score,
        icon: config?.icon,
      };
    });
  }, [dimensionScores]);

  const isLoading = isLoadingScores || isLoadingArchetype;

  if (isLoading || dimensionsWithConfig.length === 0) {
    return <ArchetypeWheelSkeleton />;
  }

  const centerX = 450;
  const centerY = 450;
  const outerRadius = 350;
  const innerRadius = 200;
  const gap = 40;
  const sectorGap = 4;
  const sectorInnerRadius = innerRadius + gap;
  const sectorAngle = 360 / dimensionsWithConfig.length;
  const actualSectorAngle = sectorAngle - sectorGap;

  const onDimensionClick = (id: number) => {
    setSelectedDimension(prev => (prev === id ? null : id));
  };

  const handleMouseEnter = (id: number, event: React.MouseEvent) => {
    setHoveredDimension(id);
    setTooltipPosition({
      x: event.clientX,
      y: event.clientY,
    });
  };

  const handleMouseLeave = () => {
    setHoveredDimension(null);
    setTooltipPosition(null);
  };

  const selectedDimensionData = dimensionsWithConfig.find(
    d => d.id === selectedDimension,
  );
  const hoveredDimensionData = dimensionsWithConfig.find(
    d => d.id === hoveredDimension,
  );
  const showArchetype = !selectedDimension && topArchetype;
  const showDimension = selectedDimensionData;

  let centerContent = null;

  const ArchetypeIcon =
    topArchetype && ARCHETYPE_CONFIG[topArchetype.name]?.icon;

  if (showArchetype && topArchetype) {
    centerContent = (
      <div className="w-full text-center">
        {ArchetypeIcon && (
          <div className="flex items-center justify-center mb-3">
            <div className="w-16 h-16" style={{ color: colors.primary }}>
              <ArchetypeIcon />
            </div>
          </div>
        )}
        <div
          className="text-2xl font-semibold mb-2"
          style={{ color: colors.text }}
        >
          {topArchetype.name}
        </div>
        <div
          className="text-sm italic px-4 max-w-60"
          style={{ color: colors.textMuted }}
        >
          "{topArchetype.metaphor}"
        </div>
      </div>
    );
  }

  if (showDimension) {
    const Icon = showDimension.icon;
    const dominantLabel =
      showDimension.score >= 0.5
        ? showDimension.right_label
        : showDimension.left_label;
    const dimensionConfig = DIMENSION_CONFIG[showDimension.name];

    centerContent = (
      <div className="w-full text-center px-4">
        {Icon && (
          <div className="flex items-center justify-center mb-4">
            <div className="w-14 h-14" style={{ color: colors.primary }}>
              <Icon />
            </div>
          </div>
        )}
        <div className="text-sm mb-2" style={{ color: colors.textMuted }}>
          {showDimension.name}
        </div>
        <div
          className="text-2xl font-semibold mb-3"
          style={{ color: colors.text }}
        >
          {dominantLabel}
        </div>
        {dimensionConfig?.expandedDescription && (
          <div
            className="text-xs italic px-2 max-w-72 leading-relaxed"
            style={{ color: colors.textMuted }}
          >
            {dimensionConfig.expandedDescription}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center relative">
      <div className="relative w-full max-w-[800px] aspect-square">
        <div className="w-full h-full">
          <svg
            className="w-full h-full transform -rotate-90"
            viewBox="0 0 900 900"
            preserveAspectRatio="xMidYMid meet"
          >
            <defs>
              <mask id="innerGlowMask">
                <rect x="0" y="0" width="900" height="900" fill="white" />
                <circle
                  cx={centerX}
                  cy={centerY}
                  r={innerRadius}
                  fill="black"
                />
              </mask>

              <mask id="glowOnlyMask">
                <rect x="0" y="0" width="1500" height="1500" fill="white" />
                <circle
                  cx={centerX}
                  cy={centerY}
                  r={outerRadius + 30}
                  fill="black"
                />
              </mask>

              <filter
                id="outerComponentGlow"
                x="-50%"
                y="-50%"
                width="300%"
                height="200%"
              >
                <feGaussianBlur in="SourceAlpha" stdDeviation="30" />
                <feOffset dx="0" dy="0" result="offsetblur" />
                <feFlood floodColor="#4B9C88" floodOpacity="0.8" />
                <feComposite in2="offsetblur" operator="in" />
                <feMerge>
                  <feMergeNode />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>

              <filter
                id="innerCircleShadow"
                x="-100%"
                y="-100%"
                width="300%"
                height="300%"
              >
                <feGaussianBlur in="SourceAlpha" stdDeviation="35" />
                <feOffset dx="0" dy="0" result="offsetblur" />
                <feFlood floodColor="#4B9C88" floodOpacity="0.8" />
                <feComposite in2="offsetblur" operator="in" />
                <feMerge>
                  <feMergeNode />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>

              <filter id="glow">
                <feGaussianBlur stdDeviation="8" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>

              <linearGradient
                id="sectorGradient"
                x1="0%"
                y1="0%"
                x2="0%"
                y2="100%"
              >
                <stop offset="0%" stopColor="rgba(255, 255, 255, 0.2)" />
                <stop offset="100%" stopColor="rgba(95, 95, 95, 0.2)" />
              </linearGradient>

              <linearGradient
                id="sectorGradientHover"
                x1="0%"
                y1="0%"
                x2="0%"
                y2="100%"
              >
                <stop offset="0%" stopColor="rgba(255, 255, 255, 0.3)" />
                <stop offset="100%" stopColor="rgba(95, 95, 95, 0.3)" />
              </linearGradient>

              <linearGradient
                id="activeGradient"
                x1="0%"
                y1="0%"
                x2="0%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#4B9C88" />
                <stop offset="100%" stopColor="#181D19" />
              </linearGradient>
            </defs>

            <circle
              cx={centerX}
              cy={centerY}
              r={outerRadius + 30}
              fill={colors.primary}
              filter="url(#outerComponentGlow)"
              mask="url(#glowOnlyMask)"
            />

            {dimensionsWithConfig.map((dimension, index) => {
              const slotStartAngle = index * sectorAngle;
              const startAngle = slotStartAngle + sectorGap / 2;
              const endAngle = startAngle + actualSectorAngle;
              const isHovered = hoveredDimension === dimension.id;
              const isActive = dimension.id === selectedDimension;

              const sectorPath = createRoundedRingSectorPath(
                centerX,
                centerY,
                sectorInnerRadius,
                outerRadius,
                startAngle,
                endAngle,
              );

              const getFillGradient = () => {
                if (isActive) return 'url(#activeGradient)';
                if (isHovered) return 'url(#sectorGradientHover)';
                return 'url(#sectorGradient)';
              };

              const SectionIcon = dimension.icon;

              return (
                <g key={dimension.id}>
                  <path
                    d={sectorPath}
                    fill={getFillGradient()}
                    className="cursor-pointer transition-all duration-300"
                    filter={isActive ? 'url(#glow)' : ''}
                    onMouseEnter={e => handleMouseEnter(dimension.id, e)}
                    onMouseLeave={handleMouseLeave}
                    onClick={() => onDimensionClick(dimension.id)}
                  />

                  {SectionIcon && (
                    <foreignObject
                      x={
                        centerX +
                        ((outerRadius + sectorInnerRadius) / 2) *
                          Math.cos(
                            ((startAngle + actualSectorAngle / 2) * Math.PI) /
                              180,
                          ) -
                        12
                      }
                      y={
                        centerY +
                        ((outerRadius + sectorInnerRadius) / 2) *
                          Math.sin(
                            ((startAngle + actualSectorAngle / 2) * Math.PI) /
                              180,
                          ) -
                        12
                      }
                      width="32"
                      height="32"
                      className="cursor-pointer"
                      onClick={() => onDimensionClick(dimension.id)}
                      onMouseEnter={e => handleMouseEnter(dimension.id, e)}
                      onMouseLeave={handleMouseLeave}
                    >
                      <div
                        className="text-white h-8 w-8"
                        style={{ transform: 'rotate(90deg)' }}
                      >
                        <SectionIcon />
                      </div>
                    </foreignObject>
                  )}
                </g>
              );
            })}

            <circle
              cx={centerX}
              cy={centerY}
              r={innerRadius}
              fill={colors.primary}
              filter="url(#innerCircleShadow)"
              mask="url(#innerGlowMask)"
              opacity="0.5"
            />
          </svg>
        </div>

        <div
          className="absolute flex items-center justify-center text-center"
          style={{
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
          }}
        >
          {centerContent}
        </div>
      </div>

      {hoveredDimension && hoveredDimensionData && tooltipPosition && (
        <div
          className="fixed z-50 px-3 py-2 text-sm rounded shadow-lg pointer-events-none max-w-xs"
          style={{
            left: `${tooltipPosition.x + 10}px`,
            top: `${tooltipPosition.y + 10}px`,
            backgroundColor: colors.background,
            color: colors.text,
            border: `1px solid ${colors.border}`,
          }}
        >
          {DIMENSION_CONFIG[hoveredDimensionData.name]?.shortTooltip}
        </div>
      )}
    </div>
  );
};

export default ArchetypeWheel;
