import AnchorIcon from '~/assets/icons/archetypes/anchor.svg';
import ChallengerIcon from '~/assets/icons/archetypes/challenger.svg';
import CollaboratorIcon from '~/assets/icons/archetypes/collaborator.svg';
import DeliberateIcon from '~/assets/icons/archetypes/dimensions/deliberate.svg';
import FacilitativeIcon from '~/assets/icons/archetypes/dimensions/facilitative.svg';
import FormalIcon from '~/assets/icons/archetypes/dimensions/formal.svg';
import IndependentIcon from '~/assets/icons/archetypes/dimensions/independent.svg';
import QualitySafetyIcon from '~/assets/icons/archetypes/dimensions/quality-safety.svg';
import ReactiveIcon from '~/assets/icons/archetypes/dimensions/reactive.svg';
import ResistantIcon from '~/assets/icons/archetypes/dimensions/resistant.svg';
import StructuredIcon from '~/assets/icons/archetypes/dimensions/structured.svg';
import ExplorerIcon from '~/assets/icons/archetypes/explorer.svg';
import OrchestratorIcon from '~/assets/icons/archetypes/orchestrator.svg';
import VisionaryIcon from '~/assets/icons/archetypes/visionary.svg';

export type DimensionConfig = {
  icon: React.ComponentType;
  description: string;
  leftLabel: string;
  rightLabel: string;
  shortTooltip: string;
  expandedDescription: string;
};

export const DIMENSION_CONFIG: Record<string, DimensionConfig> = {
  'Decision-Making Style': {
    icon: DeliberateIcon,
    description: 'How you approach choices',
    leftLabel: 'Deliberate',
    rightLabel: 'Decisive',
    shortTooltip: 'How you plan, prioritise, and make decisions at work.',
    expandedDescription:
      'Shows how you approach choices — whether you prefer a clear plan and logical process or adapt as things change. This domain reflects how you handle uncertainty and project pressure.',
  },
  'Collaboration Ethos': {
    icon: IndependentIcon,
    description: 'Team vs solo work preference',
    leftLabel: 'Independent',
    rightLabel: 'Integrative',
    shortTooltip: 'How you prefer to work with others.',
    expandedDescription:
      "Shows whether you're energised by teamwork and shared goals, or prefer autonomy and ownership in your tasks.",
  },
  'Stress & Ambiguity Response': {
    icon: ReactiveIcon,
    description: 'How you handle pressure',
    leftLabel: 'Reactive',
    rightLabel: 'Regulated',
    shortTooltip: 'How you tend to respond when pressure increases.',
    expandedDescription:
      'Shows whether you act quickly under stress or stay measured and calm. Both styles help teams perform under pressure in different ways.',
  },
  'Delivery Priorities': {
    icon: QualitySafetyIcon,
    description: 'Quality vs speed focus',
    leftLabel: 'Quality/Safety',
    rightLabel: 'Programme/Speed',
    shortTooltip: 'What you naturally prioritise when delivering work.',
    expandedDescription:
      'Shows whether you lean toward doing things to the highest standard or keeping projects on time and on budget.',
  },
  'Work Environment Fit': {
    icon: StructuredIcon,
    description: 'Structure vs flexibility',
    leftLabel: 'Structured',
    rightLabel: 'Flexible',
    shortTooltip: 'The type of setting where you work best.',
    expandedDescription:
      'Shows whether you prefer predictable routines and clear systems or enjoy variety, change, and evolving priorities.',
  },
  'Leadership Approach': {
    icon: FacilitativeIcon,
    description: 'How you lead others',
    leftLabel: 'Directive',
    rightLabel: 'Facilitative',
    shortTooltip: 'How you naturally guide and influence others.',
    expandedDescription:
      'Shows whether you prefer to lead by giving clear direction and standards or by enabling others through support and inclusion.',
  },
  'Communication Style': {
    icon: FormalIcon,
    description: 'How you communicate',
    leftLabel: 'Formal',
    rightLabel: 'Open',
    shortTooltip: 'How you share information and interact with others.',
    expandedDescription:
      'Shows whether you communicate in a considered, measured way or prefer open, expressive discussion. Both styles build trust when used effectively.',
  },
  'Adaptability & Learning': {
    icon: ResistantIcon,
    description: 'Response to change',
    leftLabel: 'Resistant',
    rightLabel: 'Receptive',
    shortTooltip: 'How you respond to change and new information.',
    expandedDescription:
      'Shows whether you prefer tried-and-tested methods or enjoy exploring new ways of working and learning from change.',
  },
};

export type ArchetypeConfig = {
  icon: React.ComponentType;
};

export const ARCHETYPE_CONFIG: Record<string, ArchetypeConfig> = {
  'The Anchor': {
    icon: AnchorIcon,
  },
  'The Visionary': {
    icon: VisionaryIcon,
  },
  'The Challenger': {
    icon: ChallengerIcon,
  },
  'The Orchestrator': {
    icon: OrchestratorIcon,
  },
  'The Explorer': {
    icon: ExplorerIcon,
  },
  'The Collaborator': {
    icon: CollaboratorIcon,
  },
};
