'use client';
import React from 'react';

import { DimensionWithScore } from '~/api/features/archetypes/types.ts';
import { Card } from '~/components/ui/card.tsx';
import { Skeleton } from '~/components/ui/skeleton';

const ArchetypeList = ({
  dimensions,
  isLoading,
}: {
  dimensions: DimensionWithScore[] | undefined;
  isLoading?: boolean;
}) => {
  if (isLoading || !dimensions) {
    return (
      <div className="space-y-6">
        {[...new Array(8)].map((_, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <Card key={i} className="p-6">
            <Skeleton className="h-4 w-32 mb-2" />
            <div className="flex items-center mb-3 gap-2">
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-7 w-12 rounded-md" />
            </div>
            <Skeleton className="w-full h-4 rounded-full" />
          </Card>
        ))}
      </div>
    );
  }

  if (!isLoading && dimensions?.length === 0) {
    return (
      <Card className="p-6 text-center">
        <p className="text-muted-foreground">No dimension scores available</p>
      </Card>
    );
  }

  // Сортировка по доминирующему проценту
  const sortedDimensions = [...dimensions].sort((a, b) => {
    const aPercent = a.score >= 0.5 ? a.score : 1 - a.score;
    const bPercent = b.score >= 0.5 ? b.score : 1 - b.score;
    return bPercent - aPercent;
  });

  return (
    <div className="space-y-6">
      {sortedDimensions.map(dimension => {
        const dominantLabel =
          dimension.score >= 0.5 ? dimension.right_label : dimension.left_label;
        const dominantPercent = Math.round(
          (dimension.score >= 0.5 ? dimension.score : 1 - dimension.score) *
            100,
        );

        return (
          <Card key={dimension.id} className="p-6">
            <p className="text-xs text-muted-foreground mb-2">
              {dimension.name}
            </p>

            <div className="flex items-center mb-3 gap-2">
              <h4 className="font-medium text-foreground text-2xl">
                {dominantLabel}
              </h4>
              <div className="px-2 py-1 rounded-md text-xs font-semibold bg-primary">
                {dominantPercent}%
              </div>
            </div>

            <div
              className="w-full rounded-full h-4"
              style={{
                background:
                  'linear-gradient(270deg, rgba(255, 255, 255, 0.2) 0%, rgba(95, 95, 95, 0.2) 100%)',
              }}
            >
              <div
                className="h-4 rounded-full transition-all duration-300"
                style={{
                  width: `${dominantPercent}%`,
                  background:
                    'linear-gradient(270deg, #4B9C88 0%, #181D19 100%)',
                  boxShadow: '0 0 8px #4B9C88',
                }}
              />
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default ArchetypeList;
