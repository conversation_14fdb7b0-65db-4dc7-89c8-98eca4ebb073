'use client';

import React, { useEffect, useState } from 'react';

import QuestionnaireComponent from '~/shared/components/QuestionnaireComponent';

import {
  useQuestionGroups,
  useQuestionnaireAnswers,
  useSaveQuestionnaireAnswers,
} from '~/api/features/questionaire/queries';
import { useToast } from '~/hooks/use-toast.ts';

type QuestionnaireAnswer = {
  question_id: number;
  value: number;
};

export default function EditWorkStyleStep({
  onComplete,
  onCancel,
  jobSeekerId,
}: {
  onComplete: () => void;
  onCancel: () => void;
  jobSeekerId: number;
}) {
  const { toast } = useToast();
  const { data, isLoading, error } = useQuestionGroups();
  const { data: existingAnswers } = useQuestionnaireAnswers(jobSeekerId);
  const saveAnswersMutation = useSaveQuestionnaireAnswers();

  const [currentGroupIndex, setCurrentGroupIndex] = useState(0);
  const [answers, setAnswers] = useState<Map<number, number>>(new Map());
  const [initialAnswers, setInitialAnswers] = useState<Map<number, number>>(
    new Map(),
  );
  const [hasChanges, setHasChanges] = useState(false);

  const groupedQuestions = React.useMemo(() => {
    if (!data) return [];

    try {
      const result = data.groups
        .map(group => {
          const groupQuestions = data.questions
            .filter(q => q.group_id === group.id)
            .map(q => ({
              id: q.id,
              name: q.name,
              group_id: q.group_id,
              created_at: q.created_at,
            }));

          return {
            id: group.id,
            name: group.name,
            created_at: group.created_at,
            questions: groupQuestions,
          };
        })
        .filter(group => group.questions.length > 0);

      return result;
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [data]);

  useEffect(() => {
    if (data && groupedQuestions.length > 0 && answers.size === 0) {
      const answersMap = new Map<number, number>();

      groupedQuestions.forEach(group => {
        group.questions.forEach(question => {
          const isTradeoffQuestion = data.answerTags.some(
            tag => tag.question_id === question.id && tag.answer_text,
          );
          answersMap.set(question.id, isTradeoffQuestion ? 1 : 3);
        });
      });

      if (existingAnswers?.answers) {
        existingAnswers.answers.forEach(answer => {
          answersMap.set(answer.question_id, answer.value);
        });
      }

      setAnswers(answersMap);
      setInitialAnswers(new Map(answersMap));
    }
  }, [data, groupedQuestions, answers.size, existingAnswers]);

  useEffect(() => {
    if (initialAnswers.size > 0 && answers.size > 0) {
      let changed = false;

      answers.forEach((value, key) => {
        if (initialAnswers.get(key) !== value) {
          changed = true;
        }
      });

      setHasChanges(changed);
    }
  }, [answers, initialAnswers]);

  const handleAnswerChange = (questionId: number, value: number) => {
    setAnswers(prevAnswers => {
      const newAnswers = new Map(prevAnswers);
      newAnswers.set(questionId, value);
      return newAnswers;
    });
  };

  const handleNext = async () => {
    if (currentGroupIndex < groupedQuestions.length - 1) {
      setCurrentGroupIndex(prevIndex => prevIndex + 1);
    } else {
      if (!hasChanges) {
        toast({
          title: 'No Changes',
          description: 'No changes have been made to your Behavioural Profile',
        });
        onComplete();
        return;
      }

      try {
        const serializedAnswers: QuestionnaireAnswer[] = Array.from(
          answers.entries(),
          // eslint-disable-next-line @typescript-eslint/naming-convention
        ).map(([question_id, value]) => ({
          question_id,
          value,
        }));

        await saveAnswersMutation.mutateAsync({
          jobSeekerId,
          answers: serializedAnswers,
        });

        toast({
          title: 'Success',
          description: 'Your Behavioural Profile preferences have been updated',
        });

        onComplete();
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to save your Behavioural Profile preferences',
          variant: 'destructive',
        });
        console.error(error);
      }
    }
  };

  const handleBack = () => {
    if (currentGroupIndex > 0) {
      setCurrentGroupIndex(prevIndex => prevIndex - 1);
    } else {
      onCancel();
    }
  };

  const getButtonText = () => {
    if (saveAnswersMutation.isPending) {
      return 'Saving...';
    }

    if (currentGroupIndex === groupedQuestions.length - 1) {
      return 'Finish';
    }

    return 'Next';
  };

  const getBackButtonText = () => {
    return currentGroupIndex === 0 ? 'Cancel' : 'Back';
  };

  return (
    <QuestionnaireComponent
      groups={groupedQuestions}
      answerTags={data?.answerTags || []}
      currentGroupIndex={currentGroupIndex}
      answers={answers}
      isLoading={isLoading}
      error={error}
      isSubmitting={saveAnswersMutation.isPending}
      onAnswerChange={handleAnswerChange}
      onNext={handleNext}
      onBack={handleBack}
      getNextButtonText={getButtonText}
      getBackButtonText={getBackButtonText}
      showBackTooltip={true}
    />
  );
}
