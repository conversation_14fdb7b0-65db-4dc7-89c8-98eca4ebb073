'use client';

import React from 'react';

import { DashBoardBreadcrumb } from '~/app/dashboard/(components)/DashBoardBreadcrumb.tsx';

import SideBar from '~/shared/components/sidebar/SideBar.tsx';

import {
  useGetChatsForUser,
  useUnreadChatsCount,
} from '~/api/features/chats/queries.ts';
import { useVacancyMatchesCounts } from '~/api/features/matches/job-seeker/queries.ts';
import ChatsIcon from '~/assets/icons/chat.svg';
import DashboardIcon from '~/assets/icons/dashboard.svg';
import MatchesIcon from '~/assets/icons/match.svg';
import PendingIcon from '~/assets/icons/pending.svg';
import SuggestionsIcon from '~/assets/icons/suggestions.svg';
import { useAuth } from '~/providers/AuthProvider.tsx';

export default function JobSeekerDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { session, jobSeekerProfile, isLoading } = useAuth();
  const { data: matchesCounts, isLoading: isLoadingCounts } =
    useVacancyMatchesCounts(jobSeekerProfile?.id);
  const { data: unreadChats, isLoading: isLoadingChats } = useUnreadChatsCount(
    jobSeekerProfile?.id,
    'job_seeker',
  );
  const { data: allChats, isLoading: isLoadingAllChats } = useGetChatsForUser(
    jobSeekerProfile?.id,
    'job_seeker',
  );

  const basePath = '/dashboard/job-seeker';

  const pendingTotal = isLoadingCounts
    ? '...'
    : (matchesCounts?.companyInterested ?? 0) +
      (matchesCounts?.companyRejected ?? 0) +
      (matchesCounts?.interested ?? 0) +
      (matchesCounts?.rejected ?? 0);

  const navItems = [
    {
      title: 'Dashboard',
      href: `${basePath}`,
      icon: <DashboardIcon className="h-4 w-4" />,
    },
    {
      title: 'Suggestions',
      href: `${basePath}/suggestions`,
      icon: <SuggestionsIcon className="h-4 w-4" />,
      badge: isLoadingCounts ? '...' : matchesCounts?.suggestions,
    },
    {
      title: 'Pending',
      href: '#',
      badge: pendingTotal,
      icon: <PendingIcon className="h-4 w-4" />,
      children: [
        {
          title: 'Interested',
          href: `${basePath}/pending/im-interested`,
          badge: isLoadingCounts ? '...' : matchesCounts?.interested,
        },
        {
          title: 'Rejected',
          href: `${basePath}/pending/i-rejected`,
          badge: isLoadingCounts ? '...' : matchesCounts?.rejected,
        },
        {
          title: 'Company Interested',
          href: `${basePath}/pending/company-interested`,
          badge: isLoadingCounts ? '...' : matchesCounts?.companyInterested,
        },
        {
          title: 'Company Rejected',
          href: `${basePath}/pending/company-rejected`,
          badge: isLoadingCounts ? '...' : matchesCounts?.companyRejected,
        },
      ],
    },
    {
      title: 'Matches',
      href: `${basePath}/matches`,
      icon: <MatchesIcon className="h-4 w-4" />,
      badge: isLoadingCounts ? '...' : matchesCounts?.matches,
    },
    {
      title: 'Chats',
      href: `${basePath}/chats`,
      icon: <ChatsIcon className="h-4 w-4" />,
      badge:
        isLoadingChats || isLoadingAllChats
          ? '...'
          : {
              count: allChats?.length ?? 0,
              hasUnread: (unreadChats?.unreadChats ?? 0) > 0,
            },
    },
  ];

  const getInitials = (fullName: string) => {
    if (!fullName) return 'JS';

    const nameParts = fullName.split(' ');
    if (nameParts.length === 1) return nameParts[0].charAt(0).toUpperCase();

    return (nameParts[0].charAt(0) + nameParts[1].charAt(0)).toUpperCase();
  };

  const profileProps =
    isLoading || !jobSeekerProfile
      ? {
          initials: 'JS',
          name: 'Job Seeker',
          email: session?.user?.email || '<EMAIL>',
          profileUrl: `${basePath}/profile`,
        }
      : {
          initials: getInitials(jobSeekerProfile?.full_name || ''),
          name: jobSeekerProfile?.full_name || 'Job Seeker',
          email: session?.user?.email || '',
          profileUrl: `${basePath}/profile`,
        };

  const supportUrl = `${basePath}/support`;
  const settingsUrl = `${basePath}/settings`;

  return (
    <div className="flex h-screen max-w-[100%] bg-background overflow-x-hidden">
      <SideBar
        role="job-seeker"
        navItems={navItems}
        profileProps={profileProps}
        supportUrl={supportUrl}
        settingsUrl={settingsUrl}
      />
      <div className="flex-1">
        <DashBoardBreadcrumb basePath={basePath} />
        {children}
      </div>
    </div>
  );
}
