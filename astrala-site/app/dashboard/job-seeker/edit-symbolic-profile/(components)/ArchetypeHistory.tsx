'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

import { ArchetypeDetails } from '~/app/dashboard/(components)/ArchetypeDetails.tsx';
import { ARCHETYPE_CONFIG } from '~/app/dashboard/job-seeker/(dashboard)/(components)/ArchetypeWheel/constants.ts';

import { ArchetypeHistoryItem } from '~/api/features/archetypes/types.ts';
import { Card, CardContent } from '~/components/ui/card.tsx';

type ArchetypeHistoryProps = {
  history: ArchetypeHistoryItem[] | [];
  isLoading: boolean;
};

export function ArchetypeHistory({
  history,
  isLoading,
}: ArchetypeHistoryProps) {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...new Array(3)].map((_, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-12 bg-muted rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          No previous assessments
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {history.map((item, index) => {
        const archetype = item.archetypes;
        const ArchetypeIcon = ARCHETYPE_CONFIG[archetype.name]?.icon;
        const isExpanded = expandedIndex === index;

        return (
          <Card key={item.archetype_id}>
            <CardContent className="p-0">
              <button
                className="w-full p-6 flex items-center justify-between hover:bg-muted/50 transition-colors"
                onClick={() => setExpandedIndex(isExpanded ? null : index)}
              >
                <div className="flex items-center gap-4">
                  {ArchetypeIcon && (
                    <div className="w-12 h-12 text-primary">
                      <ArchetypeIcon />
                    </div>
                  )}
                  <div className="text-left">
                    <h3 className="text-xl font-semibold">{archetype.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(item.created_at), 'PPP')}
                    </p>
                  </div>
                </div>
                <motion.div
                  animate={{ rotate: isExpanded ? 0 : -90 }}
                  transition={{ duration: 0.2 }}
                >
                  <ChevronDown className="h-5 w-5 text-muted-foreground" />
                </motion.div>
              </button>

              <AnimatePresence>
                {isExpanded && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="p-6 border-t">
                      <ArchetypeDetails archetype={archetype} />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
