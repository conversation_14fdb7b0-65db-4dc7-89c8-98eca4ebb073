'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList';
import { CompanyInterestedActionButtons } from '~/app/dashboard/job-seeker/(components)/ActionButtons';

import { useGetCompanyInterested } from '~/api/features/matches/job-seeker/queries.ts';
import { useAuth } from '~/providers/AuthProvider.tsx';

export default function CompanyInterestedPage() {
  const [page, setPage] = useState(1);
  const pageSize = 5;

  const { jobSeekerProfile, isLoading: isLoadingJobSeeker } = useAuth();

  const jobSeekerId = jobSeekerProfile?.id as number | undefined;
  const {
    data: companyInterestedVacancies,
    isLoading: isCompanyInterestedLoading,
    error,
  } = useGetCompanyInterested(jobSeekerId, page, pageSize);

  const isLoading = isLoadingJobSeeker || isCompanyInterestedLoading;

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={companyInterestedVacancies || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="jobSeeker"
        useAccordion={true}
        emptyMessage="When your profile resonates, your match will appear here. Something meaningful is forming behind the scenes."
        showActions={true}
        onPageChange={setPage}
      >
        {match => <CompanyInterestedActionButtons match={match} />}
      </MatchesList>
    </div>
  );
}
