'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList.tsx';

import { useGetCompanyRejected } from '~/api/features/matches/job-seeker/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function CompanyRejectedPage() {
  const [page, setPage] = useState(1);
  const pageSize = 5;

  const { jobSeekerProfile, isLoading: isLoadingJobSeeker } = useAuth();

  const jobSeekerId = jobSeekerProfile?.id as number | undefined;
  const {
    data: companyRejectedVacancies,
    isLoading: isCompanyRejectedLoading,
    error,
  } = useGetCompanyRejected(jobSeekerId, page, pageSize);

  const isLoading = isLoadingJobSeeker || isCompanyRejectedLoading;

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={companyRejectedVacancies || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="jobSeeker"
        useAccordion={true}
        emptyMessage="If a company decides not to match, it will appear here - every step brings you closer to a genuine alignment."
        showActions={false}
        onPageChange={setPage}
      />
    </div>
  );
}
