'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList';

import { useGetJobSeekerInterested } from '~/api/features/matches/job-seeker/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function ImInterestedPage() {
  const { jobSeekerProfile, isLoading: isLoadingJobSeeker } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const jobSeekerId = jobSeekerProfile?.id as number | undefined;
  const {
    data: interestedVacancies,
    isLoading: isInterestedLoading,
    error,
  } = useGetJobSeekerInterested(jobSeekerId, currentPage, pageSize);

  const isLoading = isLoadingJobSeeker || isInterestedLoading;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={interestedVacancies || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="jobSeeker"
        useAccordion={true}
        emptyMessage={`The suggestions will appear here after you click "I'm interested".`}
        showActions={false}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
