'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList';
import { IRejectedActionButtons } from '~/app/dashboard/job-seeker/(components)/ActionButtons.tsx';

import { useGetJobSeekerRejected } from '~/api/features/matches/job-seeker/queries';
import { useToast } from '~/hooks/use-toast';
import { useAuth } from '~/providers/AuthProvider';

export default function IRejectedPage() {
  const { toast } = useToast();
  const { jobSeekerProfile, isLoading: isLoadingJobSeeker } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const jobSeekerId = jobSeekerProfile?.id as number | undefined;
  const {
    data: rejectedVacancies,
    isLoading: isRejectedLoading,
    error,
    refetch,
  } = useGetJobSeekerRejected(jobSeekerId, currentPage, pageSize);

  const isLoading = isLoadingJobSeeker || isRejectedLoading;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSuccess = async () => {
    toast({
      title: 'Success!',
      description: "The vacancy has been moved to 'I'm Interested'",
    });
    await refetch();
  };

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={rejectedVacancies || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="jobSeeker"
        useAccordion={true}
        emptyMessage={`The suggestions will appear here after you click "I rejected".`}
        showActions={true}
        onPageChange={handlePageChange}
      >
        {match => (
          <IRejectedActionButtons match={match} onSuccess={handleSuccess} />
        )}
      </MatchesList>
    </div>
  );
}
