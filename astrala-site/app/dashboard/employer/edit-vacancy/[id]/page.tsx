'use client';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

import { VacancyFormSchema } from '~/app/onboarding/employer/(schemas)/vacancy.schema.ts';

import { VacancyForm } from '~/shared/components/vacancy/VacancyForm.tsx';

import { useVacancyDetails } from '~/api/entities/vacancy/queries.ts';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '~/components/ui/card.tsx';
import { Skeleton } from '~/components/ui/skeleton.tsx';
import { useAuth } from '~/providers/AuthProvider.tsx';

export default function EditVacancyPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { employerProfile } = useAuth();
  const { data: vacancy, isLoading, error } = useVacancyDetails(params.id);
  const [vacancyFormData, setVacancyFormData] =
    useState<VacancyFormSchema | null>(null);

  useEffect(() => {
    if (vacancy) {
      setVacancyFormData({
        title: vacancy.title,
        description: vacancy.description,
        country: vacancy.country,
        city: vacancy.city,
        experience_years_from: vacancy.experience_years_from,
        education_degree: vacancy.education_degree || '',
        education_discipline: vacancy.education_discipline || '',
        driving_license: vacancy.driving_license || false,
        languages: vacancy.languages || [
          {
            language: '',
            level: '',
          },
        ],
        skills: vacancy.skills || [],
        certifications: vacancy.certifications || [],
        salary_min: vacancy.salary_min,
        salary_max: vacancy.salary_max,
      });
    }
  }, [vacancy]);

  const handleCancel = () => {
    router.push('/dashboard/employer/vacancies');
  };

  const handleSuccess = () => {
    router.push('/dashboard/employer/vacancies');
  };

  if (!employerProfile) {
    return <div className="container mx-auto py-6">Loading profile...</div>;
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !vacancy) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">
              Failed to load vacancy details. Please try again.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Edit Vacancy: {vacancy.title}</CardTitle>
        </CardHeader>
        <CardContent>
          {vacancyFormData && (
            <VacancyForm
              initialData={vacancyFormData}
              isEdit={true}
              vacancyId={params.id}
              onCancel={handleCancel}
              onSuccess={handleSuccess}
              employerId={Number(employerProfile.id)}
              companyId={Number(employerProfile.company_id || 0)}
              skipWorkStyleTest={true}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
