import { SubscriptionPlan } from '~/constants/subscription.ts';

export type Plan = {
  id: SubscriptionPlan;
  name: string;
  priceMonthly: number;
  priceAnnual: number;
  features: {
    seats: number;
    jobs: number | string;
    matches: number | string;
  };
};

export const PLANS: Plan[] = [
  {
    id: 'starter',
    name: 'Starter',
    priceMonthly: 149,
    priceAnnual: 119,
    features: {
      seats: 1,
      jobs: 2,
      matches: 5,
    },
  },
  {
    id: 'pro',
    name: 'Pro',
    priceMonthly: 399,
    priceAnnual: 319,
    features: {
      seats: 3,
      jobs: 6,
      matches: 20,
    },
  },
  {
    id: 'team',
    name: 'Team',
    priceMonthly: 799,
    priceAnnual: 639,
    features: {
      seats: 8,
      jobs: 'Unlimited',
      matches: 'Unlimited',
    },
  },
];

export const PLAN_HIERARCHY: Record<NonNullable<SubscriptionPlan>, number> = {
  starter: 1,
  pro: 2,
  team: 3,
};
