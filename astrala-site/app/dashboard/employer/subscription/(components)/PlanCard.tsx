import { Check, Loader2 } from 'lucide-react';

import { Plan } from '../constants';

import { Button } from '~/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';
import { BillingPeriod, SubscriptionPlan } from '~/constants/subscription.ts';

type ButtonState = {
  disabled: boolean;
  text: string;
  variant: 'default' | 'outline';
  show: boolean;
};

type PlanCardProps = {
  plan: Plan;
  billingPeriod: BillingPeriod;
  currentPlan?: SubscriptionPlan;
  currentPeriod?: BillingPeriod;
  isPopular?: boolean;
  buttonState: ButtonState;
  isProcessing: boolean;
  onSelectPlan: (planId: SubscriptionPlan) => void;
};

export function PlanCard({
  plan,
  billingPeriod,
  currentPlan,
  currentPeriod,
  isPopular = false,
  buttonState,
  isProcessing,
  onSelectPlan,
}: PlanCardProps) {
  const price =
    billingPeriod === 'monthly' ? plan.priceMonthly : plan.priceAnnual;
  const isCurrent = currentPlan === plan.id && currentPeriod === billingPeriod;

  return (
    <Card
      className={`flex flex-col relative ${isPopular ? 'md:scale-105 shadow-lg' : ''} ${isCurrent ? 'border-primary' : ''}`}
    >
      <CardHeader>
        <CardTitle className="text-2xl">{plan.name}</CardTitle>
        {isPopular && !isCurrent && (
          <span className="text-primary py-1 rounded-lg text-sm font-semibold text-start">
            Most Popular
          </span>
        )}
        {isCurrent && (
          <span className="text-muted-foreground py-1 rounded-lg text-sm font-semibold">
            Current Plan
          </span>
        )}
        <CardDescription>
          <div className="mt-4">
            <span className="text-4xl font-bold text-foreground">£{price}</span>
            <span className="text-muted-foreground ml-2">/month</span>
          </div>
          {billingPeriod === 'annual' && (
            <p className="text-xs mt-1 text-muted-foreground">
              Billed annually at £{price * 12}
            </p>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col">
        <div className="space-y-3 mb-6 flex-1">
          <div className="flex items-center gap-2">
            <Check className="h-5 w-5 text-primary flex-shrink-0" />
            <span className="text-sm">
              <strong>{plan.features.seats}</strong>{' '}
              {plan.features.seats === 1 ? 'seat' : 'seats'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Check className="h-5 w-5 text-primary flex-shrink-0" />
            <span className="text-sm">
              <strong>{plan.features.jobs}</strong> live job
              {plan.features.jobs === 1 ? '' : 's'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Check className="h-5 w-5 text-primary flex-shrink-0" />
            <span className="text-sm">
              <strong>{plan.features.matches}</strong>{' '}
              {plan.features.matches === 'Unlimited'
                ? 'candidate matches'
                : 'matches/month'}
            </span>
          </div>
        </div>

        {buttonState.show && (
          <Button
            className="w-full"
            variant={buttonState.variant}
            onClick={() => onSelectPlan(plan.id)}
            disabled={buttonState.disabled || isProcessing}
          >
            <div className="flex items-center gap-2">
              {isProcessing && <Loader2 className="h-4 w-4 animate-spin" />}
              <span>{buttonState.text}</span>
            </div>
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
