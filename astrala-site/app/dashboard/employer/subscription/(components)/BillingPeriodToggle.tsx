import { Button } from '~/components/ui/button';
import { BillingPeriod } from '~/constants/subscription.ts';

type BillingPeriodToggleProps = {
  billingPeriod: BillingPeriod;
  onChangePeriod: (period: BillingPeriod) => void;
};

export function BillingPeriodToggle({
  billingPeriod,
  onChangePeriod,
}: BillingPeriodToggleProps) {
  return (
    <div className="flex justify-center mb-8">
      <div className="inline-flex items-center bg-muted rounded-lg p-1 gap-4">
        <Button
          onClick={() => onChangePeriod('monthly')}
          variant={billingPeriod === 'monthly' ? 'default' : 'ghost'}
        >
          Monthly
        </Button>
        <Button
          variant={billingPeriod === 'annual' ? 'default' : 'ghost'}
          onClick={() => onChangePeriod('annual')}
        >
          <div className="flex items-center gap-2">
            <span>Annually</span>
            <span
              className={
                billingPeriod === 'annual'
                  ? 'font-semibold'
                  : 'text-primary font-semibold'
              }
            >
              (Save 20%)
            </span>
          </div>
        </Button>
      </div>
    </div>
  );
}
