import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';
import { BillingPeriod, SubscriptionPlan } from '~/constants/subscription.ts';

type UsageData = {
  seats: number;
  jobs: number;
  matches: number;
};

type CurrentSubscriptionCardProps = {
  currentPlan: SubscriptionPlan;
  currentPeriod?: BillingPeriod;
  usage?: UsageData;
};

export function CurrentSubscriptionCard({
  currentPlan,
  currentPeriod,
  usage,
}: CurrentSubscriptionCardProps) {
  return (
    <Card className="mb-8">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Current Subscription</CardTitle>
            <CardDescription>
              You're on the{' '}
              <strong className="capitalize">{currentPlan}</strong> plan
              {currentPeriod && ` (${currentPeriod})`}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      {usage && (
        <CardContent>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Seats</p>
              <p className="text-2xl font-bold">{usage.seats}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Live Jobs</p>
              <p className="text-2xl font-bold">{usage.jobs}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Matches This Period</p>
              <p className="text-2xl font-bold">{usage.matches}</p>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
