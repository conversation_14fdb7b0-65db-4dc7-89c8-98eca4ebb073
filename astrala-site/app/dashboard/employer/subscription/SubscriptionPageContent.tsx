'use client';

import { useState } from 'react';
import { CreditCard, Loader2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import { BillingPeriodToggle } from './(components)/BillingPeriodToggle';
import { CurrentSubscriptionCard } from './(components)/CurrentSubscriptionCard';
import { PlanCard } from './(components)/PlanCard';
import { PLAN_HIERARCHY, PLANS } from './constants';
import {
  useCompanySubscription,
  useCompanyUsage,
  useCreateBillingPortalSession,
  useCreateSubscriptionCheckout,
} from '~/api/features/subscription/queries';
import { Button } from '~/components/ui/button';
import { BillingPeriod, SubscriptionPlan } from '~/constants/subscription.ts';
import { useToast } from '~/hooks/use-toast';
import { useAuth } from '~/providers/AuthProvider';

export default function SubscriptionPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const { employerProfile } = useAuth();
  const companyId = employerProfile?.company_id;

  const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>('monthly');

  const { data: subscription, isLoading: isLoadingSubscription } =
    useCompanySubscription(companyId!);
  const { data: usage, isLoading: isLoadingUsage } = useCompanyUsage(
    companyId!,
  );
  const createCheckout = useCreateSubscriptionCheckout();
  const createPortalSession = useCreateBillingPortalSession();

  const success = searchParams.get('success');
  const canceled = searchParams.get('canceled');

  if (success === 'true') {
    toast({
      title: 'Subscription activated!',
      description: 'Your subscription is now active.',
    });
    router.replace('/dashboard/employer/subscription');
  }

  if (canceled === 'true') {
    toast({
      title: 'Checkout canceled',
      description: 'You can subscribe anytime.',
      variant: 'destructive',
    });
    router.replace('/dashboard/employer/subscription');
  }

  const handleSelectPlan = (planId: SubscriptionPlan) => {
    if (!companyId) return;

    createCheckout.mutate({
      companyId,
      plan: planId,
      period: billingPeriod,
    });
  };

  const handleManageBilling = async () => {
    if (!companyId) return;

    const result = await createPortalSession.mutateAsync({ companyId });

    if ('url' in result) {
      window.open(result.url, '_blank');
    } else if ('error' in result) {
      toast({
        title: 'Error',
        description: result.error,
        variant: 'destructive',
      });
    }
  };

  if (isLoadingSubscription || isLoadingUsage) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const isActive = subscription?.subscription_status === 'active';
  const isTrial = subscription?.subscription_status === 'trial';
  const currentPlan = subscription?.subscription_plan;
  const currentPeriod = subscription?.billing_period;

  const getButtonState = (planId: SubscriptionPlan) => {
    if (!currentPlan) {
      return {
        disabled: false,
        text: `Get ${PLANS.find(p => p.id === planId)?.name}`,
        variant: 'default' as const,
        show: true,
      };
    }

    const currentLevel = PLAN_HIERARCHY[currentPlan];
    const targetLevel = PLAN_HIERARCHY[planId!];

    if (currentPlan === planId) {
      if (currentPeriod === billingPeriod) {
        return {
          disabled: true,
          text: 'Current Plan',
          variant: 'outline' as const,
          show: true,
        };
      }

      if (currentPeriod === 'monthly' && billingPeriod === 'annual') {
        return {
          disabled: false,
          text: 'Switch to Annual',
          variant: 'default' as const,
          show: true,
        };
      }

      return {
        disabled: true,
        text: '',
        variant: 'outline' as const,
        show: false,
      };
    }

    if (targetLevel < currentLevel) {
      return {
        disabled: true,
        text: '',
        variant: 'outline' as const,
        show: false,
      };
    }

    return {
      disabled: false,
      text: `Upgrade to ${PLANS.find(p => p.id === planId)?.name}`,
      variant: 'default' as const,
      show: true,
    };
  };

  return (
    <div className="w-full p-8">
      <div className="max-w-7xl mx-auto">
        {isActive && currentPlan && (
          <>
            <CurrentSubscriptionCard
              currentPlan={currentPlan}
              currentPeriod={currentPeriod}
              usage={usage}
            />

            <div className="mb-8 flex justify-center">
              <Button
                variant="outline"
                size="lg"
                onClick={handleManageBilling}
                disabled={createPortalSession.isPending}
              >
                {createPortalSession.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Opening...
                  </>
                ) : (
                  <>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Manage Billing & Cancel Subscription
                  </>
                )}
              </Button>
            </div>
          </>
        )}

        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">
            {isActive ? 'Upgrade Your Plan' : 'Choose Your Plan'}
          </h1>
          <p className="text-muted-foreground">
            {isTrial
              ? 'Select a plan to continue after your trial'
              : 'Select the plan that fits your hiring needs'}
          </p>
        </div>

        <BillingPeriodToggle
          billingPeriod={billingPeriod}
          onChangePeriod={setBillingPeriod}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-6">
          {PLANS.map(plan => {
            const buttonState = getButtonState(plan.id);
            const isPopular = plan.id === 'pro';

            return (
              <PlanCard
                key={plan.id}
                plan={plan}
                billingPeriod={billingPeriod}
                currentPlan={currentPlan}
                currentPeriod={currentPeriod}
                isPopular={isPopular}
                buttonState={buttonState}
                isProcessing={createCheckout.isPending}
                onSelectPlan={handleSelectPlan}
              />
            );
          })}
        </div>

        <p className="text-center text-sm text-muted-foreground">
          Prices exclude VAT
        </p>
      </div>
    </div>
  );
}
