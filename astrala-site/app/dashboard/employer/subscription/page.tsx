import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';

import SubscriptionPageContent from './SubscriptionPageContent';

export default function SubscriptionPage() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      }
    >
      <SubscriptionPageContent />
    </Suspense>
  );
}
