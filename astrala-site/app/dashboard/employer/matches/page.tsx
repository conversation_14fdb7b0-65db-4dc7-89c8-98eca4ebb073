'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList.tsx';
import { MatchFeedback } from '~/app/dashboard/(components)/MatchFeedback';
import { MatchesActionButtons } from '~/app/dashboard/employer/(components)/ActionButtons';

import { useGetMatches } from '~/api/features/matches/employer/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function MatchesPage() {
  const [page, setPage] = useState(1);
  const pageSize = 5;

  const { employerProfile, isLoading: isLoadingEmployer } = useAuth();

  const employerId = employerProfile?.id as number | undefined;
  const {
    data: matches,
    isLoading: isMatchesLoading,
    error,
  } = useGetMatches(employerId, page, pageSize);

  const isLoading = isLoadingEmployer || isMatchesLoading;

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={matches || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="employer"
        useAccordion={true}
        showFiles={true}
        emptyMessage="No matches yet. When both you and a job seeker are interested, they will appear here."
        showActions={true}
        onPageChange={setPage}
      >
        {match => (
          <div className="space-y-3">
            <MatchFeedback matchId={match.id} viewMode="employer" />

            <MatchesActionButtons match={match} />
          </div>
        )}
      </MatchesList>
    </div>
  );
}
