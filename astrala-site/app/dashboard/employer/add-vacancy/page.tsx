'use client';
import React from 'react';
import { useRouter } from 'next/navigation';

import { VacancyForm } from '~/shared/components/vacancy/VacancyForm';

import { useAuth } from '~/providers/AuthProvider';

export default function AddVacancyPage() {
  const router = useRouter();
  const { employerProfile } = useAuth();

  const handleCancel = () => {
    router.push('/dashboard/employer/vacancies');
  };

  const handleSuccess = () => {
    router.push('/dashboard/employer/vacancies');
  };

  if (!employerProfile) {
    return <div className="container mx-auto py-6">Loading profile...</div>;
  }

  return (
    <div className="container">
      <div className="mb-6">
        <VacancyForm
          onCancel={handleCancel}
          onSuccess={handleSuccess}
          employerId={Number(employerProfile.id)}
          companyId={Number(employerProfile.company_id || 0)}
          skipWorkStyleTest={false}
        />
      </div>
    </div>
  );
}
