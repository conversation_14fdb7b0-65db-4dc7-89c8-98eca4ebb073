'use client';

import React from 'react';

import { DashBoardBreadcrumb } from '~/app/dashboard/(components)/DashBoardBreadcrumb.tsx';

import SideBar from '~/shared/components/sidebar/SideBar.tsx';

import {
  useGetChatsForUser,
  useUnreadChatsCount,
} from '~/api/features/chats/queries.ts';
import { useEmployerVacancyMatchesCounts } from '~/api/features/matches/employer/queries.ts';
import ChatsIcon from '~/assets/icons/chat.svg';
import DashboardIcon from '~/assets/icons/dashboard.svg';
import MatchesIcon from '~/assets/icons/match.svg';
import PendingIcon from '~/assets/icons/pending.svg';
import SuggestionsIcon from '~/assets/icons/suggestions.svg';
import { useAuth } from '~/providers/AuthProvider.tsx';
import { getInitials } from '~/utils/getInitials';

export default function EmployerDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { session, employerProfile, isLoading } = useAuth();
  const { data: matchesCounts, isLoading: isLoadingCounts } =
    useEmployerVacancyMatchesCounts(employerProfile?.id);
  const { data: unreadChats, isLoading: isLoadingChats } = useUnreadChatsCount(
    employerProfile?.id,
    'employer',
  );
  const { data: allChats, isLoading: isLoadingAllChats } = useGetChatsForUser(
    employerProfile?.id,
    'employer',
  );

  const basePath = '/dashboard/employer';

  const pendingTotal = isLoadingCounts
    ? '...'
    : (matchesCounts?.companyInterested ?? 0) +
      (matchesCounts?.companyRejected ?? 0) +
      (matchesCounts?.jobSeekerInterested ?? 0) +
      (matchesCounts?.jobSeekerRejected ?? 0);

  const navItems = [
    {
      title: 'Dashboard',
      href: `${basePath}/vacancies`,
      icon: <DashboardIcon className="h-4 w-4" />,
    },
    {
      title: 'Suggestions',
      href: `${basePath}/suggestions`,
      icon: <SuggestionsIcon className="h-4 w-4" />,
      badge: isLoadingCounts ? '...' : matchesCounts?.suggestions,
    },
    {
      title: 'Pending',
      href: '#',
      icon: <PendingIcon className="h-4 w-4" />,
      badge: pendingTotal,
      children: [
        {
          title: 'Company Interested',
          href: `${basePath}/pending/company-interested`,
          badge: isLoadingCounts ? '...' : matchesCounts?.companyInterested,
        },
        {
          title: 'Company Rejected',
          href: `${basePath}/pending/company-rejected`,
          badge: isLoadingCounts ? '...' : matchesCounts?.companyRejected,
        },
        {
          title: 'Job Seeker Interested',
          href: `${basePath}/pending/job-seeker-interested`,
          badge: isLoadingCounts ? '...' : matchesCounts?.jobSeekerInterested,
        },
        {
          title: 'Job Seeker Rejected',
          href: `${basePath}/pending/job-seeker-rejected`,
          badge: isLoadingCounts ? '...' : matchesCounts?.jobSeekerRejected,
        },
      ],
    },
    {
      title: 'Matches',
      href: `${basePath}/matches`,
      icon: <MatchesIcon className="h-4 w-4" />,
      badge: isLoadingCounts ? '...' : matchesCounts?.matches,
    },
    {
      title: 'Chats',
      href: `${basePath}/chats`,
      icon: <ChatsIcon className="h-4 w-4" />,
      badge:
        isLoadingChats || isLoadingAllChats
          ? '...'
          : {
              count: allChats?.length ?? 0,
              hasUnread: (unreadChats?.unreadChats ?? 0) > 0,
            },
    },
  ];

  const profileProps =
    isLoading || !employerProfile
      ? {
          initials: 'EP',
          name: 'Employer',
          email: session?.user?.email || '<EMAIL>',
          profileUrl: `${basePath}/profile`,
        }
      : {
          initials: getInitials(employerProfile?.full_name || ''),
          name: employerProfile?.full_name || 'Employer',
          email: session?.user?.email || '',
          profileUrl: `${basePath}/profile`,
        };

  const supportUrl = `${basePath}/support`;
  const settingsUrl = `${basePath}/settings`;

  return (
    <div className="flex min-h-screen w-full bg-background">
      <SideBar
        role="employer"
        navItems={navItems}
        profileProps={profileProps}
        supportUrl={supportUrl}
        settingsUrl={settingsUrl}
      />
      <main className="flex-1">
        <DashBoardBreadcrumb basePath={basePath} showVacancySelector={true} />
        <div>{children}</div>
      </main>
    </div>
  );
}
