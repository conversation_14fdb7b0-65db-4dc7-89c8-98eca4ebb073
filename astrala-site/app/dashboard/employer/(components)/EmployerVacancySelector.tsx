'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { useCompanyVacancies } from '~/api/features/company/queries';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Skeleton } from '~/components/ui/skeleton';
import { useEmployerVacancyStore } from '~/store/employer-vacancies-store.ts';

export function EmployerVacancySelector() {
  const { selectedVacancyId, setSelectedVacancyId, vacancies, setVacancies } =
    useEmployerVacancyStore();

  const { data: fetchedVacancies, isLoading } = useCompanyVacancies();
  const router = useRouter();

  useEffect(() => {
    if (fetchedVacancies && fetchedVacancies.length > 0) {
      const openVacancies = fetchedVacancies.filter(
        vacancy => vacancy.status !== 'CLOSED',
      );

      const sortedVacancies = [...openVacancies].sort((a, b) =>
        a.title.localeCompare(b.title),
      );

      setVacancies(sortedVacancies);

      if (!selectedVacancyId && sortedVacancies.length > 0) {
        setSelectedVacancyId(Number(sortedVacancies[0].id));
      } else if (sortedVacancies.length === 0) {
        setSelectedVacancyId(null);
      } else if (selectedVacancyId) {
        const isSelectedVacancyOpen = sortedVacancies.some(
          vacancy => Number(vacancy.id) === selectedVacancyId,
        );

        if (!isSelectedVacancyOpen && sortedVacancies.length > 0) {
          setSelectedVacancyId(Number(sortedVacancies[0].id));
        }
      }
    }
  }, [fetchedVacancies, selectedVacancyId, setSelectedVacancyId, setVacancies]);

  if (isLoading) {
    return <Skeleton className="h-10 w-[250px]" />;
  }

  if (!vacancies || vacancies.length === 0) {
    return null;
  }

  const handleVacancyChange = (vacancyId: string) => {
    setSelectedVacancyId(Number(vacancyId));
    router.refresh();
  };

  return (
    <div className="flex items-center space-x-4">
      <span className="text-sm font-medium">Select Vacancy:</span>
      <Select
        value={selectedVacancyId?.toString() || ''}
        onValueChange={handleVacancyChange}
      >
        <SelectTrigger className="w-[250px]">
          <SelectValue placeholder="Select a vacancy" />
        </SelectTrigger>
        <SelectContent>
          {vacancies.map(vacancy => (
            <SelectItem key={vacancy.id} value={vacancy.id.toString()}>
              {vacancy.title}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
