'use client';

import { Download } from 'lucide-react';

import { Database } from '~/shared/db/generated/types';

import { Button } from '~/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';

type JobSeekerFilesProps = {
  resumePath?: string;
  certifications?: Database['public']['Tables']['job_seeker_certifications']['Row'][];
  fullName: string;
};

export function JobSeekerFiles({
  resumePath,
  certifications = [],
  fullName,
}: JobSeekerFilesProps) {
  const certificationsWithFiles = certifications.filter(cert => cert.file_path);

  const hasFiles = resumePath || certificationsWithFiles.length > 0;

  if (!hasFiles) {
    return null;
  }

  const downloadFile = (url: string, fileName: string) => {
    if (!url) return;

    fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(
            `Failed to fetch file: ${response.status} ${response.statusText}`,
          );
        }
        return response.blob();
      })
      .then(blob => {
        const blobUrl = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = blobUrl;
        a.download = fileName;

        document.body.append(a);
        a.click();

        setTimeout(() => {
          window.URL.revokeObjectURL(blobUrl);
          a.remove();
        }, 100);
      })
      .catch(error => {
        console.error('Error downloading file:', error);
        // eslint-disable-next-line no-alert
        alert('Failed to download file. Please try again.');
      });
  };

  const extractFilenameFromPath = (path: string) => {
    if (!path) return '';

    const pathWithoutQuery = path.split('?')[0];
    const parts = pathWithoutQuery.split('/');
    return parts[parts.length - 1];
  };

  const getFileExtension = (filename: string) => {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.slice(Math.max(0, lastDotIndex)) : '';
  };

  const formatFileName = (baseFileName: string, fileType: string) => {
    const originalFileName = extractFilenameFromPath(baseFileName);
    const extension = getFileExtension(originalFileName);

    return extension
      ? `${fullName} - ${fileType}${extension}`
      : `${fullName} - ${fileType}.pdf`;
  };

  const files = [];

  if (resumePath) {
    files.push({
      label: 'CV',
      filePath: resumePath,
      fileName: formatFileName(resumePath, 'CV'),
      icon: <Download className="h-4 w-4 mr-2" />,
    });
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Candidate Documents</CardTitle>
        <CardDescription>
          Access the candidate's files and certificates
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {files.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Documents</h3>
              <div className="grid grid-cols-1 gap-2">
                {files.map(file => (
                  <Button
                    key={file.filePath}
                    variant="outline"
                    className="justify-start"
                    onClick={() =>
                      downloadFile(file.filePath || '', file.fileName)
                    }
                    disabled={!file.filePath}
                  >
                    {file.icon}
                    {file.label}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {certificationsWithFiles.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Certifications</h3>
              <div className="grid grid-cols-1 gap-2">
                {certificationsWithFiles.map(cert => (
                  <Button
                    key={cert.id}
                    variant="outline"
                    className="justify-start"
                    onClick={() =>
                      downloadFile(
                        cert.file_path!,
                        formatFileName(
                          cert.file_path!,
                          `Certification ${cert.certification_id}`,
                        ),
                      )
                    }
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Certification {cert.certification_id}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
