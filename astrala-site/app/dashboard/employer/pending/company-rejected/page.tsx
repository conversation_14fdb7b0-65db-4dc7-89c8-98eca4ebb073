'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList';
import { IRejectedActionButtons } from '~/app/dashboard/employer/(components)/ActionButtons.tsx';

import { useGetCompanyRejected } from '~/api/features/matches/employer/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function CompanyRejectedPage() {
  const [page, setPage] = useState(1);
  const pageSize = 5;

  const { employerProfile, isLoading: isLoadingEmployer } = useAuth();

  const employerId = employerProfile?.id as number | undefined;
  const {
    data: companyRejected,
    isLoading: isCompanyRejectedLoading,
    error,
  } = useGetCompanyRejected(employerId, page, pageSize);

  const isLoading = isLoadingEmployer || isCompanyRejectedLoading;

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={companyRejected || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="employer"
        useAccordion={true}
        emptyMessage="The data will appear here after you reject candidates."
        showActions={true}
        onPageChange={setPage}
      >
        {match => <IRejectedActionButtons match={match} />}
      </MatchesList>
    </div>
  );
}
