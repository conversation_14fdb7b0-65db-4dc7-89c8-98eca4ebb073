'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList';
import { IRejectedActionButtons } from '~/app/dashboard/employer/(components)/ActionButtons';

import { useGetJobSeekerRejected } from '~/api/features/matches/employer/queries';
import { useToast } from '~/hooks/use-toast';
import { useAuth } from '~/providers/AuthProvider';

export default function JobSeekerRejectedPage() {
  const { toast } = useToast();
  const { employerProfile, isLoading: isLoadingEmployer } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const employerId = employerProfile?.id as number | undefined;
  const {
    data: jobSeekerRejected,
    isLoading: isJobSeekerRejectedLoading,
    error,
    refetch,
  } = useGetJobSeekerRejected(employerId, currentPage, pageSize);

  const isLoading = isLoadingEmployer || isJobSeekerRejectedLoading;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSuccess = async () => {
    toast({
      title: 'Success!',
      description: "The candidate has been moved to 'Company Interested'",
    });
    await refetch();
  };

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={jobSeekerRejected || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="employer"
        useAccordion={true}
        emptyMessage="The candidates will appear here after they reject your vacancies."
        showActions={true}
        onPageChange={handlePageChange}
      >
        {match => (
          <IRejectedActionButtons match={match} onSuccess={handleSuccess} />
        )}
      </MatchesList>
    </div>
  );
}
