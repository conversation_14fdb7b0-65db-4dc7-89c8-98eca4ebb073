'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList';

import { useGetCompanyInterested } from '~/api/features/matches/employer/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function CompanyInterestedPage() {
  const [page, setPage] = useState(1);
  const pageSize = 5;

  const { employerProfile, isLoading: isLoadingEmployer } = useAuth();

  const employerId = employerProfile?.id as number | undefined;
  const {
    data: companyInterested,
    isLoading: isCompanyInterestedLoading,
    error,
  } = useGetCompanyInterested(employerId, page, pageSize);

  const isLoading = isLoadingEmployer || isCompanyInterestedLoading;

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={companyInterested || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="employer"
        useAccordion={true}
        emptyMessage="The candidates will appear here after you express interest in their profile."
        showActions={true}
        onPageChange={setPage}
      />
    </div>
  );
}
