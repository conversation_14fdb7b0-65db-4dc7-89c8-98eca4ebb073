'use client';

import { useState } from 'react';

import { MatchesList } from '~/app/dashboard/(components)/MatchesList';
import { SuggestionsActionButtons } from '~/app/dashboard/employer/(components)/ActionButtons';

import { useGetJobSeekerInterested } from '~/api/features/matches/employer/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function JobSeekerInterestedPage() {
  const { employerProfile, isLoading: isLoadingEmployer } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const employerId = employerProfile?.id as number | undefined;
  const {
    data: jobSeekerInterested,
    isLoading: isJobSeekerInterestedLoading,
    error,
  } = useGetJobSeekerInterested(employerId, currentPage, pageSize);

  const isLoading = isLoadingEmployer || isJobSeekerInterestedLoading;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="container p-4 w-full">
      <MatchesList
        matches={jobSeekerInterested || []}
        isLoading={isLoading}
        error={error as Error | null}
        viewMode="employer"
        useAccordion={true}
        emptyMessage="The candidates will appear here after they express interest in your vacancies."
        showActions={true}
        onPageChange={handlePageChange}
      >
        {match => <SuggestionsActionButtons match={match} />}
      </MatchesList>
    </div>
  );
}
