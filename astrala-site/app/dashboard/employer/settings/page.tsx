'use client';

import { EmployeesCard } from '~/app/dashboard/employer/settings/(components)/EmployeesCard.tsx';
import { InvitationsCard } from '~/app/dashboard/employer/settings/(components)/InvitationsCard.tsx';
import { InviteEmployeeDialog } from '~/app/dashboard/employer/settings/(components)/InviteEmployeeDialog.tsx';

export default function SettingsPage() {
  return (
    <div className="container mx-auto py-6 p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Company Info</h1>
        <InviteEmployeeDialog />
      </div>

      <div className="space-y-6">
        <EmployeesCard />
        <InvitationsCard />
      </div>
    </div>
  );
}
