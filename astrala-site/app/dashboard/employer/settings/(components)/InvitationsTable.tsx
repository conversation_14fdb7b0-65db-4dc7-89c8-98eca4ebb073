'use client';

import { useState } from 'react';
import { format } from 'date-fns';

import { Pagination } from '~/shared/components/Pagination.tsx';

import { InvitationStatusBadge } from './InvitationStatusBadge';
import {
  useCompanyInvitations,
  usePagination,
} from '~/api/features/company/queries';
import { Skeleton } from '~/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';

export function InvitationsTable() {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { data, isLoading: isInvitationsLoading } = useCompanyInvitations(
    page,
    limit,
  );
  const invitations = data?.data || [];

  const pagination = usePagination({
    page: data?.page || 1,
    limit: data?.limit || 10,
    total: data?.total || 0,
    totalPages: data?.totalPages || 0,
    data: invitations,
  });

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    pagination.goToPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    pagination.changeLimit(newLimit);
  };

  if (isInvitationsLoading) {
    return (
      <div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Email</TableHead>
              <TableHead>Invited By</TableHead>
              <TableHead>Invited On</TableHead>
              <TableHead>Expires</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...new Array(3)].map((_, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-6 w-48" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-32" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-28" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-28" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (!invitations || invitations.length === 0) {
    return (
      <div className="text-center py-6 text-muted-foreground">
        No invitations have been sent yet.
      </div>
    );
  }

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Email</TableHead>
            <TableHead>Invited By</TableHead>
            <TableHead>Invited On</TableHead>
            <TableHead>Expires</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {invitations.map(invitation => (
            <TableRow key={invitation.id}>
              <TableCell className="font-medium">{invitation.email}</TableCell>
              <TableCell>{invitation.created_by_employer_name}</TableCell>
              <TableCell>
                {format(new Date(invitation.created_at), 'MMM d, yyyy')}
              </TableCell>
              <TableCell>
                {format(new Date(invitation.expires_at), 'MMM d, yyyy')}
              </TableCell>
              <TableCell>
                <div className="w-fit">
                  <InvitationStatusBadge invitation={invitation} />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {data && data.total > 0 && (
        <Pagination
          currentPage={pagination.page}
          pageNumbers={pagination.pageNumbers}
          hasPreviousPage={pagination.hasPreviousPage}
          hasNextPage={pagination.hasNextPage}
          onPageChange={handlePageChange}
          itemsPerPage={pagination.limit}
          onItemsPerPageChange={handleLimitChange}
        />
      )}
    </div>
  );
}
