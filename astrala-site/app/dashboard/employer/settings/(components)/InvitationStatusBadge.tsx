'use client';

import { isAfter } from 'date-fns';
import { CheckCircle, Mail, XCircle } from 'lucide-react';

import { InvitationTokenWithStatus } from '~/api/features/company/actions';
import { Badge } from '~/components/ui/badge';

type InvitationStatusBadgeProps = {
  invitation: InvitationTokenWithStatus;
};

export function InvitationStatusBadge({
  invitation,
}: InvitationStatusBadgeProps) {
  if (invitation.is_used) {
    return (
      <Badge className="bg-primary flex items-center gap-1 max-w-full">
        <CheckCircle size={12} />
        <span>Accepted</span>
      </Badge>
    );
  }

  const expiryDate = new Date(invitation.expires_at);
  if (isAfter(new Date(), expiryDate)) {
    return (
      <Badge className="bg-destructive flex items-center gap-1">
        <XCircle size={12} />
        <span>Expired</span>
      </Badge>
    );
  }

  return (
    <Badge className="bg-accent flex items-center gap-1">
      <Mail size={12} />
      <span>Pending</span>
    </Badge>
  );
}
