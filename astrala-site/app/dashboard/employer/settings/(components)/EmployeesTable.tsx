'use client';

import { useState } from 'react';
import { format } from 'date-fns';

import { Pagination } from '~/shared/components/Pagination';

import {
  useCompanyEmployees,
  usePagination,
} from '~/api/features/company/queries';
import { Skeleton } from '~/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';

export function EmployeesTable() {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { data, isLoading: isEmployeesLoading } = useCompanyEmployees(
    page,
    limit,
  );
  const employees = data?.data || [];

  const pagination = usePagination({
    page: data?.page || 1,
    limit: data?.limit || 10,
    total: data?.total || 0,
    totalPages: data?.totalPages || 0,
    data: employees,
  });

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    pagination.goToPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    pagination.changeLimit(newLimit);
  };

  if (isEmployeesLoading) {
    return (
      <div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Full Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Date Added</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...new Array(5)].map((_, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-6 w-32" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-48" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-28" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (!employees || employees.length === 0) {
    return (
      <div className="text-center py-6 text-placeholder">
        No employees found for this company.
      </div>
    );
  }

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Full Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Date Added</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {employees.map(employee => (
            <TableRow key={employee.id}>
              <TableCell className="font-medium">
                {employee.full_name}
              </TableCell>
              <TableCell>{employee.email || 'Email not available'}</TableCell>
              <TableCell>{employee.role}</TableCell>
              <TableCell>
                {format(new Date(employee.created_at), 'MMM d, yyyy')}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {data && data.total > 0 && (
        <Pagination
          currentPage={pagination.page}
          pageNumbers={pagination.pageNumbers}
          hasPreviousPage={pagination.hasPreviousPage}
          hasNextPage={pagination.hasNextPage}
          onPageChange={handlePageChange}
          itemsPerPage={pagination.limit}
          onItemsPerPageChange={handleLimitChange}
        />
      )}
    </div>
  );
}
