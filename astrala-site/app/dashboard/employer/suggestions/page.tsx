'use client';

import { useCallback, useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

import { MatchData } from '~/app/dashboard/(components)/MatchCard';
import { SuggestionsBase } from '~/app/dashboard/(components)/SuggestionsBase';
import { SuggestionsActionButtons } from '~/app/dashboard/employer/(components)/ActionButtons';

import {
  employerVacancyMatchesCountsKeys,
  useGetSuggestions,
  useMarkEmployerInterested,
  useMarkEmployerRejected,
} from '~/api/features/matches/employer/queries';
import { useAuth } from '~/providers/AuthProvider';
import { useEmployerVacancyStore } from '~/store/employer-vacancies-store';

export default function EmployerSuggestionsPage() {
  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const previousSuggestionRef = useRef<MatchData | null>(null);
  const { employerProfile, isLoading: isLoadingEmployer } = useAuth();
  const employerId = employerProfile?.id as number | undefined;

  const { vacancies, selectedVacancyId } = useEmployerVacancyStore();

  const hasNoVacancies = vacancies.length === 0;
  const hasNoSelectedVacancy = !selectedVacancyId;
  const hasActiveVacancy = !hasNoVacancies && !hasNoSelectedVacancy;

  const {
    data: matchesResponse,
    isLoading: isSuggestionLoading,
    error,
    refetch: refetchMatches,
  } = useGetSuggestions(employerId, 1, 1);

  const currentSuggestion = (matchesResponse?.data?.[0] ||
    null) as MatchData | null;

  const { markAsInterested, isLoading: isMarkingInterested } =
    useMarkEmployerInterested();
  const { markAsRejected, isLoading: isMarkingRejected } =
    useMarkEmployerRejected();

  const isGeneratingSuggestions =
    hasActiveVacancy &&
    !isLoadingEmployer &&
    !isSuggestionLoading &&
    !currentSuggestion &&
    !error;

  const invalidateCounts = useCallback(() => {
    if (employerId) {
      queryClient.invalidateQueries({
        queryKey: employerVacancyMatchesCountsKeys.all,
      });
    }
  }, [queryClient, employerId]);

  const handleRefetchWithCountsInvalidation = useCallback(async () => {
    if (!hasActiveVacancy) return;

    invalidateCounts();
    return refetchMatches();
  }, [invalidateCounts, refetchMatches, hasActiveVacancy]);

  useEffect(() => {
    if (!previousSuggestionRef.current && currentSuggestion) {
      invalidateCounts();
    }
    previousSuggestionRef.current = currentSuggestion;
  }, [currentSuggestion, invalidateCounts]);

  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (
      hasActiveVacancy &&
      !currentSuggestion &&
      !isLoadingEmployer &&
      !isSuggestionLoading &&
      !error
    ) {
      intervalRef.current = setInterval(() => {
        handleRefetchWithCountsInvalidation();
      }, 5000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [
    currentSuggestion,
    isLoadingEmployer,
    isSuggestionLoading,
    error,
    handleRefetchWithCountsInvalidation,
    hasActiveVacancy,
  ]);

  useEffect(() => {
    if (employerId) {
      invalidateCounts();
    }
  }, [employerId, invalidateCounts]);

  let emptyStateTitle = 'No more candidates available';
  let emptyStateDescription =
    "We'll notify you when new candidate matches become available";

  if (hasNoSelectedVacancy || hasNoVacancies) {
    emptyStateTitle = 'You have no active vacancies';
    emptyStateDescription =
      'Open or create new vacancy to start reviewing candidates';
  }

  return (
    <SuggestionsBase
      viewMode="employer"
      isProfileLoading={isLoadingEmployer}
      currentSuggestion={currentSuggestion}
      isSuggestionLoading={isSuggestionLoading}
      error={error as Error | null}
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      refetchMatches={handleRefetchWithCountsInvalidation}
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      markAsInterested={markAsInterested}
      isMarkingInterested={isMarkingInterested}
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      markAsRejected={markAsRejected}
      isMarkingRejected={isMarkingRejected}
      ActionButtons={SuggestionsActionButtons}
      emptyStateTitle={emptyStateTitle}
      emptyStateDescription={emptyStateDescription}
      interestedToastMessage="You've expressed interest in this candidate."
      rejectedToastMessage="You've rejected this candidate."
      isGeneratingSuggestions={isGeneratingSuggestions}
      generatingSuggestionsMessage="We're generating candidate suggestions for you..."
      showRefreshButton={hasActiveVacancy && !currentSuggestion}
      refreshButtonLabel="Check for new candidates"
    />
  );
}
