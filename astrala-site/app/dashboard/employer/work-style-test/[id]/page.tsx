'use client';
import React from 'react';
import { useRouter } from 'next/navigation';

import { WorkStyleTestStep } from '~/app/onboarding/employer/(components)/(steps)/WorkStyleTestStep';

import { useVacancyDetails } from '~/api/entities/vacancy/queries';
import { useAuth } from '~/providers/AuthProvider';

export default function WorkStyleTestPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { employerProfile } = useAuth();
  const { data: vacancy, isLoading } = useVacancyDetails(params.id);

  const handleCancel = () => {
    router.push('/dashboard/employer/vacancies');
  };

  const handleFinish = () => {
    router.push('/dashboard/employer/vacancies');
  };

  if (isLoading || !vacancy || !employerProfile) {
    return <div className="container mx-auto py-6">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <h2 className=" text-2xl font-bold">
        Behavioural Profile: {vacancy.title}
      </h2>
      <WorkStyleTestStep
        vacancyData={null}
        employerProfile={employerProfile}
        onCancel={handleCancel}
        existingVacancyId={Number(params.id)}
        onFinish={handleFinish}
      />
    </div>
  );
}
