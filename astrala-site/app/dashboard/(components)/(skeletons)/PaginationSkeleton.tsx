'use client';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import { Button } from '~/components/ui/button';
import { Skeleton } from '~/components/ui/skeleton';

export function PaginationSkeleton() {
  return (
    <div className="flex items-center justify-center space-x-2">
      <Button variant="ghost" size="sm" disabled>
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {Array.from({ length: 5 }).map((_, index) => (
        // eslint-disable-next-line react/jsx-key
        <Skeleton
          className={`h-8 w-8 rounded-md ${index === 0 ? 'bg-primary/20' : ''}`}
        />
      ))}

      <Button variant="ghost" size="sm" disabled>
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
