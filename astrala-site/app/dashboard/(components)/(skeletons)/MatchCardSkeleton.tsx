'use client';

import { InfoIcon, MapPin } from 'lucide-react';

import UserIcon from '~/assets/icons/user.svg';
import { Card, CardContent } from '~/components/ui/card.tsx';
import { Separator } from '~/components/ui/separator.tsx';
import { Skeleton } from '~/components/ui/skeleton';
import { cn } from '~/lib/utils';

type MatchCardSkeletonProps = {
  className?: string;
};

export function MatchCardSkeleton({ className }: MatchCardSkeletonProps) {
  return (
    <Card className={cn('mb-6', className)}>
      <CardContent className="p-6">
        <div className="flex items-center p-6">
          <div className="flex-shrink-0 p-4 bg-neutral-200 rounded-full flex items-center justify-center mr-4">
            <UserIcon className="text-neutral-400" />
          </div>
          <div className="flex flex-col justify-around flex-grow">
            <Skeleton className="h-8 w-64 mb-2" />
            <div className="mt-1 flex flex-wrap items-center">
              <div className="flex items-center mr-4">
                <InfoIcon className="w-5 h-5 mr-1 text-neutral-300" />
                <Skeleton className="h-5 w-32" />
              </div>
              <div className="flex items-center">
                <MapPin className="w-5 h-5 mr-1 text-neutral-300" />
                <Skeleton className="h-5 w-36" />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <div className="flex justify-between items-center mb-2">
            <Skeleton className="h-7 w-48" />
          </div>
          <Skeleton className="h-4 w-full" />
          <div className="flex justify-between text-xs mt-2 items-center">
            <span className="text-neutral-300">0</span>
            <span className="text-neutral-300">20</span>
            <span className="text-neutral-300">40</span>
            <span className="text-neutral-300">60</span>
            <span className="text-neutral-300">80</span>
            <span className="text-neutral-300">100</span>
          </div>
        </div>

        <div className="mt-6">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full mt-2" />
          <Skeleton className="h-4 w-3/4 mt-2" />
        </div>

        <Separator className="my-6" />

        <div>
          <Skeleton className="h-6 w-48 mb-4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full mt-2" />
          <Skeleton className="h-4 w-3/4 mt-2" />
        </div>

        <div className="mt-6">
          <div className="grid grid-cols-3 gap-4">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-full" />

            <Skeleton className="h-8 w-full py-2" />
            <Skeleton className="h-8 w-full py-2" />
            <Skeleton className="h-8 w-full py-2" />

            <Skeleton className="h-8 w-full py-2" />
            <Skeleton className="h-8 w-full py-2" />
            <Skeleton className="h-8 w-full py-2" />

            <Skeleton className="h-8 w-full py-2" />
            <Skeleton className="h-8 w-full py-2" />
            <Skeleton className="h-8 w-full py-2" />

            <Skeleton className="h-8 w-full py-2" />
            <Skeleton className="h-8 w-full py-2" />
            <Skeleton className="h-8 w-full py-2" />
          </div>
        </div>

        <div className="mt-6">
          <div className="space-y-4">
            <div>
              <Skeleton className="h-6 w-40 mb-2" />
              <div className="flex flex-wrap gap-2">
                <Skeleton className="h-8 w-20 rounded-full" />
                <Skeleton className="h-8 w-24 rounded-full" />
                <Skeleton className="h-8 w-16 rounded-full" />
                <Skeleton className="h-8 w-28 rounded-full" />
              </div>
            </div>

            <div>
              <Skeleton className="h-6 w-40 mb-2" />
              <div className="flex flex-wrap gap-2">
                <Skeleton className="h-8 w-28 rounded-full" />
                <Skeleton className="h-8 w-20 rounded-full" />
                <Skeleton className="h-8 w-24 rounded-full" />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
