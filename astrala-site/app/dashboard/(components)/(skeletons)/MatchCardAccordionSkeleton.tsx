'use client';

import { ChevronDown } from 'lucide-react';

import { Card } from '~/components/ui/card';
import { Skeleton } from '~/components/ui/skeleton';

export function MatchCardAccordionSkeleton() {
  return (
    <Card className="overflow-hidden py-2">
      <div className="flex items-center p-6 cursor-not-allowed">
        <Skeleton className="flex-shrink-0 h-16 w-16 rounded-full mr-4" />
        <div className="flex flex-col justify-around flex-grow">
          <Skeleton className="h-8 w-3/4 mb-2" />
          <div className="mt-1 flex flex-wrap items-center">
            <Skeleton className="h-5 w-40 mr-4" />
            <Skeleton className="h-5 w-32" />
          </div>
        </div>
        <div className="ml-auto flex items-center">
          <Skeleton className="h-6 w-24 rounded-full" />
          <ChevronDown className="ml-4 h-5 w-5 text-neutral-300 transition-transform duration-200 -rotate-90" />
        </div>
      </div>
    </Card>
  );
}
