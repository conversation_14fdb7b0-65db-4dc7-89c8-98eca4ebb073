'use client';

import {
  ComponentType,
  ReactElement,
  useEffect,
  useRef,
  useState,
} from 'react';
import { motion } from 'framer-motion';
import { Loader2, RefreshCw } from 'lucide-react';

import { MatchCardSkeleton } from '~/app/dashboard/(components)/(skeletons)/MatchCardSkeleton.tsx';
import { MatchCard, MatchData } from '~/app/dashboard/(components)/MatchCard';
import Placeholder from '~/app/dashboard/(components)/Placeholder.tsx';

import { Alert, AlertDescription } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import { useToast } from '~/hooks/use-toast.ts';

export type ViewMode = 'jobSeeker' | 'employer';

export type MatchesResponse = {
  data?: MatchData[];
  total?: number;
  page?: number;
  limit?: number;
};

export type QueryResult = {
  data?: MatchesResponse;
  error?: unknown;
};

export type ActionButtonsProps = {
  match: MatchData;
  onSuccess: () => void;
  isLoading: boolean;
  onInterested: () => Promise<void> | void;
  onRejected: () => void;
  disabled: boolean;
};

export type SuggestionsBaseProps = {
  viewMode: ViewMode;
  isProfileLoading: boolean;
  currentSuggestion: MatchData | null;
  isSuggestionLoading: boolean;
  error: Error | null;
  refetchMatches: () => Promise<QueryResult>;
  markAsInterested: (id: number) => Promise<void>;
  isMarkingInterested: boolean;
  markAsRejected: (id: number) => Promise<void>;
  isMarkingRejected: boolean;
  ActionButtons: ComponentType<ActionButtonsProps>;
  emptyStateTitle: string;
  emptyStateDescription: string;
  interestedToastMessage: string;
  rejectedToastMessage: string;
  isGeneratingSuggestions?: boolean;
  generatingSuggestionsMessage?: string;
  showRefreshButton?: boolean;
  refreshButtonLabel?: string;
};

const CARD_STATE = {
  NORMAL: 'normal',
  EXIT_LEFT: 'exit-left',
  EXIT_RIGHT: 'exit-right',
  LOADING: 'loading',
};

type CardState = (typeof CARD_STATE)[keyof typeof CARD_STATE];

const exitLeftVariants = {
  initial: { x: 0, opacity: 1, scale: 1 },
  animate: {
    x: -1000,
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.3,
      type: 'spring',
      stiffness: 120,
      damping: 25,
    },
  },
};

const exitRightVariants = {
  initial: { x: 0, opacity: 1, scale: 1 },
  animate: {
    x: 1000,
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.3,
      type: 'spring',
      stiffness: 120,
      damping: 25,
    },
  },
};

const loadingVariants = {
  initial: { opacity: 0, scale: 0.9 },
  animate: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.2,
    },
  },
};

export function SuggestionsBase({
  viewMode,
  isProfileLoading,
  currentSuggestion,
  isSuggestionLoading,
  error,
  refetchMatches,
  markAsInterested,
  isMarkingInterested,
  markAsRejected,
  isMarkingRejected,
  ActionButtons,
  emptyStateTitle,
  emptyStateDescription,
  interestedToastMessage,
  rejectedToastMessage,
  isGeneratingSuggestions = false,
  generatingSuggestionsMessage = "We're generating suggestions for you...",
  showRefreshButton = false,
  refreshButtonLabel = 'Refresh suggestions',
}: SuggestionsBaseProps): ReactElement {
  const { toast } = useToast();
  const [cardState, setCardState] = useState<CardState>(CARD_STATE.NORMAL);
  const [noMoreMatches, setNoMoreMatches] = useState(false);
  const currentCardRef = useRef<MatchData | null>(null);
  const initialRenderRef = useRef(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  if (
    currentSuggestion &&
    cardState === CARD_STATE.NORMAL &&
    (!currentCardRef.current ||
      currentCardRef.current.id !== currentSuggestion.id)
  ) {
    currentCardRef.current = currentSuggestion;
  }

  useEffect(() => {
    if (initialRenderRef.current && currentSuggestion) {
      initialRenderRef.current = false;
    }
  }, [currentSuggestion]);

  const isPageLoading =
    isProfileLoading ||
    (isSuggestionLoading &&
      cardState === CARD_STATE.NORMAL &&
      !currentCardRef.current);
  const isActionLoading = isMarkingInterested || isMarkingRejected;

  const handleAction = async (
    action: () => Promise<void>,
    direction: 'left' | 'right',
  ): Promise<void> => {
    if (!currentSuggestion || cardState !== CARD_STATE.NORMAL) return;

    currentCardRef.current = currentSuggestion;

    setCardState(
      direction === 'left' ? CARD_STATE.EXIT_LEFT : CARD_STATE.EXIT_RIGHT,
    );

    try {
      await action();

      setTimeout(async () => {
        setCardState(CARD_STATE.LOADING);

        try {
          const result = await refetchMatches();
          if (!result.data?.data?.length) {
            setNoMoreMatches(true);
          }
          setCardState(CARD_STATE.NORMAL);
        } catch (fetchError) {
          console.error('Error fetching new matches:', fetchError);
          setCardState(CARD_STATE.NORMAL);
        }
      }, 300);
    } catch (error) {
      console.error('ACTION: Error', error);
      setCardState(CARD_STATE.NORMAL);
      toast({
        title: 'Error',
        description: 'Failed to update status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleInterested = async (): Promise<void> => {
    await handleAction(async () => {
      if (currentSuggestion) {
        await markAsInterested(currentSuggestion.id);
        toast({
          title: 'Success!',
          description: interestedToastMessage,
        });
      }
    }, 'right');
  };

  const handleRejected = async (): Promise<void> => {
    await handleAction(async () => {
      if (currentSuggestion) {
        await markAsRejected(currentSuggestion.id);
        toast({
          title: 'Success!',
          description: rejectedToastMessage,
        });
      }
    }, 'left');
  };

  const handleRefresh = async (): Promise<void> => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    setCardState(CARD_STATE.LOADING);

    try {
      const result = await refetchMatches();
      if (!result.data?.data?.length) {
        setNoMoreMatches(true);
      } else {
        setNoMoreMatches(false);
      }
      toast({
        title: 'Suggestions refreshed',
        description: 'New suggestions have been loaded.',
      });
    } catch (error) {
      console.error('Error refreshing matches:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh suggestions. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
      setCardState(CARD_STATE.NORMAL);
    }
  };

  const renderContent = (): ReactElement => {
    if (isPageLoading) {
      return (
        <div className="w-[70%]">
          <MatchCardSkeleton />
        </div>
      );
    }

    if (isGeneratingSuggestions) {
      return (
        <div className="flex flex-col items-center justify-center py-12 px-4 text-center overflow-x-hidden">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <h3 className="text-xl font-semibold mb-2">
            {generatingSuggestionsMessage}
          </h3>
          <p className="text-muted-foreground mb-6">
            This might take a moment. Please check back later.
          </p>
          {showRefreshButton && (
            <Button
              variant="default"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="mt-4"
            >
              {isRefreshing ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Refreshing...
                </div>
              ) : (
                <div className="flex items-center">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  {refreshButtonLabel}
                </div>
              )}
            </Button>
          )}
        </div>
      );
    }

    if (error && cardState === CARD_STATE.NORMAL) {
      return (
        <Alert variant="destructive">
          <AlertDescription>
            An error occurred while loading suggestions:{' '}
            {(error as Error).message}
          </AlertDescription>
        </Alert>
      );
    }

    if (
      (!currentSuggestion && cardState === CARD_STATE.NORMAL) ||
      noMoreMatches
    ) {
      return (
        <div className="flex flex-col items-center">
          <Placeholder
            title={emptyStateTitle}
            description={emptyStateDescription}
          />
          {showRefreshButton && (
            <Button
              variant="default"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="mt-6"
            >
              {isRefreshing ? (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Refreshing...
                </div>
              ) : (
                <div className="flex items-center">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  {refreshButtonLabel}
                </div>
              )}
            </Button>
          )}
        </div>
      );
    }

    return (
      <div className="flex gap-4 w-full">
        <div className="flex-1 w-[70%] relative min-h-[600px]">
          {cardState === CARD_STATE.NORMAL && currentSuggestion && (
            <div
              className="absolute w-full"
              style={{
                transform: 'translateZ(0)',
                willChange: 'transform',
              }}
            >
              <MatchCard match={currentSuggestion} viewMode={viewMode} />
            </div>
          )}

          {cardState === CARD_STATE.EXIT_LEFT && currentCardRef.current && (
            <motion.div
              className="absolute w-full"
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-expect-error
              variants={exitLeftVariants}
              initial="initial"
              animate="animate"
              style={{
                transform: 'translateZ(0)',
                willChange: 'transform, opacity',
              }}
            >
              <MatchCard match={currentCardRef.current} viewMode={viewMode} />
            </motion.div>
          )}

          {cardState === CARD_STATE.EXIT_RIGHT && currentCardRef.current && (
            <motion.div
              className="absolute w-full"
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-expect-error
              variants={exitRightVariants}
              initial="initial"
              animate="animate"
              style={{
                transform: 'translateZ(0)',
                willChange: 'transform, opacity',
              }}
            >
              <MatchCard match={currentCardRef.current} viewMode={viewMode} />
            </motion.div>
          )}

          {cardState === CARD_STATE.LOADING && (
            <motion.div
              className="absolute w-full"
              variants={loadingVariants}
              initial="initial"
              animate="animate"
            >
              <MatchCardSkeleton />
            </motion.div>
          )}
        </div>
        <div className="w-[30%]">
          {((currentSuggestion && cardState === CARD_STATE.NORMAL) ||
            (cardState !== CARD_STATE.NORMAL && currentCardRef.current)) && (
            <ActionButtons
              match={
                cardState === CARD_STATE.NORMAL && currentSuggestion
                  ? currentSuggestion
                  : currentCardRef.current!
              }
              onSuccess={() => {}}
              isLoading={isActionLoading}
              onInterested={handleInterested}
              onRejected={handleRejected}
              disabled={cardState !== CARD_STATE.NORMAL}
            />
          )}
        </div>
      </div>
    );
  };

  return <div className="container p-4">{renderContent()}</div>;
}
