import React from 'react';

import { ARCHETYPE_CONFIG } from '~/app/dashboard/job-seeker/(dashboard)/(components)/ArchetypeWheel/constants.ts';

import { TopArchetype } from '~/api/features/archetypes/types';

type ArchetypeDetailsProps = {
  archetype: TopArchetype;
  showRoles?: boolean;
  showAbout?: boolean;
};

export function ArchetypeDetails({
  archetype,
  showRoles = true,
  showAbout = true,
}: ArchetypeDetailsProps) {
  const ArchetypeIcon = ARCHETYPE_CONFIG[archetype.name]?.icon;

  return (
    <div>
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          {ArchetypeIcon && (
            <div className="w-16 h-16 text-primary">
              <ArchetypeIcon />
            </div>
          )}
          <h3 className="text-4xl font-bold">{archetype.name}</h3>
        </div>
      </div>

      {archetype.metaphor && (
        <div className="mb-6">
          <p className="text-lg text-muted-foreground leading-relaxed italic">
            "{archetype.metaphor}"
          </p>
        </div>
      )}

      {archetype.description && (
        <div className="mb-6">
          <h4 className="font-semibold text-xl mb-2">Description</h4>
          <p className="text-muted-foreground leading-relaxed">
            {archetype.description}
          </p>
        </div>
      )}

      {archetype.dimensions_intro && (
        <div className="mb-6">
          <h4 className="font-semibold text-xl mb-2">
            Your Behavioural Dimensions
          </h4>
          <p className="text-muted-foreground leading-relaxed">
            {archetype.dimensions_intro}
          </p>
        </div>
      )}

      {showRoles &&
        archetype.role_alignments &&
        archetype.role_alignments.length > 0 && (
          <div className="mb-6">
            <h4 className="font-semibold text-xl mb-2">
              Typical Role Alignments
            </h4>
            <ul className="list-disc list-inside mb-3 text-muted-foreground">
              {archetype.role_alignments.map((role, idx) => (
                // eslint-disable-next-line react/no-array-index-key
                <li key={idx}>{role}</li>
              ))}
            </ul>
            {archetype.role_description && (
              <p className="text-muted-foreground leading-relaxed">
                {archetype.role_description}
              </p>
            )}
          </div>
        )}

      {showAbout && archetype.about_profile && (
        <div>
          <h4 className="font-semibold text-xl mb-2">About This Profile</h4>
          <p className="text-muted-foreground leading-relaxed">
            {archetype.about_profile}
          </p>
        </div>
      )}
    </div>
  );
}
