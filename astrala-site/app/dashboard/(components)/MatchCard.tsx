import React from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, Dot, InfoIcon, Mail, MapPin, Phone } from 'lucide-react';

import { ArchetypeInfoButton } from '~/app/dashboard/(components)/ArchetypeInfoButton.tsx';

import { Database } from '~/shared/db/generated/types';

import Rectangle from '~/assets/icons/rectangle.svg';
import UserIcon from '~/assets/icons/user.svg';
import { Badge } from '~/components/ui/badge.tsx';
import { Card, CardContent, CardFooter } from '~/components/ui/card.tsx';
import { Separator } from '~/components/ui/separator.tsx';
import { parseSkills } from '~/utils/parseSkills.ts';

export type MatchData = {
  id: number;
  created_at: string;
  updated_at: string;
  job_seeker_id: number;
  job_seeker_status: string;
  employer_status: string;
  ai_match_score: number;
  vacancy_id: number;

  jobSeeker?: {
    id: number;
    full_name: string;
    job_title: string;
    country?: string;
    city?: string;
    salary_min?: number;
    salary_max?: number;
    has_driving_license?: boolean;
    resume_path?: string;
    passport_path?: string;
    phone?: string;
    certifications?: Database['public']['Tables']['job_seeker_certifications']['Row'][];
    work_experience?: Array<{
      id: number;
      company_name: string;
      job_title: string;
      start_date: string;
      end_date?: string;
      comment?: string;
    }>;
    education?: Array<{
      id: number;
      type: string;
      institution: string;
      discipline: string;
      start_year: number;
      end_year?: number;
    }>;
  };

  vacancy?: {
    id: number;
    title: string;
    description: string;
    salary_min?: number;
    salary_max?: number;
    employer_id: number;
    company_id?: number;
    driving_license?: boolean;
    education_degree?: string;
    education_discipline?: string;
    country?: string;
    city?: string;
    experience_years_from?: number;
    company?: {
      id: number;
      name: string;
    };
    employer?: {
      id: number;
      full_name: string;
      company_id: number;
      email?: string;
    };
  };

  match_description?: {
    vacancy_match_id: number;
    driving_license_match: boolean;
    education_match: boolean;
    location_match: boolean;
    salary_match: boolean;
    work_experience_match: boolean;
    work_experience_years?: number;
    lacking_skills?: string | string[];
    matching_skills?: string | string[];
    matching_behavioural_tags?: string | string[];
    lacking_behavioural_tags?: string | string[];
    ai_summary?: string;
  };
};

type MatchCardProps = {
  match: MatchData;
  children?: React.ReactNode;
  viewMode?: 'jobSeeker' | 'employer';
  isAccordion?: boolean;
  isExpanded?: boolean;
  onToggle?: () => void;
};

const formatSalary = (salaryMin?: number, salaryMax?: number): string => {
  if (salaryMin && salaryMax) {
    return `£${salaryMin} - £${salaryMax}`;
  }

  if (salaryMin || salaryMax) {
    return `£${salaryMin || salaryMax}`;
  }

  return 'Not data';
};

const formatDateMonthYear = (dateString?: string): string => {
  if (!dateString) return 'Present';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

const parseTags = (tags?: string | string[]): string[] => {
  if (!tags) return [];

  if (Array.isArray(tags)) {
    return tags.filter(Boolean);
  }

  try {
    const parsedTags = JSON.parse(tags);
    if (Array.isArray(parsedTags)) {
      return parsedTags.filter(Boolean);
    }
  } catch (error) {
    return tags
      .split(',')
      .map(tag => tag.trim())
      .filter(Boolean);
  }

  return [tags].filter(Boolean);
};

const getSalaryMatch = (
  jobSeekerMin?: number,
  vacancyMax?: number,
): boolean | null => {
  if (
    jobSeekerMin === undefined ||
    jobSeekerMin === null ||
    vacancyMax === undefined ||
    vacancyMax === null
  ) {
    return null;
  }
  return jobSeekerMin <= vacancyMax;
};

export function MatchCard({
  match,
  children,
  viewMode = 'jobSeeker',
  isAccordion = false,
  isExpanded = false,
  onToggle,
}: MatchCardProps) {
  const score = match.ai_match_score
    ? Math.round(match.ai_match_score * 100)
    : 0;

  const isMatched =
    viewMode === 'employer'
      ? match.employer_status === 'INTERESTED' &&
        match.job_seeker_status === 'INTERESTED'
      : match.employer_status === 'PAID' && match.job_seeker_status === 'PAID';

  const getScoreColorClass = (score: number): string => {
    if (score >= 50) {
      return 'bg-primary';
    }
    return 'bg-destructive';
  };

  const getHeaderContent = () => {
    if (viewMode === 'jobSeeker') {
      return {
        avatar:
          match.vacancy?.company?.name?.charAt(0) ||
          match.vacancy?.employer?.full_name?.charAt(0) ||
          'J',
        title: match.vacancy?.title || 'Untitled Position',
        subtitle:
          match.vacancy?.company?.name ||
          match.vacancy?.employer?.full_name ||
          'Unknown Company',
        location:
          match.vacancy?.city && match.vacancy?.country
            ? `${match.vacancy.city}, ${match.vacancy.country}`
            : match.vacancy?.city ||
              match.vacancy?.country ||
              'Location not specified',
        email: match.vacancy?.employer?.email,
      };
    }
    return {
      avatar: match.jobSeeker?.full_name?.charAt(0) || 'C',
      title: match.jobSeeker?.full_name || 'Unnamed Candidate',
      subtitle: match.jobSeeker?.job_title || 'No job title',
      location:
        match.jobSeeker?.city && match.jobSeeker?.country
          ? `${match.jobSeeker.city}, ${match.jobSeeker.country}`
          : match.jobSeeker?.city ||
            match.jobSeeker?.country ||
            'Location not specified',
      phone: match.jobSeeker?.phone,
    };
  };

  const header = getHeaderContent();

  const salaryMatch = getSalaryMatch(
    match.jobSeeker?.salary_min,
    match.vacancy?.salary_max,
  );

  const renderWorkExperience = () => {
    if (
      !match.jobSeeker?.work_experience ||
      match.jobSeeker.work_experience.length === 0
    ) {
      return (
        <p className="text-muted-foreground">No work experience provided.</p>
      );
    }

    return (
      <div className="flex overflow-x-auto gap-4 pb-2 text-foreground">
        {match.jobSeeker.work_experience.map(exp => (
          <div
            key={exp.id}
            className="min-w-[200px] border rounded-lg p-3 flex-shrink-0 bg-[#101010]"
          >
            <div className="font-semibold">{exp.job_title}</div>
            <div className="text-sm">
              {exp.company_name}, {formatDateMonthYear(exp.start_date)} -{' '}
              {formatDateMonthYear(exp.end_date)}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderSkillsBadges = (skills: string[], isMatching: boolean) => {
    if (skills.length === 0) {
      return (
        <span className="text-muted-foreground">
          {isMatching ? 'No matching skills' : 'No missing skills'}
        </span>
      );
    }

    return (
      <div className="flex flex-wrap gap-2">
        {skills.map(skill => (
          <Badge
            key={skill}
            className={`${
              isMatching
                ? 'bg-primary text-foreground'
                : 'bg-destructive text-foreground'
            } py-1 px-2`}
          >
            {skill}
          </Badge>
        ))}
      </div>
    );
  };

  const renderBehaviouralBadges = (tags: string[], isMatching: boolean) => {
    if (tags.length === 0) {
      return (
        <span className="text-muted-foreground">
          {isMatching ? 'No matching traits' : 'No missing traits'}
        </span>
      );
    }

    return (
      <div className="flex flex-wrap gap-2">
        {tags.map(tag => (
          <Badge
            key={tag}
            className={`${
              isMatching
                ? 'bg-primary text-foreground'
                : 'bg-destructive text-foreground'
            } py-1 px-2`}
          >
            {tag}
          </Badge>
        ))}
      </div>
    );
  };

  const renderJobRequirements = () => {
    const matchingSkills = parseSkills(
      match.match_description?.matching_skills,
    );
    const lackingSkills = parseSkills(match.match_description?.lacking_skills);
    const matchingTags = parseTags(
      match.match_description?.matching_behavioural_tags,
    );

    if (viewMode === 'jobSeeker') {
      return (
        <div className="mt-6">
          <div className="grid grid-cols-3 gap-4">
            <div />
            <div className="text-start font-semibold pb-2 text-primary">
              Vacancy
            </div>
            <div className="text-start font-semibold pb-2 text-primary">
              Your CV
            </div>

            <div className="py-2 border-b font-semibold">
              Required education
            </div>
            <div className="py-2 border-b capitalize">
              {/* eslint-disable-next-line sonarjs/no-duplicate-string */}
              {match.vacancy?.education_degree || 'Not specified'}
            </div>
            <div
              className={`py-2 border-b capitalize ${
                match.match_description?.education_match
                  ? // eslint-disable-next-line sonarjs/no-duplicate-string
                    'text-primary'
                  : // eslint-disable-next-line sonarjs/no-duplicate-string
                    'text-destructive'
              }`}
            >
              {match.jobSeeker?.education &&
              match.jobSeeker.education.length > 0
                ? match.jobSeeker.education[0].type
                : 'Not specified'}
            </div>

            <div className="py-2 border-b font-semibold">Salary Range</div>
            <div className="py-2 border-b">
              {formatSalary(
                match.vacancy?.salary_min,
                match.vacancy?.salary_max,
              )}
            </div>
            <div
              className={`
                py-2 border-b
                ${salaryMatch === true ? 'text-primary' : ''}
                ${salaryMatch === false ? 'text-destructive' : ''}`}
            >
              {formatSalary(
                match.jobSeeker?.salary_min,
                match.jobSeeker?.salary_max,
              )}
            </div>

            <div className="py-2 border-b font-semibold">
              Required Work Experience
            </div>
            <div className="py-2 border-b">
              {match.vacancy?.experience_years_from !== undefined &&
              match.vacancy.experience_years_from !== null
                ? `Work experience over ${
                    match.vacancy.experience_years_from === 0
                      ? '0'
                      : match.vacancy.experience_years_from
                  } years`
                : 'Not specified'}
            </div>
            <div
              className={`py-2 border-b ${match.match_description?.work_experience_match ? 'text-primary' : 'text-destructive'}`}
            >
              {match.match_description?.work_experience_years !== undefined
                ? `Work experience over ${match.match_description.work_experience_years} years`
                : 'Not specified'}
            </div>

            <div className="py-2 border-b font-semibold">Location</div>
            <div className="py-2 border-b">
              {match.vacancy?.city && match.vacancy?.country
                ? `${match.vacancy.city}, ${match.vacancy.country}`
                : match.vacancy?.city ||
                  match.vacancy?.country ||
                  'Not specified'}
            </div>
            <div
              className={`py-2 border-b ${match.match_description?.location_match ? 'text-primary' : 'text-destructive'}`}
            >
              {match.jobSeeker?.city && match.jobSeeker?.country
                ? `${match.jobSeeker.city}, ${match.jobSeeker.country}`
                : match.jobSeeker?.city ||
                  match.jobSeeker?.country ||
                  'Not specified'}
            </div>

            {match.vacancy?.driving_license !== undefined && (
              <>
                <div className="py-2 border-b font-semibold">
                  Driving License
                </div>
                <div className="py-2 border-b">
                  {match.vacancy.driving_license
                    ? 'Driving license required'
                    : 'Driving license not required'}
                </div>
                <div
                  className={`py-2 border-b ${match.match_description?.driving_license_match ? 'text-primary' : 'text-destructive'}`}
                >
                  {match.jobSeeker?.has_driving_license
                    ? 'Driving license available'
                    : 'Driving license not available'}
                </div>
              </>
            )}

            {(matchingSkills.length > 0 || lackingSkills.length > 0) && (
              <>
                <div className="py-2 border-b font-semibold">Skills</div>
                <div className="py-2 border-b col-span-2">
                  <div className="space-y-2">
                    {matchingSkills.length > 0 && (
                      <div>{renderSkillsBadges(matchingSkills, true)}</div>
                    )}
                    {lackingSkills.length > 0 && (
                      <div>{renderSkillsBadges(lackingSkills, false)}</div>
                    )}
                  </div>
                </div>
              </>
            )}

            {matchingTags.length > 0 && (
              <>
                <div className="py-2 border-b font-semibold">Preferences</div>
                <div className="py-2 border-b col-span-2">
                  {renderBehaviouralBadges(matchingTags, true)}
                </div>
              </>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="mt-6 p-6">
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center font-semibold pb-2" />
          <div className="text-center font-semibold pb-2">Job seeker CV</div>
          <div className="text-center font-semibold pb-2">
            Vacancy requirements
          </div>

          <div className="py-2 border-b font-semibold">Required education</div>
          <div
            className={`py-2 border-b capitalize ${match.match_description?.education_match ? 'text-primary' : 'text-destructive'}`}
          >
            {match.jobSeeker?.education && match.jobSeeker.education.length > 0
              ? match.jobSeeker.education[0].type
              : 'Not specified'}
          </div>
          <div className="py-2 border-b capitalize">
            {match.vacancy?.education_degree || 'Not specified'}
          </div>

          <div className="py-2 border-b font-semibold">Salary Range</div>
          <div
            className={`py-2 border-b ${salaryMatch === true && 'text-primary'} ${salaryMatch === false && 'text-destructive'}`}
          >
            {formatSalary(
              match.jobSeeker?.salary_min,
              match.jobSeeker?.salary_max,
            )}
          </div>
          <div className="py-2 border-b">
            {formatSalary(match.vacancy?.salary_min, match.vacancy?.salary_max)}
          </div>

          <div className="py-2 border-b font-semibold">
            Required Work Experience
          </div>
          <div
            className={`py-2 border-b ${match.match_description?.work_experience_match ? 'text-primary' : 'text-destructive'}`}
          >
            {match.match_description?.work_experience_years !== undefined
              ? `Work experience over ${match.match_description.work_experience_years} years`
              : 'Not specified'}
          </div>
          <div className="py-2 border-b">
            {match.vacancy?.experience_years_from !== undefined &&
            match.vacancy.experience_years_from !== null
              ? `Work experience over ${
                  match.vacancy.experience_years_from === 0
                    ? '0'
                    : match.vacancy.experience_years_from
                } years`
              : 'Not specified'}
          </div>

          <div className="py-2 border-b font-semibold">Location</div>
          <div
            className={`py-2 border-b ${match.match_description?.location_match ? 'text-primary' : 'text-destructive'}`}
          >
            {match.jobSeeker?.city && match.jobSeeker?.country
              ? `${match.jobSeeker.city}, ${match.jobSeeker.country}`
              : match.jobSeeker?.city ||
                match.jobSeeker?.country ||
                'Not specified'}
          </div>
          <div className="py-2 border-b">
            {match.vacancy?.city && match.vacancy?.country
              ? `${match.vacancy.city}, ${match.vacancy.country}`
              : match.vacancy?.city ||
                match.vacancy?.country ||
                'Not specified'}
          </div>

          {match.vacancy?.driving_license !== undefined && (
            <>
              <div className="py-2 border-b font-semibold">Driving License</div>
              <div
                className={`py-2 border-b ${match.match_description?.driving_license_match ? 'text-primary' : 'text-destructive'}`}
              >
                {match.jobSeeker?.has_driving_license
                  ? 'Driving license available'
                  : 'Driving license not available'}
              </div>
              <div className="py-2 border-b">
                {match.vacancy.driving_license
                  ? 'Driving license required'
                  : 'Driving license not required'}
              </div>
            </>
          )}

          {(matchingSkills.length > 0 || lackingSkills.length > 0) && (
            <>
              <div className="py-2 border-b font-semibold">Skills</div>
              <div className="py-2 border-b col-span-2">
                <div className="space-y-2">
                  {matchingSkills.length > 0 && (
                    <div>{renderSkillsBadges(matchingSkills, true)}</div>
                  )}
                  {lackingSkills.length > 0 && (
                    <div>{renderSkillsBadges(lackingSkills, false)}</div>
                  )}
                </div>
              </div>
            </>
          )}

          {matchingTags.length > 0 && (
            <>
              <div className="py-2 border-b font-semibold">Preferences</div>
              <div className="py-2 border-b col-span-2">
                {renderBehaviouralBadges(matchingTags, true)}
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  const renderCardHeader = () => (
    <button
      className={`flex items-center w-full ${isAccordion ? 'cursor-pointer' : ''}  `}
      onClick={isAccordion ? onToggle : undefined}
    >
      <div className="flex-shrink-0 p-4 bg-muted border rounded-full flex items-center justify-center mr-4">
        <UserIcon className="w-12 h-12 text-primary" />
      </div>
      <div className="flex flex-col justify-around flex-grow">
        <h2 className="text-2xl font-semibold text-start">{header.title}</h2>
        <div className="mt-1 flex flex-wrap items-center text-lg">
          <div className="flex items-center mr-4">
            <InfoIcon className="w-5 h-5 mr-1 text-primary" />
            <span>{header.subtitle}</span>
          </div>
          <div className="flex items-center mr-4">
            <MapPin className="w-5 h-5 mr-1 text-primary" />
            <span>{header.location}</span>
          </div>
          {viewMode === 'employer' && isMatched && header.phone && (
            <div className="flex items-center mr-4">
              <Phone className="w-5 h-5 mr-1 text-primary" />
              <span>{header.phone}</span>
            </div>
          )}
          {viewMode === 'jobSeeker' && isMatched && header.email && (
            <div className="flex items-center">
              <Mail className="w-5 h-5 mr-1 text-primary" />
              <span>{header.email}</span>
            </div>
          )}
        </div>
      </div>
      {isAccordion && (
        <div className="ml-auto flex flex-col items-center gap-2">
          <Badge
            variant="outline"
            className={`${getScoreColorClass(score)} text-md rounded-xl text-foreground `}
          >
            <Dot />
            {score}% Score
          </Badge>
          <span className="flex items-center justify-end gap-2 w-full">
            <p className="text-muted-foreground text-sm">
              {isExpanded ? 'Show Less' : 'Show More'}
            </p>
            <motion.div
              animate={{ rotate: isExpanded ? 0 : -90 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="h-5 w-5 text-muted-foreground" />
            </motion.div>
          </span>
        </div>
      )}
    </button>
  );

  const tickGroups = Array.from({ length: 5 }, (_, groupIndex) =>
    Array.from({ length: 3 }, (_, tickIndex) => ({ groupIndex, tickIndex })),
  );

  if (isAccordion) {
    return (
      <Card className="mb-4 overflow-hidden p-4">
        {renderCardHeader()}

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="overflow-hidden"
            >
              <div className="p-6">
                <Separator />
                <CardContent className="p-0">
                  <div className="mt-6">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-bold text-xl">
                        Role Fit Alignment: {score}%
                      </span>
                    </div>
                    <div
                      className="h-4 w-full overflow-hidden relative"
                      style={{
                        background:
                          'linear-gradient(to right, #4B9C88 0%, #181D19 100%)',
                      }}
                    >
                      <div
                        className="absolute top-0 right-0 h-full bg-muted"
                        style={{
                          width: `${100 - score}%`,
                        }}
                      />
                    </div>
                    <div className="flex justify-between text-xs mt-2 items-center">
                      <span className="text-lg">0</span>
                      <span>20</span>
                      <span>40</span>
                      <span>60</span>
                      <span>80</span>
                      <span className="text-lg">100</span>
                    </div>
                  </div>

                  <div className="mt-6 text-foreground">
                    <p>
                      {viewMode === 'employer'
                        ? match.match_description?.ai_summary ||
                          'AI Summary text not available.'
                        : match.vacancy?.description ||
                          'No description provided.'}
                    </p>
                  </div>

                  <Separator className="my-6" />

                  <div>
                    <h3 className="font-medium text-lg mb-4">
                      {viewMode === 'jobSeeker'
                        ? 'Job Information'
                        : 'Work Experience'}
                    </h3>

                    {viewMode === 'jobSeeker' ? (
                      <p className="text-sm mb-4 text-foreground">
                        {match.vacancy?.description ||
                          'No description provided.'}
                      </p>
                    ) : (
                      renderWorkExperience()
                    )}
                  </div>

                  {renderJobRequirements()}
                </CardContent>
                {children && (
                  <CardFooter className="px-6 py-4 flex justify-end gap-2 border-t">
                    {children}
                  </CardFooter>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    );
  }

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        {renderCardHeader()}

        <div className="mt-6">
          <div className="flex justify-between items-center mb-2">
            <span className="font-bold text-xl">
              Role Fit Alignment: {score}%
            </span>
          </div>
          <div
            className="h-4 w-full overflow-hidden relative"
            style={{
              background: 'linear-gradient(to right, #181D19 0%, #4B9C88 100%)',
            }}
          >
            <div
              className="absolute top-0 right-0 h-full bg-[#737373]"
              style={{
                width: `${100 - score}%`,
              }}
            />
          </div>

          <div className="flex justify-between text-xs mt-2 items-center">
            <span className="text-lg text-white font-medium">0</span>
            {tickGroups.map((group, groupIdx) => (
              // eslint-disable-next-line react/no-array-index-key
              <React.Fragment key={groupIdx}>
                <div className="flex items-center justify-between px-8 gap-1 flex-1">
                  {group.map((_, tickIdx) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <Rectangle key={tickIdx} className="h-1.5 text-[#999999]" />
                  ))}
                </div>
                <span
                  className={`text-white ${groupIdx === 4 ? 'text-lg font-medium' : ''}`}
                >
                  {(groupIdx + 1) * 20}
                </span>
              </React.Fragment>
            ))}
          </div>
        </div>

        <div className="mt-6">
          <p>
            {viewMode === 'employer'
              ? match.match_description?.ai_summary ||
                'AI Summary text not available.'
              : match.vacancy?.description || 'No description provided.'}
          </p>
        </div>

        {viewMode === 'employer' && match.jobSeeker?.id && (
          <div className="flex items-center mt-4">
            <ArchetypeInfoButton
              jobSeekerId={match.jobSeeker.id}
              jobSeekerName={match.jobSeeker.full_name}
              jobTitle={match.jobSeeker.job_title}
              location={
                match.jobSeeker.city && match.jobSeeker.country
                  ? `${match.jobSeeker.city}, ${match.jobSeeker.country}`
                  : match.jobSeeker.city || match.jobSeeker.country
              }
            />
          </div>
        )}
        <Separator className="my-6" />

        <div className="">
          <h3 className="font-semibold text-xl mb-4">
            {viewMode === 'jobSeeker' ? 'Job Information' : 'Work Experience'}
          </h3>

          {viewMode === 'jobSeeker' ? (
            <p className="text-sm text-foreground mb-4">
              {match.vacancy?.description || 'No description provided.'}
            </p>
          ) : (
            renderWorkExperience()
          )}
        </div>

        {renderJobRequirements()}
      </CardContent>
      {children && (
        <CardFooter className="px-6 py-4 flex justify-end gap-2 border-t">
          {children}
        </CardFooter>
      )}
    </Card>
  );
}
