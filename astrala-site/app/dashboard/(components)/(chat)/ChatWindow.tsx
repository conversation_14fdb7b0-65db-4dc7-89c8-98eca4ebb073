import { useEffect, useRef, useState } from 'react';
import { Loader2, Send } from 'lucide-react';

import { JobInfoModal } from '~/app/dashboard/(components)/(chat)/JobInfoModal.tsx';
import { Message } from '~/app/dashboard/(components)/(chat)/Message.tsx';

import { Chat, ChatMessage } from '~/api/features/chats/actions.ts';
import UserIcon from '~/assets/icons/user.svg';
import { Button } from '~/components/ui/button.tsx';
import { ScrollArea } from '~/components/ui/scroll-area.tsx';

type ChatWindowProps = {
  messages: (ChatMessage & { _isOptimistic?: boolean })[];
  isLoading: boolean;
  onSendMessage: (text: string) => void;
  isSending: boolean;
  userType: 'employer' | 'job_seeker';
  userId: number;
  activeChat: Chat | null;
};

type GroupedMessage = {
  date: string;
  messages: (ChatMessage & { _isOptimistic?: boolean })[];
};

export function ChatWindow({
  messages,
  isLoading,
  onSendMessage,
  isSending,
  userType,
  userId,
  activeChat,
}: ChatWindowProps) {
  const [messageText, setMessageText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const unreadMessageRefs = useRef<Map<string | number, HTMLDivElement>>(
    new Map(),
  );

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
        inline: 'nearest',
      });
    }
  };

  const scrollToFirstUnread = () => {
    const firstUnreadMessage = messages.find(message => {
      const isOwnMessage =
        userType === 'employer'
          ? message.employer_id === userId
          : message.job_seeker_id === userId;

      return !isOwnMessage && !message.read_at;
    });

    if (firstUnreadMessage) {
      const element = unreadMessageRefs.current.get(firstUnreadMessage.id);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest',
        });
        return true;
      }
    }
    return false;
  };

  useEffect(
    () => {
      if (messages.length > 0 && !isLoading && activeChat) {
        const hasUnread =
          activeChat.unread_count && activeChat.unread_count > 0;

        if (hasUnread) {
          setTimeout(() => {
            const scrolledToUnread = scrollToFirstUnread();
            if (!scrolledToUnread) {
              scrollToBottom();
            }
          }, 100);
        } else {
          setTimeout(scrollToBottom, 100);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isLoading, activeChat?.id, messages.length],
  );

  const groupMessagesByDate = (
    messages: (ChatMessage & { _isOptimistic?: boolean })[],
  ): GroupedMessage[] => {
    const groups: {
      [key: string]: (ChatMessage & { _isOptimistic?: boolean })[];
    } = {};

    messages.forEach(message => {
      const date = new Date(message.created_at).toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      });

      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return Object.entries(groups).map(([date, messages]) => ({
      date,
      messages,
    }));
  };

  const handleSend = () => {
    if (messageText.trim() && !isSending) {
      onSendMessage(messageText.trim());
      setMessageText('');

      if (textareaRef.current) {
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
          }
        }, 0);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (messageText.trim() && !isSending) {
        handleSend();
      }
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessageText(e.target.value);

    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  const getChatTitle = () => {
    if (!activeChat) return 'Select a chat';

    if (userType === 'employer') {
      return activeChat.job_seeker?.full_name || 'Unknown Candidate';
    }
    return (
      activeChat.vacancy?.company?.name ||
      activeChat.vacancy?.employer?.full_name ||
      'Unknown Company'
    );
  };

  const renderMessages = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            Loading messages...
          </div>
        </div>
      );
    }

    if (messages.length === 0) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground text-center">
            <p>No messages yet</p>
            <p className="text-sm mt-1 text-muted-foreground">
              Start the conversation!
            </p>
          </div>
        </div>
      );
    }

    const groupedMessages = groupMessagesByDate(messages);

    return (
      <div className="space-y-4 pr-4">
        {groupedMessages.map(group => (
          <div key={group.date} className="space-y-2">
            <div className="flex justify-center py-2">
              <div className="text-foreground px-3 py-1 rounded-full text-xs">
                {group.date}
              </div>
            </div>

            <div className="space-y-2">
              {group.messages.map(message => {
                const isOwnMessage =
                  userType === 'employer'
                    ? message.employer_id === userId
                    : message.job_seeker_id === userId;

                return (
                  <div
                    key={message.id}
                    ref={el => {
                      if (el && !isOwnMessage && !message.read_at) {
                        unreadMessageRefs.current.set(message.id, el);
                      }
                    }}
                  >
                    <Message message={message} isOwnMessage={isOwnMessage} />
                  </div>
                );
              })}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
    );
  };

  const getChatSubtitle = () => {
    if (!activeChat) return '';
    return activeChat.vacancy?.title || 'Unknown Position';
  };

  if (!activeChat) {
    return (
      <div className="flex-1 flex items-center justify-center bg-background h-full">
        <div className="text-center text-muted-foreground">
          <h3 className="text-lg font-medium">
            Select a chat to start messaging
          </h3>
          <p className="text-sm mt-1 text-muted-foreground">
            Choose a conversation from the left sidebar
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-background h-full">
      <div className="p-4 border-b bg-background border flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex gap-4 items-center">
            <div className="flex-shrink-0 p-4 bg-secondary rounded-full flex items-center justify-center">
              <UserIcon className="w-12 h-12 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-foreground">
                {getChatTitle()}
              </h2>
              <p className="text-md text-foreground">{getChatSubtitle()}</p>
            </div>
          </div>
          <JobInfoModal
            vacancyMatchId={activeChat.vacancy_match_id}
            userType={userType}
          />
        </div>
      </div>

      <div
        className="flex-1 overflow-hidden relative"
        style={{
          background:
            'radial-gradient(90.16% 143.01% at 15.32% 21.04%, rgba(165, 239, 255, 0.2) 0%, rgba(110, 191, 244, 0.0447917) 77.08%, rgba(70, 144, 213, 0) 100%)',
        }}
      >
        <div
          className="absolute inset-0 pointer-events-none opacity-20"
          style={{
            backgroundImage: 'url(/card-bg.png)',
            backgroundRepeat: 'repeat',
            backgroundSize: 'auto',
            mixBlendMode: 'overlay',
          }}
        />
        <ScrollArea className="h-full px-4 relative z-10">
          {renderMessages()}
        </ScrollArea>
      </div>

      <div className="p-4 border-t bg-background border flex-shrink-0">
        <div className="flex gap-2 items-center justify-between">
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={messageText}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder="Type a message... (Shift+Enter for new line)"
              disabled={isSending}
              rows={1}
              className="w-full min-h-[40px] max-h-[120px] resize-none rounded-lg border bg-secondary text-foreground placeholder:text-sm placeholder:text-start placeholder:text-muted-foreground px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent overflow-y-auto disabled:opacity-50"
              style={{ height: 'auto' }}
            />
          </div>
          <Button
            onClick={handleSend}
            disabled={!messageText.trim() || isSending}
            size="sm"
            className="bg-primary hover:bg-primary/70 text-primary-foreground h-[40px] px-3 disabled:opacity-50"
          >
            {isSending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
