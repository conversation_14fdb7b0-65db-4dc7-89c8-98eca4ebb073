import { format } from 'date-fns';
import { Clock } from 'lucide-react';

import { ChatMessage } from '~/api/features/chats/actions.ts';
import UserIcon from '~/assets/icons/user.svg';

type MessageProps = {
  message: ChatMessage & { _isOptimistic?: boolean };
  isOwnMessage: boolean;
};

export function Message({ message, isOwnMessage }: MessageProps) {
  const formatTime = () => {
    try {
      return format(new Date(message.created_at), 'HH:mm');
    } catch {
      return '';
    }
  };

  const renderMessageText = (content: string | undefined) => {
    if (!content) return '';

    return content.split('\n').map((line, index, array) => (
      // eslint-disable-next-line react/no-array-index-key
      <span key={index}>
        {line}
        {index < array.length - 1 && <br />}
      </span>
    ));
  };

  const isOptimistic = message._isOptimistic === true;

  const ownMessageStyle: React.CSSProperties = {
    background: `linear-gradient(315deg, #0e2d28 0%, #23665f 100%)`,
  };

  return (
    <div className="mb-4 flex justify-start">
      <div className="flex items-start justify-start gap-2 max-w-2xl">
        <div
          className={`flex-shrink-0 w-12 h-12 ${isOwnMessage ? '' : 'bg-background'} rounded-full flex items-center justify-center`}
          style={isOwnMessage ? ownMessageStyle : undefined}
        >
          <UserIcon className="w-6 h-6" />
        </div>

        <div className="relative">
          <div className="text-sm mb-1 text-foreground">
            {isOwnMessage ? 'You' : message.sender?.full_name},{' '}
            <span>{formatTime()}</span>
          </div>
          <div
            className={`p-3 rounded-b-lg rounded-tr-lg ${
              isOwnMessage
                ? `text-white ${isOptimistic ? 'opacity-75' : ''}`
                : 'bg-[#181D19] text-foreground'
            }`}
            style={isOwnMessage ? ownMessageStyle : undefined}
          >
            <div className="text-md whitespace-pre-wrap break-all overflow-wrap-anywhere ">
              {renderMessageText(message.message_text || '')}
            </div>
            <div
              className={`text-md mt-1 flex items-center gap-1 ${
                isOwnMessage
                  ? 'text-primary-foreground/70'
                  : 'text-muted-foreground'
              }`}
            >
              {isOwnMessage && (
                <>
                  {isOptimistic ? (
                    <Clock className="h-3 w-3" />
                  ) : (
                    message.read_at && <span>✓</span>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
