import { useState } from 'react';

import { MatchCard } from '~/app/dashboard/(components)/MatchCard.tsx';

import { useGetMatchById } from '~/api/features/matches/queries.ts';
import { Button } from '~/components/ui/button.tsx';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog.tsx';

type JobInfoModalProps = {
  vacancyMatchId: number;
  userType: 'employer' | 'job_seeker';
};

export function JobInfoModal({ vacancyMatchId, userType }: JobInfoModalProps) {
  const [open, setOpen] = useState(false);

  const {
    data: matchData,
    isLoading,
    error,
  } = useGetMatchById(vacancyMatchId, userType, open);

  const viewMode = userType === 'employer' ? 'employer' : 'jobSeeker';

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default">Match Info</Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Job Information</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">Loading...</div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center text-destructive">
                Error loading job information
              </div>
            </div>
          )}

          {matchData && (
            <div className="py-4">
              <MatchCard
                match={matchData}
                viewMode={viewMode}
                isAccordion={false}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
