import { useState } from 'react';
import { useRouter } from 'next/navigation';

import { ChatsList } from './ChatList';
import { ChatWindow } from './ChatWindow';
import { useChatPage, useSendMessage } from '~/api/features/chats/queries.ts';
import { useAuth } from '~/providers/AuthProvider.tsx';

type ChatPageProps = {
  userType: 'employer' | 'job_seeker';
  activeChatId?: number;
};

export function ChatPage({ userType, activeChatId }: ChatPageProps) {
  const router = useRouter();
  const { employerProfile, jobSeekerProfile } = useAuth();

  const userId =
    userType === 'employer' ? employerProfile?.id : jobSeekerProfile?.id;

  const [selectedChatId, setSelectedChatId] = useState<number | null>(
    activeChatId || null,
  );

  const { chats, messages, isLoadingChats, isLoadingMessages } = useChatPage(
    userId,
    userType,
    selectedChatId || undefined,
  );

  const sendMessageMutation = useSendMessage();

  const handleChatSelect = (chatId: number) => {
    setSelectedChatId(chatId);
    router.push(
      `/dashboard/${userType === 'employer' ? 'employer' : 'job-seeker'}/chats/${chatId}`,
    );
  };

  const handleSendMessage = async (messageText: string) => {
    if (!selectedChatId || !userId) return;

    try {
      await sendMessageMutation.mutateAsync({
        chatId: selectedChatId,
        messageText,
        senderId: userId,
        senderType: userType,
      });
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const activeChat = chats.find(chat => chat.id === selectedChatId) || null;

  if (!userId) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-64px)] bg-background">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex h-[calc(100vh-62px)] bg-background overflow-y-hidden">
      <ChatsList
        chats={chats}
        activeChatId={selectedChatId}
        onChatSelect={handleChatSelect}
        userType={userType}
        isLoading={isLoadingChats}
      />
      <ChatWindow
        messages={messages}
        isLoading={isLoadingMessages}
        onSendMessage={handleSendMessage}
        isSending={sendMessageMutation.isPending}
        userType={userType}
        userId={userId}
        activeChat={activeChat}
      />
    </div>
  );
}
