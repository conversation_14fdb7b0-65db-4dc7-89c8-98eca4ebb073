import { ChatItem } from './ChatItem';
import { Chat } from '~/api/features/chats/actions.ts';
import { ScrollArea } from '~/components/ui/scroll-area.tsx';

type ChatsListProps = {
  chats: Chat[];
  activeChatId: number | null;
  onChatSelect: (chatId: number) => void;
  userType: 'employer' | 'job_seeker';
  isLoading: boolean;
};

export function ChatsList({
  chats,
  activeChatId,
  onChatSelect,
  userType,
  isLoading,
}: ChatsListProps) {
  if (isLoading) {
    return (
      <div className="w-80 border-r bg-background border h-full flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-muted-foreground">Loading chats...</div>
        </div>
      </div>
    );
  }

  if (chats.length === 0) {
    return (
      <div className="w-80 border-r bg-background border h-full flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-muted-foreground text-center">
            <p>No chats yet</p>
            <p className="text-sm mt-1 text-muted-foreground">
              Start conversations from your matches
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-background h-full flex flex-col">
      <div className="flex-1 overflow-hidden border-r">
        <ScrollArea className="h-full">
          <div className="p-0">
            {chats.map(chat => (
              <ChatItem
                key={chat.id}
                chat={chat}
                isActive={activeChatId === chat.id}
                onClick={() => onChatSelect(chat.id)}
                userType={userType}
              />
            ))}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
