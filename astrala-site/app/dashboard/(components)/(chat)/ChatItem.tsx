'use client';

import { format } from 'date-fns';

import { Chat } from '~/api/features/chats/actions';
import UserIcon from '~/assets/icons/user.svg';

type ChatItemProps = {
  chat: Chat;
  isActive: boolean;
  onClick: () => void;
  userType: 'employer' | 'job_seeker';
};

export function ChatItem({ chat, isActive, onClick, userType }: ChatItemProps) {
  const getDisplayName = () => {
    if (userType === 'employer') {
      return chat.job_seeker?.full_name || 'Unknown Candidate';
    }
    return (
      chat.vacancy?.company?.name ||
      chat.vacancy?.employer?.full_name ||
      'Unknown Company'
    );
  };

  const getSubtitle = () => {
    if (userType === 'employer') {
      return chat.vacancy?.title || 'Unknown Position';
    }
    return chat.vacancy?.title || 'Unknown Position';
  };

  const formatLastMessage = () => {
    if (!chat.last_message) return 'No messages yet';

    const text = chat.last_message.message_text;
    const isMyMessage = chat.last_message.sender_type === userType;
    const prefix = isMyMessage ? 'You: ' : '';

    return prefix + text;
  };

  const formatTime = () => {
    if (!chat.last_message) return '';
    try {
      return format(new Date(chat.last_message.created_at), 'HH:mm');
    } catch {
      return '';
    }
  };

  const hasUnread = chat.unread_count && chat.unread_count > 0;

  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions
    <div
      className={`p-4 cursor-pointer hover:bg-primary/10 transition-colors ${
        isActive ? 'bg-primary/30 border-l-4 border-l-primary' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 p-4 bg-secondary rounded-full flex items-center justify-center">
          <UserIcon className="w-12 h-12 text-primary" />
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h3
              className={`text-md truncate ${
                hasUnread
                  ? 'font-bold text-foreground'
                  : 'font-semibold text-foreground'
              }`}
            >
              {getDisplayName()}
            </h3>
            {hasUnread && (
              <div className="w-2 h-2 bg-destructive rounded-full" />
            )}
          </div>
          <p
            className={`text-md truncate mt-1 ${
              hasUnread ? 'text-foreground' : 'text-muted-foreground'
            }`}
          >
            {getSubtitle()}
          </p>
          <p
            className={`text-xs truncate mt-2 max-w-[200px] ${
              hasUnread
                ? 'text-foreground font-medium'
                : 'text-muted-foreground'
            }`}
          >
            {formatLastMessage()}
          </p>
        </div>

        <div className="flex flex-col items-end gap-1">
          <span
            className={`text-xs ${
              hasUnread ? 'text-foreground' : 'text-muted-foreground'
            }`}
          >
            {formatTime()}
          </span>
        </div>
      </div>
    </div>
  );
}
