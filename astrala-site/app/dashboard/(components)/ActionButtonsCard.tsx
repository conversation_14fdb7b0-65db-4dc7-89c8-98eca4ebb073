'use client';

import { ReactNode } from 'react';

import { Card, CardContent, CardHeader } from '~/components/ui/card.tsx';

type FeedbackCardProps = {
  title: string;
  description: string;
  children: ReactNode;
  vertical?: boolean;
};

export function ActionButtonsCard({
  title,
  description,
  children,
  vertical = true,
}: FeedbackCardProps) {
  const direction = vertical ? 'flex-col' : 'flex-row';

  return (
    <Card className={`flex ${direction} gap-2`}>
      <CardHeader>
        <p className="font-bold text-xl">{title}</p>
        <p className="text-muted-foreground text-ьв whitespace-pre-line">
          {description}
        </p>
      </CardHeader>
      <CardContent className="space-y-2">{children}</CardContent>
    </Card>
  );
}
