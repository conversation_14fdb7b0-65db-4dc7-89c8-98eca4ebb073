import React from 'react';
import Image from 'next/image';

import PlaceholderImage from '~/assets/images/placeholder.png';

type PlaceholderProps = {
  title: string;
  description: string;
};

const Placeholder: React.FC<PlaceholderProps> = ({ title, description }) => {
  return (
    <div className="flex flex-col gap-2 items-center justify-center text-center">
      <p className="font-bold text-2xl">{title}</p>
      <p className="text-muted-foreground">{description}</p>
      <Image
        src={PlaceholderImage}
        width={190}
        height={150}
        alt="Placeholder"
      />
    </div>
  );
};

export default Placeholder;
