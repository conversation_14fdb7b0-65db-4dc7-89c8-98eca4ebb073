'use client';

import { useEffect, useState } from 'react';
import { MessageSquare, ThumbsDown, ThumbsUp } from 'lucide-react';

import { FeedbackType } from '~/api/features/feedbacks/actions';
import {
  useGetMatchFeedback,
  useSubmitMatchFeedback,
} from '~/api/features/feedbacks/queries';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Textarea } from '~/components/ui/textarea';
import { useToast } from '~/hooks/use-toast';

type MatchFeedbackProps = {
  matchId: number;
  viewMode: 'jobSeeker' | 'employer';
};

export function MatchFeedback({ matchId, viewMode }: MatchFeedbackProps) {
  const { toast } = useToast();
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackType | null>(
    null,
  );
  const [feedbackText, setFeedbackText] = useState('');
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  const { data: existingFeedbackResponse, isLoading: isLoadingFeedback } =
    useGetMatchFeedback(matchId);

  const submitFeedbackMutation = useSubmitMatchFeedback();

  useEffect(() => {
    if (existingFeedbackResponse?.success && existingFeedbackResponse.data) {
      const feedback = existingFeedbackResponse.data;
      if (viewMode === 'jobSeeker') {
        setSelectedFeedback(feedback.jobSeekerFeedbackType || null);
        setFeedbackText(feedback.jobSeekerFeedbackText || '');
      } else {
        setSelectedFeedback(feedback.employerFeedbackType || null);
        setFeedbackText(feedback.employerFeedbackText || '');
      }
    }
  }, [existingFeedbackResponse, viewMode]);

  const handleFeedbackTypeChange = (type: FeedbackType) => {
    setSelectedFeedback(type);
  };

  const handleSubmit = async () => {
    if (!selectedFeedback) return;

    try {
      await submitFeedbackMutation.mutateAsync({
        matchId,
        feedbackType: selectedFeedback,
        feedbackText: feedbackText.trim() || undefined,
        viewMode,
      });

      setFeedbackSubmitted(true);

      toast({
        title: 'Thank you!',
        description: 'Your feedback has been submitted.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit feedback. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const existingFeedback = existingFeedbackResponse?.data;
  const hasFeedback =
    existingFeedback &&
    (viewMode === 'jobSeeker'
      ? existingFeedback.jobSeekerFeedbackType
      : existingFeedback.employerFeedbackType);

  if (hasFeedback || feedbackSubmitted) {
    return null;
  }

  const isValid = selectedFeedback !== null;
  const isSubmitting = submitFeedbackMutation.isPending;

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="font-bold text-xl">
          {viewMode === 'jobSeeker' ? 'Rate this match' : 'Rate this candidate'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoadingFeedback && (
          <div className="text-sm text-muted-foreground text-center py-4">
            Loading feedback...
          </div>
        )}

        {!isLoadingFeedback && (
          <>
            <div className="flex gap-2">
              <Button
                type="button"
                size="sm"
                onClick={() => handleFeedbackTypeChange('like')}
                className="flex-1"
                variant={selectedFeedback === 'like' ? 'default' : 'secondary'}
                disabled={isSubmitting}
              >
                <div className="flex items-center gap-2">
                  <ThumbsUp className="h-4 w-4 mr-1" />
                  Like
                </div>
              </Button>
              <Button
                type="button"
                size="sm"
                onClick={() => handleFeedbackTypeChange('dislike')}
                className="flex-1"
                variant={
                  selectedFeedback === 'dislike' ? 'default' : 'secondary'
                }
                disabled={isSubmitting}
              >
                <div className="flex items-center gap-2">
                  <ThumbsDown className="h-4 w-4 mr-1" />
                  Dislike
                </div>
              </Button>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  Comment (optional)
                </span>
              </div>
              <Textarea
                placeholder={
                  viewMode === 'jobSeeker'
                    ? 'Share your thoughts about this job match...'
                    : 'Share your thoughts about this candidate...'
                }
                value={feedbackText}
                onChange={e => setFeedbackText(e.target.value)}
                className="min-h-[80px] text-sm"
                disabled={isSubmitting}
                maxLength={500}
              />
              {feedbackText.length > 0 && (
                <div className="text-xs text-muted-foreground text-right">
                  {feedbackText.length}/500
                </div>
              )}
            </div>

            <Button
              onClick={handleSubmit}
              disabled={!isValid || isSubmitting}
              className="w-full"
              variant="default"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
            </Button>

            {!selectedFeedback && (
              <p className="text-xs text-muted-foreground text-center">
                Please select like or dislike to submit feedback
              </p>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
