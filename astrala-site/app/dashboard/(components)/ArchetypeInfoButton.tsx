'use client';

import React, { useState } from 'react';
import { InfoIcon, MapPin } from 'lucide-react';

import { ArchetypeDetails } from '~/app/dashboard/(components)/ArchetypeDetails.tsx';

import {
  useGetArchetypePinnedStatus,
  useGetTopArchetype,
} from '~/api/features/archetypes/queries';
import UserIcon from '~/assets/icons/user.svg';
import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { Skeleton } from '~/components/ui/skeleton';

type ArchetypeInfoButtonProps = {
  jobSeekerId: number;
  jobSeekerName?: string;
  jobTitle?: string;
  location?: string;
};

export function ArchetypeInfoButton({
  jobSeekerId,
  jobSeekerName,
  jobTitle,
  location,
}: ArchetypeInfoButtonProps) {
  const [open, setOpen] = useState(false);

  const { data: archetypeDetail, isLoading: isLoadingDetail } =
    useGetTopArchetype(jobSeekerId);

  const { data: pinnedStatusData, isLoading: isLoadingPinned } =
    useGetArchetypePinnedStatus(jobSeekerId);

  const isLoading = isLoadingDetail || isLoadingPinned;
  const isPinned = pinnedStatusData?.pinned ?? true;

  const buttonText = isLoading
    ? 'Loading...'
    : archetypeDetail?.name || 'No data';

  if (!isPinned) return null;
  if (!archetypeDetail?.name) return null;

  const renderContent = () => {
    if (isLoading) {
      return (
        <div>
          <div className="flex items-center mb-6">
            <Skeleton className="w-16 h-16 rounded-full mr-4" />
            <div className="flex-1">
              <Skeleton className="h-6 w-40 mb-2" />
              <Skeleton className="h-4 w-60" />
            </div>
          </div>
          <Skeleton className="h-64 w-full" />
        </div>
      );
    }

    if (!archetypeDetail) {
      return (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            No archetype data available for this candidate
          </p>
        </div>
      );
    }

    return (
      <div>
        <div className="flex items-center mb-8">
          <div className="flex-shrink-0 p-4 bg-muted border rounded-full flex items-center justify-center mr-4">
            <UserIcon className="w-12 h-12 text-primary" />
          </div>
          <div className="flex flex-col justify-around flex-grow">
            <h2 className="text-2xl font-semibold">
              {jobSeekerName || 'Unnamed Candidate'}
            </h2>
            <div className="mt-1 flex flex-wrap items-center text-lg">
              {jobTitle && (
                <div className="flex items-center mr-4">
                  <InfoIcon className="w-5 h-5 mr-1 text-primary" />
                  <span>{jobTitle}</span>
                </div>
              )}
              {location && (
                <div className="flex items-center">
                  <MapPin className="w-5 h-5 mr-1 text-primary" />
                  <span>{location}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <ArchetypeDetails
          archetype={archetypeDetail}
          showRoles={false}
          showAbout={false}
        />
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default">{buttonText}</Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">
            Interview Fit Scorecard
          </DialogTitle>
        </DialogHeader>

        <div className="mt-6">{renderContent()}</div>
        <div className="mt-6 pt-4 border-t">
          <Button
            variant="default"
            className="w-full"
            onClick={() => {
              if (!archetypeDetail) return;

              const params = new URLSearchParams({
                name: jobSeekerName || 'Unnamed Candidate',
                jobTitle: jobTitle || '',
                location: location || '',
                archetypeName: archetypeDetail.name,
                description: archetypeDetail.description || '',
                metaphor: archetypeDetail.metaphor || '',
              });

              window.open(`/print/archetype?${params.toString()}`, '_blank');
            }}
          >
            Download Interview Fit Scorecard (PDF)
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
