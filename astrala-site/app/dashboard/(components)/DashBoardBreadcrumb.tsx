'use client';

import React from 'react';
import { usePathname } from 'next/navigation';

import { EmployerVacancySelector } from '~/app/dashboard/employer/(components)/EmployerVacancySelector.tsx';

import HomeIcon from '~/assets/icons/dashboard.svg';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '~/components/ui/breadcrumb.tsx';

const routeTitles: Record<string, string> = {
  'employer': 'Employer',
  'job-seeker': 'Job Seeker',
  'suggestions': 'Suggestions',
  'pending': 'Pending',
  'im-interested': "I'm Interested",
  'i-rejected': 'I Rejected',
  'company-interested': 'Company Interested',
  'company-rejected': 'Company Rejected',
  'job-seeker-interested': 'Job Seeker Interested',
  'job-seeker-rejected': 'Job Seeker Rejected',
  'matches': 'Matches',
  'waiting-for-payment': 'Waiting For Payment',
  'paid': 'Paid',
  'profile': 'Profile',
  'support': 'Support',
  'settings': 'Settings',
  'vacancies': 'Vacancies',
};

const isNumericId = (segment: string): boolean => {
  return /^\d+$/.test(segment);
};

export function DashBoardBreadcrumb({
  basePath,
  showVacancySelector = false,
}: {
  basePath: string;
  showVacancySelector?: boolean;
}) {
  const pathname = usePathname();
  if (pathname === basePath) return null;

  const pathSegments = pathname.split('/').filter(segment => segment);

  const breadcrumbItems = pathSegments
    .filter(segment => !isNumericId(segment))
    .map(segment => {
      const title =
        routeTitles[segment] ||
        segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');

      return { title };
    });

  const isVacanciesPage = pathname === '/dashboard/employer/vacancies';
  const isAddVacancyPage = pathname === '/dashboard/employer/add-vacancy';
  const isEditVacancyPage = pathname.includes(
    '/dashboard/employer/edit-vacancy/',
  );
  const isWorkStyleTestPage = pathname.includes(
    '/dashboard/employer/work-style-test/',
  );
  const isEditCompanyPage = pathname === '/dashboard/employer/settings';
  const isChatPage = pathname.startsWith('/dashboard/employer/chats');

  const shouldShowVacancySelector =
    showVacancySelector &&
    !isVacanciesPage &&
    !isAddVacancyPage &&
    !isEditVacancyPage &&
    !isEditCompanyPage &&
    !isWorkStyleTestPage &&
    !isChatPage;

  return (
    <Breadcrumb className="py-4 px-6 border-b bg-background w-full text-foreground">
      <div className="flex justify-between items-center w-full">
        <BreadcrumbList className="text-lg">
          <BreadcrumbItem>
            <BreadcrumbPage>
              <HomeIcon className="h-5 w-5 text-foreground" />
            </BreadcrumbPage>
          </BreadcrumbItem>
          {breadcrumbItems.slice(2).map(item => (
            <React.Fragment key={item.title}>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{item.title}</BreadcrumbPage>
              </BreadcrumbItem>
            </React.Fragment>
          ))}
        </BreadcrumbList>

        {shouldShowVacancySelector && <EmployerVacancySelector />}
      </div>
    </Breadcrumb>
  );
}
