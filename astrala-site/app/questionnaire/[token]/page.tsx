'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Loader2 } from 'lucide-react';

import QuestionnaireComponent from '~/shared/components/QuestionnaireComponent';
import SubmitOverlay from '~/shared/components/SubmitOverlay';

import {
  useQuestionnaireTokenData,
  useSavePublicQuestionnaireAnswers,
  useVacancyQuestionGroups,
} from '~/api/entities/vacancy/questionnaire/queries';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';

type QuestionnaireAnswer = {
  question_id: number;
  value: number;
};

export default function PublicQuestionnairePage({
  params,
}: {
  params: { token: string };
}) {
  const [answers, setAnswers] = useState<Map<number, number>>(new Map());
  const [currentGroupIndex, setCurrentGroupIndex] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  const {
    data: tokenData,
    isLoading: tokenLoading,
    error: tokenError,
  } = useQuestionnaireTokenData(params.token);

  const {
    data: questionData,
    isLoading: questionsLoading,
    error: questionsError,
  } = useVacancyQuestionGroups();

  const saveAnswersMutation = useSavePublicQuestionnaireAnswers();

  const groupedQuestions = useMemo(() => {
    if (!questionData) return [];

    return questionData.groups.map(group => ({
      id: group.id,
      name: group.name,
      created_at: group.created_at,
      questions: questionData.questions
        .filter(q => q.group_id === group.id)
        .map(q => ({
          id: q.id,
          name: q.name,
          group_id: q.group_id,
          created_at: q.created_at,
        })),
    }));
  }, [questionData]);

  useEffect(() => {
    if (questionData && groupedQuestions.length > 0 && answers.size === 0) {
      const initialAnswers = new Map<number, number>();
      groupedQuestions.forEach(group => {
        group.questions.forEach(question => {
          initialAnswers.set(question.id, 3);
        });
      });
      setAnswers(initialAnswers);
    }
  }, [questionData, groupedQuestions, answers.size]);

  const handleAnswerChange = useCallback(
    (questionId: number, value: number) => {
      setAnswers(prev => new Map(prev.set(questionId, value)));
    },
    [],
  );

  const handleNext = async () => {
    if (currentGroupIndex < groupedQuestions.length - 1) {
      setCurrentGroupIndex(prev => prev + 1);
    } else {
      const serializedAnswers: QuestionnaireAnswer[] = Array.from(
        answers.entries(),
        // eslint-disable-next-line @typescript-eslint/naming-convention
      ).map(([question_id, value]) => ({ question_id, value }));

      setIsSubmitting(true);

      try {
        await saveAnswersMutation.mutateAsync({
          token: params.token,
          answers: serializedAnswers,
        });
        setIsCompleted(true);
      } catch (error) {
        console.error('Error saving answers:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleBack = useCallback(() => {
    if (currentGroupIndex > 0) {
      setCurrentGroupIndex(prev => prev - 1);
    }
  }, [currentGroupIndex]);

  const getButtonText = () => {
    if (isSubmitting && currentGroupIndex === groupedQuestions.length - 1) {
      return 'Submitting...';
    }
    if (currentGroupIndex === groupedQuestions.length - 1) {
      return 'Submit';
    }
    return 'Next';
  };

  if (tokenLoading || questionsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="mt-4 ">Loading questionnaire...</p>
        </div>
      </div>
    );
  }

  if (tokenError || questionsError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Invalid or expired questionnaire link.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!tokenData || tokenData.completed_at) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Already Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This questionnaire has already been completed.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isCompleted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-primary">Thank You!</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Your responses have been submitted successfully.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const overlay = isSubmitting ? (
    <SubmitOverlay
      isSubmitting={true}
      message="Submitting your preferences..."
    />
  ) : null;

  return (
    <div className="min-h-screen">
      {overlay}

      <div className="container mx-auto py-8">
        <div className="mb-8 text-center">
          <h1 className="text-2xl font-bold mb-2">
            Behavioural Insight Preferences
          </h1>
          <p className="text-muted-foreground">
            for {tokenData?.vacancy?.title} role
          </p>
          {tokenData?.note && (
            <div className="mt-4 p-4 bg-secondary rounded-lg max-w-2xl mx-auto">
              <p className="text-sm text-foreground">{tokenData.note}</p>
            </div>
          )}
        </div>

        <div className="w-full flex justify-center">
          <div className="w-full max-w-4xl">
            <QuestionnaireComponent
              groups={groupedQuestions}
              currentGroupIndex={currentGroupIndex}
              answers={answers}
              answerTags={questionData?.answerTags || []}
              description="Please answer the following questions to help define the ideal personality traits for this role."
              isLoading={false}
              error={null}
              isSubmitting={isSubmitting}
              onAnswerChange={handleAnswerChange}
              onNext={handleNext}
              onBack={handleBack}
              getNextButtonText={getButtonText}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
