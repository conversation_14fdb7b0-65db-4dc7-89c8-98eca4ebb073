import React from 'react';
import { Check } from 'lucide-react';

import { Button } from '~/components/ui/button.tsx';

export type Section = {
  id: string;
  label: string;
  ref?: React.RefObject<HTMLDivElement>;
};

type FormSectionTabProps = {
  id: string;
  label: string;
  isActive: boolean;
  isCompleted: boolean;
  onClick: (id: string) => void;
};

export const FormSectionTab: React.FC<FormSectionTabProps> = ({
  id,
  label,
  isActive,
  isCompleted,
  onClick,
}) => {
  return (
    <Button
      variant={isActive ? 'default' : 'outline'}
      className="h-12"
      onClick={() => onClick(id)}
    >
      <div className="flex items-center">
        {isCompleted ? (
          <span className="w-5 h-5 rounded-full text-primary-foreground flex items-center justify-center mr-2 flex-shrink-0">
            <Check className="w-5 h-5" />
          </span>
        ) : null}
        {label}
      </div>
    </Button>
  );
};

type FormSectionTabsProps = {
  sections: Section[];
  activeSection: string;
  completedSections: string[];
  onTabClick: (sectionId: string) => void;
};

export const FormSectionTabs: React.FC<FormSectionTabsProps> = ({
  sections,
  activeSection,
  completedSections,
  onTabClick,
}) => {
  return (
    <div className="sticky top-0 z-50 bg-background border-b w-full py-4">
      <div className="overflow-x-auto scrollbar-hide" style={{ width: '100%' }}>
        <div
          className="flex flex-row flex-nowrap justify-center gap-4"
          style={{ minWidth: 'max-content' }}
        >
          {sections.map(section => (
            <FormSectionTab
              key={section.id}
              id={section.id}
              label={section.label}
              isActive={activeSection === section.id}
              isCompleted={completedSections.includes(section.id)}
              onClick={onTabClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
