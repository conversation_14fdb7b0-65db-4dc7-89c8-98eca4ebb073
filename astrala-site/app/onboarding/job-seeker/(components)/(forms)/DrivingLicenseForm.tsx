import React, { useState } from 'react';
import { Control, UseFormSetValue } from 'react-hook-form';

import { MainInformationFormSchema } from '../../(schemas)/mainInformation.schema';

import { Button } from '~/components/ui/button';
import { FormLabel } from '~/components/ui/form.tsx';

type DrivingLicenseFormProps = {
  control: Control<MainInformationFormSchema>;
  setValue: UseFormSetValue<MainInformationFormSchema>;
};

export const DrivingLicenseForm: React.FC<DrivingLicenseFormProps> = ({
  setValue,
}) => {
  const [hasLicense, setHasLicense] = useState<boolean | null>(null);

  const handleSelection = (value: boolean) => {
    setHasLicense(value);
    setValue('has_driving_license', value);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-medium">Driving License</h2>

      <div className="space-y-3">
        <FormLabel>Do you have a driving license?</FormLabel>

        <div className="flex space-x-4">
          <Button
            type="button"
            variant={hasLicense === true ? 'default' : 'outline'}
            onClick={() => handleSelection(true)}
          >
            Yes
          </Button>

          <Button
            type="button"
            variant={hasLicense === false ? 'default' : 'outline'}
            onClick={() => handleSelection(false)}
          >
            No
          </Button>
        </div>
      </div>
    </div>
  );
};
