import React from 'react';
import { Control, useFieldArray } from 'react-hook-form';
import { Trash2 } from 'lucide-react';

import { MainInformationFormSchema } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema.ts';

import { But<PERSON> } from '~/components/ui/button.tsx';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form.tsx';
import { Input } from '~/components/ui/input.tsx';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select.tsx';
import { Textarea } from '~/components/ui/textarea.tsx';

type WorkExperienceFormProps = {
  control: Control<MainInformationFormSchema>;
};

const MONTHS = [
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' },
];

export const WorkExperienceForm: React.FC<WorkExperienceFormProps> = ({
  control,
}) => {
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'workExperience',
  });

  const currentMonth = new Date().getMonth() + 1;
  const currentYear = new Date().getFullYear();

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium">Work Experience</h2>
        <Button
          type="button"
          variant="default"
          size="sm"
          className="ml-auto"
          onClick={() =>
            append({
              companyName: '',
              jobTitle: '',
              startMonth: currentMonth,
              startYear: currentYear - 1,
              endMonth: currentMonth,
              endYear: currentYear,
              comment: '',
            })
          }
        >
          Add Work Experience
        </Button>
      </div>

      {fields.map((field, index) => (
        <div
          key={field.id}
          className="space-y-4 border p-4 rounded-lg mb-4 relative"
        >
          {index > 0 && (
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 text-destructive"
              onClick={() => remove(index)}
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          )}

          <FormField
            control={control}
            name={`workExperience.${index}.companyName`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Name</FormLabel>
                <FormControl>
                  <Input placeholder="Company name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name={`workExperience.${index}.jobTitle`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Job Title</FormLabel>
                <FormControl>
                  <Input placeholder="Job Title" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <FormLabel>Start Date</FormLabel>
              <div className="grid grid-cols-2 gap-2">
                <FormField
                  control={control}
                  name={`workExperience.${index}.startMonth`}
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        value={field.value.toString()}
                        onValueChange={value => field.onChange(Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Month" />
                        </SelectTrigger>
                        <SelectContent>
                          {MONTHS.map(month => (
                            <SelectItem
                              key={month.value}
                              value={month.value.toString()}
                            >
                              {month.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name={`workExperience.${index}.startYear`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="2024"
                          min={1940}
                          max={new Date().getFullYear()}
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                          value={field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div>
              <FormLabel>End Date</FormLabel>
              <div className="grid grid-cols-2 gap-2">
                <FormField
                  control={control}
                  name={`workExperience.${index}.endMonth`}
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        value={field?.value != null ? String(field.value) : ''}
                        onValueChange={value => field.onChange(Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Month" />
                        </SelectTrigger>
                        <SelectContent>
                          {MONTHS.map(month => (
                            <SelectItem
                              key={month.value}
                              value={month.value.toString()}
                            >
                              {month.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name={`workExperience.${index}.endYear`}
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="2025"
                          min={1940}
                          max={new Date().getFullYear() + 10}
                          {...field}
                          onChange={e => field.onChange(Number(e.target.value))}
                          value={field.value ?? ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          <FormField
            control={control}
            name={`workExperience.${index}.comment`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Comment</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Text about work experience"
                    className="min-h-24 resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      ))}
    </div>
  );
};
