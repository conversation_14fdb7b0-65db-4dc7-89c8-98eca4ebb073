import React, { useEffect, useRef, useState } from 'react';
import {
  Control,
  FieldValues,
  Path,
  PathValue,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import {
  ChevronsUpDown,
  FileText,
  Loader2,
  Plus,
  Trash2,
  Upload,
} from 'lucide-react';

import { Certification } from '../../(schemas)/mainInformation.schema';

import { useCertificationsManager } from '~/api/entities/certification/queries.ts';
import { Button } from '~/components/ui/button.tsx';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command';
import { FormField, FormItem, FormMessage } from '~/components/ui/form.tsx';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '~/components/ui/popover';

type FormWithCertifications = {
  certifications: Certification[];
};

type CertificationFormProps<T extends FieldValues & FormWithCertifications> = {
  control: Control<T>;
  setValue: UseFormSetValue<T>;
  watch: UseFormWatch<T>;
};

type CertType = 'professional' | 'membership' | 'health_safety';

const CertificationSection = ({
  title,
  placeholder,
  filteredGroups,
  selectedCertifications,
  onAddExisting,
  onCreateNew,
  onRemoveCertification,
  onFileUpload,
  onUploadClick,
  isAddingCertification,
  isRemovingCertification,
  isUploadingFile,
  allowCustom = false,
  fileInputRefs,
  getFileName,
  renderFileIcon,
}: {
  title: string;
  placeholder: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  filteredGroups: any[];
  selectedCertifications: Certification[];
  onAddExisting: (cert: Certification) => void;
  onCreateNew: (value: string) => void;
  onRemoveCertification: (id: number) => void;
  onFileUpload: (id: number, files: FileList | null) => void;
  onUploadClick: (id: number) => void;
  isAddingCertification: boolean;
  isRemovingCertification: boolean;
  isUploadingFile: boolean;
  allowCustom?: boolean;
  fileInputRefs: React.MutableRefObject<{
    [key: number]: HTMLInputElement | null;
  }>;
  getFileName: (cert: Certification) => string | null;
  renderFileIcon: () => React.ReactElement;
}) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleSelect = (cert: Certification) => {
    onAddExisting(cert);
    setOpen(false);
    setSearchQuery('');
  };

  const handleCreateNew = () => {
    if (searchQuery.trim() && allowCustom) {
      onCreateNew(searchQuery.trim());
      setOpen(false);
      setSearchQuery('');
    }
  };

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{title}</h3>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-background text-foreground border hover:bg-secondary hover:text-foreground"
            disabled={isAddingCertification}
          >
            {placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50 text-foreground" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0 bg-background border">
          <Command
            shouldFilter={false}
            className="bg-background text-foreground"
          >
            <CommandInput
              placeholder={`Search for ${title.toLowerCase().slice(4)}...`}
              value={searchQuery}
              onValueChange={setSearchQuery}
              className="bg-background text-foreground placeholder:text-muted-foreground"
            />
            <CommandList className="bg-background text-foreground max-h-60">
              {allowCustom && searchQuery.trim().length >= 2 && (
                <CommandGroup className="text-foreground">
                  <CommandItem
                    onSelect={handleCreateNew}
                    className="cursor-pointer text-primary hover:bg-secondary hover:text-primary focus:bg-secondary focus:text-primary"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add "{searchQuery}"
                  </CommandItem>
                </CommandGroup>
              )}

              {filteredGroups.map(group => {
                const visibleCerts = group.certifications.filter(
                  (cert: Certification) =>
                    !searchQuery ||
                    cert.name.toLowerCase().includes(searchQuery.toLowerCase()),
                );

                if (visibleCerts.length === 0) return null;

                return (
                  <CommandGroup
                    key={group.id}
                    heading={group.name}
                    className="text-foreground"
                  >
                    {visibleCerts.map((cert: Certification) => (
                      <CommandItem
                        key={cert.id}
                        onSelect={() => handleSelect(cert)}
                        className="cursor-pointer text-foreground bg-background hover:text-foreground focus:bg-secondary focus:text-foreground"
                      >
                        <Plus className="mr-2 h-4 w-4 text-primary" />
                        {cert.name}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                );
              })}

              {filteredGroups.every(
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (group: any) =>
                  group.certifications.filter(
                    (cert: Certification) =>
                      !searchQuery ||
                      cert.name
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase()),
                  ).length === 0,
              ) && (
                <CommandEmpty className="text-foreground p-2">
                  No certifications found.{' '}
                  {allowCustom ? 'You can add a new one.' : ''}
                </CommandEmpty>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedCertifications.length > 0 && (
        <div className="space-y-2 mt-3">
          {selectedCertifications.map((certification: Certification) => {
            const fileName = getFileName(certification);

            return (
              <div
                key={certification.id}
                className="p-3 border rounded-md flex items-center justify-between bg-background"
              >
                <div className="flex items-center gap-2">
                  <span>{certification.name}</span>

                  {fileName && (
                    <div className="text-primary flex items-center gap-1">
                      <FileText className="h-4 w-4" />
                      <span className="text-xs">{fileName}</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    size="sm"
                    onClick={() => onUploadClick(certification.id)}
                    disabled={isUploadingFile}
                    className="gap-1"
                  >
                    {renderFileIcon()}
                    <span className="text-xs">
                      {fileName ? 'Replace' : 'Upload'}
                    </span>

                    <input
                      id={`file-upload-${certification.id}`}
                      type="file"
                      className="hidden"
                      ref={(el: HTMLInputElement | null) => {
                        fileInputRefs.current[certification.id] = el;
                      }}
                      onChange={e =>
                        onFileUpload(certification.id, e.target.files)
                      }
                      accept=".pdf,.doc,.docx"
                    />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onRemoveCertification(certification.id)}
                    disabled={isRemovingCertification}
                    className="text-foreground"
                  >
                    {isRemovingCertification ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      <Trash2 className="h-5 w-5 " />
                    )}
                  </Button>
                </div>
              </div>
            );
          })}
          <p className="text-xs text-muted-foreground">
            Supported formats: PDF, DOC, DOCX - Max File Size 2MB
          </p>
        </div>
      )}
    </div>
  );
};

export const CertificationForm = <
  T extends FieldValues & FormWithCertifications,
>({
  control,
  setValue,
  watch,
}: CertificationFormProps<T>): React.ReactElement => {
  const fileInputRefs = useRef<{ [key: number]: HTMLInputElement | null }>({});

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const formCertifications: Certification[] =
    watch('certifications' as Path<T>) || [];

  const {
    getFilteredGroups,
    selectedCertifications,
    setSelectedCertifications,
    getCertificationsByType,
    addCertificationToList,
    createAndAddNewCertification,
    removeCertification,
    uploadFile,
    isAddingCertification,
    isRemovingCertification,
    isUploadingFile,
  } = useCertificationsManager();

  useEffect(() => {
    if (
      formCertifications.length === 0 &&
      selectedCertifications.length > 0 &&
      !isRemovingCertification
    ) {
      setValue(
        'certifications' as Path<T>,
        selectedCertifications as PathValue<T, Path<T>>,
      );
    }
  }, [
    formCertifications.length,
    selectedCertifications,
    setValue,
    isRemovingCertification,
  ]);

  useEffect(() => {
    if (
      formCertifications.length > 0 &&
      selectedCertifications.length === 0 &&
      !isRemovingCertification
    ) {
      setSelectedCertifications(formCertifications);
    }
  }, [
    formCertifications,
    selectedCertifications.length,
    setSelectedCertifications,
    isRemovingCertification,
  ]);

  const handleAddExistingCertification = async (
    certification: Certification,
    type: CertType,
  ) => {
    const result = await addCertificationToList(certification, type);
    if (result) {
      setValue(
        'certifications' as Path<T>,
        [...formCertifications, certification] as PathValue<T, Path<T>>,
      );
    }
  };

  const handleCreateNewCertification = async (name: string) => {
    const newCertification = await createAndAddNewCertification(
      name,
      'professional',
    );
    if (newCertification) {
      setValue(
        'certifications' as Path<T>,
        [...formCertifications, newCertification] as PathValue<T, Path<T>>,
      );
    }
  };

  const handleRemoveCertification = async (certificationId: number) => {
    const success = await removeCertification(certificationId);
    if (success) {
      const updatedCertifications = formCertifications.filter(
        certification => certification.id !== certificationId,
      );

      setValue(
        'certifications' as Path<T>,
        updatedCertifications as PathValue<T, Path<T>>,
      );

      setSelectedCertifications(updatedCertifications);
    }
  };

  const handleFileUpload = async (
    certificationId: number,
    files: FileList | null,
  ) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    if (file.size > 2 * 1024 * 1024) {
      console.error('File size exceeds 2MB limit');
      return;
    }

    try {
      const success = await uploadFile(certificationId, file);
      if (success) {
        setValue(
          'certifications' as Path<T>,
          formCertifications.map(cert =>
            cert.id === certificationId ? { ...cert, file } : cert,
          ) as PathValue<T, Path<T>>,
        );
      }
    } catch (error) {
      console.error('Error during file attachment:', error);
    }
  };

  const handleUploadClick = (certificationId: number) => {
    const fileInput = fileInputRefs.current[certificationId];
    if (fileInput) {
      fileInput.click();
    }
  };

  const getFileName = (certification: Certification) => {
    if (certification.file?.name) return certification.file.name;
    if (certification.file_path) {
      const pathParts = certification.file_path.split('/');
      return pathParts[pathParts.length - 1];
    }
    return null;
  };

  const renderFileIcon = () => {
    if (isUploadingFile) {
      return <Loader2 className="h-3 w-3 animate-spin" />;
    }
    return <Upload className="h-3 w-3" />;
  };

  return (
    <div className="space-y-6 p-2">
      <h2 className="text-lg font-medium">Certification</h2>

      <FormField
        control={control}
        name={'certifications' as Path<T>}
        render={() => (
          <FormItem>
            <div className="space-y-6">
              <CertificationSection
                title="Add Professional Certificates"
                placeholder="Search for professional certificates"
                filteredGroups={getFilteredGroups('professional')}
                selectedCertifications={getCertificationsByType('professional')}
                onAddExisting={cert =>
                  handleAddExistingCertification(cert, 'professional')
                }
                onCreateNew={handleCreateNewCertification}
                onRemoveCertification={handleRemoveCertification}
                onFileUpload={handleFileUpload}
                onUploadClick={handleUploadClick}
                isAddingCertification={isAddingCertification}
                isRemovingCertification={isRemovingCertification}
                isUploadingFile={isUploadingFile}
                allowCustom={true}
                fileInputRefs={fileInputRefs}
                getFileName={getFileName}
                renderFileIcon={renderFileIcon}
              />

              <CertificationSection
                title="Add Professional Memberships"
                placeholder="Search for professional memberships"
                filteredGroups={getFilteredGroups('membership')}
                selectedCertifications={getCertificationsByType('membership')}
                onAddExisting={cert =>
                  handleAddExistingCertification(cert, 'membership')
                }
                onCreateNew={() => {}}
                onRemoveCertification={handleRemoveCertification}
                onFileUpload={handleFileUpload}
                onUploadClick={handleUploadClick}
                isAddingCertification={isAddingCertification}
                isRemovingCertification={isRemovingCertification}
                isUploadingFile={isUploadingFile}
                allowCustom={false}
                fileInputRefs={fileInputRefs}
                getFileName={getFileName}
                renderFileIcon={renderFileIcon}
              />

              <CertificationSection
                title="Add Health & Safety Certifications"
                placeholder="Search for health & safety certifications"
                filteredGroups={getFilteredGroups('health_safety')}
                selectedCertifications={getCertificationsByType(
                  'health_safety',
                )}
                onAddExisting={cert =>
                  handleAddExistingCertification(cert, 'health_safety')
                }
                onCreateNew={() => {}}
                onRemoveCertification={handleRemoveCertification}
                onFileUpload={handleFileUpload}
                onUploadClick={handleUploadClick}
                isAddingCertification={isAddingCertification}
                isRemovingCertification={isRemovingCertification}
                isUploadingFile={isUploadingFile}
                allowCustom={false}
                fileInputRefs={fileInputRefs}
                getFileName={getFileName}
                renderFileIcon={renderFileIcon}
              />
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
