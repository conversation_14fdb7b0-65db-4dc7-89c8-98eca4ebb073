import React from 'react';
import { Control } from 'react-hook-form';

import { MainInformationFormSchema } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema.ts';

import { CountrySelect } from '~/shared/components/forms/CountrySelect';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form.tsx';
import { Input } from '~/components/ui/input.tsx';
import { PhoneInput } from '~/components/ui/phone-input';

type PersonalInfoFormProps = {
  control: Control<MainInformationFormSchema>;
};

export const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({
  control,
}) => {
  return (
    <div className="space-y-4 p-1">
      <h2 className="text-lg font-medium">Personal Information</h2>

      <FormField
        control={control}
        name="personalInfo.fullName"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Full Name</FormLabel>
            <FormControl>
              <Input placeholder="John Doe" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="personalInfo.jobTitle"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Job Title</FormLabel>
            <FormControl>
              <Input placeholder="Job Title" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="personalInfo.phone"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Phone</FormLabel>
            <FormControl>
              <PhoneInput
                placeholder="Enter phone number"
                {...field}
                onChange={value => field.onChange(value)}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-2 gap-4">
        <CountrySelect
          control={control}
          name="personalInfo.country"
          label="Country"
        />

        <FormField
          control={control}
          name="personalInfo.city"
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Input placeholder="City" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
