'use client';

import React, { useState } from 'react';
import { ArrowRight, Loader2 } from 'lucide-react';

import CVParsingProgress, {
  ParsingStatus,
} from '~/app/onboarding/job-seeker/(components)/CVParsingProgress';
import { useOnboardingStore } from '~/app/onboarding/job-seeker/(store)/useOnboardingStore';
import { ParsedResume } from '~/app/onboarding/job-seeker/page';

import { FileUpload } from '~/shared/components/forms/FileUpload';

import { useUploadResume } from '~/api/entities/job-seeker/queries';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import { CardContent, CardHeader } from '~/components/ui/card';
import { useAuth } from '~/providers/AuthProvider';

type UploadCVStepProps = {
  onComplete: (parsedData?: ParsedResume) => void;
};

const UploadCVStep: React.FC<UploadCVStepProps> = ({ onComplete }) => {
  const { jobSeekerProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [parsingStatus, setParsingStatus] = useState<ParsingStatus>('idle');
  const [error, setError] = useState<string | null>(null);

  const {
    resumeFile,
    setResumeFile,
    setParsedResumeData,
    setResumeDataApplied,
  } = useOnboardingStore();

  const uploadResumeMutation = useUploadResume();

  const handleFilesChange = (newFiles: File[]) => {
    if (newFiles.length > 0) {
      setResumeFile(newFiles[0]);
    } else {
      setResumeFile(null);
    }
    setError(null);
    setParsingStatus('idle');
  };

  const handleProgressComplete = () => {
    setLoading(false);
  };

  const handleUpload = async () => {
    if (!resumeFile || !jobSeekerProfile?.id) return;

    try {
      setLoading(true);
      setParsingStatus('uploading');
      setError(null);

      setParsedResumeData(undefined);
      setResumeDataApplied(false);

      const authUserId = jobSeekerProfile.user_id;

      if (!authUserId) {
        throw new Error('User ID not found');
      }

      await new Promise(resolve => setTimeout(resolve, 1500));

      setParsingStatus('parsing');

      await new Promise(resolve => setTimeout(resolve, 3000));

      const result = await uploadResumeMutation.mutateAsync({
        file: resumeFile,
        userId: authUserId,
        jobSeekerId: jobSeekerProfile.id,
      });

      if (!result.success) {
        throw new Error(result.error || 'Cannot upload file, try again later');
      }

      if (result.parsed_data) {
        setParsingStatus('success');
        setParsedResumeData(result.parsed_data);

        setTimeout(() => {
          onComplete(result.parsed_data);
        }, 1200);
      } else {
        console.warn('Resume uploaded but parsing returned no data');
        setParsingStatus('error');
        setError(
          result.error ||
            'Resume parsing failed, but upload was successful. Continuing without auto-fill.',
        );
        setLoading(false);
        setTimeout(() => {
          onComplete();
        }, 2000);
      }
    } catch (error_: unknown) {
      setLoading(false);
      setParsingStatus('error');
      setError(
        error_ instanceof Error ? error_.message : 'Unknown error occurred',
      );
    }
  };

  const getButtonLoadingText = () => {
    if (parsingStatus === 'uploading') {
      return 'Uploading';
    }

    if (parsingStatus === 'parsing') {
      return 'Analyzing';
    }

    if (parsingStatus === 'success') {
      return 'Completing';
    }

    return 'Processing';
  };

  return (
    <>
      <div className="text-center mb-8 mt-8">
        <h1 className="text-2xl font-bold mb-2">Welcome to Astrala! 👋</h1>
        <p className="text-secondary-foreground">
          First, you need to go through the steps that will help you use the app
        </p>
      </div>
      <div className="flex justify-center items-center w-full">
        <div className="w-[50%]">
          <CardHeader>
            <h2 className="text-center font-semibold">Upload your CV</h2>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <FileUpload
                onChange={handleFilesChange}
                value={resumeFile ? [resumeFile] : []}
              />
            </div>

            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <CVParsingProgress
              status={parsingStatus}
              onComplete={handleProgressComplete}
              className="mb-4"
            />

            <Button
              variant="default"
              className="w-full"
              onClick={handleUpload}
              disabled={!resumeFile || loading}
            >
              {loading ? (
                <div className=" flex items-center justify-center">
                  {getButtonLoadingText()}
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                </div>
              ) : (
                <div className=" flex items-center justify-center">
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </div>
              )}
            </Button>
          </CardContent>
        </div>
      </div>
    </>
  );
};

export default UploadCVStep;
