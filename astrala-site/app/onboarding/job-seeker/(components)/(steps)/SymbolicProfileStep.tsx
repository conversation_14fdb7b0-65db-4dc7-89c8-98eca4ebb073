'use client';

import React, { useState } from 'react';
import { InfoIcon, Loader2 } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';

import { ArchetypeDetails } from '~/app/dashboard/(components)/ArchetypeDetails.tsx';

import {
  useGetArchetypePinnedStatus,
  useGetTopArchetype,
  useToggleArchetypePinned,
} from '~/api/features/archetypes/queries';
import { useCompleteJobSeekerOnboarding } from '~/api/features/onboarding/queries';
import { userKeys } from '~/api/features/user/queries';
import UserIcon from '~/assets/icons/user.svg';
import { Button } from '~/components/ui/button';
import { Card, CardContent } from '~/components/ui/card';
import { useToast } from '~/hooks/use-toast';
import { useAuth } from '~/providers/AuthProvider';

type SymbolicProfileStepProps = {
  onComplete: () => void;
};

const SymbolicProfileStep: React.FC<SymbolicProfileStepProps> = ({
  onComplete,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { jobSeekerProfile } = useAuth();
  const jobSeekerId = jobSeekerProfile?.id as number | undefined;

  const [isFinishing, setIsFinishing] = useState(false);

  const { data: archetypeDetail, isLoading: isLoadingDetail } =
    useGetTopArchetype(jobSeekerId);

  const { data: pinnedStatusData, isLoading: isLoadingPinned } =
    useGetArchetypePinnedStatus(jobSeekerId);

  const togglePinnedMutation = useToggleArchetypePinned();
  const completeOnboardingMutation = useCompleteJobSeekerOnboarding();

  const isLoading = isLoadingDetail || isLoadingPinned;
  const isPinned = pinnedStatusData?.pinned ?? true;

  const handleTogglePin = async () => {
    if (!jobSeekerId) return;

    try {
      await togglePinnedMutation.mutateAsync(jobSeekerId);
      toast({
        title: isPinned ? 'Unpinned' : 'Pinned',
        description: isPinned
          ? 'Archetype unpinned from profile'
          : 'Archetype pinned to profile',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update pin status',
        variant: 'destructive',
      });
    }
  };

  const handleFinish = async () => {
    if (!jobSeekerId) return;

    try {
      setIsFinishing(true);

      await completeOnboardingMutation.mutateAsync(jobSeekerId);

      if (jobSeekerProfile?.user_id) {
        await queryClient.invalidateQueries({
          queryKey: userKeys.profile(jobSeekerProfile.user_id),
        });
      }

      setIsFinishing(false);
      onComplete();
    } catch (error) {
      console.error(error);
      setIsFinishing(false);
      toast({
        title: 'Error',
        description: 'Failed to complete onboarding',
        variant: 'destructive',
      });
    }
  };

  const getPinButtonText = () => {
    if (togglePinnedMutation.isPending) {
      return 'Updating...';
    }
    return isPinned ? 'Unpin from Profile' : 'Pin to Profile';
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
        <p>Loading your profile...</p>
      </div>
    );
  }

  if (!archetypeDetail) {
    return (
      <div className="flex justify-center items-center h-full p-8">
        <p>No archetype data available.</p>
      </div>
    );
  }

  return (
    <div className="w-full flex justify-center items-center">
      <div className="w-[60%] my-6">
        <Card>
          <CardContent className="p-8">
            <div className="flex items-start justify-between mb-8">
              <div className="flex items-center">
                <div className="flex-shrink-0 p-4 bg-muted border rounded-full flex items-center justify-center mr-4">
                  <UserIcon className="w-12 h-12 text-primary" />
                </div>
                <div className="flex flex-col justify-around flex-grow">
                  <h2 className="text-2xl font-semibold">
                    {jobSeekerProfile?.full_name || 'Unnamed Candidate'}
                  </h2>
                  <div className="mt-1 flex flex-wrap items-center text-lg">
                    {jobSeekerProfile?.job_title && (
                      <div className="flex items-center mr-4">
                        <InfoIcon className="w-5 h-5 mr-1 text-primary" />
                        <span>{jobSeekerProfile.job_title}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <Button
                variant={isPinned ? 'destructive' : 'default'}
                onClick={handleTogglePin}
                disabled={togglePinnedMutation.isPending}
              >
                {getPinButtonText()}
              </Button>
            </div>

            <ArchetypeDetails archetype={archetypeDetail} />

            <div className="mt-8 pt-6 border-t">
              <Button
                variant="secondary"
                className="w-full mb-4"
                onClick={() => {
                  const params = new URLSearchParams({
                    name: jobSeekerProfile?.full_name || 'Unnamed Candidate',
                    jobTitle: jobSeekerProfile?.job_title || '',
                    location:
                      `${jobSeekerProfile?.city || ''}, ${jobSeekerProfile?.country || ''}`.trim(),
                    archetypeName: archetypeDetail.name,
                    description: archetypeDetail.description || '',
                    metaphor: archetypeDetail.metaphor || '',
                    dimensionsIntro: archetypeDetail.dimensions_intro || '',
                    wheelDescription: archetypeDetail.wheel_description || '',
                    roleAlignments:
                      archetypeDetail.role_alignments?.join('|||') || '',
                    roleDescription: archetypeDetail.role_description || '',
                    aboutProfile: archetypeDetail.about_profile || '',
                  });

                  const newWindow = window.open(
                    `/print/archetype?${params.toString()}`,
                    '_blank',
                  );

                  if (
                    !newWindow ||
                    newWindow.closed ||
                    typeof newWindow.closed === 'undefined'
                  ) {
                    toast({
                      title: 'Popup blocked',
                      description: 'Please allow popups for this site',
                      variant: 'destructive',
                    });
                  }
                }}
              >
                Download Interview Fit Scorecard (PDF)
              </Button>

              <Button
                variant="default"
                className="w-full"
                onClick={handleFinish}
                disabled={isFinishing}
              >
                {isFinishing ? 'Completing...' : 'Finish Registration'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SymbolicProfileStep;
