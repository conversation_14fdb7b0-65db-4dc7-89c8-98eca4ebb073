'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { debounce } from 'lodash';
import { zodResolver } from '@hookform/resolvers/zod';

import { CertificationForm } from '~/app/onboarding/job-seeker/(components)/(forms)/CertificationForm';
import { DrivingLicenseForm } from '~/app/onboarding/job-seeker/(components)/(forms)/DrivingLicenseForm';
import { EducationForm } from '~/app/onboarding/job-seeker/(components)/(forms)/EducationForm';
import { PersonalInfoForm } from '~/app/onboarding/job-seeker/(components)/(forms)/PersonalInfoForm';
import { WorkExperienceForm } from '~/app/onboarding/job-seeker/(components)/(forms)/WorkExperienceForm';
import { FormSectionTabs } from '~/app/onboarding/job-seeker/(components)/FormSectionTabs';
import { useResumeParser } from '~/app/onboarding/job-seeker/(hooks)/useResumeParser';
import { useOnboardingStore } from '~/app/onboarding/job-seeker/(store)/useOnboardingStore';
import { ParsedResume } from '~/app/onboarding/job-seeker/page';

import { LanguageForm } from '~/shared/components/forms/LanguageForm';
import { SalaryForm } from '~/shared/components/forms/SalaryForm';
import { SkillsForm } from '~/shared/components/forms/SkillsForm';
import { SectionContainer } from '~/shared/SectionContainer';

import {
  MainInformationFormSchema,
  mainInformationFormSchema,
} from '../../(schemas)/mainInformation.schema';

import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import { Form } from '~/components/ui/form';
import { useToast } from '~/hooks/use-toast.ts';
import { useAuth } from '~/providers/AuthProvider';

type MainInformationStepProps = {
  onComplete: () => void;
  parsedResumeData?: ParsedResume;
};

const MainInformationStep: React.FC<MainInformationStepProps> = ({
  onComplete,
  parsedResumeData,
}) => {
  const { toast } = useToast();
  const { jobSeekerProfile } = useAuth();
  const [activeSection, setActiveSection] = useState('personalInfo');
  const [completedSections, setCompletedSections] = useState<string[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const formInitialized = useRef(false);

  const { formData, setFormData, updateFormData, isSubmitting } =
    useOnboardingStore();

  const sectionRefs = {
    personalInfo: useRef<HTMLDivElement>(null),
    education: useRef<HTMLDivElement>(null),
    workExperience: useRef<HTMLDivElement>(null),
    certifications: useRef<HTMLDivElement>(null),
    language: useRef<HTMLDivElement>(null),
    skills: useRef<HTMLDivElement>(null),
    salary: useRef<HTMLDivElement>(null),
    drivingLicense: useRef<HTMLDivElement>(null),
  };

  const sections = [
    {
      id: 'personalInfo',
      label: 'Personal Info',
      ref: sectionRefs.personalInfo,
    },
    { id: 'education', label: 'Education', ref: sectionRefs.education },
    {
      id: 'workExperience',
      label: 'Work Experience',
      ref: sectionRefs.workExperience,
    },
    {
      id: 'certifications',
      label: 'Certification',
      ref: sectionRefs.certifications,
    },
    { id: 'language', label: 'Language', ref: sectionRefs.language },
    { id: 'skills', label: 'Skill', ref: sectionRefs.skills },
    { id: 'salary', label: 'Salary Range', ref: sectionRefs.salary },
    {
      id: 'drivingLicense',
      label: 'Driving License',
      ref: sectionRefs.drivingLicense,
    },
  ];

  const form = useForm<MainInformationFormSchema>({
    resolver: zodResolver(mainInformationFormSchema),
    defaultValues: formData,
    mode: 'onChange',
    shouldFocusError: true,
  });

  const { resumeDataApplied } = useResumeParser({
    form,
    parsedResumeData,
    jobSeekerProfileId: jobSeekerProfile?.id,
  });

  useEffect(() => {
    if (parsedResumeData && !resumeDataApplied) {
      setDialogOpen(true);
    }
  }, [parsedResumeData, resumeDataApplied]);

  const debouncedUpdate = useRef(
    debounce((data: MainInformationFormSchema) => {
      updateFormData(data);
    }, 300),
  ).current;

  useEffect(() => {
    const subscription = form.watch(() => {
      if (formInitialized.current) {
        const currentData = form.getValues();
        debouncedUpdate(currentData);
      }
    });

    setTimeout(() => {
      formInitialized.current = true;
    }, 500);

    return () => {
      subscription.unsubscribe();
      debouncedUpdate.cancel();
    };
  }, [form, debouncedUpdate]);

  useEffect(() => {
    const updateCompletedSections = () => {
      const values = form.getValues();
      const completed = [];

      const personalInfo = values.personalInfo;
      if (
        personalInfo.fullName &&
        personalInfo.jobTitle &&
        personalInfo.phone &&
        personalInfo.country &&
        personalInfo.city
      ) {
        completed.push('personalInfo');
      }

      if (
        values.education.length > 0 &&
        values.education[0].type &&
        values.education[0].institution &&
        values.education[0].discipline
      ) {
        completed.push('education');
      }

      if (
        values.workExperience.length > 0 &&
        values.workExperience[0].companyName &&
        values.workExperience[0].jobTitle
      ) {
        completed.push('workExperience');
      }

      if (
        values.languages.length > 0 &&
        values.languages[0].language &&
        values.languages[0].level
      ) {
        completed.push('language');
      }

      if (values.skills.length > 0) {
        completed.push('skills');
      }

      if (values.certifications && values.certifications.length > 0) {
        completed.push('certifications');
      }

      if (values.salary_min && values.salary_max) {
        completed.push('salary');
      }

      if (values.has_driving_license !== null) {
        completed.push('drivingLicense');
      }

      setCompletedSections(completed);
    };

    const subscription = form.watch(() => updateCompletedSections());
    updateCompletedSections();

    return () => subscription.unsubscribe();
  }, [form, form.watch]);

  const handleContinue = () => {
    const currentFormData = form.getValues();
    setFormData(currentFormData);
    onComplete();
  };

  const handleError = () => {
    toast({
      title: 'Validation Error',
      description: 'Please check the data',
    });
  };

  const handleTabClick = (sectionId: string) => {
    setActiveSection(sectionId);
    const sectionRef = sectionRefs[sectionId as keyof typeof sectionRefs];
    if (sectionRef?.current) {
      sectionRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <div className="rounded-lg w-full flex flex-col justify-center items-center">
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>CV Data Applied</DialogTitle>
            <DialogDescription>
              Your information has been pre-filled from your resume. Please
              review and make any necessary adjustments.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setDialogOpen(false)}>Got It</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <FormSectionTabs
        sections={sections}
        activeSection={activeSection}
        completedSections={completedSections}
        onTabClick={handleTabClick}
      />

      <div
        style={{ width: '800px', maxWidth: '100%', boxSizing: 'border-box' }}
      >
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleContinue, handleError)}>
            <div
              className="space-y-8 p-6"
              style={{ maxWidth: '100%', boxSizing: 'border-box' }}
            >
              <SectionContainer
                id="personalInfo"
                sectionRef={sectionRefs.personalInfo}
              >
                <PersonalInfoForm control={form.control} />
              </SectionContainer>

              <SectionContainer
                id="education"
                sectionRef={sectionRefs.education}
              >
                <EducationForm
                  control={form.control}
                  setValue={form.setValue}
                  watch={form.watch}
                />
              </SectionContainer>

              <SectionContainer
                id="workExperience"
                sectionRef={sectionRefs.workExperience}
              >
                <WorkExperienceForm control={form.control} />
              </SectionContainer>

              <SectionContainer
                id="certifications"
                sectionRef={sectionRefs.certifications}
              >
                <CertificationForm
                  control={form.control}
                  setValue={form.setValue}
                  watch={form.watch}
                />
              </SectionContainer>

              <SectionContainer id="language" sectionRef={sectionRefs.language}>
                <LanguageForm control={form.control} />
              </SectionContainer>

              <SectionContainer id="skills" sectionRef={sectionRefs.skills}>
                <SkillsForm
                  control={form.control}
                  setValue={form.setValue}
                  watch={form.watch}
                />
              </SectionContainer>

              <SectionContainer id="salary" sectionRef={sectionRefs.salary}>
                <SalaryForm control={form.control} />
              </SectionContainer>

              <SectionContainer
                id="drivingLicense"
                sectionRef={sectionRefs.drivingLicense}
              >
                <DrivingLicenseForm
                  control={form.control}
                  setValue={form.setValue}
                />
              </SectionContainer>
            </div>

            <div className="p-6 border-t">
              <Button
                variant="default"
                className="self-end"
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Loading...' : 'Continue'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default MainInformationStep;
