'use client';

import React, { useEffect, useState } from 'react';
import { CheckCircle2, FileText } from 'lucide-react';

import { Progress } from '~/components/ui/progress.tsx';

export type ParsingStage = {
  id: string;
  label: string;
  progress: number;
};

export const DEFAULT_PARSING_STAGES: ParsingStage[] = [
  { id: 'uploading', label: 'Uploading file...', progress: 10 },
  { id: 'detecting', label: 'Detecting document format...', progress: 25 },
  { id: 'extracting', label: 'Extracting text...', progress: 40 },
  { id: 'analyzing', label: 'Analyzing content...', progress: 60 },
  { id: 'structuring', label: 'Structuring information...', progress: 75 },
  { id: 'finalizing', label: 'Finalizing data...', progress: 90 },
  { id: 'completed', label: 'Parsing completed!', progress: 100 },
];

export type ParsingStatus =
  | 'idle'
  | 'uploading'
  | 'parsing'
  | 'success'
  | 'error';

type CVParsingProgressProps = {
  status: ParsingStatus;
  stages?: ParsingStage[];
  onComplete?: () => void;
  className?: string;
};

const CVParsingProgress: React.FC<CVParsingProgressProps> = ({
  status,
  stages = DEFAULT_PARSING_STAGES,
  onComplete,
  className = '',
}) => {
  const [progress, setProgress] = useState(0);
  const [currentStage, setCurrentStage] = useState(0);

  useEffect(() => {
    if (status === 'idle') {
      setProgress(0);
      setCurrentStage(0);
    }
  }, [status]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (status === 'uploading' || status === 'parsing') {
      interval = setInterval(() => {
        setProgress(prevProgress => {
          if (
            prevProgress >= stages[currentStage].progress &&
            currentStage < stages.length - 1
          ) {
            setCurrentStage(currentStage + 1);
          }

          const targetValue = Math.min(
            stages[Math.min(currentStage + 1, stages.length - 2)].progress - 5,
            90,
          );

          if (prevProgress < targetValue) {
            return prevProgress + Math.random() * 1.5;
          }
          return prevProgress;
        });
      }, 300);
    }

    if (status === 'success') {
      setCurrentStage(stages.length - 1);
      setProgress(100);

      if (onComplete) {
        const timeout = setTimeout(() => {
          onComplete();
        }, 1000);

        return () => clearTimeout(timeout);
      }
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [status, currentStage, stages, onComplete]);

  if (status === 'idle' || status === 'error') {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2 text-sm font-medium">
          {status === 'success' ? (
            <CheckCircle2 className="text-primary h-4 w-4" />
          ) : (
            <FileText className="text-primary h-4 w-4" />
          )}
          <span>{stages[currentStage].label}</span>
        </div>
        <span className="text-sm font-medium">{Math.round(progress)}%</span>
      </div>
      <Progress value={progress} className="h-2" />
    </div>
  );
};

export default CVParsingProgress;
