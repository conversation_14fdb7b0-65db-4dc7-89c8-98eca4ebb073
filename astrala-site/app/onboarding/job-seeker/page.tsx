'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import MainInformationStep from '~/app/onboarding/job-seeker/(components)/(steps)/MainInformationStep';
import SymbolicProfileStep from '~/app/onboarding/job-seeker/(components)/(steps)/SymbolicProfileStep.tsx';
import UploadCVStep from '~/app/onboarding/job-seeker/(components)/(steps)/UploadCVStep';
import WorkStyleTestStep from '~/app/onboarding/job-seeker/(components)/(steps)/WorkStyleTestStep';
import { useOnboardingStore } from '~/app/onboarding/job-seeker/(store)/useOnboardingStore';

import OnboardingHeader from '~/shared/components/OnboardingHeader';

import { useAuth } from '~/providers/AuthProvider';

export type OnboardingStep =
  | 'upload-cv'
  | 'main-info'
  | 'work-style'
  | 'symbolic-profile';

type Education = {
  level: string;
  institution: string;
  start_year: number;
  end_year: number;
  discipline: string;
  comment: string;
};

type WorkExperience = {
  company: string;
  job_title: string;
  start: string;
  end: string;
  start_year: number;
  end_year: number;
  comment: string;
};

type Certification = {
  title: string;
};

type Language = {
  language: string;
  level: string;
};

export type ParsedResume = {
  full_name: string;
  phone: string;
  city: string;
  country: string;
  email: string;
  education: Education[];
  work_experience: WorkExperience[];
  certifications: Certification[];
  certification_ids: number[];
  languages: Language[];
  skill_ids: number[];
  skills: string[];
};

export default function JobSeekerOnboarding() {
  const router = useRouter();
  const { jobSeekerProfile, isLoading } = useAuth();

  const {
    currentStep,
    setCurrentStep,
    parsedResumeData,
    setParsedResumeData,
    canGoToStep,
    isStepCompleted,
  } = useOnboardingStore();

  useEffect(
    () => {
      if (isLoading) return;

      if (jobSeekerProfile?.country && jobSeekerProfile?.city) {
        if (currentStep === 'upload-cv') {
          setCurrentStep('work-style');
        }
        // eslint-disable-next-line sonarjs/no-collapsible-if
      } else if (jobSeekerProfile?.resume_path) {
        if (currentStep === 'upload-cv') {
          setCurrentStep('main-info');
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isLoading],
  );

  const handleCVUploadComplete = (parsedData?: ParsedResume) => {
    if (parsedData) {
      setParsedResumeData(parsedData);
    }

    setCurrentStep('main-info');
  };

  const handleMainInfoComplete = () => {
    setCurrentStep('work-style');
  };

  const handleWorkStyleComplete = () => {
    setCurrentStep('symbolic-profile');
  };

  const handleSymbolicProfileComplete = () => {
    router.push('/dashboard/job-seeker/suggestions');
  };

  const handleStepClick = (step: OnboardingStep) => {
    if (canGoToStep(step)) {
      setCurrentStep(step);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 'upload-cv':
        return <UploadCVStep onComplete={handleCVUploadComplete} />;
      case 'main-info':
        return (
          <MainInformationStep
            onComplete={handleMainInfoComplete}
            parsedResumeData={parsedResumeData}
          />
        );
      case 'work-style':
        return <WorkStyleTestStep onComplete={handleWorkStyleComplete} />;
      case 'symbolic-profile':
        return (
          <SymbolicProfileStep onComplete={handleSymbolicProfileComplete} />
        );
      default:
        return <div>Unknown Error</div>;
    }
  };

  if (isLoading) {
    return <div className="p-6">Loading...</div>;
  }

  const steps = [
    { id: 'upload-cv', number: 1, label: 'Upload Your CV', showDivider: true },
    {
      id: 'main-info',
      number: 2,
      label: 'Approve Information',
      showDivider: true,
    },
    {
      id: 'work-style',
      number: 3,
      label: 'Behavioural Profile',
      showDivider: true,
    },
    {
      id: 'symbolic-profile',
      number: 4,
      label: 'Your Archetype',
      showDivider: false,
    },
  ];

  return (
    <div className="min-h-screen w-full">
      <OnboardingHeader
        currentStep={currentStep}
        steps={steps}
        onStepClick={handleStepClick}
        canGoToStep={canGoToStep}
        isStepCompleted={isStepCompleted}
      />

      <div className="w-full rounded-lg">{renderStep()}</div>
    </div>
  );
}
