// Modified useResumeParser.ts
import { useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { ParsedResume } from '~/app/onboarding/job-seeker/page.tsx';

import { MainInformationFormSchema } from '../(schemas)/mainInformation.schema';
import { useOnboardingStore } from '../(store)/useOnboardingStore';

type UseResumeParserProps = {
  form: UseFormReturn<MainInformationFormSchema>;
  parsedResumeData?: ParsedResume;
  jobSeekerProfileId?: number | null;
};

export const useResumeParser = ({
  form,
  parsedResumeData,
  jobSeekerProfileId,
}: UseResumeParserProps) => {
  const { resumeDataApplied, setResumeDataApplied } = useOnboardingStore();
  const [isApplying, setIsApplying] = useState(false);

  useEffect(() => {
    const applyResumeData = async () => {
      if (parsedResumeData && !resumeDataApplied && jobSeekerProfileId) {
        setIsApplying(true);
        const currentMonth = new Date().getMonth() + 1;

        try {
          if (parsedResumeData.full_name) {
            form.setValue('personalInfo.fullName', parsedResumeData.full_name);
          }

          const jobTitle = parsedResumeData?.work_experience[0]?.job_title;
          if (jobTitle) {
            form.setValue('personalInfo.jobTitle', jobTitle);
          }

          if (parsedResumeData.phone) {
            form.setValue('personalInfo.phone', parsedResumeData.phone);
          }

          if (parsedResumeData.country) {
            form.setValue('personalInfo.country', parsedResumeData.country);
          }

          if (parsedResumeData.city) {
            form.setValue('personalInfo.city', parsedResumeData.city);
          }

          if (
            parsedResumeData.education &&
            parsedResumeData.education.length > 0
          ) {
            const educationData = parsedResumeData.education.map(edu => ({
              type: edu.level || '',
              institution: edu.institution || '',
              discipline: edu.discipline || '',
              startYear: edu.start_year || 0,
              endYear: edu.end_year || 0,
            }));
            form.setValue('education', educationData);
          }

          if (
            parsedResumeData.work_experience &&
            parsedResumeData.work_experience.length > 0
          ) {
            const workExpData = parsedResumeData.work_experience.map(exp => ({
              companyName: exp.company || '',
              jobTitle: exp.job_title || '',
              startMonth: currentMonth,
              startYear: exp.start_year || 0,
              endMonth: currentMonth,
              endYear: exp.end_year || 0,
              comment: exp.comment || '',
            }));
            form.setValue('workExperience', workExpData);
          }

          if (
            parsedResumeData.languages &&
            parsedResumeData.languages.length > 0
          ) {
            const languagesData = parsedResumeData.languages.map(lang => ({
              language: lang.language || '',
              level: lang.level || '',
            }));
            form.setValue('languages', languagesData);
          }

          // eslint-disable-next-line sonarjs/no-collapsible-if
          if (parsedResumeData.skills && parsedResumeData.skills.length > 0) {
            if (
              parsedResumeData.skill_ids &&
              parsedResumeData.skill_ids.length > 0
            ) {
              const skillsData = parsedResumeData.skills
                .map((skillName, index) => {
                  return {
                    id: parsedResumeData.skill_ids?.[index] || -(index + 1),
                    name: skillName.toLowerCase().trim(),
                  };
                })
                .filter(skill => skill.name);

              form.setValue('skills', skillsData);
            }
          }

          if (
            parsedResumeData.certifications &&
            parsedResumeData.certifications.length > 0
          ) {
            const certificationsData = parsedResumeData.certifications
              .map((cert, index) => {
                const certName = cert.title || '';
                const certId =
                  parsedResumeData.certification_ids?.[index] || -(index + 1);

                return {
                  id: certId,
                  name: certName.trim(),
                  type: certName.trim(),
                  file: null,
                  file_path: null,
                };
              })
              .filter(cert => cert.name);

            form.setValue('certifications', certificationsData);
          }

          setResumeDataApplied(true);
        } catch (error) {
          console.error('Error applying resume data:', error);
        } finally {
          setIsApplying(false);
        }
      }
    };

    applyResumeData();
  }, [
    parsedResumeData,
    resumeDataApplied,
    jobSeekerProfileId,
    form,
    setResumeDataApplied,
  ]);

  return {
    resumeDataApplied,
    isApplying,
  };
};
