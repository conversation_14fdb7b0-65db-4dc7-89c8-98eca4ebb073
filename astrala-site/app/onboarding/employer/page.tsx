'use client';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { VacancyInfoStep } from '~/app/onboarding/employer/(components)/(steps)/VacancyInfoStep';
import { WorkStyleTestStep } from '~/app/onboarding/employer/(components)/(steps)/WorkStyleTestStep';
import {
  getDefaultVacancyValues,
  VacancyFormSchema,
  vacancyFormSchema,
} from '~/app/onboarding/employer/(schemas)/vacancy.schema';
import { useEmployerOnboardingStore } from '~/app/onboarding/employer/(store)/useEmployerOnboardingStore';

import OnboardingHeader from '~/shared/components/OnboardingHeader';

import { useSignOut } from '~/api/features/auth/queries.ts';
import { useAuth } from '~/providers/AuthProvider';

export type EmployerOnboardingStep = 'vacancy-info' | 'workstyle-test';

export default function EmployerOnboardingPage() {
  const { employerProfile } = useAuth();

  const {
    currentStep,
    setCurrentStep,
    vacancyData,
    setVacancyData,
    canGoToStep,
    isStepCompleted,
    shouldUpdateForm,
    setShouldUpdateForm,
  } = useEmployerOnboardingStore();

  const form = useForm<VacancyFormSchema>({
    resolver: zodResolver(vacancyFormSchema),
    defaultValues: vacancyData || getDefaultVacancyValues(),
    mode: 'onBlur',
  });

  useEffect(() => {
    if (vacancyData && shouldUpdateForm) {
      form.reset(vacancyData);
      setShouldUpdateForm(false);
    }
  }, [form, vacancyData, shouldUpdateForm, setShouldUpdateForm]);

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (vacancyData && currentStep === 'workstyle-test') {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
      return undefined;
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [vacancyData, currentStep]);

  const steps = [
    {
      id: 'vacancy-info',
      number: 1,
      label: 'Vacancy Information',
      showDivider: true,
    },
    {
      id: 'workstyle-test',
      number: 2,
      label: 'Behavioural Profile Test',
      showDivider: false,
    },
  ];

  const handleVacancyDataSubmit = async (data: VacancyFormSchema) => {
    setVacancyData(data);
    setCurrentStep('workstyle-test');
  };

  const signOut = useSignOut();

  const handleCancel = () => {
    // eslint-disable-next-line no-alert
    const confirmed = window.confirm(
      'Are you sure you want to cancel? All your data will be lost.',
    );

    if (confirmed) {
      signOut.mutate();
    }
  };

  const handleStepClick = (step: EmployerOnboardingStep) => {
    if (canGoToStep(step)) {
      if (currentStep === 'vacancy-info' && step === 'workstyle-test') {
        const currentData = form.getValues();
        setVacancyData(currentData);
      }
      setCurrentStep(step);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 'vacancy-info':
        return (
          <VacancyInfoStep
            form={form}
            onSubmit={handleVacancyDataSubmit}
            onCancel={handleCancel}
          />
        );
      case 'workstyle-test':
        return (
          <WorkStyleTestStep
            vacancyData={vacancyData}
            employerProfile={employerProfile}
            onCancel={handleCancel}
          />
        );
      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <div className="min-h-screen w-full">
      <OnboardingHeader
        currentStep={currentStep}
        steps={steps}
        onStepClick={handleStepClick}
        canGoToStep={canGoToStep}
        isStepCompleted={isStepCompleted}
      />

      <div className="w-full rounded-lg">{renderStep()}</div>
    </div>
  );
}
