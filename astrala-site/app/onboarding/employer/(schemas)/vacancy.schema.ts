import * as z from 'zod';

export const vacancyLanguageSchema = z.object({
  language: z.string().min(1, 'Language name is required'),
  level: z.string().min(1, 'Language proficiency level is required'),
});

export type VacancyLanguage = z.infer<typeof vacancyLanguageSchema>;

export const vacancySkillSchema = z.object({
  id: z.number({ required_error: 'Skill ID is required' }),
  name: z
    .string({ required_error: 'Skill name is required' })
    .min(1, 'Skill name cannot be empty')
    .max(50, 'Skill name is too long'),
});

export type VacancySkill = z.infer<typeof vacancySkillSchema>;

export const vacancyCertificationSchema = z.object({
  id: z.number({ required_error: 'Certification ID is required' }),
  name: z
    .string({ required_error: 'Certification name is required' })
    .min(1, 'Certification name cannot be empty')
    .max(100, 'Certification name is too long'),
});

export type VacancyCertification = z.infer<typeof vacancyCertificationSchema>;

export const vacancyFormSchema = z
  .object({
    title: z
      .string()
      .min(3, 'Job title must contain at least 3 characters')
      .max(100, 'Job title is too long'),
    description: z
      .string()
      .min(10, 'Job description must contain at least 10 characters')
      .max(2000, 'Job description is too long'),
    country: z.string().min(2, 'Country must contain at least 2 characters'),
    city: z.string().min(2, 'City must contain at least 2 characters'),
    experience_years_from: z
      .number({ required_error: 'Experience years is required' })
      .min(0, 'Experience years cannot be negative')
      .max(80, 'Experience years is too high'),
    education_degree: z
      .string()
      .max(100, 'Education degree is too long')
      .optional(),
    education_discipline: z
      .string()
      .max(100, 'Education discipline is too long')
      .optional(),
    driving_license: z.boolean().default(false),
    languages: z
      .array(vacancyLanguageSchema)
      .min(1, 'At least one language is required'),
    skills: z
      .array(vacancySkillSchema)
      .min(1, 'At least one skill is required'),
    certifications: z.array(vacancyCertificationSchema).optional().default([]),
    salary_min: z
      .number()
      .min(1, 'Must be greater than 0')
      .nonnegative('Salary cannot be negative'),
    salary_max: z
      .number()
      .min(1, 'Must be greater than 0')
      .nonnegative('Salary cannot be negative'),
  })
  .refine(data => data.salary_min <= data.salary_max, {
    message: 'Cannot be greater than maximum salary',
    path: ['salary_min'],
  });

export type VacancyFormSchema = z.infer<typeof vacancyFormSchema>;

export const getDefaultVacancyValues = (): VacancyFormSchema => {
  return {
    title: '',
    description: '',
    country: '',
    city: '',
    experience_years_from: 0,
    education_degree: '',
    education_discipline: '',
    driving_license: false,
    languages: [
      {
        language: '',
        level: '',
      },
    ],
    skills: [],
    certifications: [],
    salary_min: 0,
    salary_max: 0,
  };
};
