import React from 'react';
import { Control } from 'react-hook-form';

import { VacancyFormSchema } from '~/app/onboarding/employer/(schemas)/vacancy.schema.ts';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { educationTypes } from '~/constants/educationTypes.ts';

type EducationFormProps = {
  control: Control<VacancyFormSchema>;
};

export const EducationForm: React.FC<EducationFormProps> = ({ control }) => {
  return (
    <div className="space-y-4 p-2">
      <h2 className="text-lg font-medium">Education Degree (Optional)</h2>
      <FormField
        control={control}
        name="education_degree"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Type Of Education</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Choose variant" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {educationTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="education_discipline"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Discipline</FormLabel>
            <FormControl>
              <Input placeholder="Enter discipline" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
