import React from 'react';
import { Control } from 'react-hook-form';

import { VacancyFormSchema } from '~/app/onboarding/employer/(schemas)/vacancy.schema';

import { CountrySelect } from '~/shared/components/forms/CountrySelect';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Textarea } from '~/components/ui/textarea.tsx';

type BasicJobInfoFormProps = {
  control: Control<VacancyFormSchema>;
};

export const BasicJobInfoForm: React.FC<BasicJobInfoFormProps> = ({
  control,
}) => {
  const years = Array.from({ length: 80 }, (_, i) => i + 1);

  return (
    <div className="space-y-4 p-2">
      <h2 className="text-lg font-medium">Job Information</h2>
      <FormField
        control={control}
        name="title"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Job Title</FormLabel>
            <FormControl>
              <Input placeholder="Job title" {...field} size={30} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Job Description</FormLabel>
            <FormControl>
              <Textarea placeholder="Job Description" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="experience_years_from"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Work Experience (years)</FormLabel>
            <FormControl>
              <Select
                onValueChange={value => field.onChange(Number(value))}
                value={field.value?.toString() || ''}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Minimum years of experience" />
                </SelectTrigger>
                <SelectContent>
                  {years.map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="grid grid-cols-2 gap-4">
        <CountrySelect control={control} name="country" label="Country" />
        <FormField
          control={control}
          name="city"
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Input placeholder="City" {...field} size={30} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
