import React from 'react';
import { Control } from 'react-hook-form';

import { VacancyFormSchema } from '~/app/onboarding/employer/(schemas)/vacancy.schema';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';

type DrivingLicenseFormProps = {
  control: Control<VacancyFormSchema>;
};

export const DrivingLicenseForm: React.FC<DrivingLicenseFormProps> = ({
  control,
}) => {
  return (
    <div className="space-y-4 p-2">
      <h2 className="text-lg font-medium">Driving License</h2>
      <FormField
        control={control}
        name="driving_license"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Driver's License Required</FormLabel>
            <Select
              onValueChange={value => field.onChange(value === 'yes')}
              defaultValue={field.value ? 'yes' : 'no'}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Choose variant" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="yes">Yes</SelectItem>
                <SelectItem value="no">No</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
