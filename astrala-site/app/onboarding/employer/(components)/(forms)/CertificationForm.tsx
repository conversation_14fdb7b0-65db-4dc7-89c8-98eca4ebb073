import React, { useEffect, useRef, useState } from 'react';
import { Control, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { Loader2, Plus, Search, X } from 'lucide-react';

import {
  VacancyCertification,
  VacancyFormSchema,
} from '~/app/onboarding/employer/(schemas)/vacancy.schema';

import { DropdownPortal } from '~/shared/components/DropdownPotral';

import { useCertificationSearch } from '~/api/entities/vacancy/queries';
import { Button } from '~/components/ui/button';
import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input';

type CertificationsFormProps = {
  control: Control<VacancyFormSchema>;
  setValue: UseFormSetValue<VacancyFormSchema>;
  watch: UseFormWatch<VacancyFormSchema>;
};

export const CertificationsForm: React.FC<CertificationsFormProps> = ({
  control,
  setValue,
  watch,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<VacancyCertification[]>(
    [],
  );
  const inputRef = useRef<HTMLInputElement>(null);

  const { searchCertifications } = useCertificationSearch();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const formCertifications = watch('certifications') || [];

  useEffect(() => {
    const delayDebounceFn = setTimeout(async () => {
      if (inputValue.trim().length >= 2) {
        setIsSearching(true);
        try {
          const results = await searchCertifications(inputValue);
          const filteredResults = results.filter(
            result => !formCertifications.some(cert => cert.id === result.id),
          );
          setSearchResults(filteredResults);
        } catch (error) {
          console.error('Error searching certifications:', error);
        } finally {
          setIsSearching(false);
        }
      } else {
        setSearchResults([]);
      }
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [inputValue, formCertifications, searchCertifications]);

  const handleAddExistingCertification = (
    certification: VacancyCertification,
  ) => {
    setValue('certifications', [...formCertifications, certification]);
    setInputValue('');
    setSearchResults([]);
  };

  const handleCreateNewCertification = async () => {
    if (!inputValue.trim() || inputValue.trim().length < 2) return;

    const newCertification: VacancyCertification = {
      id: Math.floor(Math.random() * -1000) - 1,
      name: inputValue.trim(),
    };

    setValue('certifications', [...formCertifications, newCertification]);
    setInputValue('');
    setSearchResults([]);
  };

  const handleRemoveCertification = (certId: number) => {
    setValue(
      'certifications',
      formCertifications.filter(cert => cert.id !== certId),
    );
  };

  const hasExactMatch = searchResults.some(
    cert => cert.name.toLowerCase() === inputValue.toLowerCase(),
  );

  const canCreateNewCertification =
    inputValue.trim().length >= 2 &&
    !hasExactMatch &&
    !isSearching &&
    searchResults.length === 0;

  const renderSearchResults = () => {
    if (isSearching) {
      return (
        <div className="p-2 text-center text-muted-foreground">
          Searching...
        </div>
      );
    }

    if (searchResults.length > 0) {
      return searchResults.map(cert => (
        <button
          key={cert.id}
          type="button"
          className="px-3 py-2 hover:bg-secondary cursor-pointer flex justify-between items-center w-full text-left text-foreground"
          onClick={() => handleAddExistingCertification(cert)}
        >
          <span>{cert.name}</span>
          <Plus className="h-4 w-4 text-primary" />
        </button>
      ));
    }

    if (inputValue.length >= 2) {
      return (
        <div className="p-2 text-center text-muted-foreground">
          No certifications found. You can add a new one.
        </div>
      );
    }

    return null;
  };

  return (
    <div className="space-y-4 p-2 text-foreground">
      <h2 className="text-lg font-medium text-foreground">
        Certification (Optional)
      </h2>
      <FormField
        control={control}
        name="certifications"
        render={() => (
          <FormItem>
            <FormLabel className="text-foreground">Add Certification</FormLabel>
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Input
                  ref={inputRef}
                  placeholder="Type at least 2 characters to search..."
                  value={inputValue}
                  onChange={e => setInputValue(e.target.value)}
                  className="pr-8 bg-background text-foreground border placeholder:text-muted-foreground"
                />
                {isSearching ? (
                  <Loader2 className="h-4 w-4 absolute right-3 top-1/2 -translate-y-1/2 animate-spin text-muted-foreground" />
                ) : (
                  <Search className="h-4 w-4 absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
                )}

                <DropdownPortal
                  targetRef={inputRef}
                  isOpen={inputValue.length >= 2}
                >
                  {renderSearchResults()}
                </DropdownPortal>
              </div>

              <Button
                type="button"
                variant="outline"
                onClick={handleCreateNewCertification}
                disabled={!canCreateNewCertification}
                className="bg-background text-foreground border hover:bg-secondary"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add New
              </Button>
            </div>
            <FormMessage className="text-destructive" />
          </FormItem>
        )}
      />

      {formCertifications.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {formCertifications.map(cert => (
            <div
              key={cert.id}
              className="px-3 py-1 border rounded-md flex items-center gap-1 bg-background text-foreground"
            >
              <span>{cert.name}</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 hover:bg-secondary"
                onClick={() => handleRemoveCertification(cert.id)}
              >
                <X className="h-3 w-3 text-foreground" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
