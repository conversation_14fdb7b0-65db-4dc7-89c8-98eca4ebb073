'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';

import { VacancyFormSchema } from '~/app/onboarding/employer/(schemas)/vacancy.schema';
import { useEmployerOnboardingStore } from '~/app/onboarding/employer/(store)/useEmployerOnboardingStore';

import QuestionnaireComponent from '~/shared/components/QuestionnaireComponent';
import SubmitOverlay from '~/shared/components/SubmitOverlay.tsx';
import { Database } from '~/shared/db/generated/types';

import { useCreateVacancy } from '~/api/entities/vacancy/queries';
import {
  useSaveVacancyQuestionnaireAnswers,
  useSendQuestionnaireEmail,
  useVacancyQuestionGroups,
  useVacancyQuestionnaireAnswers,
} from '~/api/entities/vacancy/questionnaire/queries';
import {
  companyKeys,
  useCompanyVacancies,
} from '~/api/features/company/queries';
import { useCompleteEmployerOnboarding } from '~/api/features/onboarding/queries.ts';
import { userKeys } from '~/api/features/user/queries';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Textarea } from '~/components/ui/textarea';

type VacancyQuestionnaireStepProps = {
  vacancyData: VacancyFormSchema | null;
  employerProfile: Database['public']['Tables']['employers']['Row'] | null;
  onCancel: () => void;
  existingVacancyId?: number;
  onFinish?: () => void;
};

type QuestionnaireAnswer = {
  question_id: number;
  value: number;
};

type CompletionMode = 'myself' | 'send-to-manager' | 'import-from-vacancy';

const SendToManagerSchema = z.object({
  hiringManagerName: z.string().min(1, 'Name is required'),
  hiringManagerEmail: z.string().email('Valid email required'),
  note: z.string().optional(),
});

const ImportFromVacancySchema = z.object({
  sourceVacancyId: z.string().min(1, 'Please select a vacancy'),
});

type SendToManagerForm = z.infer<typeof SendToManagerSchema>;
type ImportFromVacancyForm = z.infer<typeof ImportFromVacancySchema>;

export const WorkStyleTestStep: React.FC<VacancyQuestionnaireStepProps> = ({
  vacancyData,
  employerProfile,
  existingVacancyId,
  onFinish,
}) => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const [completionMode, setCompletionMode] =
    useState<CompletionMode>('myself');
  const importForm = useForm<ImportFromVacancyForm>({
    resolver: zodResolver(ImportFromVacancySchema),
    defaultValues: {
      sourceVacancyId: '',
    },
  });
  const selectedVacancyId = importForm.watch('sourceVacancyId');

  const {
    workStyleAnswers,
    updateWorkStyleAnswer,
    setWorkStyleAnswers,
    workStyleCurrentGroupIndex,
    setWorkStyleCurrentGroupIndex,
    isSubmitting,
    setIsSubmitting,
    resetWorkStyleState,
  } = useEmployerOnboardingStore();

  const { data, isLoading, error } = useVacancyQuestionGroups();
  const { data: companyVacancies } = useCompanyVacancies();
  const saveAnswersMutation = useSaveVacancyQuestionnaireAnswers();
  const createVacancyMutation = useCreateVacancy();
  const sendEmailMutation = useSendQuestionnaireEmail();
  const completeOnboardingMutation = useCompleteEmployerOnboarding();
  const { data: sourceAnswers } = useVacancyQuestionnaireAnswers(
    selectedVacancyId ? Number.parseInt(selectedVacancyId, 10) : undefined,
  );

  const availableVacancies = useMemo(() => {
    if (!companyVacancies) return [];

    return companyVacancies.filter(
      vacancy =>
        vacancy.has_questionnaire_answers &&
        (!existingVacancyId || Number(vacancy.id) !== existingVacancyId),
    );
  }, [companyVacancies, existingVacancyId]);

  const hasAvailableVacancies = availableVacancies.length > 0;

  const sendToManagerForm = useForm<SendToManagerForm>({
    resolver: zodResolver(SendToManagerSchema),
    defaultValues: {
      hiringManagerName: '',
      hiringManagerEmail: '',
      note: '',
    },
  });

  const groupedQuestions = useMemo(() => {
    if (!data) return [];

    try {
      const tradeoffQuestionIds = new Set(
        data.answerTags
          .filter(tag => tag.answer_text)
          .map(tag => tag.question_id)
          .filter(Boolean),
      );

      const regularGroups = data.groups
        .map(group => {
          const groupQuestions = data.questions
            .filter(q => q.group_id === group.id)
            .filter(q => !tradeoffQuestionIds.has(q.id))
            .map(q => ({
              id: q.id,
              name: q.name,
              group_id: q.group_id,
              created_at: q.created_at,
            }));

          return {
            id: group.id,
            name: group.name,
            created_at: group.created_at,
            questions: groupQuestions,
          };
        })
        .filter(group => group.questions.length > 0);

      const tradeoffQuestions = data.questions
        .filter(q => tradeoffQuestionIds.has(q.id))
        .map(q => ({
          id: q.id,
          name: q.name,
          group_id: q.group_id,
          created_at: q.created_at,
        }));

      const result = [...regularGroups];

      if (tradeoffQuestions.length > 0) {
        result.push({
          id: -1,
          name: 'Trade-off Questions',
          created_at: new Date().toISOString(),
          questions: tradeoffQuestions,
        });
      }

      return result;
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [data]);

  useEffect(() => {
    if (!existingVacancyId && workStyleAnswers.size === 0) {
      resetWorkStyleState();
    }
  }, [resetWorkStyleState, existingVacancyId, workStyleAnswers.size]);

  useEffect(() => {
    if (data && groupedQuestions.length > 0 && workStyleAnswers.size === 0) {
      try {
        const initialAnswers = new Map<number, number>();

        groupedQuestions.forEach(group => {
          group.questions.forEach(question => {
            const isTradeoffQuestion = data.answerTags.some(
              tag => tag.question_id === question.id && tag.answer_text,
            );
            initialAnswers.set(question.id, isTradeoffQuestion ? 1 : 3);
          });
        });

        setWorkStyleAnswers(initialAnswers);
      } catch (error) {
        console.error(error);
      }
    }
  }, [data, groupedQuestions, workStyleAnswers.size, setWorkStyleAnswers]);

  useEffect(() => {
    if (completionMode === 'import-from-vacancy' && !hasAvailableVacancies) {
      setCompletionMode('myself');
    }
  }, [completionMode, hasAvailableVacancies]);

  const handleAnswerChange = useCallback(
    (questionId: number, value: number) => {
      updateWorkStyleAnswer(questionId, value);
    },
    [updateWorkStyleAnswer],
  );

  const handleImportFromVacancy = async () => {
    if (!sourceAnswers || !employerProfile) {
      console.error('Missing source answers or employer profile');
      return;
    }

    setIsSubmitting(true);

    try {
      const serializedAnswers: QuestionnaireAnswer[] = sourceAnswers.map(
        answer => ({
          question_id: answer.question_id,
          value: answer.value,
        }),
      );

      if (existingVacancyId) {
        await saveAnswersMutation.mutateAsync({
          vacancyId: existingVacancyId,
          answers: serializedAnswers,
        });

        await completeOnboardingMutation.mutateAsync(employerProfile.id);

        if (employerProfile.user_id) {
          await queryClient.invalidateQueries({
            queryKey: userKeys.profile(employerProfile.user_id),
          });
        }

        await queryClient.invalidateQueries({
          queryKey: companyKeys.vacancies(),
          refetchType: 'all',
        });

        if (onFinish) {
          onFinish();
        } else {
          router.push('/dashboard/employer/vacancies');
        }
      } else {
        if (!vacancyData) {
          console.error('Missing vacancy data');
          return;
        }

        const vacancyResult = await createVacancyMutation.mutateAsync({
          title: vacancyData.title,
          description: vacancyData.description,
          employer_id: employerProfile.id,
          company_id: employerProfile.company_id!,
          country: vacancyData.country,
          city: vacancyData.city,
          experience_years_from: vacancyData.experience_years_from,
          education_degree: vacancyData.education_degree,
          education_discipline: vacancyData.education_discipline,
          driving_license: vacancyData.driving_license,
          salary_min: vacancyData.salary_min,
          salary_max: vacancyData.salary_max,
          certifications: vacancyData.certifications || [],
          languages: vacancyData.languages || [],
          skills: vacancyData.skills || [],
        });

        if (!vacancyResult.success || !vacancyResult.id) {
          throw new Error('Failed to create vacancy');
        }

        await saveAnswersMutation.mutateAsync({
          vacancyId: vacancyResult.id,
          answers: serializedAnswers,
        });

        await completeOnboardingMutation.mutateAsync(employerProfile.id);

        if (employerProfile.user_id) {
          await queryClient.invalidateQueries({
            queryKey: userKeys.profile(employerProfile.user_id),
          });
        }

        await queryClient.invalidateQueries({
          queryKey: ['employer', 'vacancies'],
        });

        router.push('/dashboard/employer/vacancies');
      }
    } catch (error) {
      console.error('Error importing answers:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSendToManager = async (formData: SendToManagerForm) => {
    if (!vacancyData || !employerProfile) {
      console.error('Missing vacancy data or employer profile');
      return;
    }

    setIsSubmitting(true);

    try {
      const vacancyResult = await createVacancyMutation.mutateAsync({
        title: vacancyData.title,
        description: vacancyData.description,
        employer_id: employerProfile.id,
        company_id: employerProfile.company_id!,
        country: vacancyData.country,
        city: vacancyData.city,
        experience_years_from: vacancyData.experience_years_from,
        education_degree: vacancyData.education_degree,
        education_discipline: vacancyData.education_discipline,
        driving_license: vacancyData.driving_license,
        salary_min: vacancyData.salary_min,
        salary_max: vacancyData.salary_max,
        certifications: vacancyData.certifications || [],
        languages: vacancyData.languages || [],
        skills: vacancyData.skills || [],
      });

      if (!vacancyResult.success || !vacancyResult.id) {
        throw new Error('Failed to create vacancy');
      }

      await sendEmailMutation.mutateAsync({
        vacancyId: vacancyResult.id,
        hiringManagerName: formData.hiringManagerName,
        hiringManagerEmail: formData.hiringManagerEmail,
        note: formData.note,
        vacancyTitle: vacancyData.title,
      });

      await completeOnboardingMutation.mutateAsync(employerProfile.id);

      if (employerProfile.user_id) {
        await queryClient.invalidateQueries({
          queryKey: userKeys.profile(employerProfile.user_id),
        });
      }

      await queryClient.invalidateQueries({
        queryKey: ['employer', 'vacancies'],
      });

      router.push('/dashboard/employer/vacancies');
    } catch (error) {
      console.error('Error sending to manager:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleMyselfNext = async () => {
    if (workStyleCurrentGroupIndex < groupedQuestions.length - 1) {
      const newIndex = workStyleCurrentGroupIndex + 1;
      setWorkStyleCurrentGroupIndex(newIndex);
    } else {
      const serializedAnswers: QuestionnaireAnswer[] = Array.from(
        workStyleAnswers.entries(),
        // eslint-disable-next-line @typescript-eslint/naming-convention
      ).map(([question_id, value]) => ({
        question_id,
        value,
      }));

      if (existingVacancyId) {
        setIsSubmitting(true);
        try {
          await saveAnswersMutation.mutateAsync({
            vacancyId: existingVacancyId,
            answers: serializedAnswers,
          });

          if (employerProfile?.id) {
            await completeOnboardingMutation.mutateAsync(employerProfile.id);

            if (employerProfile.user_id) {
              await queryClient.invalidateQueries({
                queryKey: userKeys.profile(employerProfile.user_id),
              });
            }
          }

          await queryClient.invalidateQueries({
            queryKey: companyKeys.vacancies(),
            refetchType: 'all',
          });

          if (onFinish) {
            onFinish();
          } else {
            router.push('/dashboard/employer/vacancies');
          }
        } catch (error) {
          console.error('Error saving answers:', error);
        } finally {
          setIsSubmitting(false);
        }
      } else {
        if (!vacancyData || !employerProfile) {
          console.error('Missing vacancy data or employer profile');
          return;
        }

        setIsSubmitting(true);

        try {
          const vacancyResult = await createVacancyMutation.mutateAsync({
            title: vacancyData.title,
            description: vacancyData.description,
            employer_id: employerProfile.id,
            company_id: employerProfile.company_id!,
            country: vacancyData.country,
            city: vacancyData.city,
            experience_years_from: vacancyData.experience_years_from,
            education_degree: vacancyData.education_degree,
            education_discipline: vacancyData.education_discipline,
            driving_license: vacancyData.driving_license,
            salary_min: vacancyData.salary_min,
            salary_max: vacancyData.salary_max,
            certifications: vacancyData.certifications || [],
            languages: vacancyData.languages || [],
            skills: vacancyData.skills || [],
          });

          if (!vacancyResult.success || !vacancyResult.id) {
            throw new Error('Failed to create vacancy');
          }

          await saveAnswersMutation.mutateAsync({
            vacancyId: vacancyResult.id,
            answers: serializedAnswers,
          });

          await completeOnboardingMutation.mutateAsync(employerProfile.id);

          if (employerProfile.user_id) {
            await queryClient.invalidateQueries({
              queryKey: userKeys.profile(employerProfile.user_id),
            });
          }

          await queryClient.invalidateQueries({
            queryKey: ['employer', 'vacancies'],
          });

          router.push('/dashboard/employer/vacancies');
        } catch (error) {
          console.error('Error completing onboarding:', error);
        } finally {
          setIsSubmitting(false);
        }
      }
    }
  };

  const handleBack = useCallback(() => {
    if (workStyleCurrentGroupIndex > 0) {
      const newIndex = workStyleCurrentGroupIndex - 1;
      setWorkStyleCurrentGroupIndex(newIndex);
    }
  }, [workStyleCurrentGroupIndex, setWorkStyleCurrentGroupIndex]);

  const getButtonText = () => {
    if (completionMode === 'send-to-manager') {
      return 'Send to Hiring Manager';
    }

    if (completionMode === 'import-from-vacancy') {
      return 'Import and Save';
    }

    if (
      isSubmitting &&
      workStyleCurrentGroupIndex === groupedQuestions.length - 1
    ) {
      return 'Creating vacancy...';
    }

    if (workStyleCurrentGroupIndex === groupedQuestions.length - 1) {
      return 'Finish';
    }

    return 'Next';
  };

  const renderOverlay = () => {
    if (!isSubmitting) return null;

    const getMessage = () => {
      if (completionMode === 'send-to-manager') {
        return 'Creating vacancy and sending email...';
      }
      if (completionMode === 'import-from-vacancy') {
        return 'Importing answers and saving...';
      }
      return 'Creating vacancy and saving preferences...';
    };

    return <SubmitOverlay isSubmitting={true} message={getMessage()} />;
  };

  const renderContent = () => {
    if (completionMode === 'send-to-manager') {
      return (
        <Card>
          <CardHeader>
            <CardTitle>Send to Hiring Manager</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...sendToManagerForm}>
              <form
                onSubmit={sendToManagerForm.handleSubmit(handleSendToManager)}
                className="space-y-4"
              >
                <FormField
                  control={sendToManagerForm.control}
                  name="hiringManagerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hiring Manager Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={sendToManagerForm.control}
                  name="hiringManagerEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Enter email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={sendToManagerForm.control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Note (optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Add a note for the hiring manager..."
                          rows={4}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-3 pt-4">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => setCompletionMode('myself')}
                    disabled={isSubmitting}
                  >
                    Back
                  </Button>
                  <Button
                    variant="default"
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {getButtonText()}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      );
    }

    if (completionMode === 'import-from-vacancy') {
      return (
        <Card>
          <CardHeader>
            <CardTitle>Import from Another Vacancy</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...importForm}>
              <form
                onSubmit={importForm.handleSubmit(handleImportFromVacancy)}
                className="space-y-4"
              >
                <FormField
                  control={importForm.control}
                  name="sourceVacancyId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Select Vacancy</FormLabel>
                      <Select
                        onValueChange={value => {
                          field.onChange(value);
                        }}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose a vacancy with completed behavioural profile" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableVacancies.map(vacancy => (
                            <SelectItem
                              key={vacancy.id}
                              value={String(vacancy.id)}
                            >
                              {vacancy.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-3 pt-4">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => setCompletionMode('myself')}
                    disabled={isSubmitting}
                  >
                    Back
                  </Button>
                  <Button
                    variant="default"
                    type="submit"
                    disabled={
                      isSubmitting || !importForm.watch('sourceVacancyId')
                    }
                  >
                    {getButtonText()}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      );
    }

    return (
      <QuestionnaireComponent
        groups={groupedQuestions}
        answerTags={data?.answerTags || []}
        currentGroupIndex={workStyleCurrentGroupIndex}
        answers={workStyleAnswers}
        description="These questions explore soft skills, leadership tendencies, and adaptability."
        isLoading={isLoading}
        error={error}
        isSubmitting={isSubmitting}
        onAnswerChange={handleAnswerChange}
        onNext={handleMyselfNext}
        onBack={handleBack}
        getNextButtonText={getButtonText}
      />
    );
  };

  return (
    <div className="relative">
      {renderOverlay()}

      <div className="w-full flex justify-center items-center">
        <div className="w-[70%] my-6">
          <div className="flex justify-between items-start gap-8">
            <div className="flex-1">
              <h2 className="font-bold mb-2">
                Who should define the behavioural insight preferences for this
                role?
              </h2>
              <p className="text-placeholder">
                You can complete the behavioural insight questionnaire yourself
                {hasAvailableVacancies
                  ? ', delegate it to the hiring manager, or import answers from another vacancy with completed profile'
                  : ' or delegate it to the hiring manager who will lead the new hire'}
                . Their input will guide alignment and matching.
              </p>
            </div>
            <div className="min-w-[300px]">
              <RadioGroup
                value={completionMode}
                onValueChange={value =>
                  setCompletionMode(value as CompletionMode)
                }
                className="flex flex-col gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="myself" id="myself" />
                  <label htmlFor="myself">Myself</label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem
                    value="send-to-manager"
                    id="send-to-manager"
                  />
                  <label htmlFor="send-to-manager">
                    Send to Hiring Manager
                  </label>
                </div>
                {hasAvailableVacancies && (
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem
                      value="import-from-vacancy"
                      id="import-from-vacancy"
                    />
                    <label htmlFor="import-from-vacancy">
                      Import from another vacancy
                    </label>
                  </div>
                )}
              </RadioGroup>
            </div>
          </div>
        </div>
      </div>

      <div className="w-full flex justify-center items-center">
        <div className="w-[70%] my-6">{renderContent()}</div>
      </div>
    </div>
  );
};
