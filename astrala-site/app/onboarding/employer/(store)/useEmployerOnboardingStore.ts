import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import {
  getDefaultVacancyValues,
  VacancyFormSchema,
} from '~/app/onboarding/employer/(schemas)/vacancy.schema';

export type EmployerOnboardingStep = 'vacancy-info' | 'workstyle-test';

type EmployerOnboardingState = {
  currentStep: EmployerOnboardingStep;
  setCurrentStep: (step: EmployerOnboardingStep) => void;

  vacancyData: VacancyFormSchema;
  setVacancyData: (data: VacancyFormSchema) => void;

  workStyleAnswers: Map<number, number>;
  workStyleCurrentGroupIndex: number;
  updateWorkStyleAnswer: (questionId: number, value: number) => void;
  setWorkStyleAnswers: (answers: Map<number, number>) => void;
  setWorkStyleCurrentGroupIndex: (index: number) => void;
  resetWorkStyleState: () => void;

  isSubmitting: boolean;
  setIsSubmitting: (value: boolean) => void;

  canGoToStep: (step: EmployerOnboardingStep) => boolean;
  isStepCompleted: (step: EmployerOnboardingStep) => boolean;

  shouldUpdateForm: boolean;
  setShouldUpdateForm: (value: boolean) => void;
};

export const useEmployerOnboardingStore = create<EmployerOnboardingState>()(
  persist(
    (set, get) => ({
      currentStep: 'vacancy-info',
      setCurrentStep: step => set({ currentStep: step }),

      vacancyData: getDefaultVacancyValues(),
      setVacancyData: data =>
        set({
          vacancyData: data,
          shouldUpdateForm: false,
        }),

      workStyleAnswers: new Map<number, number>(),
      workStyleCurrentGroupIndex: 0,
      updateWorkStyleAnswer: (questionId, value) => {
        const state = get();
        const newAnswers = new Map(state.workStyleAnswers);
        newAnswers.set(questionId, value);
        set({ workStyleAnswers: newAnswers });
      },
      setWorkStyleAnswers: answers => set({ workStyleAnswers: answers }),
      setWorkStyleCurrentGroupIndex: index =>
        set({ workStyleCurrentGroupIndex: index }),
      resetWorkStyleState: () =>
        set({
          workStyleAnswers: new Map<number, number>(),
          workStyleCurrentGroupIndex: 0,
        }),

      isSubmitting: false,
      setIsSubmitting: value => set({ isSubmitting: value }),

      shouldUpdateForm: true,
      setShouldUpdateForm: value => set({ shouldUpdateForm: value }),

      canGoToStep: step => {
        const state = get();

        if (step === 'vacancy-info') return true;

        if (step === 'workstyle-test') {
          return state.isStepCompleted('vacancy-info');
        }

        return false;
      },

      isStepCompleted: step => {
        const state = get();

        if (step === 'vacancy-info') {
          const {
            title,
            description,
            country,
            city,
            experience_years_from,
            languages,
            skills,
            salary_min,
            salary_max,
          } = state.vacancyData;

          const hasBasicInfo =
            Boolean(title) &&
            Boolean(description) &&
            Boolean(country) &&
            Boolean(city) &&
            experience_years_from !== undefined;

          const hasLanguages =
            languages.length > 0 && Boolean(languages[0].language);
          const hasSkills = skills.length > 0;
          const hasSalary = Boolean(salary_min) && Boolean(salary_max);

          return hasBasicInfo && hasLanguages && hasSkills && hasSalary;
        }

        return false;
      },
    }),
    {
      name: 'employer-onboarding-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        currentStep: state.currentStep,
        vacancyData: state.vacancyData,
        workStyleCurrentGroupIndex: state.workStyleCurrentGroupIndex,
        shouldUpdateForm: state.shouldUpdateForm,
        workStyleAnswers: Array.from(state.workStyleAnswers.entries()),
      }),
      merge: (persisted, current) => {
        const persistedState = persisted as Partial<EmployerOnboardingState> & {
          workStyleAnswers?: [number, number][];
        };

        return {
          ...current,
          ...persistedState,
          workStyleAnswers: persistedState.workStyleAnswers
            ? new Map(persistedState.workStyleAnswers)
            : current.workStyleAnswers,
        };
      },
    },
  ),
);
