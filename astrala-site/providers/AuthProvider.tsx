'use client';

import { createContext, ReactNode, useContext } from 'react';
import { Session } from '@supabase/supabase-js';

import { Database } from '~/shared/db/generated/types';

import { useSession, useSignOut } from '~/api/features/auth/queries';
import { useGetUserProfile } from '~/api/features/user/queries';
import type { UserRole } from '~/types/auth.types';

type JobSeeker = Database['public']['Tables']['job_seekers']['Row'];
type Employer = Database['public']['Tables']['employers']['Row'];

type AuthContextType = {
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  userRole: UserRole | undefined;
  signOut: () => void;
  userProfile: JobSeeker | Employer | null;
  jobSeekerProfile: JobSeeker | null;
  employerProfile: Employer | null;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const { data: session, isLoading: sessionLoading } = useSession();
  const { mutate: signOut } = useSignOut();

  const userId = session?.user?.id;
  const userRole = session?.role as UserRole | undefined;

  const { data: userProfileData, isLoading: profileLoading } =
    useGetUserProfile(userId);

  const userProfile = userProfileData?.profile as JobSeeker | Employer | null;
  const jobSeekerProfile =
    userRole === 'job_seeker' ? (userProfile as JobSeeker | null) : null;
  const employerProfile =
    userRole === 'employer' ? (userProfile as Employer | null) : null;

  const isLoading = sessionLoading || profileLoading;

  const value = {
    session,
    isLoading,
    isAuthenticated: Boolean(session),
    userRole,
    signOut,
    userProfile,
    jobSeekerProfile,
    employerProfile,
  };

  return (
    <AuthContext.Provider
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      value={value}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
