'use client';

import { createContext, ReactNode, useContext } from 'react';

import { useAuth } from './AuthProvider';

type OnboardingContextType = {
  isLoading: boolean;
  needsBasicOnboarding: boolean;
  needsVacancyOnboarding: boolean;
  needsJobSeekerOnboarding: boolean;
};

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined,
);

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const {
    isAuthenticated,
    userRole,
    employerProfile,
    jobSeekerProfile,
    isLoading: authLoading,
  } = useAuth();

  const needsBasicOnboarding =
    isAuthenticated && userRole === 'employer' && !employerProfile?.company_id;

  const needsVacancyOnboarding =
    isAuthenticated &&
    userRole === 'employer' &&
    Boolean(employerProfile?.company_id) &&
    !employerProfile?.onboarding_completed;

  const needsJobSeekerOnboarding =
    isAuthenticated &&
    userRole === 'job_seeker' &&
    !jobSeekerProfile?.onboarding_completed;

  const value = {
    isLoading: authLoading,
    needsBasicOnboarding,
    needsVacancyOnboarding,
    needsJobSeekerOnboarding,
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
