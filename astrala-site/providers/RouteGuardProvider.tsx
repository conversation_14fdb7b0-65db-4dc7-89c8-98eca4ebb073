'use client';
import { ReactNode, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';

import { useAuth } from './AuthProvider';
import { useOnboarding } from './OnboardingProvider';

const AUTH_PATHS = [
  '/signin',
  '/signup',
  '/forgot-password',
  '/invite',
  '/invitation-error',
  '/auth/auth-code-error',
  '/auth/confirm',
];

const PASSWORD_RESET_PATHS = ['/update-password'];

const PAYMENT_PATHS = ['/payment/success', '/payment/cancel'];

const PUBLIC_PATHS = ['/terms-and-conditions', '/questionnaire'];

const ONBOARDING_PATHS = {
  JOB_SEEKER: '/onboarding/job-seeker',
  EMPLOYER_BASIC: '/employer-onboarding',
  EMPLOYER_VACANCY: '/onboarding/employer',
};
const DASHBOARD_PATHS = {
  JOB_SEEKER: '/dashboard/job-seeker/suggestions',
  EMPLOYER: '/dashboard/employer/suggestions',
};
const HOME_PATH = '/';

export function RouteGuardProvider({ children }: { children: ReactNode }) {
  const {
    isAuthenticated,
    userRole,
    isLoading: authLoading,
    userProfile,
  } = useAuth();

  const {
    isLoading: onboardingLoading,
    needsBasicOnboarding,
    needsVacancyOnboarding,
    needsJobSeekerOnboarding,
  } = useOnboarding();

  const router = useRouter();
  const pathname = usePathname();

  const isLoading = authLoading || onboardingLoading;

  const isAuthPath = AUTH_PATHS.some(
    path => pathname === path || pathname.startsWith(`${path}/`),
  );

  const isPasswordResetPath = PASSWORD_RESET_PATHS.some(
    path => pathname === path || pathname.startsWith(`${path}/`),
  );

  const isPaymentPath = PAYMENT_PATHS.some(
    path => pathname === path || pathname.startsWith(`${path}/`),
  );

  const isPublicPath = PUBLIC_PATHS.some(
    path => pathname === path || pathname.startsWith(`${path}/`),
  );

  const isHomePath = pathname === HOME_PATH;

  const isJobSeekerOnboardingPath =
    pathname === ONBOARDING_PATHS.JOB_SEEKER ||
    pathname.startsWith(`${ONBOARDING_PATHS.JOB_SEEKER}/`);
  const isEmployerBasicOnboardingPath =
    pathname === ONBOARDING_PATHS.EMPLOYER_BASIC ||
    pathname.startsWith(`${ONBOARDING_PATHS.EMPLOYER_BASIC}/`);
  const isEmployerVacancyOnboardingPath =
    pathname === ONBOARDING_PATHS.EMPLOYER_VACANCY ||
    pathname.startsWith(`${ONBOARDING_PATHS.EMPLOYER_VACANCY}/`);

  useEffect(() => {
    if (isLoading) return;
    if (isPaymentPath) return;

    if (isPasswordResetPath) return;

    if (!isAuthenticated) {
      if (!isAuthPath && !isHomePath && !isPublicPath) {
        router.push('/signin');
      }
      return;
    }

    if (userRole === 'job_seeker' && needsJobSeekerOnboarding) {
      if (!isJobSeekerOnboardingPath) {
        router.push(ONBOARDING_PATHS.JOB_SEEKER);
      }
      return;
    }

    if (userRole === 'employer' && needsBasicOnboarding) {
      if (!isEmployerBasicOnboardingPath) {
        router.push(ONBOARDING_PATHS.EMPLOYER_BASIC);
      }
      return;
    }

    if (userRole === 'employer' && needsVacancyOnboarding) {
      if (!isEmployerVacancyOnboardingPath) {
        router.push(ONBOARDING_PATHS.EMPLOYER_VACANCY);
      }
      return;
    }

    if ((isAuthPath || isHomePath) && !isPasswordResetPath) {
      const dashboardPath =
        userRole === 'job_seeker'
          ? DASHBOARD_PATHS.JOB_SEEKER
          : DASHBOARD_PATHS.EMPLOYER;
      router.push(dashboardPath);
      return;
    }

    if (
      userRole === 'job_seeker' &&
      !needsJobSeekerOnboarding &&
      isJobSeekerOnboardingPath
    ) {
      router.push(DASHBOARD_PATHS.JOB_SEEKER);
      return;
    }

    if (
      userRole === 'employer' &&
      !needsBasicOnboarding &&
      isEmployerBasicOnboardingPath
    ) {
      router.push(DASHBOARD_PATHS.EMPLOYER);
      return;
    }

    if (
      userRole === 'employer' &&
      !needsVacancyOnboarding &&
      isEmployerVacancyOnboardingPath
    ) {
      router.push(DASHBOARD_PATHS.EMPLOYER);
      return;
    }

    const isEmployerPath = pathname.includes('/employer/');
    const isJobSeekerPath = pathname.includes('/job-seeker/');

    if (userRole === 'employer' && isJobSeekerPath) {
      router.push(DASHBOARD_PATHS.EMPLOYER);
      return;
    }

    if (userRole === 'job_seeker' && isEmployerPath) {
      router.push(DASHBOARD_PATHS.JOB_SEEKER);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isLoading,
    isAuthenticated,
    pathname,
    userRole,
    needsBasicOnboarding,
    needsVacancyOnboarding,
    needsJobSeekerOnboarding,
    router,
    userProfile,
  ]);

  return <>{children}</>;
}
