# Note that "docker build" should be executed from the project root, and run like "docker build -f astrala-site/Dockerfile ."
FROM node:20.18-alpine AS base

# 1. Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --update --no-cache libc6-compat python3 make g++ && rm -rf /var/cache/apk/*

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
COPY astrala-site/package.json ./astrala-site/
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i; \
  else echo "Lockfile not found." && exit 1; \
  fi

# 2. Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/astrala-site/node_modules* ./astrala-site/node_modules
COPY . .

# Example of adding build-time env variables:
ARG NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY
ENV NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY=$NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY
ARG NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL
ENV NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL=$NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL
ARG NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY
ENV NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY=$NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY
ARG NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL
ENV NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL=$NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL
ARG NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY
ENV NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY=$NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY
ARG NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL
ENV NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL=$NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL
ARG NEXT_PUBLIC_SUPABASE_URL
ENV NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY
ARG STRIPE_SECRET_KEY
ENV STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY

WORKDIR /app/astrala-site
RUN npm run build

# 3. Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
RUN apk add --update --no-cache curl


# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/astrala-site/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/astrala-site/.next/static ./astrala-site/.next/static
COPY --from=builder /app/astrala-site/public ./astrala-site/public

USER nextjs

EXPOSE 3000
ENV PORT=3000
CMD HOSTNAME="0.0.0.0" node astrala-site/server.js
