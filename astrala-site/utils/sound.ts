class SoundManager {
  private static instance: SoundManager;
  private isPlaying = false;
  private audio: HTMLAudioElement | null = null;

  private constructor() {
    if (typeof window !== 'undefined') {
      this.audio = new Audio('/notification.mp3');
      this.audio.volume = 0.5;

      this.audio.addEventListener('error', e => {
        console.error('SoundManager: Audio error', e);
      });

      this.audio.addEventListener('ended', () => {
        this.isPlaying = false;
      });
    }
  }

  public static getInstance(): SoundManager {
    if (!SoundManager.instance) {
      SoundManager.instance = new SoundManager();
    }
    return SoundManager.instance;
  }

  public playNotification(): void {
    if (this.isPlaying || !this.audio) {
      return;
    }

    this.isPlaying = true;

    this.audio.currentTime = 0;

    this.audio.play().catch(() => {
      this.isPlaying = false;
    });

    setTimeout(() => {
      this.isPlaying = false;
    }, 1000);
  }
}

export const soundManager = SoundManager.getInstance();
