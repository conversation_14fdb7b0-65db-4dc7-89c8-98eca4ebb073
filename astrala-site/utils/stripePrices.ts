export type StripePrices = {
  employer: number;
  jobSeeker: number;
  currency: string;
};

export function getStripePrices(): StripePrices {
  return {
    employer: Number(process.env.NEXT_PUBLIC_STRIPE_EMPLOYER_PRICE || 0),
    jobSeeker: Number(process.env.NEXT_PUBLIC_STRIPE_JOB_SEEKER_PRICE || 0),
    currency: process.env.NEXT_PUBLIC_STRIPE_CURRENCY || 'GBP',
  };
}

export function formatPrice(amount: number, currency: string): string {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency,
  }).format(amount);
}
