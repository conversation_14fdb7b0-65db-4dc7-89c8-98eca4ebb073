export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.3 (519615d)';
  };
  public: {
    Tables: {
      archetype_dimension_targets: {
        Row: {
          archetype_id: number;
          created_at: string;
          dimension_id: number;
          target_value: number;
          updated_at: string;
        };
        Insert: {
          archetype_id: number;
          created_at?: string;
          dimension_id: number;
          target_value: number;
          updated_at?: string;
        };
        Update: {
          archetype_id?: number;
          created_at?: string;
          dimension_id?: number;
          target_value?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'archetype_dimension_targets_archetype_id_fkey';
            columns: ['archetype_id'];
            isOneToOne: false;
            referencedRelation: 'archetypes';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'archetype_dimension_targets_dimension_id_fkey';
            columns: ['dimension_id'];
            isOneToOne: false;
            referencedRelation: 'dimensions';
            referencedColumns: ['id'];
          },
        ];
      };
      archetypes: {
        Row: {
          about_profile: string;
          created_at: string;
          description: string | null;
          dimensions_intro: string;
          id: number;
          metaphor: string | null;
          name: string;
          role_alignments: string[];
          role_description: string;
          updated_at: string;
          wheel_description: string;
        };
        Insert: {
          about_profile?: string;
          created_at?: string;
          description?: string | null;
          dimensions_intro?: string;
          id?: number;
          metaphor?: string | null;
          name: string;
          role_alignments?: string[];
          role_description?: string;
          updated_at?: string;
          wheel_description?: string;
        };
        Update: {
          about_profile?: string;
          created_at?: string;
          description?: string | null;
          dimensions_intro?: string;
          id?: number;
          metaphor?: string | null;
          name?: string;
          role_alignments?: string[];
          role_description?: string;
          updated_at?: string;
          wheel_description?: string;
        };
        Relationships: [];
      };
      certificate_groups: {
        Row: {
          created_at: string;
          id: number;
          name: string;
          type: Database['public']['Enums']['cert_type'];
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          name: string;
          type: Database['public']['Enums']['cert_type'];
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          name?: string;
          type?: Database['public']['Enums']['cert_type'];
          updated_at?: string;
        };
        Relationships: [];
      };
      certifications: {
        Row: {
          created_at: string;
          group_id: number | null;
          id: number;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          group_id?: number | null;
          id?: number;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          group_id?: number | null;
          id?: number;
          name?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_certifications_group';
            columns: ['group_id'];
            isOneToOne: false;
            referencedRelation: 'certificate_groups';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_messages: {
        Row: {
          chat_id: number;
          created_at: string | null;
          edited_at: string | null;
          employer_id: number | null;
          id: number;
          is_edited: boolean | null;
          job_seeker_id: number | null;
          message_text: string;
          read_at: string | null;
        };
        Insert: {
          chat_id: number;
          created_at?: string | null;
          edited_at?: string | null;
          employer_id?: number | null;
          id?: number;
          is_edited?: boolean | null;
          job_seeker_id?: number | null;
          message_text: string;
          read_at?: string | null;
        };
        Update: {
          chat_id?: number;
          created_at?: string | null;
          edited_at?: string | null;
          employer_id?: number | null;
          id?: number;
          is_edited?: boolean | null;
          job_seeker_id?: number | null;
          message_text?: string;
          read_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_messages_chat_id_fkey';
            columns: ['chat_id'];
            isOneToOne: false;
            referencedRelation: 'chats';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_messages_employer_id_fkey';
            columns: ['employer_id'];
            isOneToOne: false;
            referencedRelation: 'employers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_messages_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      chats: {
        Row: {
          created_at: string | null;
          id: number;
          is_active: boolean | null;
          updated_at: string | null;
          vacancy_match_id: number;
        };
        Insert: {
          created_at?: string | null;
          id?: number;
          is_active?: boolean | null;
          updated_at?: string | null;
          vacancy_match_id: number;
        };
        Update: {
          created_at?: string | null;
          id?: number;
          is_active?: boolean | null;
          updated_at?: string | null;
          vacancy_match_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'chats_vacancy_match_id_fkey';
            columns: ['vacancy_match_id'];
            isOneToOne: true;
            referencedRelation: 'vacancy_matches';
            referencedColumns: ['id'];
          },
        ];
      };
      companies: {
        Row: {
          billing_period: string | null;
          canceled_at: string | null;
          created_at: string;
          current_period_end: string | null;
          current_period_start: string | null;
          id: number;
          name: string;
          open_vacancy_nudge_sent_at: string | null;
          stripe_customer_id: string | null;
          stripe_subscription_id: string | null;
          subscription_plan: string | null;
          subscription_status: string;
          updated_at: string;
        };
        Insert: {
          billing_period?: string | null;
          canceled_at?: string | null;
          created_at?: string;
          current_period_end?: string | null;
          current_period_start?: string | null;
          id?: number;
          name: string;
          open_vacancy_nudge_sent_at?: string | null;
          stripe_customer_id?: string | null;
          stripe_subscription_id?: string | null;
          subscription_plan?: string | null;
          subscription_status?: string;
          updated_at?: string;
        };
        Update: {
          billing_period?: string | null;
          canceled_at?: string | null;
          created_at?: string;
          current_period_end?: string | null;
          current_period_start?: string | null;
          id?: number;
          name?: string;
          open_vacancy_nudge_sent_at?: string | null;
          stripe_customer_id?: string | null;
          stripe_subscription_id?: string | null;
          subscription_plan?: string | null;
          subscription_status?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      company_match_usage: {
        Row: {
          company_id: number;
          created_at: string;
          id: number;
          matches_used: number;
          period_end: string;
          period_start: string;
          updated_at: string;
        };
        Insert: {
          company_id: number;
          created_at?: string;
          id?: number;
          matches_used?: number;
          period_end: string;
          period_start: string;
          updated_at?: string;
        };
        Update: {
          company_id?: number;
          created_at?: string;
          id?: number;
          matches_used?: number;
          period_end?: string;
          period_start?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'company_match_usage_company_id_fkey';
            columns: ['company_id'];
            isOneToOne: true;
            referencedRelation: 'companies';
            referencedColumns: ['id'];
          },
        ];
      };
      company_trials: {
        Row: {
          company_id: number;
          created_at: string;
          ends_at: string;
          id: number;
          started_at: string;
          updated_at: string;
        };
        Insert: {
          company_id: number;
          created_at?: string;
          ends_at: string;
          id?: number;
          started_at?: string;
          updated_at?: string;
        };
        Update: {
          company_id?: number;
          created_at?: string;
          ends_at?: string;
          id?: number;
          started_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'company_trials_company_id_fkey';
            columns: ['company_id'];
            isOneToOne: true;
            referencedRelation: 'companies';
            referencedColumns: ['id'];
          },
        ];
      };
      dimensions: {
        Row: {
          created_at: string;
          id: number;
          left_label: string;
          name: string;
          right_label: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          left_label?: string;
          name: string;
          right_label?: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          left_label?: string;
          name?: string;
          right_label?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      employers: {
        Row: {
          company_id: number | null;
          created_at: string;
          email: string | null;
          full_name: string | null;
          id: number;
          onboarding_completed: boolean;
          role: string | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          company_id?: number | null;
          created_at?: string;
          email?: string | null;
          full_name?: string | null;
          id?: number;
          onboarding_completed?: boolean;
          role?: string | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          company_id?: number | null;
          created_at?: string;
          email?: string | null;
          full_name?: string | null;
          id?: number;
          onboarding_completed?: boolean;
          role?: string | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'employers_company_id_fkey';
            columns: ['company_id'];
            isOneToOne: false;
            referencedRelation: 'companies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'employers_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['user_id'];
          },
        ];
      };
      invitation_tokens: {
        Row: {
          company_id: number;
          created_at: string;
          created_by_employer_id: number;
          email: string | null;
          expires_at: string;
          id: number;
          is_used: boolean | null;
          token: string;
          updated_at: string | null;
          used_at: string | null;
          used_by_employer_id: number | null;
        };
        Insert: {
          company_id: number;
          created_at?: string;
          created_by_employer_id: number;
          email?: string | null;
          expires_at: string;
          id?: number;
          is_used?: boolean | null;
          token: string;
          updated_at?: string | null;
          used_at?: string | null;
          used_by_employer_id?: number | null;
        };
        Update: {
          company_id?: number;
          created_at?: string;
          created_by_employer_id?: number;
          email?: string | null;
          expires_at?: string;
          id?: number;
          is_used?: boolean | null;
          token?: string;
          updated_at?: string | null;
          used_at?: string | null;
          used_by_employer_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'invitation_tokens_company_id_fkey';
            columns: ['company_id'];
            isOneToOne: false;
            referencedRelation: 'companies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'invitation_tokens_created_by_employer_id_fkey';
            columns: ['created_by_employer_id'];
            isOneToOne: false;
            referencedRelation: 'employers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'invitation_tokens_used_by_employer_id_fkey';
            columns: ['used_by_employer_id'];
            isOneToOne: false;
            referencedRelation: 'employers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_answer_tags: {
        Row: {
          answer_text: string | null;
          created_at: string;
          dimension_id: number | null;
          id: number;
          question_id: number | null;
          spectrum_pole: string | null;
          tag: string;
          value: number;
        };
        Insert: {
          answer_text?: string | null;
          created_at?: string;
          dimension_id?: number | null;
          id?: number;
          question_id?: number | null;
          spectrum_pole?: string | null;
          tag: string;
          value: number;
        };
        Update: {
          answer_text?: string | null;
          created_at?: string;
          dimension_id?: number | null;
          id?: number;
          question_id?: number | null;
          spectrum_pole?: string | null;
          tag?: string;
          value?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_answer_tags_dimension_id_fkey';
            columns: ['dimension_id'];
            isOneToOne: false;
            referencedRelation: 'dimensions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_seeker_answers_tags_question_id_fkey';
            columns: ['question_id'];
            isOneToOne: false;
            referencedRelation: 'job_seeker_questionnaire_questions';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_archetypes: {
        Row: {
          archetype_id: number | null;
          calculated_at: string;
          distance: number;
          id: number;
          job_seeker_id: number | null;
        };
        Insert: {
          archetype_id?: number | null;
          calculated_at?: string;
          distance: number;
          id?: number;
          job_seeker_id?: number | null;
        };
        Update: {
          archetype_id?: number | null;
          calculated_at?: string;
          distance?: number;
          id?: number;
          job_seeker_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_archetypes_archetype_id_fkey';
            columns: ['archetype_id'];
            isOneToOne: false;
            referencedRelation: 'archetypes';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_seeker_archetypes_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_archetypes_history: {
        Row: {
          archetype_id: number;
          created_at: string;
          id: number;
          job_seeker_id: number;
        };
        Insert: {
          archetype_id: number;
          created_at?: string;
          id?: number;
          job_seeker_id: number;
        };
        Update: {
          archetype_id?: number;
          created_at?: string;
          id?: number;
          job_seeker_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_archetypes_history_archetype_id_fkey';
            columns: ['archetype_id'];
            isOneToOne: false;
            referencedRelation: 'archetypes';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_seeker_archetypes_history_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_certifications: {
        Row: {
          certification_id: number;
          created_at: string;
          file_path: string | null;
          id: number;
          job_seeker_id: number;
          updated_at: string;
        };
        Insert: {
          certification_id: number;
          created_at?: string;
          file_path?: string | null;
          id?: number;
          job_seeker_id: number;
          updated_at?: string;
        };
        Update: {
          certification_id?: number;
          created_at?: string;
          file_path?: string | null;
          id?: number;
          job_seeker_id?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_certifications_certification_id_fkey';
            columns: ['certification_id'];
            isOneToOne: false;
            referencedRelation: 'certifications';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_seeker_certifications_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_dimension_scores: {
        Row: {
          calculated_at: string;
          dimension_id: number;
          id: number;
          job_seeker_id: number;
          score: number;
        };
        Insert: {
          calculated_at?: string;
          dimension_id: number;
          id?: number;
          job_seeker_id: number;
          score: number;
        };
        Update: {
          calculated_at?: string;
          dimension_id?: number;
          id?: number;
          job_seeker_id?: number;
          score?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_dimension_scores_dimension_id_fkey';
            columns: ['dimension_id'];
            isOneToOne: false;
            referencedRelation: 'dimensions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_seeker_dimension_scores_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_educations: {
        Row: {
          created_at: string;
          discipline: string;
          end_year: number;
          id: number;
          institution: string;
          job_seeker_id: number;
          start_year: number;
          type: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          discipline: string;
          end_year: number;
          id?: number;
          institution: string;
          job_seeker_id: number;
          start_year: number;
          type: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          discipline?: string;
          end_year?: number;
          id?: number;
          institution?: string;
          job_seeker_id?: number;
          start_year?: number;
          type?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_educations_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_languages: {
        Row: {
          created_at: string;
          id: number;
          job_seeker_id: number;
          language: string;
          level: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          job_seeker_id: number;
          language: string;
          level: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          job_seeker_id?: number;
          language?: string;
          level?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_languages_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_questionnaire_answers: {
        Row: {
          created_at: string;
          id: number;
          job_seeker_id: number;
          question_id: number;
          updated_at: string | null;
          value: number;
        };
        Insert: {
          created_at?: string;
          id?: number;
          job_seeker_id: number;
          question_id: number;
          updated_at?: string | null;
          value: number;
        };
        Update: {
          created_at?: string;
          id?: number;
          job_seeker_id?: number;
          question_id?: number;
          updated_at?: string | null;
          value?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_questionnaire_answers_question_id_fkey';
            columns: ['question_id'];
            isOneToOne: false;
            referencedRelation: 'job_seeker_questionnaire_questions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'questionnaire_answers_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_questionnaire_groups: {
        Row: {
          created_at: string;
          id: number;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      job_seeker_questionnaire_questions: {
        Row: {
          created_at: string;
          group_id: number | null;
          id: number;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          group_id?: number | null;
          id?: number;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          group_id?: number | null;
          id?: number;
          name?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_questionnaire_questions_group_id_fkey';
            columns: ['group_id'];
            isOneToOne: false;
            referencedRelation: 'job_seeker_questionnaire_groups';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_skills: {
        Row: {
          created_at: string;
          id: number;
          job_seeker_id: number;
          skill_id: number;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          job_seeker_id: number;
          skill_id: number;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          job_seeker_id?: number;
          skill_id?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_skills_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_seeker_skills_skill_id_fkey';
            columns: ['skill_id'];
            isOneToOne: false;
            referencedRelation: 'skills';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_vacancy_matches: {
        Row: {
          created_at: string;
          final_score_below_threshold: boolean;
          id: number;
          job_seeker_id: number | null;
          match_score: number | null;
          vacancy_id: number | null;
        };
        Insert: {
          created_at?: string;
          final_score_below_threshold?: boolean;
          id?: number;
          job_seeker_id?: number | null;
          match_score?: number | null;
          vacancy_id?: number | null;
        };
        Update: {
          created_at?: string;
          final_score_below_threshold?: boolean;
          id?: number;
          job_seeker_id?: number | null;
          match_score?: number | null;
          vacancy_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_vacancy_matches_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_seeker_vacancy_matches_vacancy_id_fkey';
            columns: ['vacancy_id'];
            isOneToOne: false;
            referencedRelation: 'vacancies';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seeker_work_experience: {
        Row: {
          comment: string | null;
          company_name: string;
          created_at: string;
          end_date: string | null;
          end_year: number | null;
          id: number;
          job_seeker_id: number;
          job_title: string;
          start_date: string | null;
          start_year: number | null;
          updated_at: string;
        };
        Insert: {
          comment?: string | null;
          company_name: string;
          created_at?: string;
          end_date?: string | null;
          end_year?: number | null;
          id?: number;
          job_seeker_id: number;
          job_title: string;
          start_date?: string | null;
          start_year?: number | null;
          updated_at?: string;
        };
        Update: {
          comment?: string | null;
          company_name?: string;
          created_at?: string;
          end_date?: string | null;
          end_year?: number | null;
          id?: number;
          job_seeker_id?: number;
          job_title?: string;
          start_date?: string | null;
          start_year?: number | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'job_seeker_work_experience_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
        ];
      };
      job_seekers: {
        Row: {
          archetype_pinned: boolean | null;
          avatar_path: string | null;
          city: string | null;
          country: string | null;
          created_at: string;
          email: string | null;
          full_name: string | null;
          has_driving_license: boolean | null;
          id: number;
          information_update_nudge_sent_at: string | null;
          information_updated_at: string | null;
          is_activated: boolean;
          is_active: boolean;
          job_title: string | null;
          matches_last_calculated_at: string | null;
          onboarding_completed: boolean;
          onboarding_nudge_sent_at: string | null;
          phone: string | null;
          resume_path: string | null;
          salary_max: number | null;
          salary_min: number | null;
          updated_at: string;
          user_id: string;
          work_style_updated_at: string | null;
        };
        Insert: {
          archetype_pinned?: boolean | null;
          avatar_path?: string | null;
          city?: string | null;
          country?: string | null;
          created_at?: string;
          email?: string | null;
          full_name?: string | null;
          has_driving_license?: boolean | null;
          id?: number;
          information_update_nudge_sent_at?: string | null;
          information_updated_at?: string | null;
          is_activated?: boolean;
          is_active?: boolean;
          job_title?: string | null;
          matches_last_calculated_at?: string | null;
          onboarding_completed?: boolean;
          onboarding_nudge_sent_at?: string | null;
          phone?: string | null;
          resume_path?: string | null;
          salary_max?: number | null;
          salary_min?: number | null;
          updated_at?: string;
          user_id: string;
          work_style_updated_at?: string | null;
        };
        Update: {
          archetype_pinned?: boolean | null;
          avatar_path?: string | null;
          city?: string | null;
          country?: string | null;
          created_at?: string;
          email?: string | null;
          full_name?: string | null;
          has_driving_license?: boolean | null;
          id?: number;
          information_update_nudge_sent_at?: string | null;
          information_updated_at?: string | null;
          is_activated?: boolean;
          is_active?: boolean;
          job_title?: string | null;
          matches_last_calculated_at?: string | null;
          onboarding_completed?: boolean;
          onboarding_nudge_sent_at?: string | null;
          phone?: string | null;
          resume_path?: string | null;
          salary_max?: number | null;
          salary_min?: number | null;
          updated_at?: string;
          user_id?: string;
          work_style_updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'employees_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['user_id'];
          },
        ];
      };
      match_feedbacks: {
        Row: {
          created_at: string | null;
          employer_feedback_at: string | null;
          employer_feedback_text: string | null;
          employer_feedback_type:
            | Database['public']['Enums']['feedback_type']
            | null;
          id: number;
          job_seeker_feedback_at: string | null;
          job_seeker_feedback_text: string | null;
          job_seeker_feedback_type:
            | Database['public']['Enums']['feedback_type']
            | null;
          updated_at: string | null;
          vacancy_match_id: number;
        };
        Insert: {
          created_at?: string | null;
          employer_feedback_at?: string | null;
          employer_feedback_text?: string | null;
          employer_feedback_type?:
            | Database['public']['Enums']['feedback_type']
            | null;
          id?: number;
          job_seeker_feedback_at?: string | null;
          job_seeker_feedback_text?: string | null;
          job_seeker_feedback_type?:
            | Database['public']['Enums']['feedback_type']
            | null;
          updated_at?: string | null;
          vacancy_match_id: number;
        };
        Update: {
          created_at?: string | null;
          employer_feedback_at?: string | null;
          employer_feedback_text?: string | null;
          employer_feedback_type?:
            | Database['public']['Enums']['feedback_type']
            | null;
          id?: number;
          job_seeker_feedback_at?: string | null;
          job_seeker_feedback_text?: string | null;
          job_seeker_feedback_type?:
            | Database['public']['Enums']['feedback_type']
            | null;
          updated_at?: string | null;
          vacancy_match_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'match_feedbacks_vacancy_match_id_fkey';
            columns: ['vacancy_match_id'];
            isOneToOne: false;
            referencedRelation: 'vacancy_matches';
            referencedColumns: ['id'];
          },
        ];
      };
      qualifications: {
        Row: {
          created_at: string;
          id: number;
          name: string | null;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          name?: string | null;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          name?: string | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      questionnaire_tokens: {
        Row: {
          completed_at: string | null;
          created_at: string | null;
          hiring_manager_email: string;
          hiring_manager_name: string;
          id: number;
          note: string | null;
          token: string;
          updated_at: string | null;
          vacancy_id: number;
        };
        Insert: {
          completed_at?: string | null;
          created_at?: string | null;
          hiring_manager_email: string;
          hiring_manager_name: string;
          id?: number;
          note?: string | null;
          token: string;
          updated_at?: string | null;
          vacancy_id: number;
        };
        Update: {
          completed_at?: string | null;
          created_at?: string | null;
          hiring_manager_email?: string;
          hiring_manager_name?: string;
          id?: number;
          note?: string | null;
          token?: string;
          updated_at?: string | null;
          vacancy_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'questionnaire_tokens_vacancy_id_fkey';
            columns: ['vacancy_id'];
            isOneToOne: false;
            referencedRelation: 'vacancies';
            referencedColumns: ['id'];
          },
        ];
      };
      skill_groups: {
        Row: {
          created_at: string;
          id: number;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      skills: {
        Row: {
          created_at: string;
          group_id: number | null;
          id: number;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          group_id?: number | null;
          id?: number;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          group_id?: number | null;
          id?: number;
          name?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_skills_group';
            columns: ['group_id'];
            isOneToOne: false;
            referencedRelation: 'skill_groups';
            referencedColumns: ['id'];
          },
        ];
      };
      surveys: {
        Row: {
          audience: Database['public']['Enums']['SurveyAudienceType'];
          button_label: string;
          button_url: string;
          created_at: string;
          id: number;
          text: string;
          title: string;
          updated_at: string;
        };
        Insert: {
          audience: Database['public']['Enums']['SurveyAudienceType'];
          button_label: string;
          button_url: string;
          created_at?: string;
          id?: number;
          text: string;
          title: string;
          updated_at?: string;
        };
        Update: {
          audience?: Database['public']['Enums']['SurveyAudienceType'];
          button_label?: string;
          button_url?: string;
          created_at?: string;
          id?: number;
          text?: string;
          title?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      transactions: {
        Row: {
          amount: number;
          completed_at: string | null;
          created_at: string;
          currency: string;
          id: number;
          payer_id: number;
          payer_role: string;
          status: string;
          updated_at: string;
          vacancy_match_id: number;
        };
        Insert: {
          amount: number;
          completed_at?: string | null;
          created_at?: string;
          currency: string;
          id?: number;
          payer_id: number;
          payer_role: string;
          status?: string;
          updated_at?: string;
          vacancy_match_id: number;
        };
        Update: {
          amount?: number;
          completed_at?: string | null;
          created_at?: string;
          currency?: string;
          id?: number;
          payer_id?: number;
          payer_role?: string;
          status?: string;
          updated_at?: string;
          vacancy_match_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'transactions_vacancy_match_id_fkey';
            columns: ['vacancy_match_id'];
            isOneToOne: false;
            referencedRelation: 'vacancy_matches';
            referencedColumns: ['id'];
          },
        ];
      };
      users: {
        Row: {
          created_at: string;
          role: string | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          role?: string | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          role?: string | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      vacancies: {
        Row: {
          city: string;
          company_id: number;
          country: string;
          created_at: string;
          description: string;
          driving_license: boolean;
          education_degree: string | null;
          education_discipline: string | null;
          employer_id: number;
          experience_years_from: number | null;
          id: number;
          matches_last_calculated_at: string | null;
          salary_max: number;
          salary_min: number;
          status: string;
          title: string;
          updated_at: string;
        };
        Insert: {
          city: string;
          company_id: number;
          country: string;
          created_at?: string;
          description: string;
          driving_license?: boolean;
          education_degree?: string | null;
          education_discipline?: string | null;
          employer_id: number;
          experience_years_from?: number | null;
          id?: number;
          matches_last_calculated_at?: string | null;
          salary_max: number;
          salary_min: number;
          status?: string;
          title: string;
          updated_at?: string;
        };
        Update: {
          city?: string;
          company_id?: number;
          country?: string;
          created_at?: string;
          description?: string;
          driving_license?: boolean;
          education_degree?: string | null;
          education_discipline?: string | null;
          employer_id?: number;
          experience_years_from?: number | null;
          id?: number;
          matches_last_calculated_at?: string | null;
          salary_max?: number;
          salary_min?: number;
          status?: string;
          title?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancies_company_id_fkey';
            columns: ['company_id'];
            isOneToOne: false;
            referencedRelation: 'companies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancies_employer_id_fkey';
            columns: ['employer_id'];
            isOneToOne: false;
            referencedRelation: 'employers';
            referencedColumns: ['id'];
          },
        ];
      };
      vacancy_answer_tags: {
        Row: {
          answer_text: string | null;
          created_at: string;
          dimension_id: number | null;
          id: number;
          question_id: number | null;
          spectrum_pole: string | null;
          tag: string;
          value: number;
        };
        Insert: {
          answer_text?: string | null;
          created_at?: string;
          dimension_id?: number | null;
          id?: number;
          question_id?: number | null;
          spectrum_pole?: string | null;
          tag: string;
          value: number;
        };
        Update: {
          answer_text?: string | null;
          created_at?: string;
          dimension_id?: number | null;
          id?: number;
          question_id?: number | null;
          spectrum_pole?: string | null;
          tag?: string;
          value?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancy_answer_tags_dimension_id_fkey';
            columns: ['dimension_id'];
            isOneToOne: false;
            referencedRelation: 'dimensions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancy_answers_tags_duplicate_question_id_fkey';
            columns: ['question_id'];
            isOneToOne: false;
            referencedRelation: 'job_seeker_questionnaire_questions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancy_answers_tags_question_id_fkey';
            columns: ['question_id'];
            isOneToOne: false;
            referencedRelation: 'vacancy_questionnaire_questions';
            referencedColumns: ['id'];
          },
        ];
      };
      vacancy_certifications: {
        Row: {
          certification_id: number;
          created_at: string;
          id: number;
          updated_at: string;
          vacancy_id: number;
        };
        Insert: {
          certification_id: number;
          created_at?: string;
          id?: number;
          updated_at?: string;
          vacancy_id: number;
        };
        Update: {
          certification_id?: number;
          created_at?: string;
          id?: number;
          updated_at?: string;
          vacancy_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancy_certifications_certification_id_fkey';
            columns: ['certification_id'];
            isOneToOne: false;
            referencedRelation: 'certifications';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancy_certifications_vacancy_id_fkey';
            columns: ['vacancy_id'];
            isOneToOne: false;
            referencedRelation: 'vacancies';
            referencedColumns: ['id'];
          },
        ];
      };
      vacancy_languages: {
        Row: {
          created_at: string;
          id: number;
          language: string;
          level: string;
          updated_at: string;
          vacancy_id: number;
        };
        Insert: {
          created_at?: string;
          id?: number;
          language: string;
          level: string;
          updated_at?: string;
          vacancy_id: number;
        };
        Update: {
          created_at?: string;
          id?: number;
          language?: string;
          level?: string;
          updated_at?: string;
          vacancy_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancy_languages_vacancy_id_fkey';
            columns: ['vacancy_id'];
            isOneToOne: false;
            referencedRelation: 'vacancies';
            referencedColumns: ['id'];
          },
        ];
      };
      vacancy_match_descriptions: {
        Row: {
          ai_summary: string | null;
          created_at: string;
          driving_license_match: boolean | null;
          education_match: boolean | null;
          lacking_behavioural_tags: string[] | null;
          lacking_skills: string[] | null;
          location_match: boolean | null;
          matching_behavioural_tags: string[] | null;
          matching_skills: string[] | null;
          salary_match: boolean | null;
          vacancy_match_id: number;
          work_experience_match: boolean | null;
          work_experience_years: number | null;
        };
        Insert: {
          ai_summary?: string | null;
          created_at?: string;
          driving_license_match?: boolean | null;
          education_match?: boolean | null;
          lacking_behavioural_tags?: string[] | null;
          lacking_skills?: string[] | null;
          location_match?: boolean | null;
          matching_behavioural_tags?: string[] | null;
          matching_skills?: string[] | null;
          salary_match?: boolean | null;
          vacancy_match_id?: number;
          work_experience_match?: boolean | null;
          work_experience_years?: number | null;
        };
        Update: {
          ai_summary?: string | null;
          created_at?: string;
          driving_license_match?: boolean | null;
          education_match?: boolean | null;
          lacking_behavioural_tags?: string[] | null;
          lacking_skills?: string[] | null;
          location_match?: boolean | null;
          matching_behavioural_tags?: string[] | null;
          matching_skills?: string[] | null;
          salary_match?: boolean | null;
          vacancy_match_id?: number;
          work_experience_match?: boolean | null;
          work_experience_years?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancy_match_descriptions_vacancy_match_id_fkey';
            columns: ['vacancy_match_id'];
            isOneToOne: true;
            referencedRelation: 'vacancy_matches';
            referencedColumns: ['id'];
          },
        ];
      };
      vacancy_matches: {
        Row: {
          ai_match_score: number | null;
          created_at: string;
          employer_rejected_at: string | null;
          employer_status: string;
          employer_transaction_id: number | null;
          id: number;
          job_seekeer_transaction_id: number | null;
          job_seeker_id: number;
          job_seeker_rejected_at: string | null;
          job_seeker_status: string;
          updated_at: string;
          vacancy_id: number;
        };
        Insert: {
          ai_match_score?: number | null;
          created_at?: string;
          employer_rejected_at?: string | null;
          employer_status: string;
          employer_transaction_id?: number | null;
          id?: number;
          job_seekeer_transaction_id?: number | null;
          job_seeker_id: number;
          job_seeker_rejected_at?: string | null;
          job_seeker_status: string;
          updated_at?: string;
          vacancy_id: number;
        };
        Update: {
          ai_match_score?: number | null;
          created_at?: string;
          employer_rejected_at?: string | null;
          employer_status?: string;
          employer_transaction_id?: number | null;
          id?: number;
          job_seekeer_transaction_id?: number | null;
          job_seeker_id?: number;
          job_seeker_rejected_at?: string | null;
          job_seeker_status?: string;
          updated_at?: string;
          vacancy_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancy_matches_employee_transaction_id_fkey';
            columns: ['job_seekeer_transaction_id'];
            isOneToOne: false;
            referencedRelation: 'transactions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancy_matches_employer_transaction_id_fkey';
            columns: ['employer_transaction_id'];
            isOneToOne: false;
            referencedRelation: 'transactions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancy_matches_job_seeker_id_fkey';
            columns: ['job_seeker_id'];
            isOneToOne: false;
            referencedRelation: 'job_seekers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancy_matches_vacancy_id_fkey';
            columns: ['vacancy_id'];
            isOneToOne: false;
            referencedRelation: 'vacancies';
            referencedColumns: ['id'];
          },
        ];
      };
      vacancy_questionnaire_answers: {
        Row: {
          created_at: string;
          id: number;
          question_id: number;
          updated_at: string;
          vacancy_id: number;
          value: number;
        };
        Insert: {
          created_at?: string;
          id?: number;
          question_id: number;
          updated_at?: string;
          vacancy_id: number;
          value: number;
        };
        Update: {
          created_at?: string;
          id?: number;
          question_id?: number;
          updated_at?: string;
          vacancy_id?: number;
          value?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancy_questionnaire_answers_question_id_fkey';
            columns: ['question_id'];
            isOneToOne: false;
            referencedRelation: 'vacancy_questionnaire_questions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancy_questionnaire_answers_vacancy_id_fkey';
            columns: ['vacancy_id'];
            isOneToOne: false;
            referencedRelation: 'vacancies';
            referencedColumns: ['id'];
          },
        ];
      };
      vacancy_questionnaire_groups: {
        Row: {
          created_at: string;
          id: number;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      vacancy_questionnaire_questions: {
        Row: {
          created_at: string;
          group_id: number | null;
          id: number;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          group_id?: number | null;
          id?: number;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          group_id?: number | null;
          id?: number;
          name?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancy_questionnaire_questions_group_id_fkey';
            columns: ['group_id'];
            isOneToOne: false;
            referencedRelation: 'vacancy_questionnaire_groups';
            referencedColumns: ['id'];
          },
        ];
      };
      vacancy_skills: {
        Row: {
          created_at: string;
          id: number;
          skill_id: number;
          updated_at: string;
          vacancy_id: number;
        };
        Insert: {
          created_at?: string;
          id?: number;
          skill_id: number;
          updated_at?: string;
          vacancy_id: number;
        };
        Update: {
          created_at?: string;
          id?: number;
          skill_id?: number;
          updated_at?: string;
          vacancy_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'vacancy_skills_skill_id_fkey';
            columns: ['skill_id'];
            isOneToOne: false;
            referencedRelation: 'skills';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'vacancy_skills_vacancy_id_fkey';
            columns: ['vacancy_id'];
            isOneToOne: false;
            referencedRelation: 'vacancies';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      cert_type: 'professional' | 'membership' | 'health_safety';
      feedback_type: 'like' | 'dislike';
      SurveyAudienceType: 'Employer' | 'JobSeeker';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  'public'
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      cert_type: ['professional', 'membership', 'health_safety'],
      feedback_type: ['like', 'dislike'],
      SurveyAudienceType: ['Employer', 'JobSeeker'],
    },
  },
} as const;
