'use client';

import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

import { Button } from '~/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';

type PaginationProps = {
  currentPage: number;
  pageNumbers: (number | string)[];
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  onPageChange: (page: number) => void;
  itemsPerPage?: number;
  onItemsPerPageChange?: (limit: number) => void;
  className?: string;
};

export function Pagination({
  currentPage,
  pageNumbers,
  hasPreviousPage,
  hasNextPage,
  onPageChange,
  itemsPerPage,
  onItemsPerPageChange,
  className = '',
}: PaginationProps) {
  return (
    <div className={`flex items-center justify-between py-4 ${className}`}>
      <div className="flex items-center space-x-2">
        {onItemsPerPageChange && (
          <div className="flex items-center mr-4 space-x-2 ml-2">
            <span className="text-sm text-placeholder">Show</span>
            <Select
              value={String(itemsPerPage)}
              onValueChange={value => onItemsPerPageChange(Number(value))}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={String(itemsPerPage)} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={() => hasPreviousPage && onPageChange(currentPage - 1)}
          disabled={!hasPreviousPage}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        {pageNumbers.map((page, index) =>
          typeof page === 'number' ? (
            <Button
              // eslint-disable-next-line react/no-array-index-key
              key={index}
              variant={currentPage === page ? 'default' : 'outline'}
              size="sm"
              onClick={() => onPageChange(page)}
              className="min-w-8 px-3"
            >
              {page}
            </Button>
          ) : (
            // eslint-disable-next-line react/no-array-index-key
            <span key={index} className="px-1">
              {page}
            </span>
          ),
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={() => hasNextPage && onPageChange(currentPage + 1)}
          disabled={!hasNextPage}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
