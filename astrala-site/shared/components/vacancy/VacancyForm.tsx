'use client';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';

import { VacancyInfoStep } from '~/app/onboarding/employer/(components)/(steps)/VacancyInfoStep';
import {
  getDefaultVacancyValues,
  VacancyFormSchema,
  vacancyFormSchema,
} from '~/app/onboarding/employer/(schemas)/vacancy.schema';

import {
  useCreateVacancy,
  useUpdateVacancy,
} from '~/api/entities/vacancy/queries';

type VacancyFormProps = {
  initialData?: VacancyFormSchema;
  isEdit?: boolean;
  vacancyId?: string;
  onSuccess?: (id: string) => void;
  onCancel: () => void;
  employerId: number;
  companyId: number;
  skipWorkStyleTest?: boolean;
};

export function VacancyForm({
  initialData,
  isEdit = false,
  vacancyId,
  onSuccess,
  onCancel,
  employerId,
  companyId,
  skipWorkStyleTest = false,
}: VacancyFormProps) {
  const router = useRouter();
  const createVacancyMutation = useCreateVacancy();
  const updateVacancyMutation = useUpdateVacancy();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<VacancyFormSchema>({
    resolver: zodResolver(vacancyFormSchema),
    defaultValues: initialData || getDefaultVacancyValues(),
  });

  const handleSubmit = async (data: VacancyFormSchema) => {
    setIsSubmitting(true);

    try {
      if (isEdit && vacancyId) {
        const result = await updateVacancyMutation.mutateAsync({
          id: vacancyId,
          ...data,
          employer_id: employerId,
          company_id: companyId,
        });

        if (result.success) {
          if (onSuccess) {
            onSuccess(vacancyId);
          } else {
            router.push('/dashboard/employer/vacancies');
          }
        }
      } else {
        const result = await createVacancyMutation.mutateAsync({
          ...data,
          employer_id: employerId,
          company_id: companyId,
        });

        if (result.success && result.id) {
          if (skipWorkStyleTest) {
            if (onSuccess) {
              onSuccess(result.id.toString());
            } else {
              router.push('/dashboard/employer/vacancies');
            }
          } else {
            router.push(`/dashboard/employer/work-style-test/${result.id}`);
          }
        }
      }
    } catch (error) {
      console.error('Error submitting vacancy:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full rounded-lg">
      <VacancyInfoStep
        form={form}
        onSubmit={handleSubmit}
        onCancel={onCancel}
        isSubmitting={isSubmitting}
        submitLabel={isEdit ? 'Update Vacancy' : 'Create Vacancy'}
      />
    </div>
  );
}
