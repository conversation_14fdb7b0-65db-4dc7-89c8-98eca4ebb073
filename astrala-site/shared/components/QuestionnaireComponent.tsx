'use client';

import React from 'react';

import { SegmentedProgressBar } from './SegmentedProgressBar';
import { Button } from '~/components/ui/button';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from '~/components/ui/card';
import { Label } from '~/components/ui/label';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';

export type Question = {
  id: number;
  name: string;
  group_id: number | null;
  created_at: string;
};

export type QuestionGroup = {
  id: number;
  name: string;
  created_at: string;
  questions: Question[];
};

export type AnswerTag = {
  id: number;
  created_at: string;
  question_id: number | null;
  value: number;
  tag: string;
  answer_text: string | null;
};

type QuestionnaireProps = {
  groups: QuestionGroup[];
  answerTags: AnswerTag[];
  currentGroupIndex: number;
  answers: Map<number, number>;
  isLoading?: boolean;
  error?: unknown;
  isSubmitting?: boolean;

  title?: string;
  description?: string;
  cardWidth?: string;
  showBackTooltip?: boolean;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  overlayComponent?: React.ReactNode;
  progressBarExtra?: React.ReactNode;

  onAnswerChange: (questionId: number, value: number) => void;
  onNext: () => void;
  onBack: () => void;

  getNextButtonText?: () => string;
  getBackButtonText?: () => string;

  radioOptions?: Array<{ value: string; label: string }>;
};

const QuestionnaireComponent: React.FC<QuestionnaireProps> = ({
  groups,
  answerTags,
  currentGroupIndex,
  answers,
  isLoading = false,
  error = null,
  isSubmitting = false,

  title = 'Job Seeker Behavioural Insight Preferences',
  description = 'Fill the following questions to Discover your Personality Archetype - free',
  cardWidth = 'w-[100%]',
  showBackTooltip = false,
  loadingComponent,
  errorComponent,
  emptyComponent,
  overlayComponent,
  progressBarExtra,

  onAnswerChange,
  onNext,
  onBack,

  getNextButtonText = () => {
    const isLastGroup = currentGroupIndex === groups.length - 1;
    if (isSubmitting) return 'Saving...';
    if (isLastGroup) return 'Finish';
    return 'Next';
  },
  getBackButtonText = () => 'Back',

  radioOptions = [
    { value: '1', label: 'Strongly Disagree' },
    { value: '2', label: 'Disagree' },
    { value: '3', label: 'Neutral' },
    { value: '4', label: 'Agree' },
    { value: '5', label: 'Strongly Agree' },
  ],
}) => {
  const progressPercentage =
    groups.length > 0 ? ((currentGroupIndex + 1) / groups.length) * 100 : 0;

  const answerTagsByQuestionId = React.useMemo(() => {
    const grouped = new Map<number, AnswerTag[]>();
    answerTags.forEach(tag => {
      if (tag.question_id !== null && tag.answer_text) {
        if (!grouped.has(tag.question_id)) {
          grouped.set(tag.question_id, []);
        }
        grouped.get(tag.question_id)!.push(tag);
      }
    });
    return grouped;
  }, [answerTags]);

  const getQuestionOptions = (questionId: number) => {
    const tradeoffOptions = answerTagsByQuestionId.get(questionId);
    if (tradeoffOptions && tradeoffOptions.length > 0) {
      return tradeoffOptions
        .sort((a, b) => a.value - b.value)
        .map(tag => ({
          value: tag.value.toString(),
          label: tag.answer_text || `Option ${tag.value}`,
        }));
    }
    return radioOptions;
  };

  if (isLoading) {
    return (
      loadingComponent || (
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center h-64">
              <div className="text-center">Loading...</div>
            </div>
          </CardContent>
        </Card>
      )
    );
  }

  if (error) {
    return (
      errorComponent || (
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center h-64 flex-col">
              <div className="text-center text-destructive mb-4">
                Error, please try again
              </div>
            </div>
          </CardContent>
        </Card>
      )
    );
  }

  if (groups.length === 0) {
    return (
      emptyComponent || (
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center h-64 flex-col">
              <div className="text-center mb-4">Questions Not Found.</div>
            </div>
          </CardContent>
        </Card>
      )
    );
  }

  const currentGroup = groups[currentGroupIndex] || { questions: [] };
  const isFirstGroup = currentGroupIndex === 0;

  return (
    <div className="relative">
      {overlayComponent}

      <div className="w-full flex justify-center items-center">
        <div className={`${cardWidth} my-6`}>
          {title && <h2 className="font-bold">{title}</h2>}
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}

          <Card className="w-full mt-6">
            <CardHeader>
              <div className="mb-4 flex items-center gap-2">
                <SegmentedProgressBar
                  totalSegments={groups.length}
                  currentSegment={currentGroupIndex}
                  className="w-full"
                />
                <div className="text-right mt-1 text-sm text-gray-500">
                  {Math.round(progressPercentage)}%
                </div>
                {progressBarExtra}
              </div>
              <CardTitle className="flex items-center">
                {currentGroup.name}
              </CardTitle>
            </CardHeader>

            <CardContent>
              <div className="space-y-6">
                {currentGroup.questions.map((question, qIndex) => {
                  const questionOptions = getQuestionOptions(question.id);

                  return (
                    <div key={question.id} className="space-y-3">
                      <div className="font-medium">
                        {qIndex + 1}. {question.name}
                      </div>

                      <RadioGroup
                        value={answers.get(question.id)?.toString()}
                        onValueChange={value =>
                          onAnswerChange(
                            question.id,
                            Number.parseInt(value, 10),
                          )
                        }
                        className="flex justify-between items-center"
                      >
                        {questionOptions.map(option => (
                          <div
                            key={option.value}
                            className="flex flex-col items-center"
                            style={{
                              width: `${100 / questionOptions.length}%`,
                              maxWidth: `${100 / questionOptions.length}%`,
                            }}
                          >
                            <RadioGroupItem
                              value={option.value}
                              id={`q${question.id}-${option.value}`}
                              className="mb-1"
                            />
                            <Label
                              htmlFor={`q${question.id}-${option.value}`}
                              className="text-xs text-center px-1"
                            >
                              {option.label}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>
                  );
                })}
              </div>
            </CardContent>

            <div className="sticky bottom-0">
              <CardFooter className="flex justify-between">
                <Button
                  variant="secondary"
                  onClick={onBack}
                  disabled={isFirstGroup && !showBackTooltip}
                >
                  {getBackButtonText()}
                </Button>

                <Button
                  onClick={onNext}
                  disabled={isSubmitting}
                  variant="default"
                >
                  {getNextButtonText()}
                </Button>
              </CardFooter>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default QuestionnaireComponent;
