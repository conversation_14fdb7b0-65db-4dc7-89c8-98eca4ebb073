import React from 'react';
import { Building, ChevronRight, HelpCircle, LogOut, User } from 'lucide-react';
import Link from 'next/link';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import { useAuth } from '~/providers/AuthProvider';

type ProfileProps = {
  initials: string;
  name: string;
  email: string;
  profileUrl?: string;
  settingsUrl?: string;
  role: 'employer' | 'job-seeker';
};

export default function UserProfile({
  initials,
  name,
  email,
  settingsUrl,
  role,
}: ProfileProps) {
  const { signOut } = useAuth();

  const getSettingsUrl = () => {
    const basePath =
      role === 'employer' ? '/dashboard/employer' : '/dashboard/job-seeker';
    return settingsUrl || `${basePath}/settings`;
  };

  const getSettingsInfo = () => {
    if (role === 'employer') {
      return {
        text: 'Edit Company',
        icon: <Building className="mr-2 h-4 w-4" />,
      };
    }
    return {
      text: 'Edit Profile',
      icon: <User className="mr-2 h-4 w-4" />,
    };
  };

  const settingsInfo = getSettingsInfo();

  return (
    <div className="p-4 border m-4 rounded-md bg-[#101010]">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center gap-3 cursor-pointer">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#E2E8F0] text-xs font-medium text-[#0F172A]">
              {initials}
            </div>
            <div className="flex-1 overflow-hidden">
              <p className="truncate text-sm font-medium">{name}</p>
              <p className="truncate text-xs">{email}</p>
            </div>
            <ChevronRight className="h-4 w-4 text-placeholder" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="start" sideOffset={5}>
          <DropdownMenuGroup>
            <a href="mailto:<EMAIL>">
              <DropdownMenuItem>
                <HelpCircle className="mr-2 h-4 w-4" />
                <span>Support</span>
              </DropdownMenuItem>
            </a>
            <Link href={getSettingsUrl()}>
              <DropdownMenuItem>
                {settingsInfo.icon}
                <span>{settingsInfo.text}</span>
              </DropdownMenuItem>
            </Link>
          </DropdownMenuGroup>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            className="text-destructive"
            onClick={() => signOut()}
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>Logout</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
