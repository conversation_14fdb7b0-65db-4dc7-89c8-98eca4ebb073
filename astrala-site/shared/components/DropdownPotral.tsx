import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

export const DropdownPortal = ({
  children,
  targetRef,
  isOpen,
}: {
  children: React.ReactNode;
  targetRef: React.RefObject<HTMLElement>;
  isOpen: boolean;
}) => {
  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null);
  const [dropdownStyles, setDropdownStyles] = useState({
    top: 0,
    left: 0,
    width: 0,
  });

  useEffect(() => {
    const node = document.createElement('div');
    document.body.append(node);
    setPortalNode(node);

    return () => {
      node.remove();
    };
  }, []);

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  useEffect(() => {
    if (targetRef.current && isOpen) {
      const updatePosition = () => {
        const rect = targetRef.current?.getBoundingClientRect();
        if (rect) {
          setDropdownStyles({
            top: rect.bottom + window.scrollY,
            left: rect.left + window.scrollX,
            width: rect.width,
          });
        }
      };

      updatePosition();

      window.addEventListener('scroll', updatePosition);
      window.addEventListener('resize', updatePosition);

      return () => {
        window.removeEventListener('scroll', updatePosition);
        window.removeEventListener('resize', updatePosition);
      };
    }
  }, [targetRef, isOpen]);

  if (!portalNode || !isOpen) return null;

  return createPortal(
    <div
      className="fixed z-50 bg-black text-white border rounded shadow-lg max-h-60 overflow-auto"
      style={{
        position: 'absolute',
        top: `${dropdownStyles.top}px`,
        left: `${dropdownStyles.left}px`,
        width: `${dropdownStyles.width}px`,
      }}
    >
      {children}
    </div>,
    portalNode,
  );
};
