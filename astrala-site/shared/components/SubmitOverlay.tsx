import React from 'react';
import { Loader2 } from 'lucide-react';

type SubmitOverlayProps = {
  isSubmitting: boolean;
  message?: string;
};

const SubmitOverlay: React.FC<SubmitOverlayProps> = ({
  isSubmitting,
  message = 'Saving your information...',
}) => {
  if (!isSubmitting) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-[9999] backdrop-blur-md bg-black/30"
      style={{ pointerEvents: 'all' }}
    >
      <div className="flex flex-col items-center p-6 bg-white/10 rounded-xl shadow-lg backdrop-blur-sm">
        <Loader2 className="w-12 h-12 text-white animate-spin" />
        <p className="font-bold text-xl text-white mt-4">{message}</p>
      </div>
    </div>
  );
};

export default SubmitOverlay;
