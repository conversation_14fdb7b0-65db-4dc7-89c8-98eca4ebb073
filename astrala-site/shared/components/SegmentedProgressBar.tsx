import React from 'react';
import { motion } from 'framer-motion';

export const SegmentedProgressBar: React.FC<{
  totalSegments: number;
  currentSegment: number;
  className?: string;
}> = ({ totalSegments, currentSegment, className = '' }) => {
  const [prevSegment, setPrevSegment] = React.useState(currentSegment);
  const [direction, setDirection] = React.useState<'forward' | 'backward'>(
    'forward',
  );

  React.useEffect(() => {
    if (currentSegment > prevSegment) {
      setDirection('forward');
    } else if (currentSegment < prevSegment) {
      setDirection('backward');
    }
    setPrevSegment(currentSegment);
  }, [currentSegment, prevSegment]);

  return (
    <div className={`flex w-full gap-1 ${className}`}>
      {Array.from({ length: totalSegments }).map((_, index) => {
        const isCompleted = index < currentSegment;
        const isActive = index === currentSegment;
        const isPrevious =
          index === currentSegment + 1 && direction === 'backward';

        return (
          <div
            // eslint-disable-next-line react/no-array-index-key
            key={index}
            className="h-2 flex-1 rounded-full overflow-hidden bg-hover"
          >
            {isActive && direction === 'forward' && (
              <motion.div
                className="h-full bg-primary"
                initial={{ width: '0%' }}
                animate={{ width: '100%' }}
                transition={{
                  duration: 0.8,
                  ease: 'easeInOut',
                }}
              />
            )}

            {isPrevious && (
              <motion.div
                className="h-full bg-primary"
                initial={{ width: '100%' }}
                animate={{ width: '0%' }}
                transition={{
                  duration: 0.8,
                  ease: 'easeInOut',
                }}
              />
            )}

            {(isCompleted || (isActive && direction === 'backward')) && (
              <div className="h-full w-full bg-primary" />
            )}
          </div>
        );
      })}
    </div>
  );
};
