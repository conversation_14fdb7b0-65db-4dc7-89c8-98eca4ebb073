'use client';

import React from 'react';
import { ChevronRight } from 'lucide-react';

import Logo from '~/assets/logo.svg';

export type Step = {
  id: string;
  number: number;
  label: string;
  showDivider: boolean;
};

type OnboardingHeaderProps<StepType extends string = string> = {
  currentStep: StepType;
  steps: Step[];
  onStepClick?: (step: StepType) => void;
  canGoToStep?: (step: StepType) => boolean;
  isStepCompleted?: (step: StepType) => boolean;
};

export default function OnboardingHeader<StepType extends string = string>({
  currentStep,
  steps,
  onStepClick,
  canGoToStep,
  isStepCompleted,
}: OnboardingHeaderProps<StepType>) {
  const checkStepCompleted = (stepId: string) => {
    const currentStepObj = steps.find(step => step.id === currentStep);
    const stepObj = steps.find(step => step.id === stepId);

    if (!currentStepObj || !stepObj) return false;

    if (stepObj.number < currentStepObj.number) return true;

    if (isStepCompleted && steps.some(step => step.id === stepId)) {
      return isStepCompleted(stepId as StepType);
    }

    return false;
  };

  const getStepClassNames = (stepId: string) => {
    if (currentStep === stepId) {
      return 'bg-primary text-primary-foreground border';
    }
    if (checkStepCompleted(stepId)) {
      return 'bg-primary text-primary-foreground border';
    }
    return 'bg-secondary text-foreground';
  };

  const isStepAccessible = (stepId: string) => {
    if (!canGoToStep) return false;
    return canGoToStep(stepId as StepType);
  };

  const handleStepClick = (stepId: string) => {
    if (onStepClick && isStepAccessible(stepId)) {
      onStepClick(stepId as StepType);
    }
  };

  return (
    <div className="border-b bg-background text-foreground font-bold">
      <div className="flex items-center h-16">
        <div className="w-1/3 pl-6">
          <Logo className="w-40" />
        </div>

        <div className="w-2/3 flex justify-center">
          <div className="flex items-center gap-x-2 md:gap-x-6 w-full">
            {steps.map(step => (
              <React.Fragment key={step.id}>
                <button
                  className={`flex items-center w-full min-w-0 ${
                    isStepAccessible(step.id) ? 'cursor-pointer' : ''
                  }`}
                  onClick={() => handleStepClick(step.id)}
                >
                  <div
                    className={`h-8 w-8 aspect-square rounded-full flex items-center justify-center text-center font-bold ${getStepClassNames(
                      step.id,
                    )}`}
                  >
                    {checkStepCompleted(step.id) ? (
                      <ChevronRight className="h-4 w-4" />
                    ) : (
                      <span
                        className={`${checkStepCompleted(step.id) ? 'text-primary-foreground' : 'text-foreground'}`}
                      >
                        {step.number}
                      </span>
                    )}
                  </div>
                  <div
                    className={`ml-2 text-start text-xs 2xl:text-base w-full ${
                      currentStep === step.id || checkStepCompleted(step.id)
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }`}
                  >
                    {step.label}
                  </div>
                </button>

                {step.showDivider && (
                  <div className="flex items-center justify-center mx-6 text-foreground">
                    <ChevronRight
                      className={
                        checkStepCompleted(step.id)
                          ? 'text-foreground'
                          : 'text-muted-foreground'
                      }
                    />
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        <div className="w-1/3" />
      </div>
    </div>
  );
}
