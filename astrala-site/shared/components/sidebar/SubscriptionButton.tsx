import React from 'react';
import { differenceInDays } from 'date-fns';
import { CreditCard, Zap } from 'lucide-react';
import { useRouter } from 'next/navigation';

import {
  useCompanySubscription,
  useCompanyTrial,
} from '~/api/features/subscription/queries.ts';
import { Button } from '~/components/ui/button.tsx';
import { useAuth } from '~/providers/AuthProvider.tsx';

export function SubscriptionButton() {
  const router = useRouter();
  const { employerProfile } = useAuth();
  const companyId = employerProfile?.company_id;
  const { data: subscription } = useCompanySubscription(companyId!);
  const { data: trial } = useCompanyTrial(companyId!);

  if (!subscription) {
    return (
      <div className="px-3 mb-3">
        <Button
          variant="default"
          size="lg"
          className="w-full justify-start"
          onClick={() => router.push('/dashboard/employer/subscription')}
        >
          <div className="flex items-center">
            <CreditCard className="mr-2 h-4 w-4" />
            Manage Subscription
          </div>
        </Button>
      </div>
    );
  }

  const isTrial = subscription.subscription_status === 'trial';
  const isActive = subscription.subscription_status === 'active';
  const isExpired = subscription.subscription_status === 'expired';

  let daysLeft = 0;
  if (isTrial && trial?.ends_at) {
    const diff = differenceInDays(new Date(trial.ends_at), new Date());
    daysLeft = diff > 0 ? diff + 1 : 0;
  }

  const planName = subscription.subscription_plan
    ? subscription.subscription_plan.charAt(0).toUpperCase() +
      subscription.subscription_plan.slice(1)
    : '';

  const periodEndDate = subscription.current_period_end
    ? new Date(subscription.current_period_end).toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      })
    : '';

  return (
    <div className="px-3 mb-3">
      <div className="bg-muted/50 rounded-lg p-3 mb-2">
        {isTrial && (
          <>
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-semibold text-foreground">
                Trial Active
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              {daysLeft > 0
                ? `${daysLeft} day${daysLeft === 1 ? '' : 's'} remaining`
                : 'Expires today'}
            </p>
          </>
        )}

        {isActive && (
          <>
            <div className="flex items-center gap-2 mb-1">
              <CreditCard className="h-4 w-4 text-primary" />
              <span className="text-sm font-semibold text-foreground">
                {planName} Plan
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              Renews on {periodEndDate}
            </p>
          </>
        )}

        {isExpired && (
          <>
            <div className="flex items-center gap-2 mb-1">
              <CreditCard className="h-4 w-4 text-destructive" />
              <span className="text-sm font-semibold text-destructive">
                Subscription Expired
              </span>
            </div>
            <p className="text-xs text-muted-foreground">Upgrade to continue</p>
          </>
        )}
      </div>

      <Button
        variant={isTrial || isExpired ? 'default' : 'outline'}
        size="lg"
        className="w-full justify-center"
        onClick={() => router.push('/dashboard/employer/subscription')}
      >
        {isTrial || isExpired ? (
          <div className="flex items-center">
            <Zap className="mr-2 h-4 w-4" />
            Upgrade Subscription
          </div>
        ) : (
          <div className="flex items-center">
            <CreditCard className="mr-2 h-4 w-4" />
            Manage Subscription
          </div>
        )}
      </Button>
    </div>
  );
}
