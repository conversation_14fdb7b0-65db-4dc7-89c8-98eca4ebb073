import React from 'react';
import { motion } from 'framer-motion';

export default function ActiveButton({
  children,
  className = '',
  layoutId = 'active-sidebar-item',
}: {
  children: React.ReactNode;
  className?: string;
  layoutId?: string;
}) {
  return (
    <motion.div
      layoutId={layoutId}
      className={`relative rounded-lg overflow-hidden ${className}`}
      transition={{
        type: 'spring',
        stiffness: 500,
        damping: 30,
      }}
    >
      <div
        className="absolute inset-0"
        style={{ backgroundColor: '#4B9C8840' }}
      />

      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(315deg, #0e2d28 0%, #23665f 100%)',
        }}
      />

      <div
        className="absolute inset-0 rounded-lg pointer-events-none"
        style={{
          padding: '1px',
          background: `
            linear-gradient(135deg,
              #98F9FF 0%,
              rgba(152, 249, 255, 0.5) 30%,
              transparent 60%
            ),
            linear-gradient(-45deg,
              #EABFFF 0%,
              rgba(234, 191, 255, 0.5) 30%,
              transparent 60%
            )
          `,
          WebkitMask:
            'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
          WebkitMaskComposite: 'xor',
          maskComposite: 'exclude',
        }}
      />

      <div className="relative z-10">{children}</div>
    </motion.div>
  );
}
