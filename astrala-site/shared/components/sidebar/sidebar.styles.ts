import { cva } from 'class-variance-authority';

export const sidebarItem = cva(
  'flex items-center justify-between rounded-lg transition-colors',
  {
    variants: {
      variant: {
        item: 'px-4 py-3 text-base font-medium',
        child: 'px-4 py-2.5 text-sm font-medium',
      },
      active: {
        true: '',
        false: 'hover:bg-secondary',
      },
    },
    defaultVariants: {
      variant: 'item',
      active: false,
    },
  },
);

export const sidebarBadge = cva('text-xs px-2 py-1 rounded-full', {
  variants: {
    active: {
      true: 'bg-primary',
      false: 'bg-[#4B4B50]',
    },
  },
});

export const sidebarIcon = cva('', {
  variants: {
    variant: {
      item: 'h-4 w-4',
      child: 'h-3.5 w-3.5',
    },
  },
  defaultVariants: {
    variant: 'item',
  },
});
