import Link from 'next/link';

import ActiveButton from './ActiveButton';
import { sidebarBadge, sidebarItem } from './sidebar.styles';

export default function SidebarChild({
  title,
  href,
  badge,
  active,
}: {
  title: string;
  href: string;
  badge?: string | number | { count: string | number; hasUnread?: boolean };
  active: boolean;
}) {
  const badgeData =
    typeof badge === 'object' && badge !== null
      ? badge
      : { count: badge, hasUnread: false };

  const content = (
    <div className={sidebarItem({ variant: 'child', active })}>
      <span>{title}</span>
      {badgeData.count !== undefined && (
        <div className="flex items-center gap-2">
          <span className={sidebarBadge({ active })}>{badgeData.count}</span>
          {badgeData.hasUnread && (
            <span className="h-2 w-2 rounded-full bg-red-500 ring-2 ring-background" />
          )}
        </div>
      )}
    </div>
  );

  return (
    <Link href={href} className="block">
      {active ? (
        <ActiveButton layoutId="sidebar-child-active">{content}</ActiveButton>
      ) : (
        content
      )}
    </Link>
  );
}
