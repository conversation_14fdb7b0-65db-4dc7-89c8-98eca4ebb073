import React from 'react';
import { usePathname } from 'next/navigation';

import UserProfile from '../UserProfile';

import SidebarGroup from './SidebarGroup';
import SidebarItem from './SidebarItem';
import { SubscriptionButton } from './SubscriptionButton';
import Logo from '~/assets/logo.svg';

type NavItem = {
  title: string;
  href: string;
  icon?: React.ReactNode;
  badge?: string | number | { count: string | number; hasUnread?: boolean };
  children?: Array<{
    title: string;
    href: string;
    badge?: string | number | { count: string | number; hasUnread?: boolean };
  }>;
};

type SideBarProps = {
  role: 'employer' | 'job-seeker';
  navItems: NavItem[];
  profileProps: {
    initials: string;
    name: string;
    email: string;
    profileUrl?: string;
  };
  supportUrl?: string;
  settingsUrl?: string;
};

export default function Sidebar({
  navItems,
  profileProps,
  role,
}: SideBarProps) {
  const pathname = usePathname();

  const isTopLevelActive = (href: string) => pathname === href;
  const isChildActive = (href: string) =>
    pathname === href || pathname.startsWith(`${href}/`);
  const isParentActive = (item: NavItem) =>
    item.children
      ? item.children.some(child => isChildActive(child.href))
      : false;

  return (
    <>
      <aside className="w-80 border-r bg-background text-white h-screen fixed left-0 z-50">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-start px-4 py-6">
            <Logo className="w-2/3" />
          </div>

          <div className="flex-1 space-y-2 overflow-y-auto px-3">
            {navItems.map(item =>
              item.children ? (
                <SidebarGroup
                  key={item.title}
                  title={item.title}
                  icon={item.icon}
                  badge={item.badge}
                  children={item.children}
                  parentActive={isParentActive(item)}
                  isChildActive={isChildActive}
                />
              ) : (
                <SidebarItem
                  key={item.title}
                  title={item.title}
                  href={item.href}
                  icon={item.icon}
                  badge={item.badge}
                  active={isTopLevelActive(item.href)}
                />
              ),
            )}
          </div>

          {role === 'employer' && <SubscriptionButton />}

          <div className="mx-4 border-t border-gray-700/50" />
          <UserProfile {...profileProps} role={role} />
        </div>
      </aside>
      <div className="w-80 min-w-80 shrink-0" />
    </>
  );
}
