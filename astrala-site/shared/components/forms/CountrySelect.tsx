import React, { useMemo, useState } from 'react';
import { Control, FieldPath, useController } from 'react-hook-form';
import { Check, ChevronsUpDown } from 'lucide-react';

import { Button } from '~/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '~/components/ui/popover';
import { countries } from '~/constants/countries.ts';

type CountrySelectProps<T extends object> = {
  control: Control<T>;
  name: FieldPath<T>;
  label: string;
};

export const CountrySelect = <T extends object>({
  control,
  name,
  label,
}: CountrySelectProps<T>) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const { field, fieldState } = useController({
    name,
    control,
  });

  const { error } = fieldState;

  const filteredCountries = useMemo(() => {
    if (!searchQuery) return countries;

    const lowerCaseQuery = searchQuery.toLowerCase();
    return countries.filter(
      country =>
        country.label.toLowerCase().includes(lowerCaseQuery) ||
        country.value.toLowerCase().includes(lowerCaseQuery),
    );
  }, [searchQuery]);

  return (
    <FormItem>
      <FormLabel className="text-white">{label}</FormLabel>
      <FormControl>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-full justify-between bg-background text-white border hover:bg-hover hover:text-white"
            >
              {field.value
                ? countries.find(country => country.value === field.value)
                    ?.label
                : `Select ${label.toLowerCase()}...`}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50 text-white" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0 bg-background border">
            <Command shouldFilter={false} className="bg-background">
              <CommandInput
                placeholder={`Search ${label.toLowerCase()}...`}
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="bg-background text-white placeholder:text-gray-400"
              />
              <CommandList className="bg-background text-white">
                {filteredCountries.length === 0 && (
                  <CommandEmpty className="text-white">
                    No country found.
                  </CommandEmpty>
                )}
                <CommandGroup className="text-white">
                  {filteredCountries.map(country => (
                    <CommandItem
                      key={country.value}
                      value={country.value}
                      className="cursor-pointer text-white hover:bg-hover hover:text-white focus:bg-hover focus:text-white data-[selected=true]:bg-hover data-[selected=true]:text-white"
                      onSelect={currentValue => {
                        field.onChange(
                          currentValue === field.value ? '' : currentValue,
                        );
                        setOpen(false);
                      }}
                    >
                      <Check
                        className={`mr-2 h-4 w-4 text-white ${
                          field.value === country.value
                            ? 'opacity-100'
                            : 'opacity-0'
                        }`}
                      />
                      {country.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </FormControl>
      <FormMessage className="text-destructive">{error?.message}</FormMessage>
    </FormItem>
  );
};
