import React, { useState } from 'react';
import {
  Control,
  FieldValues,
  Path,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import { ChevronsUpDown, Loader2, Plus, X } from 'lucide-react';

import { Skill } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema';

import { useSkillsGroupManager } from '~/api/entities/skill/queries.ts';
import { Button } from '~/components/ui/button.tsx';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command';
import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form.tsx';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '~/components/ui/popover';

type FormWithSkills = {
  skills: Skill[];
};

type SkillsFormProps<T extends FieldValues & FormWithSkills> = {
  control: Control<T>;
  setValue: UseFormSetValue<T>;
  watch: UseFormWatch<T>;
};

export const SkillsForm = <T extends FieldValues & FormWithSkills>({
  control,
  setValue,
  watch,
}: SkillsFormProps<T>): React.ReactElement => {
  const formSkills: Skill[] = watch('skills' as Path<T>) || [];
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const {
    getFilteredGroups,
    addSkillToList,
    removeSkill,
    isAddingSkill,
    isRemovingSkill,
  } = useSkillsGroupManager(formSkills, setValue);

  const handleSelect = (skill: Skill) => {
    addSkillToList(skill);
    setOpen(false);
    setSearchQuery('');
  };

  const handleRemoveSkill = async (skillId: number) => {
    await removeSkill(skillId);
  };

  const allGroups = getFilteredGroups();

  return (
    <div className="space-y-4 p-3 text-white">
      <h2 className="text-lg font-medium text-white">Skills</h2>

      <FormField
        control={control}
        name={'skills' as Path<T>}
        render={() => (
          <FormItem>
            <FormLabel className="text-white">Add Skills</FormLabel>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className="w-full justify-between bg-background text-white border hover:bg-hover hover:text-white"
                  disabled={isAddingSkill}
                >
                  Search for skills...
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50 text-white" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0 bg-background border">
                <Command
                  shouldFilter={false}
                  className="bg-background text-white"
                >
                  <CommandInput
                    placeholder="Search for skills..."
                    value={searchQuery}
                    onValueChange={setSearchQuery}
                    className="bg-background text-white placeholder:text-gray-400"
                  />
                  <CommandList className="bg-background text-white max-h-60">
                    {allGroups.map(group => {
                      const visibleSkills = group.skills.filter(
                        (skill: Skill) =>
                          !searchQuery ||
                          skill.name
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase()),
                      );

                      if (visibleSkills.length === 0) return null;

                      return (
                        <CommandGroup
                          key={group.id}
                          heading={group.name}
                          className="text-white"
                        >
                          {visibleSkills.map((skill: Skill) => (
                            <CommandItem
                              key={skill.id}
                              onSelect={() => handleSelect(skill)}
                              className="cursor-pointer text-white hover:bg-hover hover:text-white focus:bg-hover focus:text-white"
                            >
                              <Plus className="mr-2 h-4 w-4 text-primary" />
                              {skill.name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      );
                    })}

                    {allGroups.every(
                      // eslint-disable-next-line @typescript-eslint/no-explicit-any
                      (group: any) =>
                        group.skills.filter(
                          (skill: Skill) =>
                            !searchQuery ||
                            skill.name
                              .toLowerCase()
                              .includes(searchQuery.toLowerCase()),
                        ).length === 0,
                    ) && (
                      <CommandEmpty className="text-white p-2">
                        No skills found.
                      </CommandEmpty>
                    )}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage className="text-destructive" />
          </FormItem>
        )}
      />

      {formSkills.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {formSkills.map((skill: Skill) => (
            <div
              key={skill.id}
              className="px-3 py-1 border rounded-md flex items-center gap-1 bg-background text-white"
              style={{
                maxWidth: '150px',
                width: 'auto',
                overflow: 'hidden',
                boxSizing: 'border-box',
              }}
            >
              <span
                className="truncate text-sm"
                title={skill.name}
                style={{ maxWidth: 'calc(100% - 20px)' }}
              >
                {skill.name}
              </span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 flex-shrink-0 hover:bg-hover hover:text-white"
                onClick={() => handleRemoveSkill(skill.id)}
                disabled={isRemovingSkill}
              >
                {isRemovingSkill ? (
                  <Loader2 className="h-3 w-3 animate-spin text-destructive" />
                ) : (
                  <X className="h-3 w-3 text-destructive" />
                )}
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
