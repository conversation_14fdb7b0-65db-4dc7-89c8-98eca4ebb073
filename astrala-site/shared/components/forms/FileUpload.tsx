import React, { useRef, useState } from 'react';
import { FileText, RefreshCw, Trash2, Upload } from 'lucide-react';
import Image from 'next/image';

import FileUploadIllustration from '~/assets/images/file-input.png';
import { Button } from '~/components/ui/button.tsx';

export const FILE_TYPES = {
  document: [
    { extension: '.pdf', mimeType: 'application/pdf' },
    { extension: '.doc', mimeType: 'application/msword' },
    {
      extension: '.docx',
      mimeType:
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    },
  ],
  image: [
    { extension: '.jpg', mimeType: 'image/jpeg' },
    { extension: '.jpeg', mimeType: 'image/jpeg' },
    { extension: '.png', mimeType: 'image/png' },
    { extension: '.gif', mimeType: 'image/gif' },
    { extension: '.webp', mimeType: 'image/webp' },
  ],
  spreadsheet: [
    { extension: '.xls', mimeType: 'application/vnd.ms-excel' },
    {
      extension: '.xlsx',
      mimeType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
    { extension: '.csv', mimeType: 'text/csv' },
  ],
};

export type FileTypeCategory = keyof typeof FILE_TYPES;

export type FileUploadProps = {
  onChange: (files: File[]) => void;
  value?: File[];
  maxSizeMB?: number;
  fileTypeCategory?: FileTypeCategory | FileTypeCategory[];
  multiple?: boolean;
  label?: string;
  inputId?: string;
  disabled?: boolean;
  withoutImage?: boolean;
};

export const FileUpload = ({
  onChange,
  value,
  maxSizeMB = 2,
  fileTypeCategory = 'document',
  multiple = false,
  inputId = 'file-upload',
  disabled = false,
  withoutImage = false,
}: FileUploadProps) => {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [files, setFiles] = useState<File[]>(value || []);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getAllowedFileTypes = () => {
    if (Array.isArray(fileTypeCategory)) {
      return fileTypeCategory.flatMap(category => FILE_TYPES[category]);
    }
    return FILE_TYPES[fileTypeCategory];
  };

  const allowedFileTypes = getAllowedFileTypes();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      const fileArray = Array.from(selectedFiles);
      validateFiles(fileArray);
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const validateFiles = (newFiles: File[]) => {
    setError(null);

    const allowedMimeTypes = allowedFileTypes.map(type => type.mimeType);

    const invalidFiles = newFiles.filter(
      file => !allowedMimeTypes.includes(file.type),
    );

    if (invalidFiles.length > 0) {
      const extensions = allowedFileTypes
        .map(type => type.extension)
        .join(', ');
      setError(`Please select files in ${extensions} format`);
      return;
    }

    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    const largeFiles = newFiles.filter(file => file.size > maxSizeBytes);
    if (largeFiles.length > 0) {
      setError(`File size should not exceed ${maxSizeMB}MB`);
      return;
    }

    let updatedFiles: File[];

    if (multiple) {
      updatedFiles = [...files, ...newFiles];
    } else {
      updatedFiles = newFiles.slice(0, 1);
    }

    setFiles(updatedFiles);
    onChange(updatedFiles);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const fileArray = Array.from(e.dataTransfer.files);
      validateFiles(multiple ? fileArray : [fileArray[0]]);
    }
  };

  const removeFile = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
    onChange(newFiles);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const acceptAttribute = allowedFileTypes
    .map(type => `${type.extension},${type.mimeType}`)
    .join(',');

  const getFormattedFileTypes = () => {
    return allowedFileTypes
      .map(type => type.extension.replace('.', ''))
      .join(', ')
      .toUpperCase();
  };

  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // If files are selected, show a different UI
  if (files.length > 0) {
    return (
      <div className="w-full">
        <input
          type="file"
          id={inputId}
          ref={fileInputRef}
          className="hidden"
          accept={acceptAttribute}
          onChange={handleFileChange}
          multiple={multiple}
        />

        <div className="rounded-lg border border-white border-dashed p-6 bg-black text-white">
          <div className="space-y-3">
            {files.map((file, index) => (
              <div key={`${file.name}`} className="space-y-2">
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <FileText className="h-5 w-5 text-blue" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium mb-1">Selected file:</p>
                    <p className="text-sm text-muted-foreground truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBrowseClick}
                    disabled={disabled}
                    className="flex-1"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Change
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeFile(index)}
                    disabled={disabled}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {error && (
          <div className="mt-2 text-sm text-destructive bg-red-50 p-2 rounded">
            {error}
          </div>
        )}
      </div>
    );
  }

  // Default upload UI when no files are selected
  return (
    <div className="w-full">
      <div
        className={`rounded-lg border border-white border-dashed p-6 flex flex-col items-center bg-black text-white justify-center ${
          dragActive && 'border-blue border'
        } ${!withoutImage ? 'h-72' : ''}`}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id={inputId}
          ref={fileInputRef}
          className="hidden"
          accept={acceptAttribute}
          onChange={handleFileChange}
          multiple={multiple}
        />

        <div className="flex flex-col items-center justify-center gap-4">
          {!withoutImage && (
            <div className="mb-2">
              <Image
                src={FileUploadIllustration}
                alt="File upload"
                width={128}
                height={128}
                priority
              />
            </div>
          )}

          <div className="text-center">
            <span className="text-base mb-1 flex items-center justify-center gap-1">
              <Upload className="inline-block" size={18} />
              Drag & Drop or{' '}
              <button
                type="button"
                className="text-blue hover:underline"
                onClick={handleBrowseClick}
              >
                Browse Files
              </button>
            </span>

            {!withoutImage && (
              <Button
                variant="default"
                disabled={disabled}
                className="w-full my-2"
                onClick={handleBrowseClick}
              >
                Choose file
              </Button>
            )}
          </div>
        </div>
      </div>
      <p className="text-muted-foreground mt-1">
        {getFormattedFileTypes()} - Max File Size {maxSizeMB}MB
      </p>

      {error && (
        <div className="mt-2 text-sm text-destructive bg-red-50 p-2 rounded">
          {error}
        </div>
      )}
    </div>
  );
};
