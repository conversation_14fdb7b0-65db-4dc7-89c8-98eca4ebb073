import React from 'react';
import { Control } from 'react-hook-form';

import { VacancyFormSchema } from '~/app/onboarding/employer/(schemas)/vacancy.schema.ts';
import { MainInformationFormSchema } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema.ts';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form.tsx';
import { Input } from '~/components/ui/input.tsx';

type SalaryFormProps = {
  control: Control<VacancyFormSchema> | Control<MainInformationFormSchema>;
};

export const SalaryForm: React.FC<SalaryFormProps> = ({ control }) => {
  return (
    <div className="space-y-4 p-2">
      <h3 className="text-lg font-medium">Paid Salary</h3>
      <div className="grid grid-cols-2 gap-4">
        <FormField
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          control={control}
          name="salary_min"
          render={({ field }) => (
            <FormItem>
              <FormLabel>From, £</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="e.g. 30000"
                  {...field}
                  value={field.value || ''}
                  onChange={e => {
                    const value =
                      e.target.value === ''
                        ? undefined
                        : Number(e.target.value);
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          control={control}
          name="salary_max"
          render={({ field }) => (
            <FormItem>
              <FormLabel>To, £</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="e.g. 50000"
                  {...field}
                  value={field.value || ''}
                  onChange={e => {
                    const value =
                      e.target.value === ''
                        ? undefined
                        : Number(e.target.value);
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
