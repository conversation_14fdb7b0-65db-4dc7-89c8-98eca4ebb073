import React from 'react';

import { Card, CardContent } from '~/components/ui/card.tsx';

type SectionContainerProps = {
  id: string;
  sectionRef: React.RefObject<HTMLDivElement>;
  children: React.ReactNode;
  className?: string;
};

export const SectionContainer: React.FC<SectionContainerProps> = ({
  id,
  sectionRef,
  children,
  className,
}) => {
  return (
    <Card
      ref={sectionRef}
      id={`${id}-section`}
      className={`pt-4 mb-8 w-full max-w-full overflow-hidden ${className}`}
    >
      <CardContent className="overflow-x-hidden w-full">{children}</CardContent>
    </Card>
  );
};
