export const SUBSCRIPTION_PLANS = {
  starter: {
    name: 'Starter',
    seats: 1,
    jobs: 2,
    matches: 5,
  },
  pro: {
    name: 'Pro',
    seats: 3,
    jobs: 6,
    matches: 20,
  },
  team: {
    name: 'Team',
    seats: 8,
    jobs: 999999,
    matches: 999999,
  },
} as const;

export type SubscriptionPlan = keyof typeof SUBSCRIPTION_PLANS | null;
export type SubscriptionStatus = 'trial' | 'active' | 'canceled' | 'expired';
export type BillingPeriod = 'monthly' | 'annual' | null;

export const STRIPE_PRICE_IDS = {
  starter_monthly: process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY!,
  starter_annual: process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL!,
  pro_monthly: process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY!,
  pro_annual: process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL!,
  team_monthly: process.env.NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY!,
  team_annual: process.env.NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL!,
} as const;

export function getStripePriceId(
  plan: SubscriptionPlan,
  period: BillingPeriod,
): string {
  const key = `${plan}_${period}` as keyof typeof STRIPE_PRICE_IDS;
  return STRIPE_PRICE_IDS[key];
}
