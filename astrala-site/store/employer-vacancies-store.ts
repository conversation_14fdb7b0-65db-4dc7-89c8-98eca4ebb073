import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Vacancy = {
  id: number;
  title: string;
};

type EmployerVacancyState = {
  selectedVacancyId: number | null;
  vacancies: Vacancy[];
  setSelectedVacancyId: (id: number | null) => void;
  setVacancies: (vacancies: Vacancy[]) => void;
};

export const useEmployerVacancyStore = create<EmployerVacancyState>()(
  persist(
    set => ({
      selectedVacancyId: null,
      vacancies: [],
      setSelectedVacancyId: id => set({ selectedVacancyId: id }),
      setVacancies: vacancies => set({ vacancies }),
    }),
    {
      name: 'employer-vacancy-store',
    },
  ),
);
