import { useEffect, useRef } from 'react';

export function usePageTitle() {
  const originalTitle = useRef<string>('');
  const hasNewMessage = useRef(false);
  const blinkInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      originalTitle.current = document.title;
    }

    const handleFocus = () => {
      if (hasNewMessage.current) {
        clearBlinking();
      }
    };

    const handleVisibilityChange = () => {
      if (!document.hidden && hasNewMessage.current) {
        clearBlinking();
      }
    };

    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearBlinking();
    };
  }, []);

  const clearBlinking = () => {
    if (blinkInterval.current) {
      clearInterval(blinkInterval.current);
      blinkInterval.current = null;
    }
    document.title = originalTitle.current;
    hasNewMessage.current = false;
  };

  const showNewMessageIndicator = () => {
    if (document.hidden || !document.hasFocus()) {
      hasNewMessage.current = true;

      if (!blinkInterval.current) {
        let showNew = true;
        blinkInterval.current = setInterval(() => {
          document.title = showNew ? 'New message!' : originalTitle.current;
          showNew = !showNew;
        }, 1000);
      }
    }
  };

  return { showNewMessageIndicator };
}
