CREATE TABLE dimensions (
  id SERIAL PRIMARY KEY,
  name text NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE TABLE archetypes (
  id SERIAL PRIMARY KEY,
  name text NOT NULL,
  description text,
  metaphor text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

CREATE TABLE archetype_dimension_targets (
   archetype_id integer REFERENCES archetypes(id) ON DELETE CASCADE,
   dimension_id integer REFERENCES dimensions(id) ON DELETE CASCADE,
   target_value decimal(3,2) NOT NULL CHECK (target_value >= 0.0 AND target_value <= 1.0),
   created_at timestamp with time zone NOT NULL DEFAULT now(),
   updated_at timestamp with time zone NOT NULL DEFAULT now(),
   PRIMARY KEY (archetype_id, dimension_id)
);

CREATE TABLE job_seeker_archetypes (
   id SERIAL PRIMARY KEY,
   job_seeker_id bigint REFERENCES job_seekers(id) ON DELETE CASCADE,
   archetype_id integer REFERENCES archetypes(id) ON DELETE CASCADE,
   distance decimal(4,3) NOT NULL,
   dimension_scores jsonb NOT NULL,
   calculated_at timestamp with time zone NOT NULL DEFAULT now(),
   UNIQUE(job_seeker_id, archetype_id)
);

ALTER TABLE job_seeker_answer_tags
  ADD COLUMN dimension_id integer REFERENCES dimensions(id);

ALTER TABLE vacancy_answer_tags
  ADD COLUMN dimension_id integer REFERENCES dimensions(id);

CREATE INDEX idx_archetype_dimension_targets_archetype_id ON archetype_dimension_targets(archetype_id);
CREATE INDEX idx_job_seeker_archetypes_job_seeker_id ON job_seeker_archetypes(job_seeker_id);
CREATE INDEX idx_job_seeker_answer_tags_dimension_id ON job_seeker_answer_tags(dimension_id);
CREATE INDEX idx_vacancy_answer_tags_dimension_id ON vacancy_answer_tags(dimension_id);
