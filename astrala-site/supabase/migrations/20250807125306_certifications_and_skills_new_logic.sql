CREATE TYPE cert_type AS ENUM ('professional', 'membership', 'health_safety');

CREATE TABLE certificate_groups (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  type cert_type NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE skill_groups (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

ALTER TABLE certifications ADD COLUMN group_id INT;
ALTER TABLE skills ADD COLUMN group_id INT;

CREATE INDEX idx_certifications_group_id ON certifications(group_id);
CREATE INDEX idx_skills_group_id ON skills(group_id);

ALTER TABLE certifications
  ADD CONSTRAINT fk_certifications_group
    FOREIGN KEY (group_id) REFERENCES certificate_groups(id) ON DELETE SET NULL;

ALTER TABLE skills
  ADD CONSTRAINT fk_skills_group
    FOREIGN KEY (group_id) REFERENCES skill_groups(id) ON DELETE SET NULL;
