ALTER TABLE public.companies
  ADD COLUMN stripe_customer_id TEXT,
  ADD COLUMN stripe_subscription_id TEXT,
  ADD COLUMN subscription_status TEXT NOT NULL DEFAULT 'trial',
  ADD COLUMN subscription_plan TEXT,
  ADD COLUMN billing_period TEXT,
  ADD COLUMN current_period_start TIMESTAMPTZ,
  ADD COLUMN current_period_end TIMESTAMPTZ,
  ADD COLUMN canceled_at TIMESTAMPTZ;

CREATE INDEX idx_companies_subscription_status ON public.companies(subscription_status);
CREATE INDEX idx_companies_stripe_customer ON public.companies(stripe_customer_id);
