ALTER TABLE public.job_seeker_vacancy_matches
  ADD COLUMN final_score_below_threshold boolean DEFAULT false NOT NULL;

ALTER TABLE public.job_seekers
  ADD COLUMN is_activated boolean,
ADD COLUMN email text;

UPDATE public.job_seekers
SET is_activated = true;

ALTER TABLE public.job_seekers
  ALTER COLUMN is_activated SET DEFAULT false,
ALTER COLUMN is_activated SET NOT NULL;

ALTER TABLE public.vacancy_matches
  ADD COLUMN job_seeker_rejected_at timestamptz,
ADD COLUMN employer_rejected_at timestamptz;
