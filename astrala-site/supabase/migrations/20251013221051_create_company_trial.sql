CREATE TABLE public.company_trials (
                                     id SERIAL PRIMARY KEY,
                                     company_id BIGINT NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
                                     started_at TIMESTAMPTZ NOT NULL DEFAULT now(),
                                     ends_at TIMESTAMPTZ NOT NULL,
                                     created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
                                     updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
                                     CONSTRAINT unique_company_trial UNIQUE(company_id)
);

CREATE INDEX idx_company_trials_company ON public.company_trials(company_id);
CREATE INDEX idx_company_trials_ends_at ON public.company_trials(ends_at);
