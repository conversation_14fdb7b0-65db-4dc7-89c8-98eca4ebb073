CREATE TYPE public."SurveyAudienceTypeNew" AS ENUM ('Employer', 'JobSeeker');

ALTER TABLE public.surveys
ALTER COLUMN audience TYPE public."SurveyAudienceTypeNew"
  USING (
    CASE
      WHEN audience::text = 'Employee' THEN 'Employer'::public."SurveyAudienceTypeNew"
      ELSE audience::text::public."SurveyAudienceTypeNew"
    END
  );

DROP TYPE public."SurveyAudienceType";

ALTER TYPE public."SurveyAudienceTypeNew" RENAME TO "SurveyAudienceType";
