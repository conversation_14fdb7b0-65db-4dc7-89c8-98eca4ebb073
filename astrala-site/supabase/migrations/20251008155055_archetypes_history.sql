CREATE TABLE job_seeker_archetypes_history (
                                             id serial PRIMARY KEY,
                                             job_seeker_id bigint NOT NULL REFERENCES job_seekers(id) ON DELETE CASCADE,
                                             archetype_id integer NOT NULL REFERENCES archetypes(id) ON DELETE CASCADE,
                                             created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX idx_archetypes_history_job_seeker
  ON job_seeker_archetypes_history(job_seeker_id, created_at DESC);
