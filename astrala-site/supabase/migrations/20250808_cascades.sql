ALTER TABLE public.certifications DROP CONSTRAINT IF EXISTS fk_certifications_group;
ALTER TABLE public.chat_messages DROP CONSTRAINT IF EXISTS chat_messages_job_seeker_id_fkey;
ALTER TABLE public.chat_messages DROP CONSTRAINT IF EXISTS chat_messages_chat_id_fkey;
ALTER TABLE public.chat_messages DROP CONSTRAINT IF EXISTS chat_messages_employer_id_fkey;
ALTER TABLE public.chats DROP CONSTRAINT IF EXISTS chats_vacancy_match_id_fkey;
ALTER TABLE public.employers DROP CONSTRAINT IF EXISTS employers_company_id_fkey;
ALTER TABLE public.employers DROP CONSTRAINT IF EXISTS employers_user_id_fkey;
ALTER TABLE public.invitation_tokens DROP CONSTRAINT IF EXISTS invitation_tokens_used_by_employer_id_fkey;
ALTER TABLE public.invitation_tokens DROP CONSTRAINT IF EXISTS invitation_tokens_company_id_fkey;
ALTER TABLE public.invitation_tokens DROP CONSTRAINT IF EXISTS invitation_tokens_created_by_employer_id_fkey;
ALTER TABLE public.job_seeker_answer_tags DROP CONSTRAINT IF EXISTS job_seeker_answers_tags_question_id_fkey;
ALTER TABLE public.job_seeker_certifications DROP CONSTRAINT IF EXISTS job_seeker_certifications_job_seeker_id_fkey;
ALTER TABLE public.job_seeker_certifications DROP CONSTRAINT IF EXISTS job_seeker_certifications_certification_id_fkey;
ALTER TABLE public.job_seeker_educations DROP CONSTRAINT IF EXISTS job_seeker_educations_job_seeker_id_fkey;
ALTER TABLE public.job_seeker_languages DROP CONSTRAINT IF EXISTS job_seeker_languages_job_seeker_id_fkey;
ALTER TABLE public.job_seeker_questionnaire_answers DROP CONSTRAINT IF EXISTS job_seeker_questionnaire_answers_question_id_fkey;
ALTER TABLE public.job_seeker_questionnaire_answers DROP CONSTRAINT IF EXISTS questionnaire_answers_job_seeker_id_fkey;
ALTER TABLE public.job_seeker_questionnaire_questions DROP CONSTRAINT IF EXISTS job_seeker_questionnaire_questions_group_id_fkey;
ALTER TABLE public.job_seeker_skills DROP CONSTRAINT IF EXISTS job_seeker_skills_job_seeker_id_fkey;
ALTER TABLE public.job_seeker_skills DROP CONSTRAINT IF EXISTS job_seeker_skills_skill_id_fkey;
ALTER TABLE public.job_seeker_vacancy_matches DROP CONSTRAINT IF EXISTS job_seeker_vacancy_matches_job_seeker_id_fkey;
ALTER TABLE public.job_seeker_vacancy_matches DROP CONSTRAINT IF EXISTS job_seeker_vacancy_matches_vacancy_id_fkey1;
ALTER TABLE public.job_seeker_vacancy_matches DROP CONSTRAINT IF EXISTS job_seeker_vacancy_matches_vacancy_id_fkey;
ALTER TABLE public.job_seeker_work_experience DROP CONSTRAINT IF EXISTS job_seeker_work_experience_job_seeker_id_fkey;
ALTER TABLE public.job_seekers DROP CONSTRAINT IF EXISTS employees_user_id_fkey;
ALTER TABLE public.match_feedbacks DROP CONSTRAINT IF EXISTS match_feedbacks_vacancy_match_id_fkey;
ALTER TABLE public.questionnaire_tokens DROP CONSTRAINT IF EXISTS questionnaire_tokens_vacancy_id_fkey;
ALTER TABLE public.skills DROP CONSTRAINT IF EXISTS fk_skills_group;
ALTER TABLE public.transactions DROP CONSTRAINT IF EXISTS transactions_vacancy_match_id_fkey;
ALTER TABLE public.users DROP CONSTRAINT IF EXISTS users_user_id_fkey;
ALTER TABLE public.vacancies DROP CONSTRAINT IF EXISTS vacancies_employer_id_fkey;
ALTER TABLE public.vacancies DROP CONSTRAINT IF EXISTS vacancies_company_id_fkey;
ALTER TABLE public.vacancy_answer_tags DROP CONSTRAINT IF EXISTS vacancy_answers_tags_question_id_fkey;
ALTER TABLE public.vacancy_answer_tags DROP CONSTRAINT IF EXISTS vacancy_answers_tags_duplicate_question_id_fkey;
ALTER TABLE public.vacancy_certifications DROP CONSTRAINT IF EXISTS vacancy_certifications_certification_id_fkey;
ALTER TABLE public.vacancy_certifications DROP CONSTRAINT IF EXISTS vacancy_certifications_vacancy_id_fkey;
ALTER TABLE public.vacancy_languages DROP CONSTRAINT IF EXISTS vacancy_languages_vacancy_id_fkey;
ALTER TABLE public.vacancy_match_descriptions DROP CONSTRAINT IF EXISTS vacancy_match_descriptions_vacancy_match_id_fkey;
ALTER TABLE public.vacancy_matches DROP CONSTRAINT IF EXISTS vacancy_matches_employee_transaction_id_fkey;
ALTER TABLE public.vacancy_matches DROP CONSTRAINT IF EXISTS vacancy_matches_employer_transaction_id_fkey;
ALTER TABLE public.vacancy_matches DROP CONSTRAINT IF EXISTS vacancy_matches_vacancy_id_fkey;
ALTER TABLE public.vacancy_matches DROP CONSTRAINT IF EXISTS vacancy_matches_job_seeker_id_fkey;
ALTER TABLE public.vacancy_questionnaire_answers DROP CONSTRAINT IF EXISTS vacancy_questionnaire_answers_question_id_fkey;
ALTER TABLE public.vacancy_questionnaire_answers DROP CONSTRAINT IF EXISTS vacancy_questionnaire_answers_vacancy_id_fkey;
ALTER TABLE public.vacancy_questionnaire_questions DROP CONSTRAINT IF EXISTS vacancy_questionnaire_questions_group_id_fkey;
ALTER TABLE public.vacancy_skills DROP CONSTRAINT IF EXISTS vacancy_skills_vacancy_id_fkey;
ALTER TABLE public.vacancy_skills DROP CONSTRAINT IF EXISTS vacancy_skills_skill_id_fkey;

ALTER TABLE public.users ADD CONSTRAINT users_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE public.job_seekers ADD CONSTRAINT employees_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE;
ALTER TABLE public.employers ADD CONSTRAINT employers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE;

ALTER TABLE public.employers ADD CONSTRAINT employers_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;
ALTER TABLE public.vacancies ADD CONSTRAINT vacancies_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;

ALTER TABLE public.vacancies ADD CONSTRAINT vacancies_employer_id_fkey FOREIGN KEY (employer_id) REFERENCES public.employers(id) ON DELETE CASCADE;

ALTER TABLE public.certifications ADD CONSTRAINT fk_certifications_group FOREIGN KEY (group_id) REFERENCES public.certificate_groups(id) ON DELETE CASCADE;
ALTER TABLE public.skills ADD CONSTRAINT fk_skills_group FOREIGN KEY (group_id) REFERENCES public.skill_groups(id) ON DELETE CASCADE;

ALTER TABLE public.job_seeker_certifications ADD CONSTRAINT job_seeker_certifications_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_certifications ADD CONSTRAINT job_seeker_certifications_certification_id_fkey FOREIGN KEY (certification_id) REFERENCES public.certifications(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_educations ADD CONSTRAINT job_seeker_educations_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_languages ADD CONSTRAINT job_seeker_languages_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_skills ADD CONSTRAINT job_seeker_skills_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_skills ADD CONSTRAINT job_seeker_skills_skill_id_fkey FOREIGN KEY (skill_id) REFERENCES public.skills(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_work_experience ADD CONSTRAINT job_seeker_work_experience_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE CASCADE;

ALTER TABLE public.job_seeker_questionnaire_questions ADD CONSTRAINT job_seeker_questionnaire_questions_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.job_seeker_questionnaire_groups(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_questionnaire_answers ADD CONSTRAINT questionnaire_answers_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_questionnaire_answers ADD CONSTRAINT job_seeker_questionnaire_answers_question_id_fkey FOREIGN KEY (question_id) REFERENCES public.job_seeker_questionnaire_questions(id) ON DELETE CASCADE;
ALTER TABLE public.job_seeker_answer_tags ADD CONSTRAINT job_seeker_answers_tags_question_id_fkey FOREIGN KEY (question_id) REFERENCES public.job_seeker_questionnaire_questions(id) ON DELETE CASCADE;

ALTER TABLE public.vacancy_certifications ADD CONSTRAINT vacancy_certifications_vacancy_id_fkey FOREIGN KEY (vacancy_id) REFERENCES public.vacancies(id) ON DELETE CASCADE;
ALTER TABLE public.vacancy_certifications ADD CONSTRAINT vacancy_certifications_certification_id_fkey FOREIGN KEY (certification_id) REFERENCES public.certifications(id) ON DELETE CASCADE;
ALTER TABLE public.vacancy_languages ADD CONSTRAINT vacancy_languages_vacancy_id_fkey FOREIGN KEY (vacancy_id) REFERENCES public.vacancies(id) ON DELETE CASCADE;
ALTER TABLE public.vacancy_skills ADD CONSTRAINT vacancy_skills_vacancy_id_fkey FOREIGN KEY (vacancy_id) REFERENCES public.vacancies(id) ON DELETE CASCADE;
ALTER TABLE public.vacancy_skills ADD CONSTRAINT vacancy_skills_skill_id_fkey FOREIGN KEY (skill_id) REFERENCES public.skills(id) ON DELETE CASCADE;

ALTER TABLE public.vacancy_questionnaire_questions ADD CONSTRAINT vacancy_questionnaire_questions_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.vacancy_questionnaire_groups(id) ON DELETE CASCADE;
ALTER TABLE public.vacancy_questionnaire_answers ADD CONSTRAINT vacancy_questionnaire_answers_vacancy_id_fkey FOREIGN KEY (vacancy_id) REFERENCES public.vacancies(id) ON DELETE CASCADE;
ALTER TABLE public.vacancy_questionnaire_answers ADD CONSTRAINT vacancy_questionnaire_answers_question_id_fkey FOREIGN KEY (question_id) REFERENCES public.vacancy_questionnaire_questions(id) ON DELETE CASCADE;
ALTER TABLE public.vacancy_answer_tags ADD CONSTRAINT vacancy_answers_tags_question_id_fkey FOREIGN KEY (question_id) REFERENCES public.vacancy_questionnaire_questions(id) ON DELETE CASCADE;
ALTER TABLE public.vacancy_answer_tags ADD CONSTRAINT vacancy_answers_tags_duplicate_question_id_fkey FOREIGN KEY (question_id) REFERENCES public.job_seeker_questionnaire_questions(id) ON DELETE CASCADE;
ALTER TABLE public.questionnaire_tokens ADD CONSTRAINT questionnaire_tokens_vacancy_id_fkey FOREIGN KEY (vacancy_id) REFERENCES public.vacancies(id) ON DELETE CASCADE;

ALTER TABLE public.vacancy_matches ADD CONSTRAINT vacancy_matches_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE SET NULL;
ALTER TABLE public.vacancy_matches ADD CONSTRAINT vacancy_matches_vacancy_id_fkey FOREIGN KEY (vacancy_id) REFERENCES public.vacancies(id) ON DELETE SET NULL;
ALTER TABLE public.job_seeker_vacancy_matches ADD CONSTRAINT job_seeker_vacancy_matches_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE SET NULL;
ALTER TABLE public.job_seeker_vacancy_matches ADD CONSTRAINT job_seeker_vacancy_matches_vacancy_id_fkey FOREIGN KEY (vacancy_id) REFERENCES public.vacancies(id) ON DELETE SET NULL;

ALTER TABLE public.vacancy_match_descriptions ADD CONSTRAINT vacancy_match_descriptions_vacancy_match_id_fkey FOREIGN KEY (vacancy_match_id) REFERENCES public.vacancy_matches(id) ON DELETE CASCADE;
ALTER TABLE public.match_feedbacks ADD CONSTRAINT match_feedbacks_vacancy_match_id_fkey FOREIGN KEY (vacancy_match_id) REFERENCES public.vacancy_matches(id) ON DELETE CASCADE;

ALTER TABLE public.transactions ADD CONSTRAINT transactions_vacancy_match_id_fkey FOREIGN KEY (vacancy_match_id) REFERENCES public.vacancy_matches(id) ON DELETE SET NULL;
ALTER TABLE public.vacancy_matches ADD CONSTRAINT vacancy_matches_employee_transaction_id_fkey FOREIGN KEY (job_seekeer_transaction_id) REFERENCES public.transactions(id) ON DELETE SET NULL;
ALTER TABLE public.vacancy_matches ADD CONSTRAINT vacancy_matches_employer_transaction_id_fkey FOREIGN KEY (employer_transaction_id) REFERENCES public.transactions(id) ON DELETE SET NULL;

ALTER TABLE public.chats ADD CONSTRAINT chats_vacancy_match_id_fkey FOREIGN KEY (vacancy_match_id) REFERENCES public.vacancy_matches(id) ON DELETE CASCADE;
ALTER TABLE public.chat_messages ADD CONSTRAINT chat_messages_chat_id_fkey FOREIGN KEY (chat_id) REFERENCES public.chats(id) ON DELETE CASCADE;
ALTER TABLE public.chat_messages ADD CONSTRAINT chat_messages_employer_id_fkey FOREIGN KEY (employer_id) REFERENCES public.employers(id) ON DELETE CASCADE;
ALTER TABLE public.chat_messages ADD CONSTRAINT chat_messages_job_seeker_id_fkey FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE CASCADE;

ALTER TABLE public.invitation_tokens ADD CONSTRAINT invitation_tokens_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;
ALTER TABLE public.invitation_tokens ADD CONSTRAINT invitation_tokens_created_by_employer_id_fkey FOREIGN KEY (created_by_employer_id) REFERENCES public.employers(id) ON DELETE SET NULL;
ALTER TABLE public.invitation_tokens ADD CONSTRAINT invitation_tokens_used_by_employer_id_fkey FOREIGN KEY (used_by_employer_id) REFERENCES public.employers(id) ON DELETE SET NULL;
