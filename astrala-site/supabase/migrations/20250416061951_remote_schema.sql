

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pgsodium" WITH SCHEMA "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "moddatetime" WITH SCHEMA "public";






CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";





SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."certifications" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."certifications" OWNER TO "postgres";


ALTER TABLE "public"."certifications" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."certifications_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."companies" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."companies" OWNER TO "postgres";


ALTER TABLE "public"."companies" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."companies_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seekers" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "resume_path" "text",
    "avatar_path" "text",
    "driving_license_path" "text",
    "full_name" "text",
    "job_title" "text",
    "phone" "text",
    "country" "text",
    "city" "text",
    "passport_path" "text",
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "salary_min" bigint,
    "salary_max" bigint,
    "matches_last_calculated_at" timestamp with time zone,
    "work_style_updated_at" timestamp with time zone DEFAULT "now"(),
    "information_updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."job_seekers" OWNER TO "postgres";


ALTER TABLE "public"."job_seekers" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."employees_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."employers" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "company_id" bigint,
    "full_name" "text",
    "role" "text",
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "email" "text"
);


ALTER TABLE "public"."employers" OWNER TO "postgres";


ALTER TABLE "public"."employers" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."employers_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."invitation_tokens" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "company_id" bigint NOT NULL,
    "token" "text" NOT NULL,
    "expires_at" "text" NOT NULL,
    "created_by_employer_id" bigint NOT NULL,
    "is_used" boolean DEFAULT false,
    "used_by_employer_id" bigint,
    "used_at" timestamp with time zone,
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "email" "text"
);


ALTER TABLE "public"."invitation_tokens" OWNER TO "postgres";


ALTER TABLE "public"."invitation_tokens" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."invitation_tokens_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_answer_tags" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "question_id" bigint,
    "value" integer NOT NULL,
    "tag" "text" NOT NULL
);


ALTER TABLE "public"."job_seeker_answer_tags" OWNER TO "postgres";


ALTER TABLE "public"."job_seeker_answer_tags" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_answers_tags_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_certifications" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "certification_id" bigint NOT NULL,
    "file_path" "text",
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "job_seeker_id" bigint NOT NULL
);


ALTER TABLE "public"."job_seeker_certifications" OWNER TO "postgres";


ALTER TABLE "public"."job_seeker_certifications" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_certifications_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_educations" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "type" "text" NOT NULL,
    "institution" "text" NOT NULL,
    "discipline" "text" NOT NULL,
    "start_year" bigint NOT NULL,
    "end_year" bigint NOT NULL,
    "job_seeker_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."job_seeker_educations" OWNER TO "postgres";


ALTER TABLE "public"."job_seeker_educations" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_educations_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_languages" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "language" "text" NOT NULL,
    "level" "text" NOT NULL,
    "job_seeker_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."job_seeker_languages" OWNER TO "postgres";


ALTER TABLE "public"."job_seeker_languages" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_languages_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_questionnaire_groups" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."job_seeker_questionnaire_groups" OWNER TO "postgres";


ALTER TABLE "public"."job_seeker_questionnaire_groups" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_questionaire_groups_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_questionnaire_answers" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "job_seeker_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "question_id" bigint NOT NULL,
    "value" bigint NOT NULL
);


ALTER TABLE "public"."job_seeker_questionnaire_answers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."job_seeker_questionnaire_questions" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text" NOT NULL,
    "group_id" bigint,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."job_seeker_questionnaire_questions" OWNER TO "postgres";


ALTER TABLE "public"."job_seeker_questionnaire_questions" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_questionnaire_questions_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_skills" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "skill_id" bigint NOT NULL,
    "job_seeker_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."job_seeker_skills" OWNER TO "postgres";


ALTER TABLE "public"."job_seeker_skills" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_skills_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_vacancy_matches" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "job_seeker_id" bigint,
    "vacancy_id" bigint,
    "match_score" double precision
);


ALTER TABLE "public"."job_seeker_vacancy_matches" OWNER TO "postgres";


COMMENT ON TABLE "public"."job_seeker_vacancy_matches" IS 'ai table';



ALTER TABLE "public"."job_seeker_vacancy_matches" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_vacancy_matches_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."job_seeker_work_experience" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "company_name" "text" NOT NULL,
    "job_title" "text" NOT NULL,
    "start_year" bigint,
    "end_year" bigint,
    "comment" "text",
    "job_seeker_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "start_date" "date",
    "end_date" "date"
);


ALTER TABLE "public"."job_seeker_work_experience" OWNER TO "postgres";


ALTER TABLE "public"."job_seeker_work_experience" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."job_seeker_work_experience_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."qualifications" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text",
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."qualifications" OWNER TO "postgres";


ALTER TABLE "public"."qualifications" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."qualifications_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



ALTER TABLE "public"."job_seeker_questionnaire_answers" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."questionnaire_answers_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."skills" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."skills" OWNER TO "postgres";


ALTER TABLE "public"."skills" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."tags_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."transactions" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "payer_role" "text" NOT NULL,
    "payer_id" bigint NOT NULL,
    "amount" real NOT NULL,
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "completed_at" timestamp with time zone,
    "currency" "text" NOT NULL,
    "vacancy_match_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."transactions" OWNER TO "postgres";


ALTER TABLE "public"."transactions" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."transactions_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."users" (
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role" "text",
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."vacancies" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "title" "text" NOT NULL,
    "description" "text" NOT NULL,
    "employer_id" bigint NOT NULL,
    "driving_license" boolean DEFAULT false NOT NULL,
    "company_id" bigint NOT NULL,
    "education_degree" "text",
    "education_discipline" "text",
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "country" "text" NOT NULL,
    "city" "text" NOT NULL,
    "salary_min" bigint NOT NULL,
    "salary_max" bigint NOT NULL,
    "matches_last_calculated_at" timestamp with time zone,
    "experience_years_from" bigint,
    "status" "text" DEFAULT 'OPENED'::"text" NOT NULL
);


ALTER TABLE "public"."vacancies" OWNER TO "postgres";


ALTER TABLE "public"."vacancies" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancies_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vacancy_answer_tags" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "question_id" bigint,
    "value" integer NOT NULL,
    "tag" "text" NOT NULL
);


ALTER TABLE "public"."vacancy_answer_tags" OWNER TO "postgres";


ALTER TABLE "public"."vacancy_answer_tags" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_answers_tags_duplicate_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vacancy_certifications" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "certification_id" bigint NOT NULL,
    "vacancy_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."vacancy_certifications" OWNER TO "postgres";


ALTER TABLE "public"."vacancy_certifications" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_certifications_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vacancy_languages" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "language" "text" NOT NULL,
    "level" "text" NOT NULL,
    "vacancy_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."vacancy_languages" OWNER TO "postgres";


ALTER TABLE "public"."vacancy_languages" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_languages_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vacancy_match_descriptions" (
    "vacancy_match_id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "driving_license_match" boolean DEFAULT false,
    "education_match" boolean DEFAULT false,
    "location_match" boolean DEFAULT false,
    "salary_match" boolean DEFAULT false,
    "work_experience_match" boolean DEFAULT false,
    "work_experience_years" integer,
    "lacking_skills" "text"[],
    "matching_skills" "text"[],
    "matching_behavioural_tags" "text"[],
    "lacking_behavioural_tags" "text"[]
);


ALTER TABLE "public"."vacancy_match_descriptions" OWNER TO "postgres";


ALTER TABLE "public"."vacancy_match_descriptions" ALTER COLUMN "vacancy_match_id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_match_descriptions_vacancy_match_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vacancy_matches" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "job_seeker_id" bigint NOT NULL,
    "job_seeker_status" "text" NOT NULL,
    "employer_status" "text" NOT NULL,
    "ai_match_score" double precision,
    "vacancy_id" bigint NOT NULL,
    "job_seekeer_transaction_id" bigint,
    "employer_transaction_id" bigint,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."vacancy_matches" OWNER TO "postgres";


ALTER TABLE "public"."vacancy_matches" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_matches_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vacancy_questionnaire_answers" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "question_id" bigint NOT NULL,
    "value" bigint NOT NULL,
    "vacancy_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."vacancy_questionnaire_answers" OWNER TO "postgres";


ALTER TABLE "public"."vacancy_questionnaire_answers" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_questionnaire_answers_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vacancy_questionnaire_groups" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."vacancy_questionnaire_groups" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."vacancy_questionnaire_questions" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" "text" NOT NULL,
    "group_id" bigint,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."vacancy_questionnaire_questions" OWNER TO "postgres";


ALTER TABLE "public"."vacancy_questionnaire_questions" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_questionnaire_questions_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



ALTER TABLE "public"."vacancy_questionnaire_groups" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_seeker_questionaire_groups_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."vacancy_skills" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "skill_id" bigint NOT NULL,
    "vacancy_id" bigint NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."vacancy_skills" OWNER TO "postgres";


ALTER TABLE "public"."vacancy_skills" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."vacancy_skills_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



ALTER TABLE ONLY "public"."certifications"
    ADD CONSTRAINT "certifications_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."certifications"
    ADD CONSTRAINT "certifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."companies"
    ADD CONSTRAINT "companies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seekers"
    ADD CONSTRAINT "employees_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."employers"
    ADD CONSTRAINT "employers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invitation_tokens"
    ADD CONSTRAINT "invitation_tokens_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_answer_tags"
    ADD CONSTRAINT "job_seeker_answers_tags_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_certifications"
    ADD CONSTRAINT "job_seeker_certifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_educations"
    ADD CONSTRAINT "job_seeker_educations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_languages"
    ADD CONSTRAINT "job_seeker_languages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_questionnaire_groups"
    ADD CONSTRAINT "job_seeker_questionaire_groups_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_questionnaire_questions"
    ADD CONSTRAINT "job_seeker_questionnaire_questions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_skills"
    ADD CONSTRAINT "job_seeker_skills_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_vacancy_matches"
    ADD CONSTRAINT "job_seeker_vacancy_matches_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_work_experience"
    ADD CONSTRAINT "job_seeker_work_experience_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."qualifications"
    ADD CONSTRAINT "qualifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."job_seeker_questionnaire_answers"
    ADD CONSTRAINT "questionnaire_answers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."skills"
    ADD CONSTRAINT "skills_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."skills"
    ADD CONSTRAINT "tags_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("user_id");



ALTER TABLE ONLY "public"."vacancies"
    ADD CONSTRAINT "vacancies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vacancy_answer_tags"
    ADD CONSTRAINT "vacancy_answers_tags_duplicate_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vacancy_certifications"
    ADD CONSTRAINT "vacancy_certifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vacancy_languages"
    ADD CONSTRAINT "vacancy_languages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vacancy_match_descriptions"
    ADD CONSTRAINT "vacancy_match_descriptions_pkey" PRIMARY KEY ("vacancy_match_id");



ALTER TABLE ONLY "public"."vacancy_matches"
    ADD CONSTRAINT "vacancy_matches_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vacancy_questionnaire_answers"
    ADD CONSTRAINT "vacancy_questionnaire_answers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vacancy_questionnaire_questions"
    ADD CONSTRAINT "vacancy_questionnaire_questions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vacancy_questionnaire_groups"
    ADD CONSTRAINT "vacancy_seeker_questionaire_groups_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."vacancy_skills"
    ADD CONSTRAINT "vacancy_skills_pkey" PRIMARY KEY ("id");



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."certifications" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."companies" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."employers" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seeker_certifications" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seeker_educations" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seeker_languages" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seeker_questionnaire_answers" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seeker_questionnaire_groups" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seeker_questionnaire_questions" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seeker_skills" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seeker_work_experience" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."job_seekers" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."qualifications" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."skills" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."transactions" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."vacancies" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."vacancy_certifications" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."vacancy_languages" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."vacancy_matches" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."vacancy_questionnaire_answers" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."vacancy_questionnaire_groups" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."vacancy_questionnaire_questions" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



CREATE OR REPLACE TRIGGER "handle_updated_at" BEFORE UPDATE ON "public"."vacancy_skills" FOR EACH ROW EXECUTE FUNCTION "public"."moddatetime"('updated_at');



ALTER TABLE ONLY "public"."job_seekers"
    ADD CONSTRAINT "employees_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."employers"
    ADD CONSTRAINT "employers_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id");



ALTER TABLE ONLY "public"."employers"
    ADD CONSTRAINT "employers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invitation_tokens"
    ADD CONSTRAINT "invitation_tokens_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invitation_tokens"
    ADD CONSTRAINT "invitation_tokens_created_by_employer_id_fkey" FOREIGN KEY ("created_by_employer_id") REFERENCES "public"."employers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invitation_tokens"
    ADD CONSTRAINT "invitation_tokens_used_by_employer_id_fkey" FOREIGN KEY ("used_by_employer_id") REFERENCES "public"."employers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."job_seeker_answer_tags"
    ADD CONSTRAINT "job_seeker_answers_tags_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."job_seeker_questionnaire_questions"("id");



ALTER TABLE ONLY "public"."job_seeker_certifications"
    ADD CONSTRAINT "job_seeker_certifications_certification_id_fkey" FOREIGN KEY ("certification_id") REFERENCES "public"."certifications"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."job_seeker_certifications"
    ADD CONSTRAINT "job_seeker_certifications_job_seeker_id_fkey" FOREIGN KEY ("job_seeker_id") REFERENCES "public"."job_seekers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."job_seeker_educations"
    ADD CONSTRAINT "job_seeker_educations_job_seeker_id_fkey" FOREIGN KEY ("job_seeker_id") REFERENCES "public"."job_seekers"("id");



ALTER TABLE ONLY "public"."job_seeker_languages"
    ADD CONSTRAINT "job_seeker_languages_job_seeker_id_fkey" FOREIGN KEY ("job_seeker_id") REFERENCES "public"."job_seekers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."job_seeker_questionnaire_answers"
    ADD CONSTRAINT "job_seeker_questionnaire_answers_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."job_seeker_questionnaire_questions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."job_seeker_questionnaire_questions"
    ADD CONSTRAINT "job_seeker_questionnaire_questions_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."job_seeker_questionnaire_groups"("id");



ALTER TABLE ONLY "public"."job_seeker_skills"
    ADD CONSTRAINT "job_seeker_skills_job_seeker_id_fkey" FOREIGN KEY ("job_seeker_id") REFERENCES "public"."job_seekers"("id");



ALTER TABLE ONLY "public"."job_seeker_skills"
    ADD CONSTRAINT "job_seeker_skills_skill_id_fkey" FOREIGN KEY ("skill_id") REFERENCES "public"."skills"("id");



ALTER TABLE ONLY "public"."job_seeker_vacancy_matches"
    ADD CONSTRAINT "job_seeker_vacancy_matches_job_seeker_id_fkey" FOREIGN KEY ("job_seeker_id") REFERENCES "public"."job_seekers"("id");



ALTER TABLE ONLY "public"."job_seeker_vacancy_matches"
    ADD CONSTRAINT "job_seeker_vacancy_matches_vacancy_id_fkey" FOREIGN KEY ("vacancy_id") REFERENCES "public"."vacancies"("id");



ALTER TABLE ONLY "public"."job_seeker_work_experience"
    ADD CONSTRAINT "job_seeker_work_experience_job_seeker_id_fkey" FOREIGN KEY ("job_seeker_id") REFERENCES "public"."job_seekers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."job_seeker_questionnaire_answers"
    ADD CONSTRAINT "questionnaire_answers_job_seeker_id_fkey" FOREIGN KEY ("job_seeker_id") REFERENCES "public"."job_seekers"("id");



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_vacancy_match_id_fkey" FOREIGN KEY ("vacancy_match_id") REFERENCES "public"."vacancy_matches"("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancies"
    ADD CONSTRAINT "vacancies_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id");



ALTER TABLE ONLY "public"."vacancies"
    ADD CONSTRAINT "vacancies_employer_id_fkey" FOREIGN KEY ("employer_id") REFERENCES "public"."employers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_answer_tags"
    ADD CONSTRAINT "vacancy_answers_tags_duplicate_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."job_seeker_questionnaire_questions"("id");



ALTER TABLE ONLY "public"."vacancy_answer_tags"
    ADD CONSTRAINT "vacancy_answers_tags_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."vacancy_questionnaire_questions"("id");



ALTER TABLE ONLY "public"."vacancy_certifications"
    ADD CONSTRAINT "vacancy_certifications_certification_id_fkey" FOREIGN KEY ("certification_id") REFERENCES "public"."certifications"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_certifications"
    ADD CONSTRAINT "vacancy_certifications_vacancy_id_fkey" FOREIGN KEY ("vacancy_id") REFERENCES "public"."vacancies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_languages"
    ADD CONSTRAINT "vacancy_languages_vacancy_id_fkey" FOREIGN KEY ("vacancy_id") REFERENCES "public"."vacancies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_match_descriptions"
    ADD CONSTRAINT "vacancy_match_descriptions_vacancy_match_id_fkey" FOREIGN KEY ("vacancy_match_id") REFERENCES "public"."vacancy_matches"("id");



ALTER TABLE ONLY "public"."vacancy_matches"
    ADD CONSTRAINT "vacancy_matches_employee_transaction_id_fkey" FOREIGN KEY ("job_seekeer_transaction_id") REFERENCES "public"."transactions"("id");



ALTER TABLE ONLY "public"."vacancy_matches"
    ADD CONSTRAINT "vacancy_matches_employer_transaction_id_fkey" FOREIGN KEY ("employer_transaction_id") REFERENCES "public"."transactions"("id");



ALTER TABLE ONLY "public"."vacancy_matches"
    ADD CONSTRAINT "vacancy_matches_job_seeker_id_fkey" FOREIGN KEY ("job_seeker_id") REFERENCES "public"."job_seekers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_matches"
    ADD CONSTRAINT "vacancy_matches_vacancy_id_fkey" FOREIGN KEY ("vacancy_id") REFERENCES "public"."vacancies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_questionnaire_answers"
    ADD CONSTRAINT "vacancy_questionnaire_answers_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."vacancy_questionnaire_questions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_questionnaire_answers"
    ADD CONSTRAINT "vacancy_questionnaire_answers_vacancy_id_fkey" FOREIGN KEY ("vacancy_id") REFERENCES "public"."vacancies"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_questionnaire_questions"
    ADD CONSTRAINT "vacancy_questionnaire_questions_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."vacancy_questionnaire_groups"("id");



ALTER TABLE ONLY "public"."vacancy_skills"
    ADD CONSTRAINT "vacancy_skills_skill_id_fkey" FOREIGN KEY ("skill_id") REFERENCES "public"."skills"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."vacancy_skills"
    ADD CONSTRAINT "vacancy_skills_vacancy_id_fkey" FOREIGN KEY ("vacancy_id") REFERENCES "public"."vacancies"("id") ON DELETE CASCADE;



CREATE POLICY "Enable insert for authenticated users only" ON "public"."companies" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."job_seeker_certifications" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."job_seeker_educations" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."job_seeker_languages" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."job_seeker_questionnaire_answers" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."job_seeker_work_experience" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users only" ON "public"."skills" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for users based on user_id" ON "public"."employers" FOR INSERT WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Enable insert for users based on user_id" ON "public"."job_seekers" FOR INSERT WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Enable insert for users based on user_id" ON "public"."users" FOR INSERT WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Enable read access for all users" ON "public"."companies" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."employers" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."job_seeker_questionnaire_groups" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."job_seeker_questionnaire_questions" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."job_seekers" FOR SELECT USING (true);



CREATE POLICY "Enable read access for all users" ON "public"."skills" FOR SELECT USING (true);



CREATE POLICY "Update for auth. users" ON "public"."employers" FOR UPDATE USING (("auth"."uid"() = "user_id")) WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Update for auth. users" ON "public"."job_seekers" FOR UPDATE USING (("auth"."uid"() = "user_id")) WITH CHECK (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."companies" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."employers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."job_seeker_questionnaire_groups" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."job_seeker_questionnaire_questions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."job_seeker_vacancy_matches" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."qualifications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."transactions" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."moddatetime"() TO "postgres";
GRANT ALL ON FUNCTION "public"."moddatetime"() TO "anon";
GRANT ALL ON FUNCTION "public"."moddatetime"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."moddatetime"() TO "service_role";


















GRANT ALL ON TABLE "public"."certifications" TO "anon";
GRANT ALL ON TABLE "public"."certifications" TO "authenticated";
GRANT ALL ON TABLE "public"."certifications" TO "service_role";



GRANT ALL ON SEQUENCE "public"."certifications_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."certifications_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."certifications_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."companies" TO "anon";
GRANT ALL ON TABLE "public"."companies" TO "authenticated";
GRANT ALL ON TABLE "public"."companies" TO "service_role";



GRANT ALL ON SEQUENCE "public"."companies_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."companies_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."companies_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seekers" TO "anon";
GRANT ALL ON TABLE "public"."job_seekers" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seekers" TO "service_role";



GRANT ALL ON SEQUENCE "public"."employees_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."employees_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."employees_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."employers" TO "anon";
GRANT ALL ON TABLE "public"."employers" TO "authenticated";
GRANT ALL ON TABLE "public"."employers" TO "service_role";



GRANT ALL ON SEQUENCE "public"."employers_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."employers_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."employers_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."invitation_tokens" TO "anon";
GRANT ALL ON TABLE "public"."invitation_tokens" TO "authenticated";
GRANT ALL ON TABLE "public"."invitation_tokens" TO "service_role";



GRANT ALL ON SEQUENCE "public"."invitation_tokens_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."invitation_tokens_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."invitation_tokens_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_answer_tags" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_answer_tags" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_answer_tags" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_answers_tags_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_answers_tags_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_answers_tags_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_certifications" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_certifications" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_certifications" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_certifications_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_certifications_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_certifications_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_educations" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_educations" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_educations" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_educations_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_educations_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_educations_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_languages" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_languages" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_languages" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_languages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_languages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_languages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_questionnaire_groups" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_questionnaire_groups" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_questionnaire_groups" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_questionaire_groups_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_questionaire_groups_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_questionaire_groups_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_questionnaire_answers" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_questionnaire_answers" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_questionnaire_answers" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_questionnaire_questions" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_questionnaire_questions" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_questionnaire_questions" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_questionnaire_questions_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_questionnaire_questions_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_questionnaire_questions_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_skills" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_skills" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_skills" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_skills_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_skills_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_skills_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_vacancy_matches" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_vacancy_matches" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_vacancy_matches" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_vacancy_matches_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_vacancy_matches_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_vacancy_matches_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."job_seeker_work_experience" TO "anon";
GRANT ALL ON TABLE "public"."job_seeker_work_experience" TO "authenticated";
GRANT ALL ON TABLE "public"."job_seeker_work_experience" TO "service_role";



GRANT ALL ON SEQUENCE "public"."job_seeker_work_experience_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."job_seeker_work_experience_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."job_seeker_work_experience_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."qualifications" TO "anon";
GRANT ALL ON TABLE "public"."qualifications" TO "authenticated";
GRANT ALL ON TABLE "public"."qualifications" TO "service_role";



GRANT ALL ON SEQUENCE "public"."qualifications_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."qualifications_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."qualifications_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."questionnaire_answers_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."questionnaire_answers_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."questionnaire_answers_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."skills" TO "anon";
GRANT ALL ON TABLE "public"."skills" TO "authenticated";
GRANT ALL ON TABLE "public"."skills" TO "service_role";



GRANT ALL ON SEQUENCE "public"."tags_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."tags_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."tags_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."transactions" TO "anon";
GRANT ALL ON TABLE "public"."transactions" TO "authenticated";
GRANT ALL ON TABLE "public"."transactions" TO "service_role";



GRANT ALL ON SEQUENCE "public"."transactions_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."transactions_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."transactions_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";



GRANT ALL ON TABLE "public"."vacancies" TO "anon";
GRANT ALL ON TABLE "public"."vacancies" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancies" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancies_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancies_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancies_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_answer_tags" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_answer_tags" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_answer_tags" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_answers_tags_duplicate_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_answers_tags_duplicate_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_answers_tags_duplicate_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_certifications" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_certifications" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_certifications" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_certifications_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_certifications_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_certifications_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_languages" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_languages" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_languages" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_languages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_languages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_languages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_match_descriptions" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_match_descriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_match_descriptions" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_match_descriptions_vacancy_match_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_match_descriptions_vacancy_match_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_match_descriptions_vacancy_match_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_matches" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_matches" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_matches" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_matches_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_matches_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_matches_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_questionnaire_answers" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_questionnaire_answers" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_questionnaire_answers" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_questionnaire_answers_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_questionnaire_answers_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_questionnaire_answers_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_questionnaire_groups" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_questionnaire_groups" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_questionnaire_groups" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_questionnaire_questions" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_questionnaire_questions" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_questionnaire_questions" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_questionnaire_questions_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_questionnaire_questions_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_questionnaire_questions_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_seeker_questionaire_groups_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_seeker_questionaire_groups_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_seeker_questionaire_groups_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."vacancy_skills" TO "anon";
GRANT ALL ON TABLE "public"."vacancy_skills" TO "authenticated";
GRANT ALL ON TABLE "public"."vacancy_skills" TO "service_role";



GRANT ALL ON SEQUENCE "public"."vacancy_skills_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."vacancy_skills_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."vacancy_skills_id_seq" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
