CREATE TYPE feedback_type AS ENUM ('like', 'dislike');

CREATE TABLE match_feedbacks (
   id SERIAL PRIMARY KEY,
   vacancy_match_id INT NOT NULL,

   job_seeker_feedback_type feedback_type NULL,
   job_seeker_feedback_text TEXT NULL,
   job_seeker_feedback_at TIMESTAMP NULL,

   employer_feedback_type feedback_type NULL,
   employer_feedback_text TEXT NULL,
   employer_feedback_at TIMESTAMP NULL,

   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

   FOREIGN KEY (vacancy_match_id) REFERENCES vacancy_matches(id) ON DELETE CASCADE
);
