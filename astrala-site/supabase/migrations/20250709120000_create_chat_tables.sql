CREATE TABLE chats (
   id SERIAL PRIMARY KEY,
   vacancy_match_id INTEGER NOT NULL REFERENCES vacancy_matches(id) ON DELETE CASCADE,
   created_at TIMESTAMPTZ DEFAULT NOW(),
   updated_at TIMESTAMPTZ DEFAULT NOW(),
   is_active BOOLEAN DEFAULT TRUE,
   UNIQUE(vacancy_match_id)
);

CREATE TABLE chat_messages (
   id SERIAL PRIMARY KEY,
   chat_id INTEGER NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
   employer_id INTEGER REFERENCES employers(id),
   job_seeker_id INTEGER REFERENCES job_seekers(id),
   message_text TEXT NOT NULL,
   created_at TIMESTAMPTZ DEFAULT NOW(),
   read_at TIMESTAMPTZ,
   is_edited BOOLEAN DEFAULT FALSE,
   edited_at TIMESTAMPTZ
);
