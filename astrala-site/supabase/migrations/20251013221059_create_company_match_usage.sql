CREATE TABLE public.company_match_usage (
                                          id SERIAL PRIMARY KEY,
                                          company_id BIGINT NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
                                          period_start TIMESTAMPTZ NOT NULL,
                                          period_end TIMESTAMPTZ NOT NULL,
                                          matches_used INTEGER NOT NULL DEFAULT 0,
                                          created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
                                          updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
                                          CONSTRAINT unique_company_usage UNIQUE(company_id)
);

CREATE INDEX idx_company_match_usage_company ON public.company_match_usage(company_id);
