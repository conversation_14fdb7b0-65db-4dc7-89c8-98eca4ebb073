CREATE SEQUENCE job_seeker_dimension_scores_id_seq;

CREATE TABLE public.job_seeker_dimension_scores (
                                                  id integer NOT NULL DEFAULT nextval('job_seeker_dimension_scores_id_seq'::regclass),
                                                  job_seeker_id bigint NOT NULL,
                                                  dimension_id integer NOT NULL,
                                                  score numeric(3,2) NOT NULL CHECK (score >= 0.0 AND score <= 1.0),
                                                  calculated_at timestamp with time zone NOT NULL DEFAULT now(),
                                                  CONSTRAINT job_seeker_dimension_scores_pkey PRIMARY KEY (id),
                                                  CONSTRAINT job_seeker_dimension_scores_unique UNIQUE (job_seeker_id, dimension_id),
                                                  CONSTRAINT job_seeker_dimension_scores_job_seeker_id_fkey
                                                    FOREIGN KEY (job_seeker_id) REFERENCES public.job_seekers(id) ON DELETE CASCADE,
                                                  CONSTRAINT job_seeker_dimension_scores_dimension_id_fkey
                                                    FOREIGN KEY (dimension_id) REFERENCES public.dimensions(id) ON DELETE CASCADE
);

CREATE INDEX idx_dimension_scores_job_seeker
  ON public.job_seeker_dimension_scores(job_seeker_id);

CREATE INDEX idx_dimension_scores_dimension
  ON public.job_seeker_dimension_scores(dimension_id);

ALTER TABLE public.job_seeker_archetypes
DROP COLUMN dimension_scores;
