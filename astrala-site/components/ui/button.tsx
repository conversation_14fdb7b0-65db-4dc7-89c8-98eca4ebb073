import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Slot } from '@radix-ui/react-slot';

import { cn } from '~/lib/utils';

const buttonVariants = cva(
  'relative inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 overflow-hidden',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow',
        destructive:
          'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
        success:
          'bg-success text-success-foreground shadow-sm hover:bg-success/90',
        outline:
          'border border-input bg-background shadow-sm hover:bg-[#D9D9D980]',
        secondary: 'bg-[#D9D9D9] text-[#181D19] shadow-sm hover:bg-[#D9D9D980]',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2',
        sm: 'h-8 rounded-md px-3 text-xs',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-9 w-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export type ButtonProps = {
  asChild?: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants>;

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant, size, asChild = false, type = 'button', ...props },
    ref,
  ) => {
    const Comp = asChild ? Slot : 'button';

    const isStyled = variant === 'default' || variant === 'destructive';

    if (isStyled) {
      return (
        <Comp
          className={cn(
            buttonVariants({ variant: 'ghost', size }),
            'overflow-hidden group',
            className,
          )}
          ref={ref}
          type={type}
          style={{
            background:
              variant === 'default'
                ? 'linear-gradient(315deg, #0e2d28 0%, #23665f 100%)'
                : 'linear-gradient(315deg, #2d0e0e 0%, #5f2323 100%)',
            color: variant === 'default' ? '#e0f7ff' : '#ffe0e0',
          }}
          {...props}
        >
          <div
            className="absolute inset-0 rounded-lg pointer-events-none transition-opacity duration-500 group-hover:opacity-0"
            style={{
              padding: '1px',
              background:
                variant === 'default'
                  ? `
                  linear-gradient(
                    135deg,
                    #98F9FF 0%,
                    rgba(152, 249, 255, 0.7) 10%,
                    rgba(152, 249, 255, 0.5) 20%,
                    rgba(152, 249, 255, 0.3) 30%,
                    rgba(152, 249, 255, 0.15) 40%,
                    rgba(152, 249, 255, 0.05) 50%,
                    transparent 70%
                  ),
                  linear-gradient(
                    -45deg,
                    #EABFFF 0%,
                    rgba(234, 191, 255, 0.7) 10%,
                    rgba(234, 191, 255, 0.5) 20%,
                    rgba(234, 191, 255, 0.3) 30%,
                    rgba(234, 191, 255, 0.15) 40%,
                    rgba(234, 191, 255, 0.05) 50%,
                    transparent 70%
                  )
                `
                  : `
                  linear-gradient(
                    135deg,
                    #FF6B6B 0%,
                    rgba(255, 107, 107, 0.7) 10%,
                    rgba(255, 107, 107, 0.5) 20%,
                    rgba(255, 107, 107, 0.3) 30%,
                    rgba(255, 107, 107, 0.15) 40%,
                    rgba(255, 107, 107, 0.05) 50%,
                    transparent 70%
                  ),
                  linear-gradient(
                    -45deg,
                    #FF8E8E 0%,
                    rgba(255, 142, 142, 0.7) 10%,
                    rgba(255, 142, 142, 0.5) 20%,
                    rgba(255, 142, 142, 0.3) 30%,
                    rgba(255, 142, 142, 0.15) 40%,
                    rgba(255, 142, 142, 0.05) 50%,
                    transparent 70%
                  )
                `,
              backgroundSize: '100% 100%',
              backgroundPosition: 'top left, bottom right',
              WebkitMask:
                'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
              WebkitMaskComposite: 'xor',
              maskComposite: 'exclude',
            }}
          />
          <span className="relative z-10 transition-transform duration-300 group-hover:scale-105">
            {props.children}
          </span>
        </Comp>
      );
    }

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        type={type}
        {...props}
      />
    );
  },
);
Button.displayName = 'Button';

export { Button, buttonVariants };
