import * as React from 'react';

import { cn } from '~/lib/utils';

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'relative rounded-xl text-foreground shadow overflow-hidden',
      className,
    )}
    style={{
      background:
        'radial-gradient(90.16% 143.01% at 15.32% 21.04%, rgba(165, 239, 255, 0.2) 0%, rgba(110, 191, 244, 0.0447917) 77.08%, rgba(70, 144, 213, 0) 100%)',
    }}
    {...props}
  >
    <div
      className="absolute inset-0 rounded-xl pointer-events-none"
      style={{
        padding: '1px',
        background: `
          linear-gradient(
            135deg,
            #98F9FF 0%,
            rgba(152, 249, 255, 0.7) 10%,
            rgba(152, 249, 255, 0.5) 20%,
            rgba(152, 249, 255, 0.3) 30%,
            rgba(152, 249, 255, 0.15) 40%,
            rgba(152, 249, 255, 0.05) 50%,
            transparent 70%
          ),
          linear-gradient(
            -45deg,
            #EABFFF 0%,
            rgba(234, 191, 255, 0.7) 10%,
            rgba(234, 191, 255, 0.5) 20%,
            rgba(234, 191, 255, 0.3) 30%,
            rgba(234, 191, 255, 0.15) 40%,
            rgba(234, 191, 255, 0.05) 50%,
            transparent 70%
          )
        `,
        backgroundSize: '100% 100%',
        backgroundPosition: 'top left, bottom right',
        WebkitMask:
          'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
        WebkitMaskComposite: 'xor',
        maskComposite: 'exclude',
      }}
    />
    <div
      className="absolute inset-0 rounded-xl pointer-events-none opacity-20"
      style={{
        backgroundImage: 'url(/card-bg.png)',
        backgroundRepeat: 'repeat',
        backgroundSize: 'auto',
        mixBlendMode: 'overlay',
      }}
    />
    {props.children}
  </div>
));
Card.displayName = 'Card';

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-col space-y-1.5 p-6 relative z-10', className)}
    {...props}
  />
));
CardHeader.displayName = 'CardHeader';

const CardTitle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('font-semibold leading-none tracking-tight', className)}
    {...props}
  />
));
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));
CardDescription.displayName = 'CardDescription';

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('p-6 pt-0 relative z-10', className)}
    {...props}
  />
));
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center p-6 pt-0 relative z-10', className)}
    {...props}
  />
));
CardFooter.displayName = 'CardFooter';

export {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
};
