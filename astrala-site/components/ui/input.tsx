import * as React from 'react';

import { cn } from '~/lib/utils';

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(
  ({ className, type, onChange, value, ...props }, ref) => {
    if (type === 'number') {
      const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!onChange) return;

        onChange(e);
      };

      const displayValue = value === 0 ? '' : value;

      return (
        <input
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          className={cn(
            'flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary focus:border-primary disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
            className,
          )}
          ref={ref}
          value={displayValue}
          onChange={handleNumberChange}
          {...props}
          onBlur={e => {
            if (e.target.value === '' && onChange) {
              const syntheticEvent = {
                ...e,
                target: {
                  ...e.target,
                  value: '0',
                },
              } as React.ChangeEvent<HTMLInputElement>;
              onChange(syntheticEvent);
            }

            if (props.onBlur) props.onBlur(e);
          }}
        />
      );
    }

    return (
      <input
        type={type}
        className={cn(
          'flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-background file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary focus:border-primary disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          className,
        )}
        ref={ref}
        onChange={onChange}
        value={value}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export { Input };
