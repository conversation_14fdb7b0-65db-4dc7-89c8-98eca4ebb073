'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';

import { useCreateThread, useGetThreadMessages, useSendMessage } from '~/api';

export type Message = {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  imageUrl?: string;
  timestamp: Date;
};

export function useChat(existingThreadId?: string) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [threadId, setThreadId] = useState<string | null>(
    existingThreadId || null,
  );
  const [input, setInput] = useState('');
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
  const [isUserScrolled, setIsUserScrolled] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const createThreadMutation = useCreateThread();
  const sendMessageMutation = useSendMessage();

  const {
    data: threadMessages = [],
    isLoading: isLoadingMessages,
    error: messagesError,
  } = useGetThreadMessages(existingThreadId || '');

  const isLoading =
    createThreadMutation.isPending ||
    sendMessageMutation.isPending ||
    isLoadingMessages;
  const isEmpty = messages.length === 0;

  const scrollToBottom = useCallback(
    (force = false) => {
      if (force || !isUserScrolled) {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }
    },
    [isUserScrolled],
  );

  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } =
      scrollContainerRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

    setIsUserScrolled(!isNearBottom);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages.length, scrollToBottom]);

  useEffect(() => {
    if (existingThreadId && threadMessages.length > 0) {
      const filteredMessages: Message[] = threadMessages
        .filter(msg => msg.role !== 'system')
        .map((msg, index) => ({
          id: `${existingThreadId}-${index}`,
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          imageUrl: msg.imageUrl,
          timestamp: new Date(),
        }));

      setMessages(filteredMessages);
    }
  }, [existingThreadId, threadMessages]);

  useEffect(() => {
    if (existingThreadId && threadId !== existingThreadId) {
      setThreadId(existingThreadId);
      setMessages([]);
      setInput('');
      setCopiedMessageId(null);
      setIsUserScrolled(false);
    }
  }, [existingThreadId, threadId]);

  const startChat = useCallback(async () => {
    try {
      const response = await createThreadMutation.mutateAsync();
      router.push(`/chat/${response.thread_id}`);
    } catch (error) {
      console.error('Failed to start chat:', error);
    }
  }, [createThreadMutation, router]);

  const sendMessage = useCallback(
    async (messageText?: string) => {
      const textToSend = messageText || input;

      if (!textToSend.trim() || !threadId || isLoading) {
        return;
      }

      const userMessage: Message = {
        id: `${threadId}-${Date.now()}`,
        role: 'user',
        content: textToSend,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);
      setInput('');

      try {
        const response = await sendMessageMutation.mutateAsync({
          threadId,
          message: textToSend,
        });

        const assistantMessage: Message = {
          id: `${threadId}-${Date.now() + 1}`,
          role: 'assistant',
          content: response.text,
          imageUrl: response?.image_url,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, assistantMessage]);
      } catch (error) {
        console.error('Failed to send message:', error);
        setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
      }
    },
    [input, threadId, isLoading, sendMessageMutation],
  );

  const copyMessage = useCallback((content: string, messageId: string) => {
    navigator.clipboard.writeText(content);
    setCopiedMessageId(messageId);
    setTimeout(() => setCopiedMessageId(null), 2000);
  }, []);

  const clearInput = useCallback(() => {
    setInput('');
  }, []);

  const resetChat = useCallback(() => {
    setMessages([]);
    setThreadId(null);
    setInput('');
    setCopiedMessageId(null);
    setIsUserScrolled(false);
  }, []);

  return {
    messages,
    threadId,
    input,
    copiedMessageId,
    isEmpty,
    isLoading,
    isUserScrolled,
    messagesEndRef,
    scrollContainerRef,

    setInput,
    startChat,
    sendMessage,
    copyMessage,
    clearInput,
    resetChat,
    scrollToBottom,
    handleScroll,

    createError: createThreadMutation.error,
    sendError: sendMessageMutation.error,
    messagesError,
  };
}
