'use client';

import { useCallback, useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';

import { useCreateThread, useGetThreads } from '~/api';

export type Thread = {
  id: string;
  title: string;
  active: boolean;
  createdAt: Date;
};

type ApiThread = {
  uid: string;
  name: string;
};

export function useSidebar() {
  const router = useRouter();
  const params = useParams();
  const activeThreadId = params?.threadId as string | undefined;

  const [offset, setOffset] = useState(0);
  const [allThreads, setAllThreads] = useState<ApiThread[]>([]);
  const limit = 50;

  const {
    data: currentPageThreads = [],
    isLoading,
    error,
    isFetching,
  } = useGetThreads(limit, offset);

  const createThreadMutation = useCreateThread();

  useEffect(() => {
    if (currentPageThreads.length > 0) {
      if (offset === 0) {
        setAllThreads(currentPageThreads);
      } else {
        setAllThreads(prev => [...prev, ...currentPageThreads]);
      }
    }
  }, [currentPageThreads, offset]);

  const threads: Thread[] = allThreads.map(thread => ({
    id: thread.uid,
    title: thread.name,
    active: thread.uid === activeThreadId,
    createdAt: new Date(),
  }));

  const hasMore = currentPageThreads.length === limit;

  const createNewThread = useCallback(async () => {
    try {
      const response = await createThreadMutation.mutateAsync();
      const newThread: ApiThread = {
        uid: response.thread_id,
        name: `Thread ${response.thread_id.slice(0, 8)}`,
      };
      setAllThreads(prev => [newThread, ...prev]);

      router.push(`/chat/${response.thread_id}`);
    } catch (error) {
      console.error('❌ Failed to create new thread:', error);
    }
  }, [createThreadMutation, router]);

  const selectThread = useCallback(
    (threadId: string) => {
      router.push(`/chat/${threadId}`);
    },
    [router],
  );

  const loadMoreThreads = useCallback(() => {
    setOffset(prev => prev + limit);
  }, [limit]);

  return {
    threads,
    activeThreadId,
    isLoading: isLoading && offset === 0,
    isLoadingMore: isFetching && offset > 0,
    error,
    hasMore,
    isCreatingThread: createThreadMutation.isPending,

    createNewThread,
    selectThread,
    loadMoreThreads,

    isEmpty: threads.length === 0,
  };
}
