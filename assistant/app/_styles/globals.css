@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
}



@layer base {
  :root {

    --background: 0 0% 100%;

    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;

    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;

    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;

    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;

    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;

    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;

    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;

    --input: 0 0% 89.8%;

    --ring: 0 0% 3.9%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem
  }
  .dark {
    --background: 0 0% 12%;
    --foreground: 0 0% 95%;

    --card: 0 0% 15%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 14%;
    --popover-foreground: 0 0% 95%;

    --primary: 0 0% 85%;
    --primary-foreground: 0 0% 15%;

    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 90%;

    --muted: 0 0% 18%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 22%;
    --accent-foreground: 0 0% 90%;

    --destructive: 0 70% 45%;
    --destructive-foreground: 0 0% 95%;

    --border: 0 0% 25%;
    --input: 0 0% 18%;
    --ring: 0 0% 70%;

    --chart-1: 220 60% 55%;
    --chart-2: 160 50% 50%;
    --chart-3: 30 70% 60%;
    --chart-4: 280 55% 65%;
    --chart-5: 340 65% 60%
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
