import { FC, PropsWithChildren } from 'react';

import './_styles/globals.css';

import { TooltipProvider } from '~/components/ui/tooltip.tsx';
import { QueryProvider } from '~/providers/QueryProvider.tsx';

export const metadata = {
  title: '<PERSON>trala AI Assistant',
  description: 'Generated by Next.js',
};

const RootLayout: FC<PropsWithChildren> = ({ children }) => {
  return (
    <html lang="en" className="dark">
      <body>
        <QueryProvider>
          <TooltipProvider>{children}</TooltipProvider>
        </QueryProvider>
      </body>
    </html>
  );
};

export default RootLayout;
