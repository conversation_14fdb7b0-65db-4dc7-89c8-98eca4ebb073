'use client';

import { FC } from 'react';
import { useParams } from 'next/navigation';

import { Chat } from '~/app/(components)/Chat';
import { Sidebar } from '~/app/(components)/Sidebar';

const ThreadPage: FC = () => {
  const params = useParams();
  const threadId = params?.threadId as string;

  return (
    <main className="h-screen flex bg-background">
      <aside className="w-64 bg-muted/30 border-r border-border">
        <Sidebar />
      </aside>
      <section className="flex-1">
        <Chat threadId={threadId} />
      </section>
    </main>
  );
};

export default ThreadPage;
