import { Skeleton } from '~/components/ui/skeleton.tsx';

type MessageSkeletonProps = {
  role: 'user' | 'assistant';
  isTyping?: boolean;
};

export function MessageSkeleton({ role, isTyping }: MessageSkeletonProps) {
  if (role === 'user') {
    return (
      <div className="grid auto-rows-auto grid-cols-[minmax(72px,1fr)_auto] gap-y-2 [&:where(>*)]:col-start-2 w-full max-w-[var(--thread-max-width)] py-4">
        <div className="flex flex-col items-end col-start-1 row-start-2 mr-3 mt-2.5">
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
        <div className="max-w-[calc(var(--thread-max-width)*0.8)] col-start-2 row-start-2">
          <Skeleton className="h-16 w-full rounded-3xl" />
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-[auto_auto_1fr] grid-rows-[auto_1fr] relative w-full max-w-[var(--thread-max-width)] py-4">
      <div className="max-w-[calc(var(--thread-max-width)*0.8)] col-span-2 col-start-2 row-start-1 my-1.5 space-y-2">
        {isTyping ? (
          <div className="flex items-center space-x-2">
            <div className="flex space-x-1">
              <div
                className="w-2 h-2 bg-muted rounded-full animate-bounce"
                style={{ animationDelay: '0ms' }}
              />
              <div
                className="w-2 h-2 bg-muted rounded-full animate-bounce"
                style={{ animationDelay: '150ms' }}
              />
              <div
                className="w-2 h-2 bg-muted rounded-full animate-bounce"
                style={{ animationDelay: '300ms' }}
              />
            </div>
            <span className="text-muted-foreground text-sm">Typing...</span>
          </div>
        ) : (
          <>
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </>
        )}
      </div>
      {!isTyping && (
        <div className="flex gap-1 col-start-3 row-start-2 -ml-1">
          <Skeleton className="h-6 w-6 rounded-md" />
          <Skeleton className="h-6 w-6 rounded-md" />
        </div>
      )}
    </div>
  );
}
