'use client';

import React from 'react';
import { motion } from 'framer-motion';

import { cn } from '~/lib/utils';

type ThreadListItemProps = {
  thread: {
    id: string;
    title: string;
  };
  isActive: boolean;
  onSelect: () => void;
};

export const ThreadListItem = React.memo(
  ({ thread, isActive, onSelect }: ThreadListItemProps) => {
    return (
      <motion.div
        className="relative overflow-hidden"
        animate={{
          x: isActive ? 8 : 0,
        }}
        transition={{
          duration: 0.2,
          ease: 'easeOut',
        }}
      >
        <motion.div
          className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-full z-10"
          initial={false}
          animate={{
            opacity: isActive ? 1 : 0,
            scaleY: isActive ? 1 : 0,
          }}
          transition={{
            duration: 0.2,
            ease: 'easeOut',
          }}
        />

        <button
          className={cn(
            'w-full flex items-center gap-3 rounded-lg px-3 py-2.5 text-start transition-colors duration-200 relative',
            'hover:bg-accent hover:text-accent-foreground',
            'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
            isActive && 'bg-accent text-accent-foreground',
          )}
          onClick={onSelect}
        >
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{thread.title}</p>
          </div>
        </button>
      </motion.div>
    );
  },
);
