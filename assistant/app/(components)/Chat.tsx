'use client';

import ReactMarkdown from 'react-markdown';
import {
  ArrowDownIcon,
  CheckIcon,
  CopyIcon,
  SendHorizontalIcon,
} from 'lucide-react';
import Image from 'next/image';

import { MessageSkeleton } from '~/app/(components)/MessageSkeleton.tsx';

import { Message, useChat } from '../(hooks)/useChat';

import { TooltipIconButton } from '~/components/assistant-ui/tooltip-icon-button';

type ChatProps = {
  threadId?: string;
};

export function Chat({ threadId }: ChatProps) {
  const {
    messages,
    input,
    copiedMessageId,
    isEmpty,
    isLoading,
    isUserScrolled,
    messagesEndRef,
    scrollContainerRef,
    setInput,
    startChat,
    sendMessage,
    copyMessage,
    scrollToBottom,
    handleScroll,
  } = useChat(threadId);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage();

    const textarea = e.currentTarget.querySelector('textarea');
    if (textarea) {
      textarea.style.height = 'auto';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (input.trim() && !isLoading) {
        sendMessage();

        const textarea = e.currentTarget;
        setTimeout(() => {
          textarea.style.height = 'auto';
        }, 0);
      }
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);

    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 160)}px`; // max ~10 строк
  };

  const renderUserMessage = (content: string) => {
    return content.split('\n').map((line, index, array) => (
      // eslint-disable-next-line react/no-array-index-key
      <span key={index}>
        {line}
        {index < array.length - 1 && <br />}
      </span>
    ));
  };

  const renderAssistantMessage = (message: Message) => {
    return (
      <>
        {message?.imageUrl && (
          <Image
            src={`data:image/png;base64,${message.imageUrl}`}
            alt="Chart Image"
            width={600}
            height={400}
            className="max-w-full h-auto rounded-lg mb-4"
            unoptimized={true}
          />
        )}

        <ReactMarkdown
          components={{
            h1: ({ children }) => (
              <h1 className="text-lg font-bold mb-3 mt-4">{children}</h1>
            ),
            h2: ({ children }) => (
              <h2 className="text-base font-bold mb-2 mt-3">{children}</h2>
            ),
            h3: ({ children }) => (
              <h3 className="text-sm font-bold mb-2 mt-2">{children}</h3>
            ),
            p: ({ children }) => (
              <p className="mb-3 leading-relaxed">{children}</p>
            ),
            ul: ({ children }) => (
              <ul className="mb-3 ml-4 list-disc space-y-1">{children}</ul>
            ),
            ol: ({ children }) => (
              <ol className="mb-3 ml-4 list-decimal space-y-1">{children}</ol>
            ),
            li: ({ children }) => (
              <li className="leading-relaxed">{children}</li>
            ),
            strong: ({ children }) => (
              <strong className="font-semibold">{children}</strong>
            ),
            em: ({ children }) => <em className="italic">{children}</em>,
            code: ({ children }) => (
              <code className="bg-muted px-1 py-0.5 rounded text-sm">
                {children}
              </code>
            ),
            pre: ({ children }) => (
              <pre className="bg-muted p-3 rounded-md overflow-x-auto text-sm mb-3">
                {children}
              </pre>
            ),
            blockquote: ({ children }) => (
              <blockquote className="border-l-4 border-muted-foreground/20 pl-4 my-3 italic">
                {children}
              </blockquote>
            ),
          }}
        >
          {message.content}
        </ReactMarkdown>
      </>
    );
  };

  return (
    <div
      className="bg-background box-border flex h-full flex-col overflow-hidden"
      style={{ ['--thread-max-width' as string]: '42rem' }}
    >
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className="flex h-full flex-col items-center overflow-y-scroll scroll-smooth bg-inherit px-4 pt-8"
      >
        {isEmpty && !threadId && (
          <div className="flex w-full max-w-[var(--thread-max-width)] flex-grow flex-col">
            <div className="flex w-full flex-grow flex-col items-center justify-center">
              <p className="mt-4 font-medium">Character Profile Assistant</p>
              <p className="text-muted-foreground mb-8 text-center">
                Ready to help you create your professional character profile
              </p>
            </div>

            <div className="mt-3 flex w-full items-stretch justify-center gap-4 pb-12">
              <button
                onClick={startChat}
                disabled={isLoading}
                className="hover:bg-muted/80 flex max-w-sm grow basis-0 flex-col items-center justify-center rounded-lg border p-3 transition-colors ease-in"
              >
                <span className="line-clamp-2 text-ellipsis text-sm font-semibold">
                  {isLoading ? 'Starting...' : 'Start Character Assessment'}
                </span>
              </button>
            </div>
          </div>
        )}

        {isEmpty && threadId && isLoading && (
          <>
            <div className="w-full max-w-[var(--thread-max-width)] flex-grow">
              <MessageSkeleton role="assistant" />
              <MessageSkeleton role="user" />
              <MessageSkeleton role="assistant" />
              <MessageSkeleton role="user" />
              <MessageSkeleton role="assistant" />
              <MessageSkeleton role="user" />
              <MessageSkeleton role="assistant" />
              <MessageSkeleton role="user" />
              <MessageSkeleton role="assistant" />
              <MessageSkeleton role="user" />
            </div>
            <div className="min-h-8" />
          </>
        )}

        {!isEmpty && (
          <div className="w-full max-w-[var(--thread-max-width)]">
            {messages.map(message => (
              <div key={message.id}>
                {message.role === 'user' ? (
                  <div className="grid auto-rows-auto grid-cols-[minmax(72px,1fr)_auto] gap-y-2 [&:where(>*)]:col-start-2 w-full max-w-[var(--thread-max-width)] py-4">
                    <div className="bg-muted text-foreground max-w-[calc(var(--thread-max-width)*0.8)] break-words rounded-3xl px-5 py-2.5 col-start-2 row-start-2">
                      {renderUserMessage(message.content)}
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-[auto_auto_1fr] grid-rows-[auto_1fr] relative w-full max-w-[var(--thread-max-width)] py-4">
                    <div className="text-foreground max-w-[calc(var(--thread-max-width)*0.8)] break-words leading-7 col-span-2 col-start-2 row-start-1 my-1.5">
                      {renderAssistantMessage(message)}
                    </div>

                    <div className="text-muted-foreground flex gap-1 col-start-3 row-start-2 -ml-1 data-[floating]:bg-background data-[floating]:absolute data-[floating]:rounded-md data-[floating]:border data-[floating]:p-1 data-[floating]:shadow-sm">
                      <TooltipIconButton
                        tooltip="Copy"
                        onClick={() => copyMessage(message.content, message.id)}
                      >
                        {copiedMessageId === message.id ? (
                          <CheckIcon />
                        ) : (
                          <CopyIcon />
                        )}
                      </TooltipIconButton>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {isLoading && <MessageSkeleton role="assistant" isTyping />}
            <div ref={messagesEndRef} />
          </div>
        )}

        {!isEmpty && <div className="min-h-8 flex-grow" />}

        {(!isEmpty || threadId) && (
          <div className="sticky bottom-0 mt-3 flex w-full max-w-[var(--thread-max-width)] flex-col items-center justify-end rounded-t-lg bg-inherit pb-4">
            {isUserScrolled && (
              <TooltipIconButton
                tooltip="Scroll to bottom"
                variant="outline"
                className="absolute -top-8 rounded-full"
                onClick={() => scrollToBottom(true)}
              >
                <ArrowDownIcon />
              </TooltipIconButton>
            )}

            <form onSubmit={handleSubmit} className="w-full">
              <div className="focus-within:border-ring/20 flex w-full flex-wrap items-end rounded-lg border bg-inherit px-2.5 shadow-sm transition-colors ease-in">
                <textarea
                  value={input}
                  onChange={handleTextareaChange}
                  onKeyDown={handleKeyDown}
                  placeholder="Write a message..."
                  className="placeholder:text-muted-foreground min-h-[24px] max-h-40 flex-grow resize-none border-none bg-transparent px-2 py-4 text-sm outline-none focus:ring-0 disabled:cursor-not-allowed overflow-y-auto"
                  disabled={isLoading}
                  rows={1}
                  style={{ height: 'auto' }}
                />
                <TooltipIconButton
                  tooltip="Send"
                  type="submit"
                  disabled={isLoading || !input.trim()}
                  variant="default"
                  className="my-2.5 size-8 p-2 transition-opacity ease-in"
                >
                  <SendHorizontalIcon />
                </TooltipIconButton>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
