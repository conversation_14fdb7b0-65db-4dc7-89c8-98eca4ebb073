'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { PlusIcon } from 'lucide-react';

import { ThreadListItem } from '~/app/(components)/ThreadListItem.tsx';
import { useSidebar } from '~/app/(hooks)/useSidebar';

import { Button } from '~/components/ui/button';
import { Skeleton } from '~/components/ui/skeleton';

export function Sidebar() {
  const {
    threads,
    activeThreadId,
    isEmpty,
    isLoading,
    isLoadingMore,
    hasMore,
    isCreatingThread,
    createNewThread,
    selectThread,
    loadMoreThreads,
  } = useSidebar();

  const renderThreadsList = () => {
    if (isLoading) {
      return (
        <div className="space-y-1">
          {Array.from({ length: 8 }).map((_, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <ThreadSkeleton key={index} />
          ))}
        </div>
      );
    }

    if (isEmpty) {
      return (
        <div className="flex flex-col items-center justify-center h-32 text-center">
          <p className="text-muted-foreground text-sm">No threads yet</p>
          <p className="text-muted-foreground text-xs mt-1">
            Start a new conversation!
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-1">
        {threads.map(thread => (
          <ThreadListItem
            key={thread.id}
            thread={thread}
            isActive={thread.id === activeThreadId}
            onSelect={() => selectThread(thread.id)}
          />
        ))}

        {isLoadingMore && (
          <div className="space-y-1 pt-2">
            {Array.from({ length: 3 }).map((_, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <ThreadSkeleton key={index} />
            ))}
          </div>
        )}

        {hasMore && !isLoadingMore && (
          <div className="pt-2">
            <Button
              onClick={loadMoreThreads}
              variant="ghost"
              className="w-full text-sm text-muted-foreground hover:text-foreground"
              disabled={isLoadingMore}
            >
              Load More
            </Button>
          </div>
        )}

        {!hasMore && threads.length > 0 && (
          <div className="pt-4 pb-2 text-center">
            <p className="text-xs text-muted-foreground">All threads loaded</p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full p-4">
      <div className="flex flex-col items-stretch gap-1.5 h-full">
        <div className="flex gap-1">
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1"
          >
            <Button
              onClick={createNewThread}
              disabled={isCreatingThread}
              className="data-[active]:bg-muted hover:bg-muted flex items-center justify-start gap-1 rounded-lg px-2.5 py-2 text-start w-full"
              variant="ghost"
            >
              <motion.div
                animate={{ rotate: isCreatingThread ? 360 : 0 }}
                transition={{
                  duration: 0.5,
                  repeat: isCreatingThread ? Infinity : 0,
                }}
              >
                <PlusIcon />
              </motion.div>
              {isCreatingThread ? 'Creating...' : 'New Thread'}
            </Button>
          </motion.div>
        </div>

        <div className="flex-1 overflow-y-auto min-h-0 overflow-x-hidden pr-4">
          {renderThreadsList()}
        </div>

        <div className="pt-4 border-t border-border shrink-0">
          <p className="text-xs text-muted-foreground text-center">
            Character Profile Assistant
          </p>
          <AnimatePresence mode="wait">
            {threads.length > 0 && (
              <motion.p
                key={threads.length}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
                className="text-xs text-muted-foreground text-center mt-1"
              >
                {threads.length} thread{threads.length === 1 ? '' : 's'}
              </motion.p>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}

function ThreadSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0.5 }}
      animate={{ opacity: [0.5, 1, 0.5] }}
      transition={{ duration: 1.5, repeat: Infinity }}
      className="flex items-center gap-2 rounded-lg px-3 py-2"
    >
      <Skeleton className="h-4 flex-grow" />
      <Skeleton className="h-4 w-4" />
    </motion.div>
  );
}
