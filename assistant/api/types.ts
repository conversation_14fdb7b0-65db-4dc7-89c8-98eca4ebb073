export type CreateThreadResponse = {
  thread_id: string;
  messages: Array<{
    role: string;
    content: string;
  }>;
};

export type SendMessageRequest = {
  threadId: string;
  message: string;
};

export type Thread = {
  uid: string;
  name: string;
};

export type ApiThread = {
  threads: Thread[];
};

export type ThreadMessage = {
  role: 'system' | 'assistant' | 'user';
  content: string;
  imageUrl?: string;
};

export type SendMessageResponse = {
  text: string;
  image_url?: string;
};

export type GetThreadMessagesResponse = ThreadMessage[];
