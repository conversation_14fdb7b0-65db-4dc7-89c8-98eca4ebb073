import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  createThread,
  getThreadMessages,
  getThreads,
  sendMessage,
} from './actions';
import {
  CreateThreadResponse,
  GetThreadMessagesResponse,
  SendMessageRequest,
  SendMessageResponse,
  Thread,
} from './types';

export function useCreateThread() {
  const queryClient = useQueryClient();

  return useMutation<CreateThreadResponse>({
    mutationFn: async () => {
      return await createThread();
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['threads'] });
    },
  });
}

export function useSendMessage() {
  return useMutation<SendMessageResponse, Error, SendMessageRequest>({
    mutationFn: async ({ threadId, message }) => {
      return await sendMessage(threadId, message);
    },
  });
}

export function useGetThreads(limit = 10, offset = 0) {
  return useQuery<Thread[]>({
    queryKey: ['threads', limit, offset],
    queryFn: async () => {
      const data = await getThreads(limit, offset);
      return data.threads;
    },
  });
}

export function useGetThreadMessages(threadId: string) {
  return useQuery<GetThreadMessagesResponse>({
    queryKey: ['thread-messages', threadId],
    queryFn: async () => {
      return await getThreadMessages(threadId);
    },
    enabled: Boolean(threadId),
  });
}
