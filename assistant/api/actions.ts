'use server';

import {
  ApiThread,
  CreateThreadResponse,
  SendMessageResponse,
  ThreadMessage,
} from './types';

const API_URL = process.env.API_URL || 'http://backend:8000';

type GetThreadMessagesResponse = ThreadMessage[];

export async function createThread(): Promise<CreateThreadResponse> {
  const response = await fetch(`${API_URL}/threads/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to create thread: ${response.statusText}`);
  }

  return response.json();
}

export async function sendMessage(
  threadId: string,
  message: string,
): Promise<SendMessageResponse> {
  const response = await fetch(`${API_URL}/threads/${threadId}/chat`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ message }),
  });

  if (!response.ok) {
    throw new Error(`Failed to send message: ${response.statusText}`);
  }

  return response.json();
}

export async function getThreads(limit = 10, offset = 0): Promise<ApiThread> {
  const response = await fetch(
    `${API_URL}/threads?limit=${limit}&offset=${offset}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );

  if (!response.ok) {
    throw new Error(`Failed to get threads: ${response.statusText}`);
  }

  return response.json();
}

export async function getThreadMessages(
  threadId: string,
): Promise<GetThreadMessagesResponse> {
  const response = await fetch(`${API_URL}/threads/${threadId}/messages`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get thread messages: ${response.statusText}`);
  }

  const data = await response.json();

  return data.map((msg: SendMessageResponse) => ({
    ...msg,
    imageUrl: msg.image_url,
  }));
}
