# Note that "docker build" should be executed from the project root, and run like "docker build -f assistant-site/Dockerfile ."
FROM node:20.18-alpine AS base

# 1. Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --update --no-cache libc6-compat python3 make g++ && rm -rf /var/cache/apk/*

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
COPY assistant/package.json ./assistant/
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i; \
  else echo "Lockfile not found." && exit 1; \
  fi

# 2. Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/assistant/node_modules* ./assistant/node_modules
COPY . .

# Example of adding build-time env variables:
# ARG SENTRY_AUTH_TOKEN
# ENV SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN

WORKDIR /app/assistant
RUN npm run build

# 3. Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
RUN apk add --update --no-cache curl


# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/assistant/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/assistant/.next/static ./assistant/.next/static
COPY --from=builder /app/assistant/public ./assistant/public

USER nextjs

EXPOSE 3000
ENV PORT=3000
CMD HOSTNAME="0.0.0.0" node assistant/server.js
