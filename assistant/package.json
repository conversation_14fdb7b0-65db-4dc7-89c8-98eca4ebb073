{"name": "@astrala/assistant", "version": "1.0.0", "description": "", "private": true, "author": "", "license": "ISC", "scripts": {"dev": "dotenv -e ../.env.local -- next dev", "build": "dotenv -e ../.env -- next build", "start": "dotenv -e ../.env -- next start", "gen:types": "source ../.env && supabase gen types typescript --project-id $SUPABASE_PROJECT_ID > shared/db/generated/types.ts", "db:stop": "dotenv -e ../.env.local -- supabase stop", "db:start": "dotenv -e ../.env.local -- supabase start", "typecheck": "tsc", "lint": "npm run typecheck && next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@assistant-ui/react": "^0.10.20", "@assistant-ui/react-ai-sdk": "^0.10.13", "@assistant-ui/react-markdown": "^0.10.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "lucide-react": "^0.511.0", "next": "^14.2.15", "postmark": "^4.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "supabase": "^2.0.0", "tailwindcss": "^3.4.13"}}