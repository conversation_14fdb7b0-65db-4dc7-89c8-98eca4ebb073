{
  "compilerOptions": {
    "composite": true,

    /* Language and Environment */
    "target": "es2023",
    "sourceMap": true,

    /* Modules */
    "module": "commonjs",
    "moduleResolution": "node",
    "resolveJsonModule": true,

    "allowJs": true,
    "allowImportingTsExtensions": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    "alwaysStrict": true,

    "removeComments": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,

    "strict": true,

    /* Completeness */
    "skipLibCheck": true
  },
  "exclude": ["node_modules"]
}
