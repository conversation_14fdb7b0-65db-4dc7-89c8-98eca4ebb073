const { configure, presets } = require('eslint-kit');

/** @type {import("eslint").ExtendedLinterConfig} */
module.exports = configure({
  mode: 'only-errors',
  presets: [
    presets.typescript({
      root: '.',
      tsconfig: 'tsconfig.json',
      enforceUsingType: true,
    }),
    presets.nextJs(),
    presets.react(),
    presets.imports({
      sort: {
        newline: true,
        groups: [
          ['^react', '^\\w', '^@\\w+'],
          ['^~/app'],
          ['^~/widgets'],
          ['^~/features'],
          ['^~/entities'],
          ['^~/shared'],
          ['^\\.\\.(?!/?$)', '^\\.\\./?$'],
          ['^\\u0000'],
        ],
      },
    }),
    presets.prettier({
      semi: true,
      singleQuote: true,
      tabWidth: 2,
      quoteProps: 'consistent',
      trailingComma: 'all',
      bracketSpacing: true,
      arrowParens: 'avoid',
      printWidth: 80,
    }),
  ],
  extend: {
    root: true,
    settings: {
      next: {
        rootDir: 'astrala-site/',
      },
    },
    ignorePatterns: ['*.js', '*.cjs', '*.mjs'],
  },
});
