const { configure, presets } = require("eslint-kit");

module.exports = configure({
  root: __dirname,
  allowDebug: process.env.NODE_ENV !== "production",
  mode: "default",
  presets: [
    presets.imports({
      sort: {
        newline: true,
        groups: [
          // side effects
          ["^\\u0000"],
          // node.js libraries and scoped libraries
          [
            "^(child_process|crypto|events|fs|http|https|os|path)(/.*)?$",
            "^@?\\w",
          ],
          // common aliases (@/, ~/) and anything not matched
          ["^@/", "^~", "^"],
          // relative imports
          ["^\\."],
        ],
      },
      alias: {
        root: "./src",
        paths: {
          "@": "./src",
        },
      },
    }),
    presets.typescript({
      root: "./",
      tsconfig: "tsconfig.json",
      enforceUsingType: true,
      parserOptions: {
        project: "./tsconfig.json",
        tsconfigRootDir: __dirname,
      },
    }),
    presets.prettier({
      trailingComma: "es5",
    }),
    presets.react({
      version: "detect",
      newJSXTransform: true,
    }),
    presets.nextJs(),
  ],
  extend: {
    rules: {
      // Custom rules can be added here
      "no-console": process.env.NODE_ENV === "production" ? "error" : "warn",
    },
  },
});
