Epic,Tasks,Milestone,Comments,FE (PERT),FE (Opt),FE (Real),FE (Pes),BE PERT),BE Opt),BE (Real),BE (Pes),UI/UX Design,QA,PM
Authentication & Role-Based Routing,"As a user, I want to be routed to discovery after login so that I can browse professionals.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Authentication & Role-Based Routing,"As a user with an IBP account, I want to switch to my IBP dashboard so that I can manage my business.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Authentication & Role-Based Routing,"As an IBP, I want to be blocked from full dashboard access until onboarding is complete so that verification requirements are enforced.",<PERSON>,<PERSON>e Connect KYC,1.0,0,1,2,1.0,0,1,2,,0.5,0.3
Authentication & Role-Based Routing,"As an unverified IBP, I want to see a verification pending banner so that I understand my account status.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Onboarding,"As an IBP, I want to see an onboarding introduction so that I understand the setup process before starting.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Onboarding,"As an IBP, I want to enter my personal and business information so that my professional profile is correctly created.",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
IBP Onboarding,"As an IBP, I want my zip code to automatically resolve my city and state so that data entry is easier.",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
IBP Onboarding,"As an IBP, I want required fields to be validated so that incomplete submissions are prevented",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Onboarding,"As an IBP, I want to select my professional specialties so that clients can discover me correctly.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
IBP Onboarding,"As an IBP, I want to be required to select at least one specialty so that my services are categorized.",MVP,,0.0,0,0,0,0.0,0,0,0,,0.0,0.0
IBP Onboarding,"As an IBP, I want to upload my professional license number so that my account can be verified.",MVP,,0.2,0,0,1,0.8,0,1,1,,0.3,0.1
IBP Onboarding,"As an IBP, I want my account status to change to pending verification after submitting my license so that I know verification is in progress.",MVP,,0.0,0,0,0,0.2,0,0,1,,0.0,0.0
IBP Onboarding,"As an IBP, I want to review a summary of my onboarding information so that I can confirm accuracy before entering the dashboard.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
IBP Service Management,"As an IBP, I want to view a list of my services so that I can manage what clients can book.",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
IBP Service Management,"As an IBP, I want to add new services so that clients can book them.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Service Management,"As an IBP, I want to define service name, duration, price, and category so that bookings are accurate.",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
IBP Service Management,"As an IBP, I want duration and price to be required so that services are always bookable.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Service Management,"As an IBP, I want to create required and optional add-ons so that I can customize services.",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
IBP Service Management,"As an IBP, I want required add-ons to be automatically applied so that bookings always include them.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
IBP Service Management,"As an IBP, I want to review the full service summary before saving so that pricing and duration are correct.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Availability Management,"As an IBP, I want to set my weekly availability so that clients can book time with me.",MVP,,2.0,1,2,3,1.0,0,1,2,,0.8,0.4
IBP Availability Management,"As an IBP, I want to be required to configure at least one day so that booking is possible.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
IBP Availability Management,"As an IBP, I want to add time off so that clients cannot book me when I am unavailable.",MVP,,2.0,1,2,3,1.0,0,1,2,,0.8,0.4
IBP Availability Management,"As an IBP, I want time off to override weekly availability so that conflicts are avoided.",MVP,,1.2,1,1,2,1.2,1,1,2,,0.6,0.3
IBP Availability Management,"As an IBP, I want to define which days of the week a specific service can be booked",Post MVP,,1.2,1,1,2,1.2,1,1,2,,0.6,0.3
IBP Cancellation Policy,"As an IBP, I want to define a free cancellation window so that clients know when cancellations are allowed.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Cancellation Policy,"As an IBP, I want to configure a late cancellation fee so that my time is protected.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Cancellation Policy,"As an IBP, I want to configure a no-show fee so that missed appointments are handled fairly.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Cancellation Policy,"As an IBP, I want to preview my cancellation policy exactly as clients will see it so that there are no surprises.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Cancellation Policy,"As an IBP, I want to save my policy only after reviewing it so that incorrect settings are avoided.",MVP,,0.0,0,0,0,0.0,0,0,0,,0.0,0.0
IBP Cancellation Policy,"As an IBP, I want the authorization hold amount to match my late cancellation fee so that fees can be enforced.",MVP,,0.0,0,0,0,2.0,1,2,3,,0.5,0.3
IBP Cancellation Policy,"As an IBP, I want clients to be required to agree to my cancellation policy before booking so that consent is explicit.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Client Import,"As an IBP, I want to upload a client list file so that my existing clients can be imported.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
IBP Client Import,"As an IBP, I want to receive confirmation that my upload was received so that I know the process started.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Client Import,"As an IBP, I want to understand that imports are processed manually so that expectations are clear.",MVP,,0.0,0,0,0,0.0,0,0,0,,0.0,0.0
IBP Dashboard,"As an IBP, I want to see my verification status so that I understand my account state.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
IBP Dashboard,"As an IBP, I want to see my next appointment so that I can prepare.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
IBP Dashboard,"As an IBP, I want quick access to key actions so that I can manage my business efficiently.",MVP,,1.0,0,1,2,1.0,0,1,2,,0.5,0.3
Client Discovery & IBP Profiles,"As a Client, I want to search for professionals so that I can find someone relevant.",MVP,,1.0,0,1,2,1.0,0,1,2,,0.5,0.3
Client Discovery & IBP Profiles,"As a Client, I want to filter professionals by specialty so that discovery is faster.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
Client Discovery & IBP Profiles,"As a Client, I want to view only verified professionals so that I can trust the platform.",MVP,,0.0,0,0,0,0.2,0,0,1,,0.0,0.0
Client Discovery & IBP Profiles,"As a Client, I want to view an IBP profile so that I can evaluate services before booking.",MVP,,2.0,1,2,3,1.0,0,1,2,,0.8,0.4
Client Service & Add-On Selection,"As a Client, I want to view full service details so that I understand what I am booking.",MVP,,0.0,0,0,0,0.0,0,0,0,,0.0,0.0
Client Service & Add-On Selection,"As a Client, I want required add-ons to be automatically applied so that the booking is valid.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Client Service & Add-On Selection,"As a Client, I want to select optional add-ons so that I can customize my appointment.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
Client Service & Add-On Selection,"As a Client, I want duration and price to update dynamically so that there are no surprises.",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
Client Service & Add-On Selection,"As a client, I want to reschedule my appointment without changing the service type, within the IBP’s available time slots",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
Client Booking & Waitlist-Ready Flow,"As a Client, I want to see only valid booking time slots so that my service fits the schedule.",MVP,,1.0,0,1,2,1.0,0,1,2,,0.5,0.3
Client Booking & Waitlist-Ready Flow,"As a Client, I want unavailable days to clearly show no availability so that I am not confused.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Client Booking & Waitlist-Ready Flow,"As a Client, I want to request a notification when no slots are available so that I can book later.",Post MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Client Booking & Waitlist-Ready Flow,"As a Client, I want to be notified when a slot opens so that I can quickly book it.",Post MVP,,2.0,1,2,3,1.0,0,1,2,,0.8,0.4
Client Booking Review & Payment Authorization,"As a Client, I want to review my booking details before confirming so that everything is correct.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Client Booking Review & Payment Authorization,"As a Client, I want to explicitly agree to the cancellation policy so that expectations are clear.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Client Booking Review & Payment Authorization,"As a Client, I want to add a payment method so that my appointment can be secured.",MVP,Stripe Checkout will be used,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
Client Booking Review & Payment Authorization,"As a Client, I want to see confirmation once my booking is complete so that I feel confident.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Client Cancellation,"As a Client, I want to review cancellation consequences before confirming so that I understand fees.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Client Cancellation,"As a Client, I want free cancellations to complete without charges when eligible.",MVP,,0.2,0,0,1,1.0,0,1,2,,0.3,0.1
Client Cancellation,"As a Client, I want to be warned before a late cancellation fee is applied so that I can decide.",MVP,,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
Client Dashboard,"As a Client, I want to view upcoming and past appointments so that I can manage my schedule.",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
Client Dashboard,"As a Client, I want to view recommended and purchased products so that I can revisit them.",MVP,,1.0,0,1,2,0.2,0,0,1,,0.3,0.1
Client Dashboard,"As a Client, I want to manage my payment methods so that future bookings are easy.",MVP,Stripe Billing Portal will be used,0.2,0,0,1,0.2,0,0,1,,0.1,0.0
Notifications,"As a user, I want to receive booking notifications so that I stay informed.",MVP,,0.0,0,0,0,1.0,0,1,2,,0.3,0.1
Notifications,"As a user, I want to receive rebooking notifications so that I stay informed about changes to my appointment",MVP,,0.0,0,0,0,0.2,0,0,1,,0.0,0.0
Notifications,"As a user, I want to receive cancellation notifications so that outcomes are clear.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Notifications,"As a user, I want to receive payment and authorization notifications so that charges are transparent.",MVP,,0.2,0,0,1,0.0,0,0,0,,0.0,0.0
Notifications,"As a user, I want the system to send me push notifications when they are enabled",Post MVP,,0.0,0,0,0,4.0,3,4,5,,1.0,0.5
Misc,Project Setup,MVP,,1.0,0,1,2,1.0,0,1,2,6,0.5,0.9
Misc,Project Deployment,MVP,,1.0,0,1,2,1.0,0,1,2,,0.5,0.3
Misc,Responsive Mobile UI/UX,MVP,,4.2,3,4,6,0.0,0,0,0,2,1.0,0.7