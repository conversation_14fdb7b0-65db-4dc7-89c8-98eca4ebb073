## **SECTION 1 — GLOBAL UX STRUCTURE**

This section defines the foundational UX structure that governs navigation, role separation, routing, and global interaction rules across the Easy Beauty MVP. All rules in this section apply consistently across IBP and Client experiences.

---

### **1.1 USER MODEL**

Easy Beauty uses a **unified user model**. Every signed-up user can book appointments (client capabilities), and any user can additionally create an IBP account to become a service provider.

#### **Base User (All Users)**

All users have client capabilities and can:

- Discover and view professional profiles

- Book appointments and select time slots

- Authorize payments (holds only)

- Cancel appointments

- View appointment history

- Manage account settings

#### **Independent Beauty Professionals (IBP)**

Any user can create an IBP account to become a service provider. IBPs additionally manage:

- Onboarding & professional verification

- Services and add-on rules

- Availability and time-off

- Cancellation and no-show policies

- Appointments (as provider)

- Client management (Concierge Import)

- Booking enforcement controls (authorization holds, policy acknowledgment)

- Business settings

---

### **1.2 GLOBAL NAVIGATION STRUCTURE**

Navigation is role-specific and fixed for <PERSON>. No dynamic or configurable navigation is permitted in this release.

#### **IBP Navigation Tabs (MVP)**

- Dashboard

- Appointments

- Services

- Availability

- Policies

- Clients

- Settings

#### **Client Navigation Tabs (MVP)**

- Home / Discovery

- Appointments

- Profile

Navigation bars are persistent and fixed to the bottom of the screen on mobile.

---

### **1.3 GLOBAL UI RULES**

These rules apply to **all screens, modals, and flows**.

#### **Buttons & CTAs**

- **Primary CTA** → Solid Black button

- **Secondary CTA** → Outlined button

- **Tertiary CTA** → Text-only action

- **Gold (\#C9A34A)** may be used for tertiary accents only and must never be used for primary or secondary CTAs

#### **Layout & Interaction**

- Sticky headers on all scrollable screens

- Pull-to-refresh enabled on list-based screens

- Minimum tap target size: **44px**

- Back navigation always returns to the previous logical state

- Loading states use minimal spinners with concise, neutral copy

#### **Typography**

- Headers: **Helvetica Neue**

- Body text: **Lato** or **Open Sans**

- Minimum body size: **14pt**

#### **Spacing & Visual Style**

- Clean, neutral backgrounds (white or very light gray)

- Light shadows on cards only

- No decorative clutter or ornamental UI elements

---

### **1.4 POST-LOGIN ROUTING**

Routing is determined immediately upon authentication.

- Default route → **Discovery Home** (all users start here)

- Users with an IBP account can switch to **IBP Dashboard** via navigation

Additional rules:

- IBP onboarding **must be completed** before full IBP dashboard access is granted

- Unverified IBPs must see a persistent **"Verification Pending"** banner across all IBP dashboard views

---

### **1.5 DEVICE & PLATFORM RULES**

The MVP is mobile-first but must adhere to the following platform constraints:

- iOS-native spacing and interaction conventions

- Android-safe margins (no edge-touchable controls)

- Layouts must be scalable to responsive web in future releases

---

## **SECTION 2 — IBP EXPERIENCE**

This section defines **every screen, behavior, and logic rule** required for the Independent Beauty Professional (IBP) MVP experience.

This section is **automation-first**, **commerce-free**, and **implementation-ready**.  
 It includes only features required to support predictable booking, enforcement, and operational control.

**Included in this section:**

- Onboarding & verification

- Service and add-on configuration

- Availability & time-off

- Cancellation and no-show enforcement

- Client import (Concierge Upload)

- IBP Dashboard

No product catalog, purchasing, or payouts are included in MVP.

---

### **2.1 IBP ONBOARDING FLOW**

#### **Screen IBP-OB-01 — Onboarding Intro**

**Purpose**  
 Introduce the IBP onboarding process and set expectations.

**Required UI**

- Title: _Set up your professional profile_

- Short benefit-oriented description

- Primary CTA: **Start Setup**

**Behavior / Logic**

- CTA routes to **Business Basics**

- No skip option in MVP

---

#### **Screen IBP-OB-02 — Business Basics**

**Purpose**  
 Collect core identity and business information.

**Fields**

- First name

- Last name

- Business name (optional)

- Phone number

- Email (locked if already provided)

- Zip code (auto-resolves city/state)

**Validation**

- Phone required

- Zip code required

**Behavior**

- Inline validation only

- CTA enabled only when required fields are valid

---

#### **Screen IBP-OB-03 — Specialties & Categories**

**Purpose**  
 Define the professional scope of services.

**UI**

- Multi-select grid

**Categories**

- Hair

- Nails

- Braids

- Barbering

- Skin

- Brows

- Lashes

- Makeup

**Rules**

- At least one specialty required

- Used later to constrain service categories

---

#### **Screen IBP-OB-04 — License Upload & Verification**

**Purpose**  
 Collect and verify professional credentials.

**Fields**

- License image upload

- License number

- State

- Expiration date

**CTA**

- Submit

**Behavior / Logic**

- IBP status set to `pending_verification`

- User proceeds to Onboarding Summary

- No dashboard editing access until verified

---

#### **Screen IBP-OB-05 — Onboarding Summary**

**Purpose**  
 Confirm submitted information and communicate verification status.

**UI**

- Summary card of submitted details

- Persistent banner: _Verification in progress_

- Primary CTA: **Go to Dashboard**

**Behavior**

- Dashboard loads in restricted mode until verification completes

---

### **2.2 IBP SERVICE & ADD-ON CONFIGURATION**

#### **Screen IBP-SRV-01 — Services List**

**Purpose**  
 Display all services offered by the IBP.

**UI**

- List of services showing:
  - Service name

  - Duration

  - Price

- Primary CTA: **Add Service**

---

#### **Screen IBP-SRV-02 — Create / Edit Service**

**Purpose**  
 Define a base service.

**Fields**

- Service name

- Duration (minutes)

- Price

- Category (from selected specialties)

**Rules**

- Duration required

- Price required

- No service stacking logic in MVP

---

#### **Screen IBP-SRV-03 — Add-On Rules**

**Purpose**  
 Allow IBPs to define add-ons and control booking behavior.

**Fields**

- Add-on name

- Duration

- Price

- Toggle: **Required** / **Optional**

**Behavior / Logic**

- Required add-ons are automatically applied to bookings

- Optional add-ons are client-selectable

- Total service duration \= Base service \+ Required add-ons

---

#### **Screen IBP-SRV-04 — Service Summary**

**Purpose**  
 Review combined service configuration before saving.

**UI**

- Base duration and price

- Required add-ons summary

- Optional add-ons summary

- Primary CTA: **Save Service**

---

### **2.3 IBP AVAILABILITY**

#### **Screen IBP-AVL-01 — Weekly Availability**

**Purpose**  
 Define recurring availability.

**UI**

- Day-of-week selectors

- Start time / end time per day

**Rules**

- At least one day required

- Overlapping times not allowed

---

#### **Screen IBP-AVL-02 — Time-Off Management**

**Purpose**  
 Override availability with one-off exceptions.

**UI**

- Time-off list

- Add Time-Off CTA

- Start / end date selectors

**Behavior**

- Time-off always overrides weekly availability

- Affected slots removed from client booking

### **2.3.3 IBP SERVICE-SPECIFIC DAY AVAILABILITY (MVP)**

This feature allows an IBP to restrict which **services** can be booked on which **days of the week** (e.g., “Color only on Tuesdays”). This prevents the need for duplicate profiles and supports service-aware scheduling without adding complex rules.

**Scope (MVP)**

- Day-of-week restrictions only

- No time-of-day restrictions per service

- No conditional logic chains

- Default behavior remains: services bookable on any day the IBP is available

---

### **Screen IBP-AVL-03 — Service Availability Rules**

**Purpose**  
 Allow the IBP to define optional service-level day restrictions so clients only see valid days/times for that service.

**Required UI Elements**

- Title: “Service Availability”

- Subtext: “Choose which days this service can be booked.”

- Service selector (if entered from Availability area)
  - List of services (name \+ duration)

  - Selecting a service opens its rule configuration

- Rule type toggle:
  - Option A (default): “Available on all working days”

  - Option B: “Only available on selected days”

- Day-of-week selector (visible only when Option B is selected):
  - Mon / Tue / Wed / Thu / Fri / Sat / Sun (checkboxes)

**Primary CTA**

- “Save”

**Secondary CTA**

- “Cancel”

---

### **Entry Points (MVP)**

**Entry Path A — From Service Setup**

- From Screen IBP-SRV-02 (Create/Edit Service)

- Add a row: “Service Availability”

- Tapping opens IBP-AVL-03 with the current service pre-selected

**Entry Path B — From Availability Tab**

- Add a row/card on IBP-AVL-01 (Weekly Availability) screen:
  - Label: “Service Availability Rules”

  - Subtext: “Limit specific services to specific days”

- Tapping opens IBP-AVL-03 where the IBP selects the service first

---

### **Behavior / Interaction Logic (MVP)**

**Default Rule**

- If no rule exists for a service:
  - That service is bookable on **any day** the IBP has Weekly Availability (IBP-AVL-01), excluding Time-Off (IBP-AVL-02)

**Rule Enforcement**

- If “Only available on selected days” is set:
  - The system restricts booking availability for that service to those selected weekdays only

  - Weekly Availability time windows still apply

  - Time-Off still overrides all availability

**Validation Rules**

- If Option B is selected (“Only available on selected days”):
  - At least one weekday must be selected

  - Save is disabled until at least one day is chosen

- If Option A is selected:
  - No weekday selection is stored

**Conflict Handling**

- If the IBP selects weekdays where Weekly Availability is not set:
  - This is allowed, but those days will yield **no bookable slots** until Weekly Availability is configured

  - Display a non-blocking note:
    - “Selected days require matching weekly availability to generate bookable times.”

---

### **Client-Facing Impact (MVP)**

This is invisible to Clients as a feature. Clients simply see:

- Only the valid dates/times that match:
  - Service-specific weekdays (if configured)

  - IBP weekly availability

  - Total service duration (including add-ons)

  - Time-Off exclusions

Clients must never see:

- An error stating “service not available on this day”  
   Instead, invalid days should appear as **no available times** for that service.

---

### **AI Readiness (System-Level Only)**

This flow generates structured signals used for future scheduling intelligence. No AI-driven behavior or UI is active in MVP.

**Logged Signals**

- Service weekday restrictions (service_id → allowed weekdays)

- Booking attempts for services by weekday

- Conversion rate by service weekday configuration

- Availability utilization density by weekday

---

### **Data Requirements (Implementation Notes)**

**Service Rule Model (minimum)**

- service_id

- allowed_weekdays (array of weekdays) OR null (meaning “all working days”)

- updated_at

**Evaluation Order**

1. Weekly Availability (IBP-AVL-01)

2. Time-Off override (IBP-AVL-02)

3. Service weekday restriction (IBP-AVL-03)

4. Duration fit logic (Client booking slot generation)

---

### **Exit Conditions**

**Save**

- Persists rule state for the selected service

- Returns to the previous screen (Service Edit or Availability)

**Cancel**

- No changes saved

- Returns to previous screen

---

### **2.4 IBP CANCELLATION & ENFORCEMENT**

#### **Screen IBP-CAN-01 — Policy Intro**

**Purpose**  
 Explain the importance of cancellation enforcement.

**Copy**

_Protect your time with a clear cancellation policy._

**CTA**

- Continue

---

#### **Screen IBP-CAN-02 — Cancellation Window**

**Purpose**  
 Define the free cancellation cutoff.

**Fields**

- Dropdown: 6h, 12h, 24h, 36h, 48h, 72h

**Logic**

- Defines the free vs late cancellation boundary

---

#### **Screen IBP-CAN-03 — Late Cancellation Fee**

**Purpose**  
 Define automatic penalties for late cancellations.

**Fields**

- Fee type: Flat or Percentage

- Fee amount

**Behavior**

- Fee automatically charged if cancellation occurs inside window

---

#### **Screen IBP-CAN-04 — No-Show Fee**

**Purpose**  
 Define penalties for no-shows.

**Fields**

- Fee type

- Fee amount

**Behavior**

- Fee is **not** auto-charged

- IBP must manually confirm no-show before charging

---

#### **Screen IBP-CAN-05 — Review Policy**

**Purpose**  
 Allow IBP to review the policy exactly as clients will see it.

**UI**

- View-only policy summary

- Free cancellation window

- Late cancellation fee

- No-show fee

- Informational note:

  _Clients must agree to this policy before booking._

**CTAs**

- Primary: **Save Policy**

- Secondary: **Back to Edit**

**Logic Synchronization**

- Authorization hold amount \= late cancellation fee

- No-show fee excluded from authorization hold

- Client must explicitly acknowledge policy before booking

**AI Readiness (System-Level Only)**  
Cancellation timing and enforcement outcomes are logged for future policy effectiveness analysis. No AI-driven behavior is active in MVP.

---

### **2.5 IBP CLIENT IMPORT (CONCIERGE)**

#### **Screen IBP-IMP-01 — Client Import Intro**

**Purpose**  
 Support IBPs transitioning from other platforms.

**Copy**

_Upload your client list and our team will import it for you._

**CTA**

- Upload File

---

#### **Screen IBP-IMP-02 — File Upload**

**Fields**

- File picker (CSV, XLSX)

- Email confirmation field

**UI Note**

- _Processing typically takes 24–72 hours._

---

#### **Screen IBP-IMP-03 — Submission Complete**

**Copy**

_Your client list has been received. We’ll notify you when the import is complete._

**Logic**

- Entire process is manual admin-side

- No automation in MVP

---

### **2.6 IBP DASHBOARD**

#### **Screen IBP-DASH-01 — Dashboard**

**Purpose**  
 Provide a centralized operational overview.

**UI Components**

- Verification status banner (if pending)

- Upcoming appointments

- Today’s schedule

- Quick actions:
  - Manage Services

  - Manage Availability

  - Manage Policies

**AI Readiness (System-Level Only)**  
 Appointment patterns and availability changes are logged for future scheduling intelligence.  
 No recommendations or automation are active in MVP.

---

## **SECTION 3 — CLIENT EXPERIENCE**

This section defines every screen, behavior, and logic rule required for the **Client MVP experience**.  
 It includes:

- Discovery

- IBP profile viewing

- Service & add-on selection

- Booking & time selection

- Payment authorization

- Cancellation

- Appointment management

- Notifications (event-driven)

All flows prioritize **commitment-based booking**, **policy enforcement**, and **low-friction scheduling**, while remaining **waitlist-ready** for future intelligence layers.

---

### **3.1 DISCOVERY & IBP PROFILE FLOW**

#### **Screen CLT-DSC-01 — Discovery Home**

**Purpose**  
 Allow Clients to discover and evaluate Independent Beauty Professionals.

**UI Elements**

- Search bar (name, service, specialty)

- Filter chips (specialties)

- List of verified IBPs:
  - Profile photo

  - Name

  - Primary specialties

  - City / state

  - Verification badge

**Behavior / Logic**

- Default sort: proximity or relevance

- Only verified IBPs appear

- Tapping an IBP → IBP Profile

---

#### **Screen CLT-DSC-02 — IBP Profile**

**Purpose**  
 Provide Clients with a complete view of the professional before booking.

**Sections**

- Header: photo, name, specialties

- About section (optional, IBP-provided)

- Services list

- Cancellation policy summary (collapsed)

- Reviews placeholder (non-functional in MVP)

**Primary CTA**

- Book

**Behavior / Logic**

- Selecting a service → Service Detail

---

### **3.2 SERVICE & ADD-ON SELECTION**

#### **Screen CLT-SRV-01 — Service Detail**

**Purpose**  
 Present service details and allow add-on selection.

**UI Elements**

- Service name

- Base duration

- Base price

- Required add-ons (auto-applied)

- Optional add-ons (multi-select)

- Dynamic total duration & price

- CTA: Select Time

**Behavior / Logic**

- Required add-ons cannot be deselected

- Optional add-ons update duration and price dynamically

- CTA enabled only when a valid configuration exists

**AI Readiness (System-Level Only)**  
Service and add-on selection behavior is logged for future recommendation and bundling intelligence. No AI-driven suggestions are active in MVP.

---

###

### **3.3 BOOKING & TIME SELECTION**

#### **Screen CLT-BKG-01 — Select Date & Time**

**Purpose**  
 Allow Clients to choose a time slot that fully accommodates the selected service duration.

**UI Elements**

- Calendar selector

- Time slot list (valid slots only)

**Behavior / Logic**

- Only slots that fit total duration are displayed

- Time-off and unavailable blocks are excluded

- If no valid slots:
  - Display empty-state message

  - Show “Notify me if a slot opens” (waitlist-ready)

**AI Readiness Note \+ Logic Note (add to CLT-BKG-01):** Time slots and valid days are filtered by IBP weekly availability, time-off, and any service-specific weekday restrictions configured by the IBP.

---

### **3.4 PAYMENT AUTHORIZATION & CONFIRMATION**

#### **Screen CLT-PAY-01 — Authorization Disclosure**

**Purpose**  
 Explain payment authorization requirements prior to booking.

**Copy**  
 “A temporary authorization equal to the cancellation fee will be placed on your card to secure this appointment.”

**UI Elements**

- Authorization amount breakdown

- CTA: Add Payment Method

---

#### **Screen CLT-PAY-02 — Payment Method Modal**

**Purpose**  
 Collect payment method for authorization hold.

**UI Elements**

- Payment provider drop-in (e.g., Stripe/Square)

- Legal authorization copy

- CTA: Confirm Booking

- Secondary CTA: Cancel

**Behavior / Logic**

- Authorization placed upon submission

- Failure shows inline error

- Success proceeds to confirmation

---

#### **Screen CLT-PAY-03 — Booking Confirmation**

**Purpose**  
 Confirm successful booking and communicate next steps.

**UI Elements**

- Appointment summary

- Free cancellation deadline

- CTA: View Appointment

- Secondary CTA: Cancel Appointment

---

### **3.5 CLIENT CANCELLATION FLOW**

**Logic Overview**

- Free cancellation outside IBP window

- Late cancellation fee applied automatically

- Authorization hold converts to charge only if late

- No-show fees are never auto-charged

---

#### **Screen CLT-CAN-01 — Cancel Appointment Modal**

**Purpose**  
 Allow Client to cancel with full policy visibility.

**UI Elements**

- Appointment summary

- Cancellation window status

- Fee implications (if applicable)

- CTA: Cancel Appointment

- Secondary CTA: Keep Appointment

---

#### **Screen CLT-CAN-02 — Free Cancellation Success**

**Purpose**  
 Confirm successful free cancellation.

**UI**

- Short confirmation message

---

#### **Screen CLT-CAN-03 — Late Cancellation Warning**

**Purpose**  
 Warn Client of fee before confirmation.

**Copy Example**  
 “This cancellation is within the IBP’s cancellation window. A fee of $\[amount\] will be applied.”

**CTA**

- Confirm Cancellation

---

#### **Screen CLT-CAN-04 — Late Cancellation Submitted**

**Purpose**  
 Confirm cancellation and applied fee.

**UI**

- Confirmation message

- Fee summary

**AI Readiness (System-Level Only)**  
 Cancellation timing and behavior is logged for future policy optimization and enforcement insights. No AI decisions are made in MVP.

---

### **3.6 CLIENT DASHBOARD**

#### **Screen CLT-DASH-01 — Client Dashboard**

**Purpose**  
 Provide Clients with a centralized view of appointments and account details.

**UI Elements**

- Upcoming appointments

- Past appointments

- Payment methods

- Profile settings

**Behavior / Logic**

- Tapping an appointment → Appointment detail

- No product or commerce elements in MVP

---

### **Section 3 Status**

- Scope-aligned

- Automation-first

- No commerce or catalog features

- Waitlist-ready without queue logic

## **SECTION 4 — NOTIFICATIONS UX**

Notifications in Easy Beauty are **event-driven, operational, and trust-building**.  
 They exist to confirm actions, reinforce policy enforcement, and reduce uncertainty—**not to market or upsell**.

### **Notification Principles**

- One clear message per notification

- Actionable and time-relevant

- Professional, calm, and concise

- No emojis, slang, or promotional language

---

### **4.1 DELIVERY CHANNELS (MVP)**

**Primary**

- Push notifications (highest priority)

**Secondary**

- Email fallback (when push is unavailable or disabled)

**Out of Scope (MVP)**

- SMS

- In-app inbox history

---

### **4.2 BOOKING NOTIFICATIONS**

**Triggered Events**

- Appointment confirmed

- Appointment reminder

- Appointment updated

**Copy Examples**

- “Your appointment is confirmed.”

- “Your appointment with \[IBP Name\] is coming up tomorrow.”

- “Your appointment details have been updated.”

**Behavior / Logic**

- Confirmation fires immediately after successful authorization

- Reminder fires 24 hours before appointment start time

- Updates fire only when date/time/service changes

**AI Readiness (System-Level Only)**  
 Notification engagement (open/dismiss) is logged for future timing optimization.  
 No adaptive notification timing is active in MVP.

---

### **4.3 CANCELLATION NOTIFICATIONS**

**Free Cancellation**

- “Your cancellation is complete. No fee was applied.”

**Late Cancellation**

- “Your cancellation is complete. A late cancellation fee of $\[amount\] was charged according to \[IBP Name\]’s policy.”

**IBP-Initiated Cancellation**

- “Your appointment with \[IBP Name\] has been canceled.”

**Behavior / Logic**

- Fee messaging must exactly match policy configuration

- Notification fires immediately after cancellation confirmation

---

### **4.4 PAYMENT & AUTHORIZATION NOTIFICATIONS**

**Triggered Events**

- Payment method saved

- Authorization hold placed

- Authorization released

- Authorization converted to charge

**Copy Examples**

- “Your payment method has been saved.”

- “A temporary authorization hold has been placed to secure your appointment.”

- “Your authorization hold has been released.”

- “Your authorization was converted to a charge due to a late cancellation.”

**Behavior / Logic**

- Authorization notifications must reflect real payment state

- No speculative or delayed messaging

---

### **4.5 WAITLIST-READY NOTIFICATIONS (DISPLAY-ONLY)**

**Status**

- UI-ready only

- No logic or queueing in MVP

**Trigger (Future)**

- Appointment slot becomes available due to cancellation

**Copy**

- “A new time opened up with \[IBP Name\] on \[date\]. Tap to book.”

**Behavior (Future-Ready)**

- Tapping notification routes to:
  - IBP Profile

  - Service pre-selected

  - Date pre-selected

- Slot must still be available at time of tap

**AI Readiness (System-Level Only)**  
 Waitlist interest and response behavior is logged for future ranking and prioritization logic.  
 No automated waitlist ordering exists in MVP.

---

### **4.6 NOTIFICATION GOVERNANCE RULES**

- Notifications must never contradict policy rules

- No notification should require interpretation

- All copy must be deterministic and system-truthful

- Duplicate notifications for the same event are not permitted

---

### **Section 4 Status**

- Fully aligned with Automation-First MVP

- No commerce, marketing, or promotional messaging

- AI-ready without AI-driven behavior

## **SECTION 5 — UI COMPONENTS & STANDARDS**

This section defines the **core UI components and interaction standards** used consistently across all IBP and Client MVP flows.  
 The goal is predictability, speed, and visual restraint.

All components defined here are **reusable system components**, not one-off designs.

---

### **5.1 TYPOGRAPHY**

**Font Families**

- Headers: **Helvetica Neue**

- Body: **Lato** or **Open Sans**

**Sizing Rules**

- Minimum body text size: **14pt**

- Headers must scale hierarchically (H1 → H3)

- Avoid excessive font-weight variation

**Tone**

- Clean

- Modern

- Professional

- No decorative fonts or stylized lettering

---

### **5.2 COLOR SYSTEM**

**Primary Colors**

- **Black** — primary CTAs, primary text

- **White** — backgrounds and content space

**Tertiary Brand Accent**

- **Gold (\#C9A34A)**

**Usage Rules**

- Gold may be used only for:
  - Tertiary accents

  - Dividers

  - Non-interactive highlights

- Gold must **never** be used for:
  - Primary CTAs

  - Critical actions

  - Error or warning states

---

### **5.3 CTA HIERARCHY**

**Primary CTA**

- Solid black button

- White text

- Highest visual priority

- One per screen whenever possible

**Secondary CTA**

- Outlined button

- Neutral border

- Used for alternative actions

**Tertiary CTA**

- Text-only

- Minimal styling

- No gold coloring

- Used for dismissive or optional actions

---

### **5.4 CARDS & LIST ITEMS**

**General Specifications**

- Rounded corners

- Consistent internal padding

- Optional soft shadow (very subtle)

- Clear visual separation

- No heavy borders or decoration

**Used For**

- Service cards

- Appointment cards

- Availability blocks

- IBP listing previews

**Behavior**

- Entire card tappable when appropriate

- Disabled states must be visually distinct

---

### **5.5 FORM FIELDS**

**Field Requirements**

- Clear label above input

- Neutral placeholder text

- Inline validation messages

- Required fields marked with “\*”

**States**

- Default

- Focus

- Error

- Disabled

**Behavior**

- Validation errors appear immediately after submission attempt

- Error copy must explain how to fix the issue

---

### **5.6 MODALS**

All modals must include:

- Title

- Supporting text (if needed)

- Primary CTA

- Secondary CTA

- Clear exit mechanism (X or Cancel)

**Used For**

- Appointment cancellation confirmation

- Payment authorization explanation

- Policy acknowledgment

- Waitlist “Notify Me” request

**Rules**

- Modals must not stack

- Modal actions must be reversible when possible

---

### **5.7 TABS & NAVIGATION BARS**

**Specifications**

- Fixed bottom navigation

- Active tab:
  - Bold label

  - Underlined in black

- Inactive tabs:
  - Neutral gray text

- Safe-area padding respected (iOS & Android)

**Behavior**

- Tab state must persist on return

- Navigation must never reset user context unexpectedly

---

### **5.8 SYSTEM FEEDBACK STATES**

**Loading**

- Minimal spinners only

- Short, neutral copy
  - Example: “Loading availability…”

**Empty States**

- Clear explanation

- Optional next action
  - Example: “No availability set yet. Add your first time block.”

**Success States**

- Brief confirmation

- Auto-dismiss where appropriate

**Error States**

- Inline

- Specific

- Actionable

## **SECTION 6 — COPY GUIDELINES**

Copy in Easy Beauty supports **clarity, confidence, and task completion**.  
 It should feel premium and human, without being casual or distracting.

All copy rules apply to **IBP and Client flows equally**.

---

### **6.1 GENERAL TONE**

Copy must be:

- Direct

- Warm

- Confident

- Minimal

- Professional

Copy must **never**:

- Use slang

- Use emojis

- Use humor

- Use exclamation marks

- Over-explain obvious actions

---

### **6.2 VOICE PRINCIPLES**

- Use short, declarative sentences

- Lead with the action, not the explanation

- Prefer clarity over personality

- Avoid marketing language inside operational flows

**Preferred**

- “Choose a time that works for you.”

- “Add a payment method to secure your appointment.”

- “Set your availability to let clients book time with you.”

**Avoid**

- “You’re all set\!”

- “Let’s get started\!”

- “No worries\!”

---

### **6.3 CONFIRMATION & FEEDBACK COPY**

**Confirmations**

- Must reflect actual system state

- Must avoid ambiguity

Examples:

- “Your appointment is confirmed.”

- “Your cancellation is complete.”

- “Your payment method has been saved.”

**Errors**

- Must explain what happened

- Must explain how to fix it

Example:

- “This time is no longer available. Please select a new slot.”

---

### **6.4 POLICY & FINANCIAL COPY**

Policy-related copy must be:

- Explicit

- Neutral

- Non-negotiable in tone

Examples:

- “Cancellations within 24 hours will incur a $20 fee.”

- “A temporary authorization will be placed on your card.”

**Rules**

- Never soften policy language

- Never imply discretion where none exists

- Never hide financial consequences

---

### **6.5 SYSTEM-GENERATED COPY**

Dynamic copy (dates, amounts, names) must:

- Match backend values exactly

- Update in real time

- Never be approximated or rounded visually

---

### **Section 6 Status**

- Copy rules finalized

- Consistent across all MVP flows

- Safe for automation and enforcement

## **SECTION 7 — ACCESSIBILITY & INTERACTION**

Accessibility and interaction standards in Easy Beauty ensure the product is **usable, predictable, and inclusive** across devices, abilities, and usage contexts.

All rules in this section are **mandatory** for MVP.

---

### **7.1 TAP TARGET & TOUCH STANDARDS**

**Minimum Tap Target**

- Minimum height: **44px**

- Adequate spacing between adjacent interactive elements

- No overlapping tap areas

**Rules**

- CTAs must never be too close to destructive actions

- Text-only actions must still meet tap-size requirements

---

### **7.2 COLOR & CONTRAST**

**Contrast Requirements**

- All text must meet **WCAG AA** contrast standards

- Black on white or white on black preferred

- Gold (\#C9A34A) must **never** be used for text

**States**

- Error states must use high-contrast color cues

- Disabled states must be visually distinct without reducing readability

---

### **7.3 ERROR STATES & RECOVERY**

**Error Presentation**

- Displayed inline, close to the source

- Clear explanation of the issue

- Actionable guidance on resolution

**Rules**

- Errors must never blame the user

- Errors must not require page reloads to resolve

- Errors must not block navigation unless data loss would occur

---

### **7.4 NAVIGATION BEHAVIOR**

**Back Navigation**

- Always returns to the previous logical step

- Never resets completed data unintentionally

**Flow Integrity**

- No dead ends

- All modals must have a clear exit

- System interruptions must preserve user state

---

### **7.5 LOADING & STATE FEEDBACK**

**Loading States**

- Use minimal spinners only

- Include short explanatory text where delay exceeds 1 second

Example:

- “Loading available times…”

**State Preservation**

- Data entered must persist across temporary interruptions

- Navigation away and back must not reset progress unintentionally

---

### **7.6 KEYBOARD & SYSTEM SUPPORT (FUTURE-READY)**

While MVP is mobile-first:

- Inputs must support system keyboards

- Focus order must be logical

- UI must not rely on gesture-only interactions

This ensures smooth expansion to responsive web in future versions.

---

### **Section 7 Status**

- Accessibility standards finalized

- Interaction rules locked for MVP

- Fully compliant with future platform expansion

## **SECTION 8 — FUTURE-READY HOOKS**

This section defines **UI and data-capture hooks** that prepare Easy Beauty for future intelligence features without introducing automation, decisioning, or behavioral changes in the MVP.

All items in this section are **non-functional placeholders** or **system-level logging only**.

---

### **8.1 WAITLIST-READY HOOKS**

**Purpose**  
 Prepare the booking experience for future waitlist ranking and allocation without implementing queue logic in MVP.

**Where It Appears**

- Select Date & Time screen

- Displayed only when no valid slots exist for the selected service duration

**UI Element**

- Text-only tertiary CTA:
  - “Notify me if a slot opens”

**MVP Behavior**

- Captures interest only

- Triggers a notification when a matching slot opens

- No reservation

- No prioritization

- No guarantee of availability

**AI Readiness (System-Level Only)**  
 Client interest, response timing, and conversion behavior are logged for future waitlist ranking models.  
 No automated ordering or allocation logic is active in MVP.

---

### **8.2 SMART RESCHEDULING (FUTURE)**

**Intent**  
 Enable future proactive rescheduling suggestions based on cancellations, availability shifts, and demand patterns.

**MVP Status**

- No UI

- No logic

- No notifications

**AI Readiness (System-Level Only)**  
 Cancellation timing and reschedule behavior are logged to support future rescheduling intelligence.

---

### **8.3 PREDICTIVE AVAILABILITY (FUTURE)**

**Intent**  
 Support future availability forecasting for IBPs (e.g., “high demand days,” “likely gaps”).

**MVP Status**

- Availability is fully manual

- No recommendations shown

- No predictions surfaced

**AI Readiness (System-Level Only)**  
 Availability patterns, booking density, and gap frequency are captured for future predictive models.

---

### **8.4 AI BOOKING RECOMMENDATIONS (FUTURE)**

**Intent**  
 Enable future recommendations such as:

- Suggested times

- Suggested add-ons

- Suggested booking windows

**MVP Status**

- No recommendations

- No ranking

- No nudging

**AI Readiness (System-Level Only)**  
 Service selection paths, add-on choices, and booking completion rates are logged for future recommendation engines.

---

### **8.5 FEATURE FLAGS & EXPERIMENTATION (INTERNAL)**

**Purpose**  
 Allow Easy Beauty to safely test future intelligence features without redesigning UX or breaking flows.

**MVP Scope**

- Internal-only flags

- No user-facing toggles

- No experimental UI visible to users

**Examples (Future)**

- Alternate waitlist messaging

- Different reminder timing

- Scheduling logic variants

---

### **Section 8 Status**

- Explicitly non-functional in MVP

- Signals-only, no automation

- Safe for future AI expansion

- Zero impact on current scope or timeline
