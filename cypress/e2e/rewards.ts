import {
  When,
  Then,
  DataTable,
  Before,
} from "@badeball/cypress-cucumber-preprocessor";

import { APIWorkspaceAdapter } from "../support/commands";

import "../support/common";

Before(() => {
  const workspaceName = `Workspace ${Math.round(
    Math.random() * Number.MAX_SAFE_INTEGER
  )}`;

  cy.then(() => workspaceName).as("workspaceName");

  cy.getWorkspaceUrl(workspaceName)
    .then((workspaceUrl) => new APIWorkspaceAdapter(workspaceUrl))
    .as("workspace");

  cy.get<APIWorkspaceAdapter>("@workspace")
    .then((workspace) => workspace.getMembers(10))
    .then(() => cy.visit("/"))
    .then(() => cy.login({ workspaceName }))
    .then(() => cy.passOnboarding(workspaceName));
});

When("I use the {string} template", (templateName: string) => {
  cy.get(`[data-test="template-card-${templateName}"]`).within(() => {
    cy.contains("Use").click();
  });
});

When("I fill in the reward form with:", (dataTable: DataTable) => {
  cy.get('[data-test="reward-form"]').within(() => {
    dataTable.rows().forEach(([field, value]) => {
      if (field.toLowerCase() === "description") {
        cy.get(`textarea[name="${field.toLowerCase()}"]`).clear().type(value);
      } else {
        cy.get(`input[name="${field.toLowerCase()}"]`).clear().type(value);
      }
    });
  });
});

When("I save the reward", () => {
  cy.get('button[type="submit"][form="AddRewardForm"]').click();
});

Then(
  "I should see the {string} reward with {string} points",
  (rewardName: string, points: string) => {
    cy.get(`[data-test="reward-card-${rewardName}"]`).within(() => {
      cy.contains(points);
    });
  }
);

When("I click the Add Reward button", () => {
  cy.get('[data-test="add-reward"]').click();
});

When(
  "I change the points for the {string} activity to {string}",
  (activityName: string, points: string) => {
    cy.get(`[data-test="activity-field-${activityName}"]`).within(() => {
      cy.get("input").clear().type(points).blur();
    });
  }
);

Then(
  "the points for the {string} activity should be {string}",
  (activityName: string, points: string) => {
    cy.get(`[data-test="activity-field-${activityName}"]`).within(() => {
      cy.get("input").should("have.value", points);
    });
  }
);
