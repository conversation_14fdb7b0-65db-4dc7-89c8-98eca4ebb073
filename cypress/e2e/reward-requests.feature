Feature: Rewards requests
  Background:
    Given I am on the "rewards/requests" page

  Scenario: Approving a reward request
    When I approve the "Coffee Mug" reward request
    Then the "Coffee Mug" reward request should be approved
    And the "T-Shirt" reward request should be rejected

  Scenario: Rejecting a reward request
    When I reject the "T-Shirt" reward request with reason "Test rejection"
    Then the "T-Shirt" reward request should be rejected

  Scenario: Viewing the leaderboard
    When I approve the "Coffee Mug" reward request
    And I am on the "rewards/leaderboard" page
    And I select the "SPENDERS" tab

    Then I should see "primary.owner" in the leaderboard

  Scenario: Manually updationg a coins balance
    When I am on the "rewards/coins" page
    And I update the coins balance for "primary.owner" to "500"
    Then I should see "primary.owner" with "500" coins in the members list
    When I refresh the page
    Then I should see "primary.owner" with "500" coins in the members list
