import { When, Then, Before } from "@badeball/cypress-cucumber-preprocessor";

import {
  APIWorkspaceAdapter,
  RewardRequestStatus,
  Workspace,
} from "../support/commands";

import "../support/common";

Before(() => {
  const workspaceName = `Workspace ${Math.round(
    Math.random() * Number.MAX_SAFE_INTEGER
  )}`;

  cy.then(() => workspaceName).as("workspaceName");

  cy.getWorkspaceUrl(workspaceName)
    .then((workspaceUrl) => new APIWorkspaceAdapter(workspaceUrl))
    .as("workspace");

  cy.get<APIWorkspaceAdapter>("@workspace")
    .then((workspace) => workspace.getMembers(10))
    .then(() => cy.visit("/"))
    .then(() => cy.login({ workspaceName }))
    .then(() => cy.passOnboarding(workspaceName))
    .then(() => cy.updateMemberCoinBalance(workspaceName, "U1", 100))
    .then(() =>
      cy
        .addRewards(workspaceName, [
          { name: "Coffee Mug", points: 100 },
          { name: "T-Shirt", points: 250 },
        ])
        .then((workspace: Workspace) =>
          Promise.all([
            cy.addRewardRequests(workspaceName, [
              {
                requesterId: "U1",
                rewardId: workspace.rewardsSettings.rewards[0].id,
                status: RewardRequestStatus.Pending,
              },
            ]),
            cy.addRewardRequests(workspaceName, [
              {
                requesterId: "U1",
                rewardId: workspace.rewardsSettings.rewards[1].id,
                status: RewardRequestStatus.Pending,
              },
            ]),
          ])
        )
    );
});

When("I approve the {string} reward request", (rewardName: string) => {
  cy.get('[data-test="requests-table"]')
    .find('[data-test="reward-name"]')
    .contains(rewardName)
    .parents("tr")
    .find('[data-test^="approve-request-button-"]')
    .click();
});

Then("the {string} reward request should be approved", (rewardName: string) => {
  cy.get('[data-test="requests-table"]')
    .find('[data-test="reward-name"]')
    .contains(rewardName)
    .parents("tr")
    .find('[data-test^="request-status-"]')
    .should("contain.text", "approved");
});

When(
  "I reject the {string} reward request with reason {string}",
  (rewardName: string, reason: string) => {
    cy.get('[data-test="requests-table"]')
      .find('[data-test="reward-name"]')
      .contains(rewardName)
      .parents("tr")
      .find('[data-test^="reject-request-button-"]')
      .click();

    cy.get('[data-test="reject-request-modal"]').within(() => {
      cy.get("textarea").type(reason);
      cy.get('button[type="submit"]').click();
    });
  }
);

Then("the {string} reward request should be rejected", (rewardName: string) => {
  cy.get('[data-test="requests-table"]')
    .find('[data-test="reward-name"]')
    .contains(rewardName)
    .parents("tr")
    .find('[data-test^="request-status-"]')
    .should("contain.text", "rejected");
});

When("I select the {string} tab", (tabName: "EARNERS" | "SPENDERS") => {
  cy.get(`[data-test="leaderboard-tab-${tabName}"]`).click();
});

Then("I should see {string} in the leaderboard", (name: string) => {
  cy.get('[data-test="rewards-leaderboard"]').contains(name);
});

When(
  "I update the coins balance for {string} to {string}",
  (name: string, coins: string) => {
    cy.contains(".dsg-row", name).within(() => {
      cy.get(".dsg-cell--balance")
        .trigger("mousedown")
        .find(".dsg-input")
        .focus()
        // https://github.com/cypress-io/cypress/issues/3817#issuecomment-774517631
        .type("text")
        .clear()
        .type(coins)
        .type("{enter}");
    });
  }
);

When(
  "I should see {string} with {string} coins in the members list",
  (name: string, coins: string) => {
    cy.contains(".dsg-row", name).within(() => {
      cy.get(".dsg-cell--balance").contains(coins);
    });
  }
);
