Feature: Rewards settings
  Background:
    Given I am on the "rewards" page

  Scenario: Creating a reward from a template
    When I am on the "settings/rewards" page
    And I use the "Productivity Book" template
    And I fill in the reward form with:
      | Field       | Value                     |
      | Name        | Test Reward from Template |
      | Description | A test description        |
      | Points      | 123                       |
    And I save the reward
    Then I should see the "Test Reward from Template" reward with "123" points
    When I refresh the page
    Then I should see the "Test Reward from Template" reward with "123" points

  Scenario: Creating a reward from scratch
    When I am on the "settings/rewards" page
    And I click the Add Reward button
    And I fill in the reward form with:
      | Field       | Value                    |
      | Name        | Test Reward from Scratch |
      | Description | Another test description |
      | Points      | 456                      |
    And I save the reward
    Then I should see the "Test Reward from Scratch" reward with "456" points
    When I refresh the page
    Then I should see the "Test Reward from Scratch" reward with "456" points

  Sc<PERSON><PERSON>: Change points for activities
    When I am on the "settings/rewards" page
    And I change the points for the "KUDOS" activity to "10"
    And I refresh the page
    Then the points for the "KUDOS" activity should be "10"
