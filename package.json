{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "private": true, "author": "", "license": "ISC", "workspaces": ["*"], "scripts": {"start": "npm run dev --workspace=@astrala/site", "build": "npm run build --workspace=@astrala/site", "dev:cron": "npm run dev --workspace=@astrala/cron", "typecheck": "tsc -p tsconfig.json --noEmit && npm run typecheck --workspaces", "lint": "npm run lint --workspaces", "prepare": "husky install"}, "devDependencies": {"@pulumi/azure-native": "^2.88.0", "@pulumi/docker": "^4.6.1", "@pulumi/pulumi": "^3.152.0", "@types/eslint": "^8.56.10", "dotenv": "^16.4.5", "dotenv-cli": "^7.4.2", "eslint": "^8.57.1", "eslint-kit": "^11.22.0", "husky": "^8.0.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "supabase": "^2.33.9", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}