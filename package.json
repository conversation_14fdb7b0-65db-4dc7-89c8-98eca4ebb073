{"name": "scopezilla", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "next build", "generate:api": "npx orval --config ./orval.config.ts", "lint:check": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 0", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "prepare": "husky", "start": "concurrently --raw 'next dev' 'docker compose up --build'", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "engines": {"node": "24.2.0", "npm": "11.3.0"}, "devDependencies": {"@dotenvx/dotenvx": "^1.51.1", "@types/node": "24.10.0", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.2", "concurrently": "^9.2.1", "eslint-kit": "^11.39.0", "husky": "^9.1.7", "lint-staged": "^16.2.6", "orval": "^7.16.0", "prettier": "^3.6.2", "supabase": "^2.54.11", "typescript": "^5.9.3"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "next": "^16.0.1"}}