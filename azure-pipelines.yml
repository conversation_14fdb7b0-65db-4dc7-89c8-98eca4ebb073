pool: 'Azure Cloud'
jobs:
  - job: Lint
    dependsOn:
      - Development
    container: 'node:20.18.0'
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/development')
    steps:
      - script: npm i
        displayName: 'Install dependencies'
      - script: npm run lint
        displayName: 'Run lint'
  - job: Development
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/development')
    variables:
      - group: global
      - group: development
    steps:
      - template: deploy-steps.yml
      - script: |
          docker rmi -f $(docker images -aq -f since=node:20.18.0)
          docker system prune -f
        displayName: 'Clean Docker cache'
      - script: |
          cd astrala-site && \
          npx supabase link --project-ref $SUPABASE_PROJECT_ID && \
          npx supabase db push --password $SUPABASE_DB_PASSWORD && \
          cd ..
        displayName: 'Migrate database'
        env:
          SUPABASE_DB_PASSWORD: $(SUPABASE_DB_PASSWORD)
          SUPABASE_PROJECT_ID: $(SUPABASE_PROJECT_ID)
      - task: AzureCLI@2
        displayName: 'Deploy'
        inputs:
          connectedServiceNameARM: 'Astrala (dev)'
          scriptType: bash
          scriptLocation: inlineScript
          addSpnToEnvironment: true
          inlineScript: |
            export ARM_CLIENT_ID=$servicePrincipalId
            export ARM_CLIENT_SECRET=$servicePrincipalKey
            export ARM_TENANT_ID=$tenantId
            export ARM_SUBSCRIPTION_ID=$(az account show --query id -o tsv)
            pulumi stack select astrala/development
            pulumi up --yes
        env:
          AZURE_OPENAI_API_KEY: $(AZURE_OPENAI_API_KEY)
          AZURE_OPENAI_ENDPOINT: $(AZURE_OPENAI_ENDPOINT)
          NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY: $(STRIPE_PRICE_STARTER_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL: $(STRIPE_PRICE_STARTER_ANNUAL)
          NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY: $(STRIPE_PRICE_PRO_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL: $(STRIPE_PRICE_PRO_ANNUAL)
          NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY: $(STRIPE_PRICE_TEAM_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL: $(STRIPE_PRICE_TEAM_ANNUAL)
          NEXT_PUBLIC_SUPABASE_ANON_KEY: $(SUPABASE_ANON_KEY)
          NEXT_PUBLIC_SUPABASE_URL: $(SUPABASE_URL)
          PARSING_API_TOKEN: $(PARSING_API_TOKEN)
          PARSING_SENTRY_URL: $(PARSING_SENTRY_URL)
          POSTMARK_SERVER_API: $(POSTMARK_SERVER_API)
          PULUMI_ACCESS_TOKEN: $(PULUMI_ACCESS_TOKEN)
          STRIPE_SECRET_KEY: $(STRIPE_SECRET_KEY)
          STRIPE_WEBHOOK_SECRET: $(STRIPE_WEBHOOK_SECRET)
          SUPABASE_DB_PASSWORD: $(SUPABASE_DB_PASSWORD)
          SUPABASE_PROJECT_ID: $(SUPABASE_PROJECT_ID)
          SUPABASE_SERVICE_KEY: $(SUPABASE_SERVICE_KEY)
          WORKER_SENTRY_URL: $(WORKER_SENTRY_URL)
          SITE_URL: $(SITE_URL)
  - job: Staging
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/staging')
    variables:
      - group: global
      - group: staging
    steps:
      - template: deploy-steps.yml
      - script: |
          docker rmi -f $(docker images -aq -f since=node:20.18.0)
          docker system prune -f
        displayName: 'Clean Docker cache'
      - script: |
          cd astrala-site && \
          npx supabase link --project-ref $SUPABASE_PROJECT_ID && \
          npx supabase db push --password $SUPABASE_DB_PASSWORD && \
          cd ..
        displayName: 'Migrate database'
        env:
          SUPABASE_DB_PASSWORD: $(SUPABASE_DB_PASSWORD)
          SUPABASE_PROJECT_ID: $(SUPABASE_PROJECT_ID)
      - task: AzureCLI@2
        displayName: 'Deploy'
        inputs:
          connectedServiceNameARM: 'Astrala (staging)'
          scriptType: bash
          scriptLocation: inlineScript
          addSpnToEnvironment: true
          inlineScript: |
            export ARM_CLIENT_ID=$servicePrincipalId
            export ARM_CLIENT_SECRET=$servicePrincipalKey
            export ARM_TENANT_ID=$tenantId
            export ARM_SUBSCRIPTION_ID=$(az account show --query id -o tsv)
            pulumi stack select astrala/staging
            pulumi up --yes
        env:
          AZURE_OPENAI_API_KEY: $(AZURE_OPENAI_API_KEY)
          AZURE_OPENAI_ENDPOINT: $(AZURE_OPENAI_ENDPOINT)
          NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY: $(STRIPE_PRICE_STARTER_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL: $(STRIPE_PRICE_STARTER_ANNUAL)
          NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY: $(STRIPE_PRICE_PRO_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL: $(STRIPE_PRICE_PRO_ANNUAL)
          NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY: $(STRIPE_PRICE_TEAM_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL: $(STRIPE_PRICE_TEAM_ANNUAL)
          NEXT_PUBLIC_SUPABASE_ANON_KEY: $(SUPABASE_ANON_KEY)
          NEXT_PUBLIC_SUPABASE_URL: $(SUPABASE_URL)
          PARSING_API_TOKEN: $(PARSING_API_TOKEN)
          PARSING_SENTRY_URL: $(PARSING_SENTRY_URL)
          POSTMARK_SERVER_API: $(POSTMARK_SERVER_API)
          PULUMI_ACCESS_TOKEN: $(PULUMI_ACCESS_TOKEN)
          STRIPE_SECRET_KEY: $(STRIPE_SECRET_KEY)
          STRIPE_WEBHOOK_SECRET: $(STRIPE_WEBHOOK_SECRET)
          SUPABASE_DB_PASSWORD: $(SUPABASE_DB_PASSWORD)
          SUPABASE_PROJECT_ID: $(SUPABASE_PROJECT_ID)
          SUPABASE_SERVICE_KEY: $(SUPABASE_SERVICE_KEY)
          WORKER_SENTRY_URL: $(WORKER_SENTRY_URL)
          SITE_URL: $(SITE_URL)
  - job: Production
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')
    variables:
      - group: global
      - group: production
    steps:
      - template: deploy-steps.yml
      - script: |
          docker rmi -f $(docker images -aq -f since=node:20.18.0)
          docker system prune -f
        displayName: 'Clean Docker cache'
      - script: |
          cd astrala-site && \
          npx supabase link --project-ref $SUPABASE_PROJECT_ID && \
          npx supabase db push --password $SUPABASE_DB_PASSWORD && \
          cd ..
        displayName: 'Migrate database'
        env:
          SUPABASE_DB_PASSWORD: $(SUPABASE_DB_PASSWORD)
          SUPABASE_PROJECT_ID: $(SUPABASE_PROJECT_ID)
      - task: AzureCLI@2
        displayName: 'Deploy'
        inputs:
          connectedServiceNameARM: 'Astrala (production)'
          scriptType: bash
          scriptLocation: inlineScript
          addSpnToEnvironment: true
          inlineScript: |
            export ARM_CLIENT_ID=$servicePrincipalId
            export ARM_CLIENT_SECRET=$servicePrincipalKey
            export ARM_TENANT_ID=$tenantId
            export ARM_SUBSCRIPTION_ID=$(az account show --query id -o tsv)
            pulumi stack select astrala/production
            pulumi up --yes
        env:
          AZURE_OPENAI_API_KEY: $(AZURE_OPENAI_API_KEY)
          AZURE_OPENAI_ENDPOINT: $(AZURE_OPENAI_ENDPOINT)
          NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY: $(STRIPE_PRICE_STARTER_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL: $(STRIPE_PRICE_STARTER_ANNUAL)
          NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY: $(STRIPE_PRICE_PRO_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL: $(STRIPE_PRICE_PRO_ANNUAL)
          NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY: $(STRIPE_PRICE_TEAM_MONTHLY)
          NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL: $(STRIPE_PRICE_TEAM_ANNUAL)
          NEXT_PUBLIC_SUPABASE_ANON_KEY: $(SUPABASE_ANON_KEY)
          NEXT_PUBLIC_SUPABASE_URL: $(SUPABASE_URL)
          PARSING_API_TOKEN: $(PARSING_API_TOKEN)
          PARSING_SENTRY_URL: $(PARSING_SENTRY_URL)
          POSTMARK_SERVER_API: $(POSTMARK_SERVER_API)
          PULUMI_ACCESS_TOKEN: $(PULUMI_ACCESS_TOKEN)
          STRIPE_SECRET_KEY: $(STRIPE_SECRET_KEY)
          STRIPE_WEBHOOK_SECRET: $(STRIPE_WEBHOOK_SECRET)
          SUPABASE_DB_PASSWORD: $(SUPABASE_DB_PASSWORD)
          SUPABASE_PROJECT_ID: $(SUPABASE_PROJECT_ID)
          SUPABASE_SERVICE_KEY: $(SUPABASE_SERVICE_KEY)
          WORKER_SENTRY_URL: $(WORKER_SENTRY_URL)
          SITE_URL: $(SITE_URL)
trigger:
  branches:
    include:
      - development
      - staging
      - main
