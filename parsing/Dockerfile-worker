FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

WORKDIR /app

RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libpq-dev \
    curl \
    build-essential \
    && pip install pipenv \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN pip install pipenv
COPY Pipfile Pipfile.lock ./
RUN pipenv install --deploy --system

COPY entrypoint-worker.sh /usr/local/bin/entrypoint.sh

RUN #rm -f .secrets.toml

RUN chmod +x /usr/local/bin/entrypoint.sh

COPY . .

ENV ENV_FOR_DYNACONF="test"

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
