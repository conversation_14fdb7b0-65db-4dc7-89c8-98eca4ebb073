from datetime import date, datetime, timezone
from typing import Optional
from uuid import uuid4

import pytest
import sqlalchemy as sa

import models as m
from matching.query_helper import SqlQueryHelper
from matching.similarity import get_raw_scores
from worker import find_new_matches_


TAGS = tuple(f"tag{i + 1}" for i in range(15))


# Helper Functions
async def create_job_seeker(
    dbsession,
    driving_license_path,
    job_title="Senior Python Developer",
    country="GB",
    city="London",
    salary_min=1000,
    salary_max=1500,
    resume_path: Optional[str] = "not_empty_path",
):
    job_seeker_id = await dbsession.scalar(
        sa.insert(m.JobSeeker)
        .values(
            {
                m.JobSeeker.user_id: str(uuid4()),
                m.JobSeeker.has_driving_license: driving_license_path is not None,
                m.JobSeeker.job_title: job_title,
                m.JobSeeker.country: country,
                m.JobSeeker.city: city,
                m.JobSeeker.resume_path: resume_path,
                m.JobSeeker.updated_at: datetime.now(tz=timezone.utc),
                m.JobSeeker.salary_min: salary_min,
                m.JobSeeker.salary_max: salary_max,
            }
        )
        .returning(m.JobSeeker.id)
    )
    return job_seeker_id


async def create_education(dbsession, job_seeker_id, degree, discipline):
    await dbsession.execute(
        sa.insert(m.JobSeekerEducation).values(
            {
                m.JobSeekerEducation.job_seeker_id: job_seeker_id,
                m.JobSeekerEducation.type: degree,
                m.JobSeekerEducation.discipline: discipline,
            }
        )
    )


async def create_company(dbsession, name="Test Company"):
    company_id = await dbsession.scalar(
        sa.insert(m.Company)
        .values(
            {
                m.Company.name: name,
                m.Company.created_at: datetime.now(tz=timezone.utc),
                m.Company.updated_at: datetime.now(tz=timezone.utc),
            }
        )
        .returning(m.Company.id)
    )
    return company_id


async def create_work_experience(dbsession, job_seeker_id, job_title, company_name="Test Company", end_date=date(year=2010, month=1, day=1)):
    await dbsession.execute(
        sa.insert(m.JobSeekerWorkExperience).values(
            {
                m.JobSeekerWorkExperience.job_seeker_id: job_seeker_id,
                m.JobSeekerWorkExperience.job_title: job_title,
                m.JobSeekerWorkExperience.company_name: company_name,
                m.JobSeekerWorkExperience.start_date: date(year=2000, month=1, day=1),
                m.JobSeekerWorkExperience.end_date: end_date,
            }
        )
    )


async def create_skills(dbsession, skills):
    skill_ids = []
    for skill in skills:
        skill_ids.append(
            await dbsession.scalar(
                sa.insert(m.Skill).values({m.Skill.name: skill}).returning(m.Skill.id)
            )
        )
    return skill_ids


async def link_skills_to_job_seeker(dbsession, job_seeker_id, skill_ids):
    await dbsession.execute(
        sa.insert(m.JobSeekerSkill).values(
            [
                {
                    m.JobSeekerSkill.skill_id: skill_id,
                    m.JobSeekerSkill.job_seeker_id: job_seeker_id,
                }
                for skill_id in skill_ids
            ]
        )
    )


async def install_answer_tags(dbsession, tags=TAGS):
    for i in range(3):
        await dbsession.execute(
            sa.insert(m.JobSeekerAnswerTag).values(
                [
                    {
                        m.JobSeekerAnswerTag.question_id: i + 1,
                        m.JobSeekerAnswerTag.value: j + 1,
                        m.JobSeekerAnswerTag.tag: tags[5 * i + j],
                    }
                    for j in range(5)
                ]
            )
        )
        await dbsession.execute(
            sa.insert(m.VacancyAnswerTag).values(
                [
                    {
                        m.VacancyAnswerTag.question_id: i + 1,
                        m.VacancyAnswerTag.value: j + 1,
                        m.VacancyAnswerTag.tag: tags[5 * i + j],
                    }
                    for j in range(5)
                ]
            )
        )


async def create_vacancy(
    dbsession,
    title,
    company_id=None,
    employer_id=12345,
    education_degree="Bachelor's",
    education_discipline="Computer science",
    country="GB",
    city="London",
    salary_min=1000,
    salary_max=1500,
    experience_years_from=3,
    status="OPENED",
):
    # Create a default company if none provided
    if company_id is None:
        company_id = await create_company(dbsession, "Default Test Company")

    vacancy_id = await dbsession.scalar(
        sa.insert(m.Vacancy)
        .values(
            {
                m.Vacancy.title: title,
                m.Vacancy.company_id: company_id,
                m.Vacancy.employer_id: employer_id,
                m.Vacancy.driving_license: True,
                m.Vacancy.education_degree: education_degree,
                m.Vacancy.education_discipline: education_discipline,
                m.Vacancy.country: country,
                m.Vacancy.city: city,
                m.Vacancy.salary_min: salary_min,
                m.Vacancy.salary_max: salary_max,
                m.Vacancy.experience_years_from: experience_years_from,
                m.Vacancy.status: status,
            }
        )
        .returning(m.Vacancy.id)
    )
    return vacancy_id


async def link_skills_to_vacancy(dbsession, vacancy_id, skill_ids):
    await dbsession.execute(
        sa.insert(m.VacancySkill).values(
            [
                {
                    m.VacancySkill.skill_id: skill_id,
                    m.VacancySkill.vacancy_id: vacancy_id,
                }
                for skill_id in skill_ids
            ]
        )
    )


async def calculate_test(dbsession):
    qh = SqlQueryHelper(dbsession)
    pairs, vacancies, job_seekers = await qh.get_uncalculated_pairs()
    job_seeker_vacancy_scores = get_raw_scores(vacancies, job_seekers, pairs)
    await find_new_matches_(
        dbsession, vacancies, job_seekers, job_seeker_vacancy_scores
    )


async def test_ideal_match(dbsession):
    await install_answer_tags(dbsession)
    job_seeker_id = await create_job_seeker(
        dbsession, "some-not-empty-path", resume_path=None
    )
    await create_work_experience(dbsession, job_seeker_id, "Senior Python Developer")
    for degree, discipline in [
        ("School", None),
        ("Bachelor's", "Computer science"),
        ("Master's", "Cybersecurity"),
    ]:
        await create_education(dbsession, job_seeker_id, degree, discipline)

    skills = ["Python", "SqlAlchemy", "Alembic", "Postgreql", "AWS EC2", "AWS S3"]
    skill_ids = await create_skills(dbsession, skills)
    await link_skills_to_job_seeker(dbsession, job_seeker_id, skill_ids)

    vacancy_id = await create_vacancy(
        dbsession, "Senior Python Developer", status="CLOSED"
    )
    await link_skills_to_vacancy(dbsession, vacancy_id, skill_ids)

    await dbsession.execute(
        sa.insert(m.JobSeekerQuestionnaireAnswer).values(
            [
                {
                    m.JobSeekerQuestionnaireAnswer.question_id: 1,
                    m.JobSeekerQuestionnaireAnswer.value: 1,
                    m.JobSeekerQuestionnaireAnswer.job_seeker_id: job_seeker_id,
                },
                {
                    m.JobSeekerQuestionnaireAnswer.question_id: 2,
                    m.JobSeekerQuestionnaireAnswer.value: 1,
                    m.JobSeekerQuestionnaireAnswer.job_seeker_id: job_seeker_id,
                },
                {
                    m.JobSeekerQuestionnaireAnswer.question_id: 3,
                    m.JobSeekerQuestionnaireAnswer.value: 1,
                    m.JobSeekerQuestionnaireAnswer.job_seeker_id: job_seeker_id,
                },
            ]
        )
    )

    await dbsession.execute(
        sa.insert(m.VacancyQuestionnaireAnswer).values(
            [
                {
                    m.VacancyQuestionnaireAnswer.question_id: 1,
                    m.VacancyQuestionnaireAnswer.value: 1,
                    m.VacancyQuestionnaireAnswer.vacancy_id: vacancy_id,
                },
                {
                    m.VacancyQuestionnaireAnswer.question_id: 2,
                    m.VacancyQuestionnaireAnswer.value: 1,
                    m.VacancyQuestionnaireAnswer.vacancy_id: vacancy_id,
                },
                {
                    m.VacancyQuestionnaireAnswer.question_id: 3,
                    m.VacancyQuestionnaireAnswer.value: 1,
                    m.VacancyQuestionnaireAnswer.vacancy_id: vacancy_id,
                },
            ]
        )
    )

    await dbsession.execute(
        sa.insert(m.JobSeekerVacancyMatch).values(
            {
                m.JobSeekerVacancyMatch.match_score: 0,
                m.JobSeekerVacancyMatch.job_seeker_id: job_seeker_id,
                m.JobSeekerVacancyMatch.vacancy_id: vacancy_id,
            }
        )
    )

    await calculate_test(dbsession)

    q = sa.select(
        m.VacancyMatch.id,
        m.VacancyMatch.ai_match_score,
        m.VacancyMatchDescription.ai_summary,
        m.VacancyMatchDescription.location_match,
        m.VacancyMatchDescription.salary_match,
        m.VacancyMatchDescription.driving_license_match,
        m.VacancyMatchDescription.matching_skills,
        m.VacancyMatchDescription.lacking_skills,
        m.VacancyMatchDescription.education_match,
        m.VacancyMatchDescription.work_experience_match,
        m.VacancyMatchDescription.work_experience_years,
        m.VacancyMatchDescription.matching_behavioural_tags,
        m.VacancyMatchDescription.lacking_behavioural_tags,
    )

    match = (await dbsession.execute(q)).fetchone()
    assert match is None

    await dbsession.execute(
        sa.update(m.Vacancy).values(
            {
                m.Vacancy.status: "OPENED",
                m.Vacancy.matches_last_calculated_at: None,
            }
        )
    )
    await calculate_test(dbsession)
    match = (await dbsession.execute(q)).fetchone()
    assert match is None

    await dbsession.execute(
        sa.update(m.JobSeeker).values(
            {
                m.JobSeeker.resume_path: "some_resume_path",
                m.JobSeeker.matches_last_calculated_at: None,
            }
        )
    )
    await calculate_test(dbsession)

    count = await dbsession.scalar(sa.select(sa.func.count(m.JobSeekerVacancyMatch.id)))

    assert count == 1

    final_score = await dbsession.scalar(sa.select(m.JobSeekerVacancyMatch.match_score))
    assert final_score == 1

    match = (await dbsession.execute(q)).fetchone()

    print(match.ai_summary)

    assert match.ai_match_score == 1.0
    assert match.location_match
    assert match.salary_match
    assert match.driving_license_match
    assert match.education_match
    assert sorted(match.matching_skills) == sorted(skills)
    assert match.lacking_skills == []
    assert sorted(match.matching_behavioural_tags) == sorted(
        [TAGS[i * 5] for i in range(3)]
    )
    assert match.lacking_behavioural_tags == []
    assert match.work_experience_match
    assert match.work_experience_years == 10


async def test_bad_match(dbsession):
    await install_answer_tags(dbsession)
    job_seeker_id = await create_job_seeker(dbsession, None)
    for degree, discipline in [
        ("School", None),
        ("Bachelor's", "Computer science"),
    ]:
        await create_education(dbsession, job_seeker_id, degree, discipline)

    await create_work_experience(dbsession, job_seeker_id, "Senior Python Developer")

    skills = [
        "Python",
        "SqlAlchemy",
        "Alembic",
        "Postgresql",
        "AWS EC2",
        "AWS S3",
        "Truck driving",
        "Asbestos awareness",
    ]
    skill_ids = await create_skills(dbsession, skills)
    await link_skills_to_job_seeker(dbsession, job_seeker_id, skill_ids[:6])

    vacancy_id = await create_vacancy(
        dbsession,
        "Truck driver",
        education_degree="Master's",
        education_discipline="Heavy machinery architecture",
        country="FR",
        city="Paris",
        salary_min=100,
        salary_max=150,
    )
    await link_skills_to_vacancy(dbsession, vacancy_id, skill_ids[6:])

    await dbsession.execute(
        sa.insert(m.JobSeekerQuestionnaireAnswer).values(
            [
                {
                    m.JobSeekerQuestionnaireAnswer.question_id: 1,
                    m.JobSeekerQuestionnaireAnswer.value: 1,
                    m.JobSeekerQuestionnaireAnswer.job_seeker_id: job_seeker_id,
                },
                {
                    m.JobSeekerQuestionnaireAnswer.question_id: 2,
                    m.JobSeekerQuestionnaireAnswer.value: 1,
                    m.JobSeekerQuestionnaireAnswer.job_seeker_id: job_seeker_id,
                },
                {
                    m.JobSeekerQuestionnaireAnswer.question_id: 3,
                    m.JobSeekerQuestionnaireAnswer.value: 1,
                    m.JobSeekerQuestionnaireAnswer.job_seeker_id: job_seeker_id,
                },
            ]
        )
    )

    await dbsession.execute(
        sa.insert(m.VacancyQuestionnaireAnswer).values(
            [
                {
                    m.VacancyQuestionnaireAnswer.question_id: 1,
                    m.VacancyQuestionnaireAnswer.value: 2,
                    m.VacancyQuestionnaireAnswer.vacancy_id: vacancy_id,
                },
                {
                    m.VacancyQuestionnaireAnswer.question_id: 2,
                    m.VacancyQuestionnaireAnswer.value: 2,
                    m.VacancyQuestionnaireAnswer.vacancy_id: vacancy_id,
                },
                {
                    m.VacancyQuestionnaireAnswer.question_id: 3,
                    m.VacancyQuestionnaireAnswer.value: 2,
                    m.VacancyQuestionnaireAnswer.vacancy_id: vacancy_id,
                },
            ]
        )
    )

    await calculate_test(dbsession)

    match = (
        await dbsession.execute(
            sa.select(
                m.VacancyMatch.id,
                m.VacancyMatch.ai_match_score,
                m.VacancyMatchDescription.ai_summary,
                m.VacancyMatchDescription.location_match,
                m.VacancyMatchDescription.salary_match,
                m.VacancyMatchDescription.driving_license_match,
                m.VacancyMatchDescription.matching_skills,
                m.VacancyMatchDescription.lacking_skills,
                m.VacancyMatchDescription.education_match,
                m.VacancyMatchDescription.work_experience_match,
                m.VacancyMatchDescription.work_experience_years,
                m.VacancyMatchDescription.matching_behavioural_tags,
                m.VacancyMatchDescription.lacking_behavioural_tags,
            )
        )
    ).fetchone()

    print(match.ai_summary)

    assert match is not None
    assert match.ai_match_score < 0.09
    assert not match.location_match
    assert not match.salary_match
    assert not match.driving_license_match
    assert not match.education_match
    assert not match.work_experience_match
    assert match.work_experience_years == 0
    assert match.matching_skills == []
    assert sorted(match.lacking_skills) == sorted(skills[6:])
    assert match.matching_behavioural_tags == []
    assert sorted(match.lacking_behavioural_tags) == sorted(
        [TAGS[i * 5 + 1] for i in range(3)]
    )


@pytest.mark.asyncio
async def test_several_matches(dbsession):
    skills = [
        "Python",
        "SqlAlchemy",
        "Alembic",
        "Postgresql",
        "AWS EC2",
        "AWS S3",
    ]
    skill_ids = await create_skills(dbsession, skills)

    for i in range(9):
        job_seeker_id = await create_job_seeker(dbsession, "some-not-empty-path")
        for degree, discipline in [
            ("School", None),
            ("Bachelor's", "Computer science"),
            ("Master's", "Cybersecurity"),
        ]:
            await create_education(dbsession, job_seeker_id, degree, discipline)
        await link_skills_to_job_seeker(dbsession, job_seeker_id, skill_ids)

        vacancy_id = await create_vacancy(dbsession, "Senior Python Developer")
        await link_skills_to_vacancy(dbsession, vacancy_id, skill_ids)

    await calculate_test(dbsession)

    matches_q = sa.select(
        m.VacancyMatch.id, m.VacancyMatchDescription.vacancy_match_id
    ).select_from(
        sa.join(
            m.VacancyMatch,
            m.VacancyMatchDescription,
            m.VacancyMatch.id == m.VacancyMatchDescription.vacancy_match_id,
        )
    )

    matches = (await dbsession.execute(matches_q)).fetchall()

    assert len(matches) == 81

    vacancy_id = await create_vacancy(dbsession, "Senior Python Developer")
    await link_skills_to_vacancy(dbsession, vacancy_id, skill_ids)

    await calculate_test(dbsession)

    matches = (await dbsession.execute(matches_q)).fetchall()

    assert len(matches) == 90

    job_seeker_id = await create_job_seeker(dbsession, "some-not-empty-path")
    await calculate_test(dbsession)

    matches = (await dbsession.execute(matches_q)).fetchall()

    assert len(matches) == 100

    job_seeker_id = await create_job_seeker(dbsession, "some-not-empty-path")
    job_seeker_id = await create_job_seeker(dbsession, "some-not-empty-path")
    vacancy_id = await create_vacancy(dbsession, "Senior Python Developer")
    await link_skills_to_vacancy(dbsession, vacancy_id, skill_ids)
    await calculate_test(dbsession)

    matches = (await dbsession.execute(matches_q)).fetchall()

    assert len(matches) == 130

    await dbsession.execute(
        sa.update(m.VacancyMatch)
        .where(m.VacancyMatch.vacancy_id == vacancy_id)
        .values({m.VacancyMatch.job_seeker_status: "any_status"})
    )

    await calculate_test(dbsession)

    matches = (await dbsession.execute(matches_q)).fetchall()

    assert len(matches) == 132


@pytest.mark.asyncio
async def test_company_filtering_job_seeker_matches(dbsession):
    """Test that job seekers don't get matched with vacancies from their current company"""

    # Create companies
    current_company_id = await create_company(dbsession, "Current Company")
    other_company_id = await create_company(dbsession, "Other Company")

    # Create a job seeker with current work experience
    job_seeker_id = await create_job_seeker(dbsession, "some-not-empty-path")

    # Add current work experience (end_date is None)
    await create_work_experience(
        dbsession,
        job_seeker_id,
        "Senior Python Developer",
        company_name="Current Company",
        end_date=None  # Current job
    )

    # Add past work experience (should not affect filtering)
    await create_work_experience(
        dbsession,
        job_seeker_id,
        "Junior Developer",
        company_name="Past Company",
        end_date=date(year=2020, month=1, day=1)  # Past job
    )

    # Create vacancies
    current_company_vacancy_id = await create_vacancy(
        dbsession,
        "Python Developer",
        company_id=current_company_id
    )

    other_company_vacancy_id = await create_vacancy(
        dbsession,
        "Python Developer",
        company_id=other_company_id
    )

    # Create initial match scores for both vacancies
    await dbsession.execute(
        sa.insert(m.JobSeekerVacancyMatch).values([
            {
                m.JobSeekerVacancyMatch.match_score: 0.8,
                m.JobSeekerVacancyMatch.job_seeker_id: job_seeker_id,
                m.JobSeekerVacancyMatch.vacancy_id: current_company_vacancy_id,
            },
            {
                m.JobSeekerVacancyMatch.match_score: 0.8,
                m.JobSeekerVacancyMatch.job_seeker_id: job_seeker_id,
                m.JobSeekerVacancyMatch.vacancy_id: other_company_vacancy_id,
            }
        ])
    )

    # Test the filtering
    qh = SqlQueryHelper(dbsession)
    matches = await qh.get_new_matches_for_job_seeker(job_seeker_id)

    # Should only get the match from the other company, not current company
    assert len(matches) == 1
    assert other_company_vacancy_id in matches
    assert current_company_vacancy_id not in matches


@pytest.mark.asyncio
async def test_company_filtering_vacancy_matches(dbsession):
    """Test that vacancies don't get matched with job seekers who currently work at the same company"""

    # Create company
    company_id = await create_company(dbsession, "Test Company")

    # Create job seekers
    current_employee_id = await create_job_seeker(dbsession, "some-not-empty-path")
    other_job_seeker_id = await create_job_seeker(dbsession, "some-not-empty-path")

    # Add current work experience for first job seeker
    await create_work_experience(
        dbsession,
        current_employee_id,
        "Senior Developer",
        company_name="Test Company",
        end_date=None  # Current job
    )

    # Add past work experience for second job seeker (different company)
    await create_work_experience(
        dbsession,
        other_job_seeker_id,
        "Senior Developer",
        company_name="Other Company",
        end_date=date(year=2020, month=1, day=1)  # Past job
    )

    # Create vacancy from the same company
    vacancy_id = await create_vacancy(
        dbsession,
        "Senior Developer",
        company_id=company_id
    )

    # Create initial match scores for both job seekers
    await dbsession.execute(
        sa.insert(m.JobSeekerVacancyMatch).values([
            {
                m.JobSeekerVacancyMatch.match_score: 0.8,
                m.JobSeekerVacancyMatch.job_seeker_id: current_employee_id,
                m.JobSeekerVacancyMatch.vacancy_id: vacancy_id,
            },
            {
                m.JobSeekerVacancyMatch.match_score: 0.8,
                m.JobSeekerVacancyMatch.job_seeker_id: other_job_seeker_id,
                m.JobSeekerVacancyMatch.vacancy_id: vacancy_id,
            }
        ])
    )

    # Test the filtering
    qh = SqlQueryHelper(dbsession)
    matches = await qh.get_new_matches_for_vacancy(vacancy_id)

    # Should only get the match from the external job seeker, not current employee
    assert len(matches) == 1
    assert other_job_seeker_id in matches
    assert current_employee_id not in matches
