#!/usr/bin/env python3
import asyncio

import asyncpg
from asyncpg import DuplicateFunctionError

from config import settings as s

psql_url = f"postgresql://{s.DATABASE_USER}:{s.DATABASE_PASSWORD}@{s.DATABASE_HOST}:{s.DATABASE_PORT}"


def check_test_db():
    if s.DATABASE_HOST not in ("localhost", "127.0.0.1", "postgres"):
        print(s.DATABASE_HOST)
        raise Exception("Use local database only!")


async def setup_db_for_tests():
    check_test_db()

    conn = await asyncpg.connect(psql_url)

    await conn.execute(f"drop database if exists {s.DATABASE_DB}")
    await conn.execute("commit")
    await conn.execute(f"create database {s.DATABASE_DB}")
    await conn.execute("commit")
    await conn.close()


if __name__ == "__main__":
    asyncio.run(setup_db_for_tests())
