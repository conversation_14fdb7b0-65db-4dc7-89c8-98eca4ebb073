import itertools
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession
import models as m
from config import log, settings as st
from matching.query_helper import Sql<PERSON>ueryHelper
from matching.similarity import semantic_similarity_bert
from matching.calculations import (
    build_match_description_for_ai,
    evaluate_behavioral_tags,
    evaluate_dr_licence,
    evaluate_education_match,
    evaluate_experience_match,
    evaluate_location_salary,
    evaluate_skills_match,
)
from utils import generate_ai_match_description


class MatchesGenerator:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.qh = SqlQueryHelper(session)

    async def generate_matches_for_vacancy(self, vacancy_id: int):
        log.info(f"Start calculating more matches for {vacancy_id=}")

        vacancy = (await self.qh.fetch_vacancy_details([vacancy_id]))[0]

        job_seeker_scores = await self.qh.get_new_matches_for_vacancy(vacancy_id)

        if not job_seeker_scores:
            log.info(f"No more matches for {vacancy_id=}, skipping...")
            return

        job_seekers = await self.qh.fetch_job_seekers(job_seeker_scores.keys())

        skills_mapping = await self.qh.fetch_skill_names(vacancy.skills)

        descriptions = []
        for job_seeker in job_seekers:
            raw_match = job_seeker_scores[job_seeker.id]
            seeker_educations = await self.qh.fetch_job_seeker_educations(job_seeker.id)
            description = await self.match_job_seeker_vacancy(
                score=raw_match.score,
                match_id=raw_match.id,
                vacancy=vacancy,
                skills_mapping=skills_mapping,
                job_seeker=job_seeker,
                job_seeker_educations=seeker_educations,
            )
            if description:
                descriptions.append(description)

        await self.qh.insert_descriptions(descriptions)

        log.info(f"Added {len(descriptions)} new matches for {vacancy.id=}")

    async def generate_matches_for_job_seeker(self, job_seeker_id: int):
        log.info(f"[Matching] Started searching matches for {job_seeker_id=}")
        job_seeker = (
            await self.qh.fetch_job_seekers([job_seeker_id], include_job_titles=True)
        )[0]
        if not job_seeker:
            log.info(f"[Matching] Job seeker {job_seeker_id} not found")
            return

        vacancy_initial_scores = await self.qh.get_new_matches_for_job_seeker(
            job_seeker_id
        )
        if not vacancy_initial_scores:
            log.info(f"No more matches for {job_seeker.id=}, skipping...")
            return

        log.info(f"[Matching] Top matches {vacancy_initial_scores}")

        vacancies = await self.qh.fetch_vacancy_details(vacancy_initial_scores.keys())
        seeker_educations = await self.qh.fetch_job_seeker_educations(job_seeker.id)
        all_skill_ids = list(
            itertools.chain(*[v.skills for v in vacancies if v.skills])
        )
        skills_mapping = await self.qh.fetch_skill_names(all_skill_ids)

        descriptions = []
        for vacancy in vacancies:
            raw_match = vacancy_initial_scores[vacancy.id]
            description = await self.match_job_seeker_vacancy(
                score=raw_match.score,
                match_id=raw_match.id,
                vacancy=vacancy,
                skills_mapping=skills_mapping,
                job_seeker=job_seeker,
                job_seeker_educations=seeker_educations,
            )
            if description:
                descriptions.append(description)

        await self.qh.insert_descriptions(descriptions)

        log.info(f"Added {len(descriptions)} new matches for {job_seeker=}")

    async def calculate_new_vacancies(self) -> Optional[list[int]]:
        new_vacancies = await self.qh.get_new_vacancies()
        log.info(f"[Matching] Found {new_vacancies=}")

        if new_vacancies:
            for vacancy in new_vacancies[:10]:
                await self.generate_matches_for_vacancy(vacancy)

            await self.qh.update_vacancies(new_vacancies)

        return new_vacancies

    async def calculate_new_job_seekers(self) -> Optional[list[int]]:
        new_job_seekers = await self.qh.get_new_job_seekers()

        log.info(f"[Matching] Found {len(new_job_seekers)} new job seekers")

        if new_job_seekers:
            for job_seeker in new_job_seekers[:10]:
                await self.generate_matches_for_job_seeker(job_seeker)

            await self.qh.update_job_seekers(new_job_seekers)

        return new_job_seekers

    async def recalculate_vacancies(self) -> Optional[list[int]]:
        vacancies_to_recalculate = await self.qh.get_vacancies_to_recalculate()
        log.info(f"[Matching] Found {vacancies_to_recalculate=}")

        if vacancies_to_recalculate:
            for vacancy in vacancies_to_recalculate:
                await self.generate_matches_for_vacancy(vacancy.id)

            await self.qh.update_vacancies([v.id for v in vacancies_to_recalculate])

        return vacancies_to_recalculate

    async def recalculate_job_seekers(self) -> Optional[list[int]]:
        job_seekers_to_recalculate = await self.qh.get_js_to_recalculate()
        log.info(f"[Matching] Found {job_seekers_to_recalculate=}")

        if job_seekers_to_recalculate:
            for job_seeker in job_seekers_to_recalculate:
                await self.generate_matches_for_job_seeker(job_seeker)

            await self.qh.update_job_seekers(job_seekers_to_recalculate)

        return job_seekers_to_recalculate

    async def match_job_seeker_vacancy(
        self,
        score,
        vacancy,
        match_id,
        skills_mapping,
        job_seeker,
        job_seeker_educations,
    ):

        similarity_fn = semantic_similarity_bert
        min_score = st.JOB_EXPERIENCE_MATCH_MIN_SCORE

        education_score, edu_discipline, edu_matched = await evaluate_education_match(
            vacancy, job_seeker_educations, similarity_fn
        )

        matching_skills, lacking_skills, skill_penalty = evaluate_skills_match(
            vacancy, job_seeker, skills_mapping
        )

        loc_match, sal_match, loc_penalty, sal_penalty = evaluate_location_salary(
            vacancy, job_seeker
        )

        behav_match, behav_lack, behav_penalty_percent = await evaluate_behavioral_tags(
            self.qh, vacancy.id, job_seeker.id
        )

        driving_license_match, driving_license_penalty = await evaluate_dr_licence(
            vacancy.driving_license, job_seeker.has_driving_license
        )

        exp_score, exp_years = await evaluate_experience_match(
            self.qh, vacancy, job_seeker.id, similarity_fn, min_score
        )

        final_score = score
        final_score -= score * driving_license_penalty
        final_score -= score * (1 - education_score) * 0.1
        final_score -= score * skill_penalty
        final_score -= score * loc_penalty
        final_score -= score * sal_penalty
        final_score -= 0.2 * score * (1 - exp_score)
        final_score -= behav_penalty_percent * score

        if final_score < st.MIN_SCORE:
            await self.qh.update_raw_match(match_id)
            return

        vm = m.VacancyMatchDescription
        match_info = {
            vm.driving_license_match: driving_license_match,
            vm.education_match: edu_matched,
            vm.matching_skills: matching_skills,
            vm.lacking_skills: lacking_skills,
            vm.location_match: loc_match,
            vm.salary_match: sal_match,
            vm.matching_behavioural_tags: behav_match,
            vm.lacking_behavioural_tags: behav_lack,
            vm.work_experience_match: (exp_years or 0)
            >= (vacancy.experience_years_from or 0),
            vm.work_experience_years: exp_years,
        }

        match_id = await self.qh.insert_new_match(
            job_seeker.id, vacancy.id, final_score
        )
        ai_text = build_match_description_for_ai(
            vacancy, job_seeker, match_info, final_score
        )

        try:
            summary = await generate_ai_match_description(raw_match_text=ai_text)
        except Exception as e:
            summary = ""

        match_info[vm.ai_summary] = summary
        match_info[vm.vacancy_match_id] = match_id

        return match_info
