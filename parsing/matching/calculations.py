from datetime import date
import models as m


async def evaluate_education_match(vacancy, educations, similarity_fn):
    if not vacancy.education_discipline:
        return 0.0, None, False

    best_score, best_discipline = 0.0, None
    for edu in educations:
        if edu.type and edu.type.lower() == vacancy.education_degree.lower():
            score = similarity_fn(
                edu.discipline.lower(), vacancy.education_discipline.lower()
            )
            if score > best_score:
                best_score = score
                best_discipline = edu.discipline
    return best_score, best_discipline, best_score != 0.0


def evaluate_skills_match(vacancy, job_seeker, skills_mapping):
    if not vacancy.skills:
        return [], [], 0.35

    vacancy_skills = set(vacancy.skills or [])
    seeker_skills = set(job_seeker.skills or [])
    matching = vacancy_skills & seeker_skills
    lacking = vacancy_skills - seeker_skills

    matching_mapped = {skills_mapping.get(s) for s in matching} - {None}
    lacking_mapped = {skills_mapping.get(s) for s in lacking} - {None}
    penalty = len(lacking) * 0.35 / len(vacancy.skills)

    return list(matching_mapped), list(lacking_mapped), penalty


def evaluate_location_salary(vacancy, job_seeker):
    location_match = False
    salary_match = False
    location_penalty = 0.0
    salary_penalty = 0.0

    if (
        vacancy.city
        and job_seeker.city
        and (
            job_seeker.city.lower() != vacancy.city.lower()
            or job_seeker.country.lower() != vacancy.country.lower()
        )
    ):
        location_penalty = 0.1
    else:
        location_match = True

    if (
        vacancy.salary_max
        and job_seeker.salary_min
        and job_seeker.salary_min > vacancy.salary_max
    ):
        salary_penalty = 0.1
    elif (
        vacancy.salary_min
        and job_seeker.salary_min
        and job_seeker.salary_min > vacancy.salary_min
    ):
        salary_penalty = 0.1
    else:
        salary_match = True

    return location_match, salary_match, location_penalty, salary_penalty


async def evaluate_behavioral_tags(qh, vacancy_id, seeker_id):
    tags_vac = await qh.get_vacancy_behavioural_tags(vacancy_id)
    tags_seek = await qh.get_job_seeker_behavioural_tags(seeker_id)
    tags_vac, tags_seek = set(tags_vac or []), set(tags_seek or [])
    matching = tags_vac & tags_seek
    lacking = tags_vac - tags_seek
    penalty_percent = (len(lacking) / len(tags_vac)) * 0.1 if tags_vac else 0
    return matching, lacking, penalty_percent


async def evaluate_experience_match(qh, vacancy, seeker_id, similarity_fn, min_score):
    if not vacancy.experience_years_from:
        return 1.0, None

    experiences = await qh.get_seeker_work_experiences(seeker_id)
    current_date = date.today()
    relevant_jobs = []

    for job in experiences:
        sim = similarity_fn(vacancy.job_title, job.job_title)
        if sim >= min_score:
            relevant_jobs.append((sim, job))

    total_days, score = 0, 0
    required_days = vacancy.experience_years_from * 365

    for sim, job in sorted(relevant_jobs, key=lambda x: x[0], reverse=True):
        days = (job.end_date or current_date) - job.start_date
        days = days.days
        if total_days < required_days:
            score += sim * min(days, required_days - total_days) / required_days
        total_days += days

    return score, total_days // 365


async def evaluate_dr_licence(vacancy_driving_license, js_license):
    if vacancy_driving_license:
        if not js_license:
            return False, 0.05

    return True, 0


def build_match_description_for_ai(vacancy, job_seeker, match_info, final_score):
    def list_or_none(items, empty="none"):
        return ", ".join(map(str, items)) if items else empty

    vm = m.VacancyMatchDescription

    return f"""
Job Title: {vacancy.job_title}
Location match: {"yes" if match_info.get(vm.location_match) else "no"}
Salary match: {"yes" if match_info.get(vm.salary_match) else "no"}
Education match: {"yes" if match_info.get(vm.education_match) else "no"}
Experience years: {match_info.get(vm.work_experience_years)} (required: {vacancy.experience_years_from})
Skills matched: {list_or_none(match_info.get(vm.matching_skills))}
Skills missing: {list_or_none(match_info.get(vm.lacking_skills))}
Tags matched: {list_or_none(match_info.get(vm.matching_behavioural_tags))}
Tags missing: {list_or_none(match_info.get(vm.lacking_behavioural_tags))}
Driving license required: {"yes" if vacancy.driving_license else "no"}
Driving license match: {"yes" if match_info.get(vm.driving_license_match, True) else "no"}
Final score: {round(final_score, 2)}
"""
