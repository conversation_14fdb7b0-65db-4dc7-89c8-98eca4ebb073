import json
import pickle
from datetime import datetime, timezone
from typing import Iterable, Optional

from sqlalchemy.ext.asyncio import AsyncSession
import sqlalchemy as sa
import models as m
from sqlalchemy.dialects.postgresql import insert
from config import log, settings as st


class SqlQueryHelper:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.total_egress_bytes = 0

    async def get_new_job_seekers(self):
        result = (
            await self.session.scalars(
                sa.select(m.JobSeeker.id)
                .where(
                    sa.and_(
                        m.JobSeeker.matches_last_calculated_at.is_(None),
                        m.JobSeeker.resume_path.isnot(None),
                        m.JobSeeker.is_active,
                    )
                )
                .order_by(m.JobSeeker.updated_at.asc())
            )
            or []
        )

        result = list(result)

        egress_bytes = len(pickle.dumps(result))
        # log.info(f"get_new_job_seekers: {egress_bytes}B")
        self.total_egress_bytes += egress_bytes
        return result

    async def get_new_vacancies(self):
        result = (
            await self.session.scalars(
                sa.select(m.Vacancy.id)
                .where(
                    sa.and_(
                        m.Vacancy.matches_last_calculated_at.is_(None),
                        m.Vacancy.status == "OPENED",
                    )
                )
                .order_by(m.Vacancy.updated_at.asc())
            )
            or []
        )

        result = list(result)

        egress_bytes = len(pickle.dumps(result))
        # log.info(f"get_new_vacancies: {egress_bytes}B")
        self.total_egress_bytes += egress_bytes
        return result

    async def get_js_to_recalculate(
        self, ids_to_exclude: Optional[Iterable[int]] = None
    ) -> list[int]:
        result = await self.session.scalars(
            sa.select(m.JobSeeker.id.distinct().label("id"))
            .select_from(
                sa.outerjoin(
                    m.JobSeeker,
                    m.VacancyMatch,
                    sa.and_(
                        m.JobSeeker.id == m.VacancyMatch.job_seeker_id,
                        m.VacancyMatch.job_seeker_status == st.DEFAULT_STATUS,
                        m.VacancyMatch.employer_status == st.DEFAULT_STATUS,
                    ),
                )
            )
            .where(
                sa.and_(
                    m.JobSeeker.id.not_in(ids_to_exclude or []),
                    m.JobSeeker.resume_path.isnot(None),
                    m.JobSeeker.is_active,
                )
            )
            .having(sa.func.count(m.VacancyMatch.id) < int(st.NEW_MATCHES_LIMIT))
            .group_by(m.JobSeeker.id)
        )

        result = list(result)

        egress_bytes = len(json.dumps(result).encode("utf-8"))
        # log.info(f"get_js_to_recalculate: {egress_bytes}B")
        self.total_egress_bytes += egress_bytes

        return result

    async def get_vacancies_to_recalculate(
        self, ids_to_exclude: Optional[Iterable[int]] = None
    ):
        result = (
            await self.session.execute(
                sa.select(m.Vacancy.id.distinct().label("id"))
                .select_from(
                    sa.outerjoin(
                        m.Vacancy,
                        m.VacancyMatch,
                        sa.and_(
                            m.Vacancy.id == m.VacancyMatch.vacancy_id,
                            m.VacancyMatch.job_seeker_status == st.DEFAULT_STATUS,
                            m.VacancyMatch.employer_status == st.DEFAULT_STATUS,
                        ),
                    )
                )
                .where(
                    sa.and_(
                        m.Vacancy.id.not_in(ids_to_exclude or []),
                        m.Vacancy.status == "OPENED",
                    )
                )
                .having(
                    sa.func.count(m.VacancyMatch.id) < int(st.NEW_MATCHES_LIMIT)
                )  # int() for linter
                .group_by(m.Vacancy.id)
            )
        ).fetchall()

        egress_bytes = len(pickle.dumps(result))
        log.info(f"get_vacancies_to_recalculate: {egress_bytes}B")
        return result

    async def fetch_existing_matches_for_job_seeker(self, job_seeker_id: int):
        result = (
            await self.session.scalar(
                sa.select(sa.func.array_agg(m.VacancyMatch.vacancy_id)).where(
                    m.VacancyMatch.job_seeker_id == job_seeker_id
                )
            )
            or []
        )
        egress_bytes = len(json.dumps(result).encode("utf-8"))
        # log.info(f"fetch_existing_matches_for_job_seeker: {egress_bytes}B")
        self.total_egress_bytes += egress_bytes
        return result

    async def fetch_existing_matches_for_vacancy(
        self, vacancy_id: int
    ) -> Optional[Iterable[int]]:
        result = (
            await self.session.scalar(
                sa.select(sa.func.array_agg(m.VacancyMatch.job_seeker_id)).where(
                    m.VacancyMatch.vacancy_id == vacancy_id
                )
            )
            or []
        )
        egress_bytes = len(json.dumps(result).encode("utf-8"))
        self.total_egress_bytes += egress_bytes
        # log.info(f"fetch_existing_matches_for_vacancy: {egress_bytes}B")
        return result

    async def get_job_seeker_to_calculate(self, vacancy_id: int) -> list[int]:
        not_calculated_js = await self.session.scalars(
            sa.select(m.JobSeeker.id)
            .select_from(
                sa.outerjoin(
                    m.JobSeeker,
                    m.VacancyMatch,
                    sa.and_(
                        m.JobSeeker.id == m.VacancyMatch.job_seeker_id,
                        m.VacancyMatch.vacancy_id == vacancy_id,
                    ),
                )
            )
            .where(
                sa.and_(
                    m.VacancyMatch.id.is_(None),
                    m.JobSeeker.resume_path.isnot(None),
                    m.JobSeeker.is_active,
                )
            )
        )
        result = list(not_calculated_js)
        egress_bytes = len(json.dumps(result).encode("utf-8"))
        self.total_egress_bytes += egress_bytes
        # log.info(f"not_calculated_js: {egress_bytes}B")

        return list(result)

    async def fetch_job_seeker_titles(self, job_seeker_ids: Iterable[int]):
        if not job_seeker_ids:
            return None

        result = (
            await self.session.execute(
                sa.select(
                    m.JobSeeker.id,
                    m.JobSeeker.job_title,
                    sa.func.array_agg(m.JobSeekerWorkExperience.job_title)
                    .filter(m.JobSeekerWorkExperience.job_title.isnot(None))
                    .label("job_titles"),
                )
                .select_from(
                    sa.outerjoin(
                        m.JobSeeker,
                        m.JobSeekerWorkExperience,
                        m.JobSeeker.id == m.JobSeekerWorkExperience.job_seeker_id,
                    )
                )
                .where(
                    sa.and_(
                        m.JobSeeker.id.in_(job_seeker_ids),
                        m.JobSeeker.resume_path.isnot(None),
                        m.JobSeeker.is_active,
                    )
                )
                .group_by(m.JobSeeker.id)
            )
        ).fetchall()

        egress_bytes = len(pickle.dumps(result))
        self.total_egress_bytes += egress_bytes
        # log.info(f"fetch_job_seeker_titles: {egress_bytes}B")
        return result

    async def fetch_job_seeker_educations(self, job_seeker_id: int):
        result = (
            await self.session.execute(
                sa.select(
                    m.JobSeekerEducation.type,
                    m.JobSeekerEducation.discipline,
                ).where(m.JobSeekerEducation.job_seeker_id == job_seeker_id)
            )
        ).fetchall()
        egress_bytes = len(pickle.dumps(result))
        self.total_egress_bytes += egress_bytes
        # log.info(f"fetch_job_seeker_educations: {egress_bytes}B")
        return result

    async def fetch_job_seekers(
        self, job_seeker_ids: Iterable[int], include_job_titles=False
    ):
        selection_set = [
            m.JobSeeker.id,
            m.JobSeeker.salary_min,
            m.JobSeeker.country,
            m.JobSeeker.city,
            m.JobSeeker.has_driving_license,
            sa.func.array_agg(m.JobSeekerSkill.skill_id)
            .filter(m.JobSeekerSkill.skill_id.isnot(None))
            .label("skills"),
        ]
        select_from = sa.outerjoin(
            m.JobSeeker,
            m.JobSeekerSkill,
            m.JobSeeker.id == m.JobSeekerSkill.job_seeker_id,
        )

        if include_job_titles:
            selection_set += [
                m.JobSeeker.job_title,
                sa.func.array_agg(m.JobSeekerWorkExperience.job_title)
                .filter(m.JobSeekerWorkExperience.job_title.isnot(None))
                .label("job_titles"),
            ]
            select_from = select_from.outerjoin(
                m.JobSeekerWorkExperience,
                m.JobSeeker.id == m.JobSeekerWorkExperience.job_seeker_id,
            )

        job_seekers = (
            await self.session.execute(
                sa.select(*selection_set)
                .select_from(select_from)
                .where(
                    sa.and_(
                        m.JobSeeker.id.in_(job_seeker_ids),
                        m.JobSeeker.resume_path.isnot(None),
                        m.JobSeeker.is_active,
                    )
                )
                .group_by(m.JobSeeker.id)
            )
        ).fetchall()

        egress_bytes = len(pickle.dumps(job_seekers))
        self.total_egress_bytes += egress_bytes
        # log.info(f"fetch_job_seekers: {egress_bytes}B")

        return job_seekers

    async def fetch_vacancy_list(self, vacancy_ids: list[int]):
        result = (
            await self.session.execute(
                sa.select(m.Vacancy.id, m.Vacancy.title.label("job_title")).where(
                    sa.and_(
                        m.Vacancy.id.in_(vacancy_ids),
                        m.Vacancy.status == "OPENED",
                    )
                )
            )
        ).fetchall()

        egress_bytes = len(pickle.dumps(result))
        self.total_egress_bytes += egress_bytes
        # log.info(f"fetch_vacancy_list: {egress_bytes}B")

        return result

    async def fetch_vacancy_details(self, vacancy_ids: Iterable[int]):
        result = (
            await self.session.execute(
                sa.select(
                    m.Vacancy.id,
                    m.Vacancy.city,
                    m.Vacancy.country,
                    m.Vacancy.title.label("job_title"),
                    m.Vacancy.driving_license,
                    m.Vacancy.education_degree,
                    m.Vacancy.education_discipline,
                    m.Vacancy.salary_max,
                    m.Vacancy.salary_min,
                    m.Vacancy.experience_years_from,
                    sa.func.array_agg(m.VacancySkill.skill_id)
                    .filter(m.VacancySkill.skill_id.isnot(None))
                    .label("skills"),
                )
                .select_from(
                    sa.outerjoin(
                        m.Vacancy,
                        m.VacancySkill,
                        m.Vacancy.id == m.VacancySkill.vacancy_id,
                    )
                )
                .where(
                    sa.and_(
                        m.Vacancy.id.in_(vacancy_ids),
                        m.Vacancy.status == "OPENED",
                    )
                )
                .group_by(m.Vacancy.id)
            )
        ).fetchall()

        egress_bytes = len(pickle.dumps(result))
        self.total_egress_bytes += egress_bytes
        # log.info(f"fetch_vacancy_details: {egress_bytes}B")

        return result

    async def fetch_skill_names(self, skill_ids: list[int]):
        if not skill_ids:
            return []

        result = (
            await self.session.execute(
                sa.select(m.Skill.id, m.Skill.name).where(m.Skill.id.in_(skill_ids))
            )
        ).fetchall() or []
        egress_bytes = len(pickle.dumps(result))
        self.total_egress_bytes += egress_bytes
        # log.info(f"fetch_skill_names: {egress_bytes}B")

        return dict(result)

    async def insert_job_seeker_vacancy_matches(
        self, job_seeker_id: int, scores: dict[int, float]
    ):
        await self.session.execute(
            insert(m.JobSeekerVacancyMatch)
            .values(
                [
                    {
                        m.JobSeekerVacancyMatch.job_seeker_id: job_seeker_id,
                        m.JobSeekerVacancyMatch.vacancy_id: vid,
                        m.JobSeekerVacancyMatch.match_score: score,
                    }
                    for vid, score in scores.items()
                ]
            )
            .on_conflict_do_nothing()
        )

    async def get_new_matches_for_vacancy(self, vacancy_id: int):
        vacancy_company_name = await self.session.scalar(
            sa.select(m.Company.name)
            .select_from(
                sa.join(m.Vacancy, m.Company, m.Vacancy.company_id == m.Company.id)
            )
            .where(m.Vacancy.id == vacancy_id)
        )

        existing_matches = sa.select(m.VacancyMatch.job_seeker_id).where(
            m.VacancyMatch.vacancy_id == vacancy_id
        )
        q = (
            sa.select(
                m.JobSeekerVacancyMatch.id.label("id"),
                m.JobSeekerVacancyMatch.job_seeker_id.label("job_seeker_id"),
                m.JobSeekerVacancyMatch.match_score.label("score"),
            )
            .where(
                m.JobSeekerVacancyMatch.vacancy_id == vacancy_id,
                m.JobSeekerVacancyMatch.job_seeker_id.not_in(existing_matches),
                m.JobSeekerVacancyMatch.match_score >= st.MIN_SCORE,
                ~m.JobSeekerVacancyMatch.final_score_below_threshold,
            )
            .order_by(m.JobSeekerVacancyMatch.match_score.desc())
            .limit(st.NEW_MATCHES_LIMIT)
        )

        if vacancy_company_name:
            current_employees_subquery = sa.select(m.JobSeekerWorkExperience.job_seeker_id).where(
                sa.and_(
                    m.JobSeekerWorkExperience.company_name == vacancy_company_name,
                    m.JobSeekerWorkExperience.end_date.is_(None)
                )
            )
            q = q.where(m.JobSeekerVacancyMatch.job_seeker_id.not_in(current_employees_subquery))

        result = (await self.session.execute(q)).fetchall() or []
        egress_bytes = len(pickle.dumps(result))
        self.total_egress_bytes += egress_bytes
        # log.info(f"get_new_matches_for_vacancy: {egress_bytes}B")

        job_seeker_mapping = {j.job_seeker_id: j for j in result}

        return job_seeker_mapping

    async def insert_raw_vacancy_matches(
        self, vacancy_id: int, job_seeker_scores: dict[int, float]
    ):
        await self.session.execute(
            insert(m.JobSeekerVacancyMatch)
            .values(
                [
                    {
                        m.JobSeekerVacancyMatch.job_seeker_id: seeker_id,
                        m.JobSeekerVacancyMatch.vacancy_id: vacancy_id,
                        m.JobSeekerVacancyMatch.match_score: score,
                    }
                    for seeker_id, score in job_seeker_scores.items()
                ]
            )
            .on_conflict_do_nothing()
        )

    async def insert_new_match(
        self, job_seeker_id: int, vacancy_id: int, final_score: float
    ):
        return await self.session.scalar(
            insert(m.VacancyMatch)
            .values(
                {
                    m.VacancyMatch.job_seeker_id: job_seeker_id,
                    m.VacancyMatch.vacancy_id: vacancy_id,
                    m.VacancyMatch.ai_match_score: round(final_score, 8),
                    m.VacancyMatch.job_seeker_status: st.DEFAULT_STATUS,
                    m.VacancyMatch.employer_status: st.DEFAULT_STATUS,
                }
            )
            .on_conflict_do_nothing()
            .returning(m.VacancyMatch.id)
        )

    async def insert_descriptions(self, descriptions: list):
        if descriptions:
            await self.session.execute(
                sa.insert(m.VacancyMatchDescription).values(descriptions)
            )

    async def get_new_matches_for_job_seeker(self, job_seeker_id: int):
        current_company_names = await self.get_job_seeker_current_company_names(job_seeker_id)

        query = (
            sa.select(
                m.JobSeekerVacancyMatch.id.label("id"),
                m.JobSeekerVacancyMatch.vacancy_id.label("vacancy_id"),
                m.JobSeekerVacancyMatch.match_score.label("score"),
            )
            .select_from(
                sa.join(
                    m.JobSeekerVacancyMatch,
                    m.Vacancy,
                    m.JobSeekerVacancyMatch.vacancy_id == m.Vacancy.id
                ).join(
                    m.Company,
                    m.Vacancy.company_id == m.Company.id
                )
            )
            .where(
                m.JobSeekerVacancyMatch.job_seeker_id == job_seeker_id,
                m.JobSeekerVacancyMatch.vacancy_id.not_in(
                    sa.select(m.VacancyMatch.vacancy_id).where(
                        m.VacancyMatch.job_seeker_id == job_seeker_id
                    )
                ),
                m.JobSeekerVacancyMatch.match_score >= st.MIN_SCORE,
                ~m.JobSeekerVacancyMatch.final_score_below_threshold,
            )
            .limit(st.NEW_MATCHES_LIMIT)
        )

        if current_company_names:
            query = query.where(~m.Company.name.in_(current_company_names))

        result = (await self.session.execute(query)).fetchall() or []

        egress_bytes = len(pickle.dumps(result))
        self.total_egress_bytes += egress_bytes
        # log.info(f"get_new_matches_for_job_seeker: {egress_bytes}B")

        vacancy_mapping = {v.vacancy_id: v for v in result}

        return vacancy_mapping

    async def get_seeker_work_experiences(self, seeker_id: int):
        result = (
            await self.session.execute(
                sa.select(
                    m.JobSeekerWorkExperience.job_title,
                    m.JobSeekerWorkExperience.start_date,
                    m.JobSeekerWorkExperience.end_date,
                ).where(
                    m.JobSeekerWorkExperience.job_seeker_id == seeker_id,
                )
            )
        ).fetchall() or []

        egress_bytes = len(pickle.dumps(result))
        self.total_egress_bytes += egress_bytes
        # log.info(f"get_seeker_work_experiences: {egress_bytes}B")

        return result

    async def get_job_seeker_current_company_names(self, job_seeker_id: int) -> list[str]:
        """Get the names of companies where the job seeker currently works (end_date is NULL)"""
        result = (
            await self.session.execute(
                sa.select(m.JobSeekerWorkExperience.company_name)
                .where(
                    sa.and_(
                        m.JobSeekerWorkExperience.job_seeker_id == job_seeker_id,
                        m.JobSeekerWorkExperience.end_date.is_(None)
                    )
                )
            )
        ).fetchall() or []

        # Extract company names from the result tuples
        company_names = [row.company_name for row in result if row.company_name]

        egress_bytes = len(pickle.dumps(company_names))
        self.total_egress_bytes += egress_bytes

        return company_names

    async def get_vacancy_behavioural_tags(self, vacancy_id):
        result = await self.session.scalar(
            sa.select(sa.func.array_agg(m.VacancyAnswerTag.tag))
            .select_from(
                sa.join(
                    m.VacancyAnswerTag,
                    m.VacancyQuestionnaireAnswer,
                    sa.and_(
                        m.VacancyAnswerTag.question_id
                        == m.VacancyQuestionnaireAnswer.question_id,
                        m.VacancyAnswerTag.value == m.VacancyQuestionnaireAnswer.value,
                    ),
                )
            )
            .where(m.VacancyQuestionnaireAnswer.vacancy_id == vacancy_id)
        )
        egress_bytes = len(json.dumps(result).encode("utf-8"))
        self.total_egress_bytes += egress_bytes
        # log.info(f"get_vacancy_behavioural_tags: {egress_bytes}B")

        return result

    async def get_job_seeker_behavioural_tags(self, job_seeker_id):
        result = await self.session.scalar(
            sa.select(sa.func.array_agg(m.JobSeekerAnswerTag.tag))
            .select_from(
                sa.join(
                    m.JobSeekerAnswerTag,
                    m.JobSeekerQuestionnaireAnswer,
                    sa.and_(
                        m.JobSeekerAnswerTag.question_id
                        == m.JobSeekerQuestionnaireAnswer.question_id,
                        m.JobSeekerAnswerTag.value
                        == m.JobSeekerQuestionnaireAnswer.value,
                    ),
                )
            )
            .where(m.JobSeekerQuestionnaireAnswer.job_seeker_id == job_seeker_id)
        )

        egress_bytes = len(json.dumps(result).encode("utf-8"))
        self.total_egress_bytes += egress_bytes
        # log.info(f"get_job_seeker_behavioural_tags: {egress_bytes}B")

        return result

    async def get_vacancies_to_calculate(self, job_seeker_id: int):
        not_calculated_vacancies = (
            await self.session.execute(
                sa.select(m.Vacancy.id)
                .select_from(
                    sa.outerjoin(
                        m.Vacancy,
                        m.VacancyMatch,
                        sa.and_(
                            m.Vacancy.id == m.VacancyMatch.vacancy_id,
                            m.VacancyMatch.job_seeker_id == job_seeker_id,
                        ),
                    )
                )
                .where(
                    sa.and_(
                        m.VacancyMatch.id.is_(None),
                        m.Vacancy.status == "OPENED",
                    )
                )
            )
        ).fetchall()

        egress_bytes = len(pickle.dumps(not_calculated_vacancies))
        self.total_egress_bytes += egress_bytes
        # log.info(f"get_vacancies_to_calculate: {egress_bytes}B")

        return not_calculated_vacancies

    async def update_vacancies(self, vacancies: list[int]):
        current_datetime = datetime.now(tz=timezone.utc)

        await self.session.execute(
            sa.update(m.Vacancy)
            .where(m.Vacancy.id.in_(vacancies))
            .values(
                {
                    m.Vacancy.matches_last_calculated_at: current_datetime,
                    m.Vacancy.updated_at: current_datetime,
                }
            )
        )

    async def update_job_seekers(self, job_seekers):
        current_datetime = datetime.now(tz=timezone.utc)

        await self.session.execute(
            sa.update(m.JobSeeker)
            .where(m.JobSeeker.id.in_(job_seekers))
            .values(
                {
                    m.JobSeeker.matches_last_calculated_at: current_datetime,
                    m.JobSeeker.updated_at: current_datetime,
                }
            )
        )

    async def delete_vacancy_scores(self, vacancy_id):
        cte = sa.select(m.VacancyMatch.vacancy_id).where(
            m.VacancyMatch.vacancy_id == vacancy_id
        )

        await self.session.execute(
            sa.delete(m.JobSeekerVacancyMatch).where(
                sa.and_(
                    m.JobSeekerVacancyMatch.vacancy_id == vacancy_id,
                    m.JobSeekerVacancyMatch.job_seeker_id.not_in(cte),
                )
            )
        )

    async def delete_job_seeker_scores(self, job_seeker_id: int):
        cte = sa.select(m.VacancyMatch.vacancy_id).where(
            m.VacancyMatch.job_seeker_id == job_seeker_id
        )

        await self.session.execute(
            sa.delete(m.JobSeekerVacancyMatch).where(
                sa.and_(
                    m.JobSeekerVacancyMatch.job_seeker_id == job_seeker_id,
                    m.JobSeekerVacancyMatch.vacancy_id.not_in(cte),
                )
            )
        )

    async def get_uncalculated_vacancy_job_pairs(self):
        pairs = set()
        new_js = await self.get_new_job_seekers()
        if new_js:
            await self.session.execute(
                sa.delete(m.JobSeekerVacancyMatch).where(
                    m.JobSeekerVacancyMatch.job_seeker_id.in_(new_js)
                )
            )
            vacancies_ids = list(
                await self.session.scalars(
                    sa.select(m.Vacancy.id).where(
                        sa.and_(
                            m.Vacancy.status == "OPENED", m.Vacancy.title.isnot(None)
                        )
                    )
                )
            )
            pairs.update([(v_id, j_id) for j_id in new_js for v_id in vacancies_ids])

        new_v = await self.get_new_vacancies()
        if new_v:
            await self.session.execute(
                sa.delete(m.JobSeekerVacancyMatch).where(
                    m.JobSeekerVacancyMatch.vacancy_id.in_(new_v)
                )
            )
            job_seeker_ids = list(
                await self.session.scalars(
                    sa.select(m.JobSeeker.id).where(
                        sa.and_(
                            m.JobSeeker.resume_path.isnot(None),
                            m.JobSeeker.is_active,
                        )
                    )
                )
            )
            pairs.update([(v_id, j_id) for j_id in job_seeker_ids for v_id in new_v])
        return pairs

    async def get_uncalculated_pairs(self) -> (list, dict, dict):
        new_pairs = await self.get_uncalculated_vacancy_job_pairs()
        vacancy_titles = dict(
            (
                await self.session.execute(
                    sa.select(m.Vacancy.id, m.Vacancy.title).where(
                        m.Vacancy.id.in_([n[0] for n in new_pairs])
                    )
                )
            ).fetchall()
            or []
        )

        job_seekers = (
            await self.session.execute(
                sa.select(
                    m.JobSeeker.id,
                    m.JobSeeker.job_title,
                    sa.funcfilter(
                        sa.func.array_agg(m.JobSeekerWorkExperience.job_title),
                        m.JobSeekerWorkExperience.job_title.isnot(None),
                    ).label("job_titles"),
                )
                .select_from(
                    sa.outerjoin(
                        m.JobSeeker,
                        m.JobSeekerWorkExperience,
                        m.JobSeeker.id == m.JobSeekerWorkExperience.job_seeker_id,
                    )
                )
                .group_by(m.JobSeeker.id)
                .where(m.JobSeeker.id.in_([p[1] for p in new_pairs]))
            )
        ).fetchall()

        job_seeker_titles = {}
        for row in job_seekers:
            job_seeker_titles[row.id] = [row.job_title] if row.job_title else []
            if row.job_titles:
                job_seeker_titles[row.id].extend(row.job_titles)

        return new_pairs, vacancy_titles, job_seeker_titles

    async def insert_new_pairs(self, pairs):
        for i in range(0, len(pairs), 10000):
            await self.session.execute(
                sa.insert(m.JobSeekerVacancyMatch).values(
                    pairs[i : i + 10000],
                )
            )

    async def update_raw_match(self, match_id):
        await self.session.execute(
            sa.update(m.JobSeekerVacancyMatch)
            .values({m.JobSeekerVacancyMatch.final_score_below_threshold: True})
            .where(m.JobSeekerVacancyMatch.id == match_id)
        )
