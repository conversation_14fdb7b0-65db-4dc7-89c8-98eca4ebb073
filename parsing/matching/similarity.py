import multiprocessing

from scipy.spatial.distance import cosine
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_distances
from config import log
import numpy as np

import models as m

# model = SentenceTransformer('paraphrase-mpnet-base-v2')
model = SentenceTransformer("bert-base-nli-mean-tokens")
num_workers = min(4, multiprocessing.cpu_count())


def semantic_similarity_bert(str1, str2):
    embedding1, embedding2 = model.encode(
        [str1, str2],
        batch_size=32,
        num_workers=num_workers,
        convert_to_numpy=True,
        device="cpu",
    )

    result = cosine(embedding1, embedding2)

    return 1 - result


def semantic_similarity_bert_batch_vectors(vec1: str, vec_list: list) -> float:
    distances = cosine_distances(vec1, vec_list)

    similarities = 1 - distances[0]

    return round(float(np.max(similarities)), 6)


def semantic_similarity_bert_batch(str1: str, str_list: list[str]) -> float:
    embeddings = model.encode(
        [str1] + str_list,
        batch_size=32,
        num_workers=num_workers,
        convert_to_numpy=True,
        device="cpu",
    )

    vec1 = embeddings[0].reshape(1, -1)
    vec_others = embeddings[1:]

    distances = cosine_distances(vec1, vec_others)

    similarities = 1 - distances[0]

    return round(float(np.max(similarities)), 6)


def find_semantic_similarity(
    vacancy_job_title: str, seeker_job_titles: list[str]
) -> float:
    log.info("Starting semantic similarity calculation...")

    if not vacancy_job_title or not seeker_job_titles:
        return 0.0

    best_score = semantic_similarity_bert_batch(vacancy_job_title, seeker_job_titles)

    if best_score == 0 and any(seeker_job_titles) and vacancy_job_title is not None:
        raise Exception(f"Warning: {best_score} detected")

    log.info("Finished semantic similarity calculation...")

    return best_score


def get_raw_scores(vacancies: dict, job_seekers: dict, vacancy_job_seekers_mapping):
    vacancies_embedded = dict(
        zip(
            vacancies.keys(),
            model.encode(
                list(vacancies.values()),
                batch_size=32,
                num_workers=num_workers,
                convert_to_numpy=True,
                device="cpu",
            ),
        )
    )
    job_seekers_embedded = {
        j_id: model.encode(
            job_titles,
            batch_size=32,
            num_workers=num_workers,
            convert_to_numpy=True,
            device="cpu",
        )
        for j_id, job_titles in job_seekers.items()
    }

    scores_to_insert = []
    for vacancy_id, job_seeker_id in vacancy_job_seekers_mapping:
        match_score = 0
        vacancy_embedding = vacancies_embedded[vacancy_id]
        job_seeker_embeddings = job_seekers_embedded[job_seeker_id]
        if job_seeker_embeddings.size > 0:
            match_score = semantic_similarity_bert_batch_vectors(
                vacancy_embedding.reshape(1, -1), job_seeker_embeddings
            )
        scores_to_insert.append(
            {
                m.JobSeekerVacancyMatch.job_seeker_id: job_seeker_id,
                m.JobSeekerVacancyMatch.vacancy_id: vacancy_id,
                m.JobSeekerVacancyMatch.match_score: match_score,
            }
        )

    return scores_to_insert
