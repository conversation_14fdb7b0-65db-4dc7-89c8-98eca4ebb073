[default]

    AZURE_FORM_RECOGNIZER_ENDPOINT = ""
    AZURE_FORM_RECOGNIZER_KEY = ""
    AZURE_OPENAI_API_KEY = ""
    AZURE_OPENAI_ENDPOINT = ""
    AZURE_DEPLOYMENT = ""

    DATABASE_PORT = ""
    DATABASE_USER = ""
    DATABASE_DB = ""
    DATABASE_PASSWORD = ""
    DATABASE_HOST = ""

    LOG_TO_SENTRY = ""
    MIN_SCORE = 0.01

[test]
    AZURE_FORM_RECOGNIZER_ENDPOINT = ""
    AZURE_FORM_RECOGNIZER_KEY = ""
    AZURE_OPENAI_API_KEY = ""
    AZURE_OPENAI_ENDPOINT = ""
    AZURE_DEPLOYMENT = ""

    DATABASE_PORT = ""
    DATABASE_USER = ""
    DATABASE_DB = ""
    DATABASE_PASSWORD = ""
    DATABASE_HOST = ""
    MIN_SCORE = 0.2

[global]
    PROJECT_NAME = 'astrala-parsing'
    API_TOKEN = 'J4s9XvB2LmQyLT2AY3NWB8CA74B1GD0x'
    SENTRY_URL = "https://<EMAIL>/4508960938393600"
    DEFAULT_STATUS = "PENDING"
    LOG_TO_SENTRY = true
    JOB_EXPERIENCE_MATCH_MIN_SCORE = 0.5
    NEW_MATCHES_LIMIT = 10
