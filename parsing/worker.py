import asyncio
from asyncio import sleep
import time
from datetime import datetime, timedelta

from sentry_sdk import capture_exception
from sqlalchemy.ext.asyncio import AsyncSession
from config import log
from db import persistent_engine
from matching.generator import MatchesGenerator
from matching.query_helper import SqlQueryHelper
from matching.similarity import get_raw_scores

TOTAL_BYTES = 0
LAST_TIME_LOGGED = datetime.now()


async def find_new_matches_(
    session: AsyncSession, vacancy_ids, job_seeker_ids, job_seeker_vacancy_pairs
):
    global TOTAL_BYTES
    global LAST_TIME_LOGGED
    log.info("[Matching] Start checking new matches")

    mg = MatchesGenerator(session)
    await mg.qh.insert_new_pairs(job_seeker_vacancy_pairs)
    await mg.qh.update_vacancies(vacancy_ids)
    await mg.qh.update_job_seekers(job_seeker_ids)

    new_vacancies = await mg.calculate_new_vacancies()
    new_job_seekers = await mg.calculate_new_job_seekers()
    await mg.recalculate_vacancies()
    await mg.recalculate_job_seekers()

    TOTAL_BYTES += mg.qh.total_egress_bytes
    log.info(
        f"[Matching] Total egress bytes for the last function call {mg.qh.total_egress_bytes}B"
    )

    if datetime.now() - LAST_TIME_LOGGED >= timedelta(minutes=60):
        log.info(f"[Matching] Total egress bytes for the last hour {TOTAL_BYTES}B")
        TOTAL_BYTES = 0
        LAST_TIME_LOGGED = datetime.now()

    return sum([len(new_vacancies), len(new_job_seekers)])


async def find_new_matches():
    async with AsyncSession(persistent_engine) as session:
        async with session.begin():
            log.info("[Matching] Start searching for new pairs")
            qh = SqlQueryHelper(session)
            pairs, vacancies, job_seekers = await qh.get_uncalculated_pairs()

    log.info("[Matching] Start calculating new pairs")
    job_seeker_vacancy_scores = get_raw_scores(vacancies, job_seekers, pairs)
    log.info("[Matching] Finished calculating new pairs")
    async with AsyncSession(persistent_engine) as session:
        async with session.begin():
            await find_new_matches_(
                session, vacancies.keys(), job_seekers.keys(), job_seeker_vacancy_scores
            )


async def main():
    no_results_delay = 5  # seconds
    result = await find_new_matches()
    log.info(result)
    if not result:
        log.info(f"[Matching] Sleeping {no_results_delay} seconds")
        await sleep(no_results_delay)
    return result


if __name__ == "__main__":
    log.info("[Matching] Start worker")
    delay = 2  # seconds

    while True:
        # Path(settings.MS_ALIVE_FILE_PATH).touch()
        try:
            log.info("[Matching] running script")
            start_time = time.time()
            asyncio.run(main())
            end_time = time.time()
            log.info(f"[Matching] total time: {end_time - start_time:.2f} seconds")
        except Exception as e:
            log.exception("[Matching] Crashed!")
            capture_exception(e)
            time.sleep(delay)
