from sqlalchemy.ext.asyncio import AsyncSession
import sqlalchemy as sa
import models as m


async def get_db_skills(session: AsyncSession):
    skills = dict(
        (
            await session.execute(
                sa.select(
                    m.Skill.name,
                    m.Skill.id,
                )
            )
        ).fetchall()
    )
    return skills


async def get_db_certifications(session: AsyncSession):
    certifications = dict(
        (
            await session.execute(
                sa.select(
                    m.Certification.name,
                    m.Certification.id,
                )
            )
        ).fetchall()
    )
    return certifications
