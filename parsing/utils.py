import asyncio
import io
import time
from typing import IO, Optional

import requests
from aiohttp import ClientSession
from docx import Document
from azure.ai.formrecognizer.aio import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from sqlalchemy.ext.asyncio import AsyncSession

from config import log, settings
from openai import AsyncAzureOpenAI, RateLimitError
import filetype

from api.exceptions import BadRequest
from api.schema import get_model_for_ai

client = AsyncAzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version="2024-10-21",
    azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
    azure_deployment=settings.AZURE_DEPLOYMENT,
)


async def test_parse_file(session: AsyncSession, filepath: str):
    text = await extract_data_from_file(filepath)
    return await parse_extracted_text(session, text)


async def parse_cv(session: AsyncSession, link: str):
    text = await get_text_from_presigned_link(link)
    return await parse_extracted_text(session, text)


async def parse_extracted_text(session: AsyncSession, text: str):
    try:
        response_model = await get_model_for_ai(session)
        completion = await client.beta.chat.completions.parse(
            model="gpt-4o-2024-08-06",
            messages=[
                {
                    "role": "system",
                    "content": "Extract the cv information. Skills should all present in the text",
                },
                {"role": "user", "content": text},
            ],
            temperature=0,
            response_format=response_model,
        )

        event = completion.choices[0].message.parsed
        return event
    except Exception as e:
        log.exception(e)
        raise BadRequest(f"error while downloading file: {e}")


async def get_text_from_presigned_link(link: str) -> str:
    try:
        head_response = requests.head(link)
        content_type = head_response.headers.get("Content-Type", "unknown")

        file = await download_file_from_presigned_link(link)
    except Exception as e:
        log.exception(e)
        raise BadRequest(f"error while downloading file: {e}")

    if content_type == "application/pdf":
        return await extract_text_from_pdf(file)
    elif (
        content_type
        == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ):
        return extract_text_from_docx(file)
    else:
        raise BadRequest(f"Unsupported content type: {content_type}")


async def download_file_from_presigned_link(link: str) -> io.BytesIO:
    async with ClientSession() as session:
        async with session.get(link) as response:
            response.raise_for_status()
            file_bytes = await response.read()

    return io.BytesIO(file_bytes)


async def extract_data_from_file(filepath: str) -> Optional[str]:
    with open(filepath, "rb") as f:
        file_signature = filetype.guess(f.read(2048))
        f.seek(0)

        if not file_signature:
            raise BadRequest("Unsupported filetype")
        elif file_signature.extension == "docx":
            return extract_text_from_docx(f)
        elif file_signature.extension == "pdf":
            return await extract_text_from_pdf(f)
        else:
            raise BadRequest("Unsupported filetype")


def extract_text_from_docx(file: IO[bytes]) -> str:
    doc = Document(file)
    text = "\n".join([para.text for para in doc.paragraphs])
    return text


async def extract_text_from_pdf(file: IO[bytes]) -> str:
    async with DocumentAnalysisClient(
        settings.AZURE_FORM_RECOGNIZER_ENDPOINT,
        AzureKeyCredential(settings.AZURE_FORM_RECOGNIZER_KEY),
    ) as document_analysis_client:

        poller = await document_analysis_client.begin_analyze_document(
            "prebuilt-document", document=file
        )
        result = await poller.result()

    extracted_text = "\n".join(
        [line.content for page in result.pages for line in page.lines]
    )
    return extracted_text


async def generate_ai_match_description(
    raw_match_text: str, retries: int = 3, delay: float = 2.0
):
    for attempt in range(retries):
        try:
            start_time = time.time()

            completion = await client.beta.chat.completions.parse(
                model="gpt-4o-2024-08-06",
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "You are an HR assistant."
                            "Based on the following structured match data between a job vacancy and a candidate,"
                            " write a brief 2-sentence summary assessing the match quality.Be professional,"
                            " concise, and helpful.Include strengths and weaknesses. "
                            "Return only the two-sentence summary."
                        ),
                    },
                    {"role": "user", "content": raw_match_text},
                ],
                temperature=0.7,
            )

            event = completion.choices[0].message.content

            end_time = time.time()
            log.info(
                f"[Matching] AI request total time: {end_time - start_time:.2f} seconds"
            )

            return event

        except RateLimitError:
            if attempt < retries - 1:
                await asyncio.sleep(delay)
                continue
            else:
                log.error("Rate limit exceeded and retries exhausted.")
                raise

        except Exception as e:
            log.exception(e)
            raise BadRequest(f"Error while generating ai match description: {e}")
