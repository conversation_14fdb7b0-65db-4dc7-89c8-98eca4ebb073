import uvicorn
from fastapi import FastAPI

from api.root import root_router
from config import settings

app = FastAPI(
    title=settings.PROJECT_NAME,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

app.include_router(root_router)


if __name__ == "__main__":
    uvicorn.run(
        app, host="0.0.0.0", port=8000, log_level="info", reload=False, log_config=None
    )
