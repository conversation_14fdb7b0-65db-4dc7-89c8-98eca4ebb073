from fastapi import APIRouter, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import JSONResponse, RedirectResponse

from api.dependencies import verify_token
from api.schema import CVInfo
from db import get_session_dep
from utils import parse_cv, test_parse_file

root_router = APIRouter()


@root_router.get("/", include_in_schema=False)
def index():
    return RedirectResponse("/docs")


@root_router.get("/healthcheck", tags=["healthcheck"])
async def healthcheck():
    return JSONResponse({"status": "ok"}, status.HTTP_200_OK)


@root_router.post("/process-cv-test", dependencies=[Depends(verify_token)])
async def process_cv_test(
    filepath: str,
    db: AsyncSession = get_session_dep,
) -> CVInfo:
    return await test_parse_file(db, filepath)


@root_router.post("/process-cv", dependencies=[Depends(verify_token)])
async def process_cv(
    link: str,
    db: AsyncSession = get_session_dep,
) -> CVInfo:
    return await parse_cv(db, link)
