from fastapi import HTT<PERSON><PERSON>x<PERSON>, Security
from fastapi.security import HTTPAuthorizationCredentials, HTT<PERSON><PERSON>earer
from starlette import status

from config import settings

security = HTTPBearer()


def verify_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    if credentials.credentials != settings.API_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return True
