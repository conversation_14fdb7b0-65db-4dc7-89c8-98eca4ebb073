from enum import Enum
from typing import List, Optional, Type

from pydantic import BaseModel, Field, model_validator
from sqlalchemy.ext.asyncio import AsyncSession

from db_skills import get_db_certifications, get_db_skills


class Education(BaseModel):
    level: str = Field(
        ..., description="Education level, like masters, bachelors or school"
    )
    institution: str
    start_year: int
    end_year: Optional[int] = None
    discipline: Optional[str] = Field(..., description="Major for americans")
    comment: Optional[str] = None


class WorkExperience(BaseModel):
    company: str
    job_title: str
    start: Optional[str] = None
    end: Optional[str] = None
    start_year: int
    end_year: Optional[int] = Field(
        ..., description="Work end year. If this job has not ended yet, leave it empty"
    )
    comment: Optional[str] = None


class Language(BaseModel):
    language: str = Field(..., description="ISO 639 - 1 code")
    level: Optional[str] = None


class BaseCVInfo(BaseModel):
    full_name: Optional[str] = None
    phone: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = Field(
        default=None,
        description="ISO 3166-2 code, should be deduced from the city if country not specified",
    )
    email: Optional[str] = None
    education: Optional[List[Education]] = None
    work_experience: Optional[List[WorkExperience]] = None
    certifications: Optional[List[str]] = None
    languages: Optional[List[Language]] = None
    skill_ids: Optional[List[int]] = None
    certification_ids: Optional[List[int]] = None


class CVInfo(BaseCVInfo):
    skills: Optional[List[str]] = None


def create_dynamic_enum(name: str, skills: dict[str, int]) -> Type[Enum]:
    return Enum(name, {val: val for val in skills})


async def get_model_for_ai(session: AsyncSession) -> Type[BaseModel]:
    certifications = await get_db_certifications(session)
    skills = await get_db_skills(session)
    CertificationsEnum = create_dynamic_enum(
        "CertificationsEnum", certifications.keys()
    )
    SkillsEnum = create_dynamic_enum("SkillsEnum", skills.keys())

    class CVInfoTmp(BaseCVInfo):
        certifications: Optional[List[CertificationsEnum]] = None
        skills: Optional[List[SkillsEnum]] = None

        @model_validator(mode="after")
        def convert_skill_ids(self):
            if isinstance(self.skills, list):
                self.skill_ids = [
                    skills[skill.name] for skill in self.skills if skill.name in skills
                ]

            return self

        @model_validator(mode="after")
        def convert_certificate_ids(self):
            if isinstance(self.certifications, list):
                self.certification_ids = [
                    certifications[certification.name]
                    for certification in self.certifications
                    if certification.name in certifications
                ]

            return self

    return CVInfoTmp
