[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[[source]]
name = "pytorch-cpu"
url = "https://download.pytorch.org/whl/cpu"
verify_ssl = true

[packages]
torch = {version = "*", index = "pytorch-cpu"}
httpx = "==0.27.2"
python-docx = "*"
azure-ai-formrecognizer = "*"
openai = "*"
pdf2image = "*"
dynaconf = "*"
fastapi = "*"
uvicorn = "*"
filetype = "*"
aiohttp = "*"
asyncpg = "*"
SQLAlchemy = { extras = ["asyncio"]  }
alembic = "*"
sentry-sdk = "*"
scikit-learn = "*"
sentence-transformers = "*"
timer = "*"

[dev-packages]
pytest-asyncio = "==0.22.0"
black = "*"
pytest = "==8.1.1"

[requires]
python_version = "3.11"
