# Match Scoring Algorithm

This algorithm calculates a **match score** between a job seeker and a vacancy using semantic vector comparisons and multiple weighted compatibility criteria. The match score is based primarily on **cosine similarity** between textual representations of key attributes.

---

### 🔍 Core Matching Principle

The core compatibility is calculated from the **cosine similarity** between:

- vacancy's title 
- and either:
  - job seeker's job_title, or
  - any of job seeker's work experience job_title's (whichever yields the best similarity)

This **best cosine similarity** defines the **100% reference score**, and all weighted components contribute proportionally to the final score.

---

### 🧠 Embedding Model

We use the **[`bert-base-nli-mean-tokens`](https://www.sbert.net/docs/pretrained_models.html#sentence-embedding-models)** model from [SentenceTransformers](https://www.sbert.net/), designed for semantic textual similarity tasks.

```python
from sentence_transformers import SentenceTransformer
model = SentenceTransformer("bert-base-nli-mean-tokens")
```

This model generates high-quality sentence embeddings, allowing us to compare fields like job titles, skills, education, and more using cosine similarity.

---

### 🧮 Match Score Breakdown

| Component                    | Weight (%) | Description                                                                 |
|-----------------------------|------------|-----------------------------------------------------------------------------|
| `driving_license_match`     | 5%         | Whether the job seeker's license status matches the vacancy requirement    |
| `education_match`           | up to 10%  | Based on cosine similarity between fields of study (if specified)          |
| `skills_match`              | up to 35%  | Overlap of required and possessed skills                                   |
| `location_match`            | 10%        | Proximity or compatibility of job and candidate locations                  |
| `salary_match`              | 10%        | Alignment between expected and offered salary                              |
| `behavioural_tags_match`    | up to 10%  | Similarity between personality/behavioral tags                             |
| `work_experience_match`     | up to 20%  | Based on cosine similarity of titles and years of relevant experience      |

---

### 🎓 Education Matching Details

If both the **vacancy's required discipline** and the **job seeker's field of study** are specified, we calculate the **cosine similarity** between them using the same embedding model. The `education_match` score is then scaled accordingly:

- A **perfect discipline match** yields the full **10%**
- A **partial match** yields a proportional score (e.g. 0.7 similarity → 7%)
- If disciplines are not specified, this score will be calculated from education levels match

---

