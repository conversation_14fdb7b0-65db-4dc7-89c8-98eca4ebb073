from models.base import Model
import sqlalchemy as sa


class JobSeekerQuestionnaireAnswer(Model):
    __tablename__ = "job_seeker_questionnaire_answers"

    id = sa.Column(sa.BigInteger, primary_key=True)
    created_at = sa.Column(sa.DateTime(timezone=True))
    job_seeker_id = sa.Column(sa.BigInteger)
    updated_at = sa.Column(sa.DateTime(timezone=True), default=sa.func.now())
    question_id = sa.Column(sa.BigInteger)
    value = sa.Column(sa.BigInteger)
