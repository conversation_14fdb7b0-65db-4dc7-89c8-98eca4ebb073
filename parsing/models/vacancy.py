import sqlalchemy as sa

from models.base import Model


class Vacancy(Model):
    __tablename__ = "vacancies"

    id = sa.Column(sa.BigInteger, primary_key=True)
    created_at = sa.Column(sa.DateTime(timezone=True))
    title = sa.Column(sa.Text)
    description = sa.Column(sa.Text)
    employer_id = sa.Column(sa.BigInteger)
    driving_license = sa.Column(sa.Boolean)
    company_id = sa.Column(sa.BigInteger)
    education_degree = sa.Column(sa.Text)
    education_discipline = sa.Column(sa.Text)
    experience_years_from = sa.Column(sa.Integer)
    updated_at = sa.Column(sa.DateTime(timezone=True), default=sa.func.now())
    matches_last_calculated_at = sa.Column(sa.DateTime(timezone=True))
    country = sa.Column(sa.Text)
    city = sa.Column(sa.Text)
    status = sa.Column(sa.Text)
    salary_min = sa.Column(sa.BigInteger)
    salary_max = sa.Column(sa.BigInteger)
