from models.base import Model
import sqlalchemy as sa


class VacancyMatch(Model):
    __tablename__ = "vacancy_matches"

    id = sa.Column(sa.BigInteger, primary_key=True)
    created_at = sa.Column(sa.DateTime(timezone=True))
    job_seeker_id = sa.Column(sa.ForeignKey("job_seekers.id"))
    job_seeker_status = sa.Column(sa.Text, default="PENDING")
    employer_status = sa.Column(sa.Text)
    ai_match_score = sa.Column(sa.Float)
    vacancy_id = sa.Column(sa.ForeignKey("vacancies.id"))
    job_seeker_transaction_id = sa.Column(sa.BigInteger)
    employer_transaction_id = sa.Column(sa.BigInteger)
    updated_at = sa.Column(sa.DateTime(timezone=True))
