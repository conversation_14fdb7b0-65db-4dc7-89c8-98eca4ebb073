from models.base import Model
import sqlalchemy as sa


class VacancyMatchDescription(Model):
    __tablename__ = "vacancy_match_descriptions"

    vacancy_match_id = sa.Column(
        sa.<PERSON>ey("vacancy_matches.id", ondelete="CASCADE"), primary_key=True
    )

    ai_summary = sa.Column(sa.Text)
    driving_license_match = sa.Column(sa.Bo<PERSON>, default=False, server_default="FALSE")
    education_match = sa.Column(sa.Bo<PERSON>, default=False, server_default="FALSE")
    location_match = sa.Column(sa.Bo<PERSON>, default=False, server_default="FALSE")
    salary_match = sa.Column(sa.Bo<PERSON>, default=False, server_default="FALSE")
    work_experience_match = sa.Column(sa.Bo<PERSON>, default=False, server_default="FALSE")
    work_experience_years = sa.Column(sa.Integer)

    matching_behavioural_tags = sa.Column(sa.ARRAY(sa.Text, dimensions=1))
    lacking_behavioural_tags = sa.Column(sa.ARRAY(sa.Text, dimensions=1))
    lacking_skills = sa.Column(sa.ARRAY(sa.Text, dimensions=1))
    matching_skills = sa.Column(sa.ARRAY(sa.Text, dimensions=1))
