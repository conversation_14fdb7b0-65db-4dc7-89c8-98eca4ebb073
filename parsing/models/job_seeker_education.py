from models.base import Model
import sqlalchemy as sa


class JobSeekerEducation(Model):
    __tablename__ = "job_seeker_educations"

    id = sa.Column(sa.BigInteger, primary_key=True)
    created_at = sa.Column(sa.DateTime(timezone=True))
    type = sa.Column(sa.Text)
    institution = sa.Column(sa.Text)
    discipline = sa.Column(sa.Text)
    start_year = sa.Column(sa.BigInteger)
    end_year = sa.Column(sa.BigInteger)
    job_seeker_id = sa.Column(sa.ForeignKey("job_seekers.id"))
    updated_at = sa.Column(sa.DateTime(timezone=True))
