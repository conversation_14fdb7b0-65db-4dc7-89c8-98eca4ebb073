from models.base import Model
import sqlalchemy as sa


class JobSeekerVacancyMatch(Model):
    __tablename__ = "job_seeker_vacancy_matches"

    id = sa.Column(sa.BigInteger, primary_key=True)
    created_at = sa.Column(sa.DateTime(timezone=True))
    job_seeker_id = sa.Column(sa.ForeignKey("job_seekers.id"))
    match_score = sa.Column(sa.Float)
    vacancy_id = sa.Column(sa.ForeignKey("vacancies.id"))

    final_score_below_threshold = sa.Column(
        sa.Boolean, default=False, server_default="FALSE"
    )
