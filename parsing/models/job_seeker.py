from models.base import Model
from sqlalchemy.dialects.postgresql import UUID
import sqlalchemy as sa


class JobSeeker(Model):
    __tablename__ = "job_seekers"

    id = sa.Column(sa.BigInteger, primary_key=True)

    avatar_path = sa.Column(sa.Text)
    city = sa.Column(sa.Text)
    country = sa.Column(sa.Text)
    created_at = sa.Column(sa.DateTime(timezone=True))
    driving_license_path = sa.Column(sa.Text)
    full_name = sa.Column(sa.Text)
    job_title = sa.Column(sa.Text)
    is_active = sa.Column(sa.Boolean, default=True)
    has_driving_license = sa.Column(sa.Boolean, default=False, server_default="FALSE")
    matches_last_calculated_at = sa.Column(sa.DateTime(timezone=True))
    passport_path = sa.Column(sa.Text)
    phone = sa.Column(sa.Text)
    resume_path = sa.Column(sa.Text)
    salary_max = sa.Column(sa.BigInteger)
    salary_min = sa.Column(sa.BigInteger)
    updated_at = sa.Column(sa.DateTime(timezone=True))
    user_id = sa.Column(UUID, nullable=False)
