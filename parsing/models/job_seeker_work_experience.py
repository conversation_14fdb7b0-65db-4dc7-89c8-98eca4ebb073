from models.base import Model
import sqlalchemy as sa


class JobSeekerWorkExperience(Model):
    __tablename__ = "job_seeker_work_experience"

    id = sa.Column(sa.BigInteger, primary_key=True)
    created_at = sa.Column(sa.DateTime(timezone=True))
    company_name = sa.Column(sa.Text)
    job_title = sa.Column(sa.Text)
    start_year = sa.Column(sa.BigInteger)
    start_date = sa.Column(sa.Date)
    end_year = sa.Column(sa.BigInteger)
    end_date = sa.Column(sa.Date)
    comment = sa.Column(sa.Text)
    job_seeker_id = sa.Column(sa.BigInteger)
    updated_at = sa.Column(sa.DateTime(timezone=True))
