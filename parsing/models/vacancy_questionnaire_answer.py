from models.base import Model
import sqlalchemy as sa


class VacancyQuestionnaireAnswer(Model):
    __tablename__ = "vacancy_questionnaire_answers"

    id = sa.Column(sa.BigInteger, primary_key=True)
    created_at = sa.Column(sa.DateTime(timezone=True))
    question_id = sa.Column(sa.BigInteger)
    value = sa.Column(sa.BigInteger)
    vacancy_id = sa.Column(sa.BigInteger)
    updated_at = sa.Column(sa.DateTime(timezone=True))
