from models.base import Model
import sqlalchemy as sa


class JobSeekerSkill(Model):
    __tablename__ = "job_seeker_skills"

    id = sa.Column(sa.BigInteger, primary_key=True)
    created_at = sa.Column(sa.DateTime(timezone=True))
    skill_id = sa.Column(sa.<PERSON>ey("skills.id"))
    job_seeker_id = sa.Column(sa.ForeignKey("job_seekers.id"))
    updated_at = sa.Column(sa.DateTime(timezone=True))
