steps:
  - script: |
      curl -fsSL https://get.pulumi.com/ | sh \
        && echo "##vso[task.prependpath]$HOME/.pulumi/bin"
    displayName: 'Install Pulumi'

  - script: pulumi login
    displayName: 'Login to Pulumi'
    env:
      PULUMI_ACCESS_TOKEN: $(PULUMI_ACCESS_TOKEN)

  - script: curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
    displayName: 'Install Azure CLI'

  - script: |
      curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh | bash \
        && export NVM_DIR="$HOME/.nvm" \
        && \. "$NVM_DIR/nvm.sh" --no-use \
        && nvm install \
        && nvm use \
        && echo "##vso[task.prependpath]$(dirname "$(which node)")"
    displayName: 'Install Node.js'

  - script: |
      npm i
    displayName: 'Install npm dependencies'
