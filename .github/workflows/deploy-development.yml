name: Deploy Development
on:
  push:
    branches:
      - development
jobs:
  deploy:
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
    environment:
      name: development
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Link Supabase project
        run: npx supabase link --project-ref ${{ vars.SUPABASE_PROJECT_ID }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Push Supabase config
        run: npx supabase config push --project-ref ${{ vars.SUPABASE_PROJECT_ID }}
        env:
          POSTMARK_SERVER_API: ${{ secrets.POSTMARK_SERVER_API }}
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
