name: Deploy to Development

on:
  push:
    branches:
      - development

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run Prisma migrations
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.DIRECT_URL }}

      - name: Link Supabase project
        run: npx supabase link --project-ref ${{ vars.SUPABASE_PROJECT_ID }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Push Supabase config
        run: npx supabase config push --project-ref ${{ vars.SUPABASE_PROJECT_ID }}
        env:
          SITE_URL: ${{ vars.SITE_URL }}
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Deploy to DigitalOcean App Platform
        run: |
          # Update app spec with environment variables
          cat .do/app.development.yaml | \
            sed "s|\${DATABASE_URL}|${{ secrets.DATABASE_URL }}|g" | \
            sed "s|\${OPP_API_KEY}|${{ secrets.OPP_API_KEY }}|g" | \
            sed "s|\${OPP_API_URL}|${{ vars.OPP_API_URL }}|g" | \
            sed "s|\${OPP_FILES_API_URL}|${{ vars.OPP_FILES_API_URL }}|g" | \
            sed "s|\${OPP_SANDBOX_COMPLIANCE_STATUS_CHANGE_ENABLED}|\"true\"|g" | \
            sed "s|\${OPP_WEBHOOK_SECRET}|${{ secrets.OPP_WEBHOOK_SECRET }}|g" | \
            sed "s|\${OPP_WEBHOOK_SECRET}|${{ secrets.OPP_WEBHOOK_SECRET }}|g" | \
            sed "s|\${REPO_NAME}|${{ github.event.repository.name }}|g" | \
            sed "s|\${REPO_OWNER}|${{ github.repository_owner }}|g" | \
            sed "s|\${REPO_PASSWORD}|${{ secrets.GH_ACCESS_TOKEN }}|g" | \
            sed "s|\${REPO_USERNAME}|${{ github.sha }}|g" | \
            sed "s|\${RESEND_API_KEY}|${{ secrets.RESEND_API_KEY }}|g" | \
            sed "s|\${RESEND_API_KEY}|${{ secrets.RESEND_API_KEY }}|g" | \
            sed "s|\${SENTRY_AUTH_TOKEN}|${{ secrets.SENTRY_AUTH_TOKEN }}|g" | \
            sed "s|\${SENTRY_DSN}|${{ vars.SENTRY_DSN }}|g" | \
            sed "s|\${SENTRY_ENVIRONMENT}|${{ vars.SENTRY_ENVIRONMENT }}|g" | \
            sed "s|\${SENTRY_ORG}|${{ vars.SENTRY_ORG }}|g" | \
            sed "s|\${SENTRY_PROJECT}|${{ vars.SENTRY_PROJECT }}|g" | \
            sed "s|\${SUPABASE_SECRET_KEY}|${{ secrets.SUPABASE_SECRET_KEY }}|g" | \
            sed "s|\${SUPABASE_URL}|${{ vars.SUPABASE_URL }}|g" \
            > app-spec.yaml

          # Deploy or update the app
          if doctl apps list | grep -q "renopay-development"; then
            APP_ID=$(doctl apps list --format ID,Spec.Name --no-header | grep renopay-development | awk '{print $1}')
            doctl apps update $APP_ID --spec app-spec.yaml --wait
          else
            doctl apps create --spec app-spec.yaml --wait
          fi
