/* eslint-disable */
const { spawn } = require("child_process");
const fs = require("fs");
const path = require("path");

const PORT = process.env.PORT || 3000;
const ENV_FILEPATH = path.join(__dirname, "..", ".env");

async function main() {
  const subdomain = getOrCreateSubdomain();
  const lt = spawn("npx", ["lt", `--port=${PORT}`, `--subdomain=${subdomain}`]);

  let urlFound = false;
  let healthCheckInterval = null;

  lt.stdout.on("data", (data) => {
    const output = data.toString();

    const match = output.match(/your url is:\s*(https:\/\/[^\s]+)/i);
    if (match && !urlFound) {
      const url = match[1];
      urlFound = true;
      updateEnvFile("LOCALTUNNEL_SITE_URL", url);

      healthCheckInterval = setInterval(() => {
        fetch(`${url}/api/healthcheck`)
          .then((r) => {
            if (!r.ok) {
              throw new Error("LocalTunnel has died...");
            }
            return r.json();
          })
          .catch((error) => {
            console.error(error);
          });
      }, 10000);
    }
  });

  lt.stderr.on("data", (data) => {
    console.error(data.toString().trim());
  });

  lt.on("close", (code) => {
    if (healthCheckInterval) {
      clearInterval(healthCheckInterval);
    }
    if (code !== null) {
      main();
    }
  });

  process.on("SIGINT", () => {
    if (healthCheckInterval) {
      clearInterval(healthCheckInterval);
    }
    lt.kill();
  });
}

function getOrCreateSubdomain() {
  let envContent = fs.readFileSync(ENV_FILEPATH, "utf-8");
  const lines = envContent.split("\n");

  for (const line of lines) {
    if (line.startsWith("LOCALTUNNEL_SUBDOMAIN=")) {
      const subdomain = line.split("=")[1].trim();
      if (subdomain) {
        return subdomain;
      }
    }
  }

  const subdomain = generateRandomSubdomain();
  updateEnvFile("LOCALTUNNEL_SUBDOMAIN", subdomain);
  return subdomain;
}

function generateRandomSubdomain() {
  const chars = "abcdefghijklmnopqrstuvwxyz0123456789";
  let result = "popugaj-";
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function updateEnvFile(variable, value) {
  let envContent = fs.readFileSync(ENV_FILEPATH, "utf-8");
  const lines = envContent.split("\n");
  let updated = false;

  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith(`${variable}=`)) {
      lines[i] = `${variable}=${value}`;
      updated = true;
      break;
    }
  }

  if (!updated) {
    if (envContent && !envContent.endsWith("\n")) {
      lines.push("");
    }
    lines.push(`${variable}=${value}`);
  }

  fs.writeFileSync(ENV_FILEPATH, lines.join("\n"));
}

main();
