{"name": "@astrala/cron", "version": "1.0.0", "description": "Astrala cron jobs service", "type": "module", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "typecheck": "tsc --noEmit", "lint": "npm run typecheck && eslint . --ext .ts,.js"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "node-cron": "^3.0.3", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "eslint": "^9.33.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}