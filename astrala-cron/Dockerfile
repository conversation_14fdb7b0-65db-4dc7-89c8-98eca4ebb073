FROM node:20.18-alpine AS base

FROM base AS deps
RUN apk add --update --no-cache libc6-compat && rm -rf /var/cache/apk/*

WORKDIR /app

COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
COPY astrala-cron/package.json ./astrala-cron/
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i; \
  else echo "Lockfile not found." && exit 1; \
  fi

FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/astrala-cron/node_modules* ./astrala-cron/node_modules
COPY . .

WORKDIR /app/astrala-cron
RUN npm run build

FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
RUN addgroup -g 1001 -S nodejs
RUN adduser -S cronuser -u 1001

COPY --from=builder --chown=cronuser:nodejs /app/astrala-cron/dist ./astrala-cron/dist
COPY --from=builder --chown=cronuser:nodejs /app/astrala-cron/package.json ./astrala-cron/package.json
COPY --from=deps --chown=cronuser:nodejs /app/astrala-cron/node_modules ./astrala-cron/node_modules
COPY --from=deps --chown=cronuser:nodejs /app/node_modules ./node_modules
COPY --from=deps --chown=cronuser:nodejs /app/package.json ./package.json

USER cronuser
CMD ["node", "astrala-cron/dist/index.js"]
