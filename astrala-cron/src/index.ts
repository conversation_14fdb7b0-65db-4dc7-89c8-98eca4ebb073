import * as cron from 'node-cron';
// import { sendBiweeklyNotifications } from './jobs/biweekly-notifications';
import { sendOnboardingNudgeEmails } from './jobs/onboarding-nudge.js';
import { sendProfileUpdateNudgeEmails } from './jobs/profile-update-nudge.js';
import { sendOpenVacancyNudgeEmails } from './jobs/open-vacancy-nudge.js';
import { sendSurveyEmails } from './jobs/survey-emails.js';

console.log('Astrala Cron Service start');

// cron.schedule(
//   '0 9 */5 * *',
//   async () => {
//     console.log('📧 Starting biweekly notifications job...');

//     try {
//       await sendBiweeklyNotifications();
//       console.log('Notifications completed successfully');
//     } catch (error) {
//       console.error('Notifications failed:', error);
//     }
//   },
//   {
//     timezone: 'UTC',
//   },
// );

cron.schedule(
  '*/10 * * * *',
  async () => {
    console.log('📧 Starting onboarding nudge emails job...');

    try {
      await sendOnboardingNudgeEmails();
      console.log('Onboarding nudge emails completed successfully');
    } catch (error) {
      console.error('Onboarding nudge emails failed:', error);
    }
  },
  {
    timezone: 'UTC',
  },
);

cron.schedule(
  '*/10 * * * *',
  async () => {
    console.log('📧 Starting profile update nudge emails job...');

    try {
      await sendProfileUpdateNudgeEmails();
      console.log('Profile update nudge emails completed successfully');
    } catch (error) {
      console.error('Profile update nudge emails failed:', error);
    }
  },
  {
    timezone: 'UTC',
  },
);

cron.schedule(
  '*/10 * * * *',
  async () => {
    console.log('📧 Starting open vacancy nudge emails job...');

    try {
      await sendOpenVacancyNudgeEmails();
      console.log('Open vacancy nudge emails completed successfully');
    } catch (error) {
      console.error('Open vacancy nudge emails failed:', error);
    }
  },
  {
    timezone: 'UTC',
  },
);

cron.schedule(
  '*/5 * * * *',
  async () => {
    console.log('📧 Starting survey emails job...');

    try {
      await sendSurveyEmails();
      console.log('Survey emails completed successfully');
    } catch (error) {
      console.error('Survey emails failed:', error);
    }
  },
  {
    timezone: 'UTC',
  },
);

process.on('SIGINT', () => {
  process.exit(0);
});
