import { supabase } from '../lib/supabase.js';
import { sendEmail, composeEmailFromTemplate } from '../lib/email.js';

export async function sendSurveyEmails() {
  console.log('📧 Starting survey emails job...');

  const { data: surveys, error: surveysError } = await supabase
    .from('surveys')
    .select('*')
    .order('created_at', { ascending: true });

  if (surveysError) {
    throw new Error(`Failed to fetch surveys: ${surveysError.message}`);
  }

  if (!surveys || surveys.length === 0) {
    console.log('📊 No surveys found to process');
    return;
  }

  console.log(`📊 Found ${surveys.length} surveys to process`);

  let totalEmailsSent = 0;
  let totalErrors = 0;

  for (const survey of surveys) {
    try {
      console.log(`📧 Processing survey: ${survey.title} (ID: ${survey.id})`);
      
      let receivers: Array<{ email: string; full_name: string }> = [];

      if (survey.audience === 'Employee') {
        const { data: employers, error: employersError } = await supabase
          .from('employers')
          .select('email, full_name')
          .not('email', 'is', null);

        if (employersError) {
          console.error(`❌ Failed to fetch employers for survey ${survey.id}: ${employersError.message}`);
          totalErrors++;
          continue;
        }

        receivers = employers || [];
        console.log(`📊 Found ${receivers.length} employers to send survey to`);
      } else if (survey.audience === 'JobSeeker') {
        const { data: jobSeekers, error: jobSeekersError } = await supabase
          .from('job_seekers')
          .select('email, full_name')
          .eq('is_active', true)
          .not('email', 'is', null);

        if (jobSeekersError) {
          console.error(`❌ Failed to fetch job seekers for survey ${survey.id}: ${jobSeekersError.message}`);
          totalErrors++;
          continue;
        }

        receivers = jobSeekers || [];
        console.log(`📊 Found ${receivers.length} job seekers to send survey to`);
      }

      let surveyEmailsSent = 0;
      let surveyEmailErrors = 0;

      for (const receiver of receivers) {
        try {
          await sendSurveyEmail(
            receiver.email,
            survey.title,
            survey.text,
            survey.button_label,
            survey.button_url
          );
          surveyEmailsSent++;
          totalEmailsSent++;
        } catch (error) {
          console.error(`❌ Failed to send survey email to ${receiver.email}: ${error}`);
          surveyEmailErrors++;
          totalErrors++;
        }
      }

      console.log(`📊 Survey ${survey.id}: ${surveyEmailsSent} emails sent, ${surveyEmailErrors} errors`);

      const { error: deleteError } = await supabase
        .from('surveys')
        .delete()
        .eq('id', survey.id);

      if (deleteError) {
        console.error(`❌ Failed to delete survey ${survey.id}: ${deleteError.message}`);
        totalErrors++;
      } else {
        console.log(`✅ Successfully deleted survey ${survey.id} from database`);
      }

    } catch (error) {
      console.error(`❌ Failed to process survey ${survey.id}: ${error}`);
      totalErrors++;
    }
  }

  console.log(`📊 Survey emails job completed: ${totalEmailsSent} emails sent, ${totalErrors} errors`);
}

async function sendSurveyEmail(
  email: string,
  heading: string,
  body: string,
  ctaTitle: string,
  ctaLink: string
) {
  const textBody = body.trim();
  const htmlBody = composeEmailFromTemplate({
    heading: heading,
    body: textBody,
    cta: ctaTitle,
    ctaLink: ctaLink,
  });

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject: heading,
    htmlBody,
    textBody,
    tag: 'Survey',
  });

  if (result.success) {
    console.log(`✅ Sent survey email to ${email}`);
  } else {
    throw new Error(`Failed to send email: ${result.error}`);
  }
}
