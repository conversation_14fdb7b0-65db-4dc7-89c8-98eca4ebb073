import { supabase } from '../lib/supabase.js';
import { sendEmail, composeEmailFromTemplate } from '../lib/email.js';
import { assert } from 'console';

export async function sendOnboardingNudgeEmails() {
  const siteUrl = process.env.SITE_URL!;

  assert(siteUrl, 'SITE_URL env variable should be defined');
  
  const { data: inactiveJobSeekers, error } = await supabase
    .from('job_seekers')
    .select('id, email, full_name, created_at')
    .eq('onboarding_completed', false)
    .is('onboarding_nudge_sent_at', null)
    .not('email', 'is', null)
    .lt('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

  if (error) {
    throw new Error(`Failed to fetch inactive job seekers: ${error.message}`);
  }

  if (!inactiveJobSeekers || inactiveJobSeekers.length === 0) {
    console.log('📊 No inactive job seekers found who need onboarding nudge emails');
    return;
  }

  console.log(
    `📊 Found ${inactiveJobSeekers.length} inactive job seekers who need onboarding nudge emails`,
  );

  let successCount = 0;
  let errorCount = 0;

  for (const jobSeeker of inactiveJobSeekers) {
    try {
      // Send the onboarding nudge email
      await sendOnboardingNudgeEmail(
        jobSeeker.email,
        jobSeeker.full_name || 'there',
        siteUrl,
      );

      // Update the database to mark that we sent the nudge email
      const { error: updateError } = await supabase
        .from('job_seekers')
        .update({ onboarding_nudge_sent_at: new Date().toISOString() })
        .eq('id', jobSeeker.id);

      if (updateError) {
        console.error(
          `❌ Failed to update onboarding_nudge_sent_at for job seeker ${jobSeeker.id}: ${updateError.message}`,
        );
        errorCount++;
      } else {
        successCount++;
      }
    } catch (error) {
      console.error(
        `❌ Failed to send onboarding nudge email to ${jobSeeker.email}: ${error}`,
      );
      errorCount++;
    }
  }

  console.log(
    `📧 Onboarding nudge emails completed: ${successCount} sent successfully, ${errorCount} failed`,
  );
}

async function sendOnboardingNudgeEmail(
  email: string,
  name: string,
  siteUrl: string,
) {
  const subject = 'Unlock your symbolic behavioural profile — and explore aligned opportunities';
  const textBody = `
    Hi ${name}!

    We’re excited to help you find opportunities that genuinely align with how you work, lead, and thrive.

    To support this, we invite you to complete your Behavioural Insight Profile - a short discovery process that helps employers see beyond your CV and recognise your natural strengths.

    It only takes a few minutes and will enrich your profile and matching opportunities.

    Warm Regards,
    AstralaNexus
  `.trim();
  const htmlBody = composeEmailFromTemplate({
    heading: subject,
    body: textBody,
    cta: 'Begin My Behavioural Insights Journey',
    ctaLink: siteUrl,
  });

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject,
    htmlBody,
    textBody,
    tag: 'OnboardingNudge',
  });

  if (result.success) {
    console.log(`✅ Sent onboarding nudge email to ${email}`);
  } else {
    throw new Error(`Failed to send email: ${result.error}`);
  }
}
