import { supabase } from '../lib/supabase.js';
import { sendEmail, composeEmailFromTemplate } from '../lib/email.js';
import { assert } from 'console';

export async function sendProfileUpdateNudgeEmails() {
  const siteUrl = process.env.SITE_URL!;
  
  assert(siteUrl, 'SITE_URL env variable should be defined');
  
  const sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString();
  
  const { data: allStaleProfileJobSeekers, error } = await supabase
    .from('job_seekers')
    .select('id, email, full_name, information_updated_at, information_update_nudge_sent_at')
    .eq('is_active', true)
    .eq('onboarding_completed', true)
    .not('email', 'is', null)
    .lt('information_updated_at', sixtyDaysAgo);

  if (error) {
    throw new Error(`Failed to fetch job seekers with stale profiles: ${error.message}`);
  }

  if (!allStaleProfileJobSeekers || allStaleProfileJobSeekers.length === 0) {
    console.log('📊 No active job seekers found with stale profiles (60+ days old)');
    return;
  }

  // Filter to only include job seekers who either:
  // 1. Have never received a nudge email, OR
  // 2. Have updated their profile since the last nudge was sent
  const staleProfileJobSeekers = allStaleProfileJobSeekers.filter(jobSeeker => {
    // If never sent a nudge, include them
    if (!jobSeeker.information_update_nudge_sent_at) return true;

    // If they updated their profile after the last nudge, include them
    const lastNudgeDate = new Date(jobSeeker.information_update_nudge_sent_at);
    const lastUpdateDate = new Date(jobSeeker.information_updated_at);
    return lastUpdateDate > lastNudgeDate;
  });

  if (!staleProfileJobSeekers || staleProfileJobSeekers.length === 0) {
    console.log('📊 No active job seekers found who need profile update nudge emails');
    return;
  }

  console.log(
    `📊 Found ${staleProfileJobSeekers.length} active job seekers who need profile update nudge emails`,
  );

  let successCount = 0;
  let errorCount = 0;

  for (const jobSeeker of staleProfileJobSeekers) {
    try {
      // Send the profile update nudge email
      await sendProfileUpdateNudgeEmail(
        jobSeeker.email,
        jobSeeker.full_name || 'there',
        siteUrl,
      );

      // Update the database to mark that we sent the nudge email
      const { error: updateError } = await supabase
        .from('job_seekers')
        .update({ information_update_nudge_sent_at: new Date().toISOString() })
        .eq('id', jobSeeker.id);

      if (updateError) {
        console.error(
          `❌ Failed to update information_update_nudge_sent_at for job seeker ${jobSeeker.id}: ${updateError.message}`,
        );
        errorCount++;
      } else {
        successCount++;
      }
    } catch (error) {
      console.error(
        `❌ Failed to send profile update nudge email to ${jobSeeker.email}: ${error}`,
      );
      errorCount++;
    }
  }

  console.log(
    `📧 Profile update nudge emails completed: ${successCount} sent successfully, ${errorCount} failed`,
  );
}

async function sendProfileUpdateNudgeEmail(
  email: string,
  name: string,
  siteUrl: string,
) {
  const subject = 'Keep your profile current - update your CV when ready';
  const textBody = `
    Hi ${name}!

    We noticed it’s been a while since you last refreshed your CV.

    When the time feels right, for example, after a project milestone or new role - you can upload an updated CV so employers can see your most recent experience alongside your symbolic behavioural profile.

    This helps ensure your skills and achievements reflect where you are today.

    Warm regards,
    AstralaNexus
  `.trim();
  const htmlBody = composeEmailFromTemplate({
    heading: subject,
    body: textBody,
    cta: 'Update My CV',
    ctaLink: `${siteUrl}/dashboard/job-seeker/settings`,
  });

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject,
    htmlBody,
    textBody,
    tag: 'ProfileUpdateNudge',
  });

  if (result.success) {
    console.log(`✅ Sent profile update nudge email to ${email}`);
  } else {
    throw new Error(`Failed to send email: ${result.error}`);
  }
}
