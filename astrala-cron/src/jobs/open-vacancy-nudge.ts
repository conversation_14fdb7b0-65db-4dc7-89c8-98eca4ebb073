import { supabase } from '../lib/supabase.js';
import { sendEmail, composeEmailFromTemplate } from '../lib/email.js';
import { assert } from 'console';

type VacancyInfo = {
  id: number;
  title: string;
  created_at: string;
  city: string;
  country: string;
};

type CompanyWithVacancies = {
  id: number;
  name: string;
  open_vacancy_nudge_sent_at: string | null;
  vacancies: VacancyInfo[];
  employers: Array<{
    email: string;
    full_name: string;
  }>;
};

export async function sendOpenVacancyNudgeEmails() {
  const siteUrl = process.env.SITE_URL!;
  
  assert(siteUrl, 'SITE_URL env variable should be defined');
  
  const sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString();

  const { data: companiesWithOldVacancies, error: vacanciesError } = await supabase
    .from('vacancies')
    .select(`
      id,
      title,
      created_at,
      city,
      country,
      company_id,
      companies!inner(
        id,
        name,
        open_vacancy_nudge_sent_at
      )
    `)
    .eq('status', 'OPENED')
    .lt('created_at', sixtyDaysAgo);

  if (vacanciesError) {
    throw new Error(`Failed to fetch companies with old vacancies: ${vacanciesError.message}`);
  }

  if (!companiesWithOldVacancies || companiesWithOldVacancies.length === 0) {
    console.log('📊 No companies found with vacancies open for 60+ days');
    return;
  }

  // Group vacancies by company
  const companiesMap = new Map<number, CompanyWithVacancies>();
  
  for (const vacancy of companiesWithOldVacancies) {
    const company = vacancy.companies as unknown as typeof companiesWithOldVacancies[number]['companies'][number];
    const companyId = company.id;
    
    if (!companiesMap.has(companyId)) {
      companiesMap.set(companyId, {
        id: companyId,
        name: company.name,
        open_vacancy_nudge_sent_at: company.open_vacancy_nudge_sent_at,
        vacancies: [],
        employers: []
      });
    }
    
    companiesMap.get(companyId)!.vacancies.push({
      id: vacancy.id,
      title: vacancy.title,
      created_at: vacancy.created_at,
      city: vacancy.city,
      country: vacancy.country,
    });
  }

  for (const [companyId, companyData] of companiesMap) {
    const { data: employers, error: employersError } = await supabase
      .from('employers')
      .select('email, full_name')
      .eq('company_id', companyId)
      .not('email', 'is', null);

    if (employersError) {
      console.error(`❌ Failed to fetch employers for company ${companyId}: ${employersError.message}`);
      continue;
    }

    if (employers && employers.length > 0) {
      companyData.employers = employers;
    }
  }

  const companiesWithEmployers = Array.from(companiesMap.values()).filter(
    company => {
      if (company.employers.length === 0) return false;

      if (!company.open_vacancy_nudge_sent_at) return true;

      const lastNotified = new Date(company.open_vacancy_nudge_sent_at);
      const twoWeeksAgoDate = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000);
      return lastNotified < twoWeeksAgoDate;
    }
  );

  console.log(
    `📊 Found ${companiesWithEmployers.length} companies with old vacancies`,
  );

  let successCount = 0;
  let errorCount = 0;

  for (const company of companiesWithEmployers) {
    try {
      // Send emails to all employers of this company
      for (const employer of company.employers) {
        await sendOpenVacancyNudgeEmail(
          employer.email,
          employer.full_name || 'there',
          company.name,
          company.vacancies,
          siteUrl,
        );
      }

      // Update the database to mark that we sent the nudge email to this company
      const { error: updateError } = await supabase
        .from('companies')
        .update({ open_vacancy_nudge_sent_at: new Date().toISOString() })
        .eq('id', company.id);

      if (updateError) {
        console.error(
          `❌ Failed to update open_vacancy_nudge_sent_at for company ${company.id}: ${updateError.message}`,
        );
        errorCount++;
      } else {
        successCount++;
        console.log(`✅ Sent open vacancy nudge emails to ${company.employers.length} employers at ${company.name}`);
      }
    } catch (error) {
      console.error(
        `❌ Failed to send open vacancy nudge emails to company ${company.name}: ${error}`,
      );
      errorCount++;
    }
  }

  console.log(
    `📧 Open vacancy nudge emails completed: ${successCount} companies notified successfully, ${errorCount} failed`,
  );
}

async function sendOpenVacancyNudgeEmail(
  email: string,
  name: string,
  companyName: string,
  vacancies: VacancyInfo[],
  siteUrl: string,
) {
  const subject = 'Are your vacancies still live?';
  const textBody = `
    Hi ${name}!

    We noticed that some of your vacancies has been live for over 60 days:
    ${vacancies.map((vacancy) => `– ${vacancy.title} in ${vacancy.city}, ${vacancy.country}`).join("\n")}

    To help us maintain meaningful alignment between candidates and roles, could you confirm whether this position is still active?

    This ensures we continue presenting the right opportunities to the right people.

    Warm regards,
    AstralaNexus
  `.trim();
  const htmlBody = composeEmailFromTemplate({
    heading: subject,
    body: textBody,
    cta: 'Review Your Vacancies',
    ctaLink: `${siteUrl}/dashboard/employer/vacancies`,
  });

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject,
    htmlBody,
    textBody,
    tag: 'OpenVacancyNudge',
  });

  if (result.success) {
    console.log(`✅ Sent open vacancy nudge email to ${email} at ${companyName}`);
  } else {
    throw new Error(`Failed to send email: ${result.error}`);
  }
}
