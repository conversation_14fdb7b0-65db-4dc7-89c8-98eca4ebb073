import { supabase } from '../lib/supabase.js';
import { sendEmail } from '../lib/email.js';

export async function sendBiweeklyNotifications() {
  const siteUrl = process.env.SITE_URL!;

  const { data: employers, error: employersError } = await supabase
    .from('employers')
    .select('email, full_name')
    .not('email', 'is', null);

  if (employersError) {
    throw new Error(`Failed to fetch employers: ${employersError.message}`);
  }

  const { data: jobSeekers, error: jobSeekersError } = await supabase
    .from('job_seekers')
    .select('email, full_name')
    .eq('is_active', true)
    .eq('onboarding_completed', true)
    .not('email', 'is', null);

  if (jobSeekersError) {
    throw new Error(`Failed to fetch job seekers: ${jobSeekersError.message}`);
  }

  console.log(
    `📊 Found ${employers?.length || 0} employers, ${jobSeekers?.length || 0} job seekers`,
  );

  if (employers) {
    for (const employer of employers) {
      await sendEmployerNotification(
        employer.email,
        employer.full_name || 'there',
        siteUrl,
      );
    }
  }

  if (jobSeekers) {
    for (const jobSeeker of jobSeekers) {
      await sendJobSeekerNotification(
        jobSeeker.email,
        jobSeeker.full_name || 'there',
        siteUrl,
      );
    }
  }
}

async function sendEmployerNotification(
  email: string,
  name: string,
  siteUrl: string,
) {
  const subject = 'New candidates available on Astrala Nexus';

  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h1 style="color: #333;">Hi ${name}!</h1>
      <p style="font-size: 16px; line-height: 1.6;">
        We've found new potential candidates that might be a great fit for your open positions.
      </p>
      <p style="font-size: 16px; line-height: 1.6;">
        Check out the latest talent and connect with top candidates on our platform.
      </p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${siteUrl}" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block;">
          View New Candidates
        </a>
      </div>
      <p style="font-size: 14px; color: #666;">
        Best regards,<br>
        The Astrala Nexus Team
      </p>
    </div>
  `;

  const textBody = `
    Hi ${name}!

    We've found new potential candidates that might be a great fit for your open positions.

    Check out the latest talent and connect with top candidates on our platform: ${siteUrl}

    Best regards,
    The Astrala Nexus Team
  `;

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject,
    htmlBody,
    textBody,
    tag: 'BiweeklyNotification',
  });

  if (result.success) {
    console.log(`✅ Sent employer notification to ${email}`);
  } else {
    console.error(
      `❌ Failed to send employer notification to ${email}: ${result.error}`,
    );
  }
}

async function sendJobSeekerNotification(
  email: string,
  name: string,
  siteUrl: string,
) {
  const subject = 'New job opportunities on Astrala Nexus';

  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h1 style="color: #333;">Hi ${name}!</h1>
      <p style="font-size: 16px; line-height: 1.6;">
        Great news! We've discovered new job opportunities that match your profile and skills.
      </p>
      <p style="font-size: 16px; line-height: 1.6;">
        Don't miss out on these exciting career opportunities waiting for you.
      </p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${siteUrl}" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block;">
          View New Jobs
        </a>
      </div>
      <p style="font-size: 14px; color: #666;">
        Best regards,<br>
        The Astrala Nexus Team
      </p>
    </div>
  `;

  const textBody = `
    Hi ${name}!

    Great news! We've discovered new job opportunities that match your profile and skills.

    Don't miss out on these exciting career opportunities waiting for you: ${siteUrl}

    Best regards,
    The Astrala Nexus Team
  `;

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject,
    htmlBody,
    textBody,
    tag: 'BiweeklyNotification',
  });

  if (result.success) {
    console.log(`✅ Sent job seeker notification to ${email}`);
  } else {
    console.error(
      `❌ Failed to send job seeker notification to ${email}: ${result.error}`,
    );
  }
}
