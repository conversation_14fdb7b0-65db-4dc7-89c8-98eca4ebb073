[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = "*"
alembic = "*"
asyncpg = "*"
SQLAlchemy = { extras = ["asyncio"]  }
uvicorn = "*"
sentry-sdk = "*"
dynaconf = "*"
httpx = "*"
matplotlib = "*"
numpy = "*"
openai = "*"
azure-ai-textanalytics = "*"
networkx = "*"

[dev-packages]
pytest-asyncio = "==0.22.0"
black = "*"
pytest = "==8.1.1"

[requires]
python_version = "3.11"
