version: '3.12'

services:
  postgres:
    container_name: "postgres"
    image: postgres:16.4
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=behav_graph

  app:
    build: .
    image: behav-chart-app
    platform: linux/amd64
    container_name: api_container
    environment:
      ENV_FOR_DYNACONF: "development"
      DATABASE_HOST: postgres
    ports:
      - "8000:8000"
    depends_on:
      - postgres
