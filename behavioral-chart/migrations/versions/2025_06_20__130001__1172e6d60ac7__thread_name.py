"""thread name

Revision ID: 1172e6d60ac7
Revises: a131ad68c5c3
Create Date: 2025-06-20 13:00:01.893135

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "1172e6d60ac7"
down_revision: Union[str, None] = "a131ad68c5c3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("threads", sa.Column("name", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("threads", "name")
    # ### end Alembic commands ###
