"""profile model

Revision ID: bf204c1e118c
Revises: ddeeaf3ac567
Create Date: 2025-06-18 11:42:23.056373

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "bf204c1e118c"
down_revision: Union[str, None] = "ddeeaf3ac567"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "profiles",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("image", sa.LargeBinary(), nullable=False),
        sa.Column("cognitive", sa.Integer(), server_default="0", nullable=False),
        sa.Column("emotional", sa.Integer(), server_default="0", nullable=False),
        sa.Column("ethical", sa.Integer(), server_default="0", nullable=False),
        sa.Column("strategic", sa.Integer(), server_default="0", nullable=False),
        sa.Column("symbolic", sa.Integer(), server_default="0", nullable=False),
        sa.Column("thread_id", sa.Text(), nullable=True),
        sa.Column(
            "record_created",
            sa.DateTime(),
            server_default=sa.text("statement_timestamp()"),
            nullable=False,
        ),
        sa.Column(
            "record_modified",
            sa.DateTime(),
            server_default=sa.text("statement_timestamp()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["thread_id"],
            ["threads.uid"],
            name=op.f("fk_profiles_thread_id_threads"),
            onupdate="CASCADE",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_profiles")),
    )
    op.create_index(
        op.f("ix_profiles_record_modified"),
        "profiles",
        ["record_modified"],
        unique=False,
    )
    op.create_index(
        op.f("ix_profiles_thread_id"), "profiles", ["thread_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_profiles_thread_id"), table_name="profiles")
    op.drop_index(op.f("ix_profiles_record_modified"), table_name="profiles")
    op.drop_table("profiles")
    # ### end Alembic commands ###
