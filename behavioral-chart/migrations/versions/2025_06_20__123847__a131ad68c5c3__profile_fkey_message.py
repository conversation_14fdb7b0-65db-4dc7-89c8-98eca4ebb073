"""profile_fkey_message

Revision ID: a131ad68c5c3
Revises: bf204c1e118c
Create Date: 2025-06-20 12:38:47.167420

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "a131ad68c5c3"
down_revision: Union[str, None] = "bf204c1e118c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("profiles", sa.Column("message_id", sa.BigInteger(), nullable=True))
    op.execute(
        """UPDATE profiles p
            SET message_id = sub.message_id
            FROM (
                SELECT DISTINCT ON (m.thread_id)
                    m.thread_id,
                    m.id AS message_id
                FROM messages m
                WHERE m.role = 'assistant'
                ORDER BY m.thread_id, m.record_created DESC
            ) sub
            WHERE p.thread_id = sub.thread_id;"""
    )
    op.drop_index(op.f("ix_profiles_thread_id"), table_name="profiles")
    op.create_index(
        op.f("ix_profiles_message_id"), "profiles", ["message_id"], unique=False
    )
    op.drop_constraint(
        op.f("fk_profiles_thread_id_threads"), "profiles", type_="foreignkey"
    )
    op.create_foreign_key(
        op.f("fk_profiles_message_id_messages"),
        "profiles",
        "messages",
        ["message_id"],
        ["id"],
        onupdate="CASCADE",
        ondelete="CASCADE",
    )
    op.drop_column("profiles", "thread_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "profiles",
        sa.Column("thread_id", sa.TEXT(), autoincrement=False, nullable=True),
    )
    op.drop_constraint(
        op.f("fk_profiles_message_id_messages"), "profiles", type_="foreignkey"
    )
    op.create_foreign_key(
        op.f("fk_profiles_thread_id_threads"),
        "profiles",
        "threads",
        ["thread_id"],
        ["uid"],
        onupdate="CASCADE",
        ondelete="CASCADE",
    )
    op.drop_index(op.f("ix_profiles_message_id"), table_name="profiles")
    op.create_index(
        op.f("ix_profiles_thread_id"), "profiles", ["thread_id"], unique=False
    )
    op.drop_column("profiles", "message_id")
    # ### end Alembic commands ###
