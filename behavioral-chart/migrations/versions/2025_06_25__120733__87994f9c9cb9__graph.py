"""graph

Revision ID: 87994f9c9cb9
Revises: 1172e6d60ac7
Create Date: 2025-06-25 12:07:33.128961

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "87994f9c9cb9"
down_revision: Union[str, None] = "1172e6d60ac7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "profiles",
        sa.Column("graph_data", sa.JSON(), server_default="{}", nullable=False),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("profiles", "graph_data")
    # ### end Alembic commands ###
