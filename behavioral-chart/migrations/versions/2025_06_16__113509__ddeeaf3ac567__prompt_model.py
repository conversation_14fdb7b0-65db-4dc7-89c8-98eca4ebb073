"""prompt model

Revision ID: ddeeaf3ac567
Revises: bbe54a0ffce6
Create Date: 2025-06-16 11:35:09.263123

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from api.thread import INITIAL__PROMPT

# revision identifiers, used by Alembic.
revision: str = "ddeeaf3ac567"
down_revision: Union[str, None] = "bbe54a0ffce6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "prompts",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("content", sa.Text(), nullable=False),
        sa.Column(
            "record_created",
            sa.DateTime(),
            server_default=sa.text("statement_timestamp()"),
            nullable=False,
        ),
        sa.Column(
            "record_modified",
            sa.DateTime(),
            server_default=sa.text("statement_timestamp()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_prompts")),
    )
    op.create_index(
        op.f("ix_prompts_record_modified"), "prompts", ["record_modified"], unique=False
    )
    escaped_prompt = INITIAL__PROMPT.replace("'", "''")  # Escape single quotes for SQL
    op.execute(f"INSERT INTO prompts (content) VALUES ('{escaped_prompt}')")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_prompts_record_modified"), table_name="prompts")
    op.drop_table("prompts")
    # ### end Alembic commands ###
