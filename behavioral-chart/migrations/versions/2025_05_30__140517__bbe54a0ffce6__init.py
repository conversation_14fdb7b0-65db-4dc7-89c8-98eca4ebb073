"""init

Revision ID: bbe54a0ffce6
Revises:
Create Date: 2025-05-30 14:05:17.472170

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "bbe54a0ffce6"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "threads",
        sa.Column("uid", sa.Text(), nullable=False),
        sa.Column(
            "record_created",
            sa.DateTime(),
            server_default=sa.text("statement_timestamp()"),
            nullable=False,
        ),
        sa.Column(
            "record_modified",
            sa.DateTime(),
            server_default=sa.text("statement_timestamp()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("uid", name=op.f("pk_threads")),
    )
    op.create_index(
        op.f("ix_threads_record_modified"), "threads", ["record_modified"], unique=False
    )
    op.create_table(
        "messages",
        sa.Column("id", sa.BigInteger(), nullable=False),
        sa.Column("thread_id", sa.Text(), nullable=True),
        sa.Column("content", sa.Text(), nullable=False),
        sa.Column("role", sa.Text(), nullable=False),
        sa.Column(
            "record_created",
            sa.DateTime(),
            server_default=sa.text("statement_timestamp()"),
            nullable=False,
        ),
        sa.Column(
            "record_modified",
            sa.DateTime(),
            server_default=sa.text("statement_timestamp()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["thread_id"],
            ["threads.uid"],
            name=op.f("fk_messages_thread_id_threads"),
            onupdate="CASCADE",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_messages")),
    )
    op.create_index(
        op.f("ix_messages_record_modified"),
        "messages",
        ["record_modified"],
        unique=False,
    )
    op.create_index(
        op.f("ix_messages_thread_id"), "messages", ["thread_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_messages_thread_id"), table_name="messages")
    op.drop_index(op.f("ix_messages_record_modified"), table_name="messages")
    op.drop_table("messages")
    op.drop_index(op.f("ix_threads_record_modified"), table_name="threads")
    op.drop_table("threads")
    # ### end Alembic commands ###
