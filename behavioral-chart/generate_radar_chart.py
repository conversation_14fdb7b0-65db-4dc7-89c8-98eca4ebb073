import matplotlib.pyplot as plt
import numpy as np
from io import BytesIO


def generate_radar_chart(data: dict) -> bytes:
    """
    Generates a radar chart from a dictionary of {label: value} and returns it as binary PNG.

    :param data: Dictionary where keys are axis labels and values are numerical scores (1–100)
    :return: PNG image in binary format
    """
    if not data or len(data) < 3:
        raise ValueError(
            "At least 3 dimensions are required to generate a radar chart."
        )

    labels = list(data.keys())
    values = list(data.values())

    # Close the loop for radar chart
    values += values[:1]
    num_vars = len(labels)

    # Angles for each axis
    angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()
    angles += angles[:1]

    # Create plot
    fig, ax = plt.subplots(figsize=(6, 6), subplot_kw=dict(polar=True))
    ax.plot(angles, values, linewidth=2)
    ax.fill(angles, values, alpha=0.25)

    ax.set_theta_offset(np.pi / 2)
    ax.set_theta_direction(-1)
    ax.set_thetagrids(np.degrees(angles[:-1]), labels)
    ax.set_ylim(0, 100)

    # Save to BytesIO
    buf = BytesIO()
    plt.savefig(buf, format="png", bbox_inches="tight")
    plt.close(fig)
    buf.seek(0)

    return buf.getvalue()  # Binary PNG image
