import uvicorn
from fastapi import Fast<PERSON><PERSON>
from starlette.middleware.cors import CORSMiddleware

from api import root_router, thread_router
from config import settings

app = FastAPI(
    title=settings.PROJECT_NAME,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

app.include_router(root_router)
app.include_router(thread_router)

origins = [
    "https://assistant-dev.astralanexus.ai",
    "http://localhost:3000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

if __name__ == "__main__":
    uvicorn.run(
        app, host="0.0.0.0", port=8000, log_level="info", reload=False, log_config=None
    )
