import random

adjectives = [
    "sublime",
    "hopeful",
    "brave",
    "ancient",
    "bold",
    "quiet",
    "fancy",
    "eager",
    "gentle",
    "shiny",
    "rapid",
    "calm",
    "bright",
    "dark",
    "graceful",
    "happy",
    "lucky",
    "mighty",
    "proud",
    "silent",
    "swift",
    "tiny",
    "vast",
    "wild",
    "zealous",
    "clever",
    "curious",
    "daring",
    "elegant",
    "fierce",
    "glorious",
    "jolly",
    "kind",
    "lively",
    "noble",
    "peaceful",
    "quirky",
    "radiant",
    "silly",
    "witty",
]

nouns = [
    "flower",
    "river",
    "mountain",
    "tree",
    "sun",
    "moon",
    "cloud",
    "forest",
    "dream",
    "ocean",
    "meadow",
    "storm",
    "flame",
    "echo",
    "star",
    "breeze",
    "hill",
    "mist",
    "shadow",
    "stone",
]


def generate_readable_name(thread_uid: str):
    adjective = random.choice(adjectives)
    noun = random.choice(nouns)
    return f"{adjective}-{noun}-{thread_uid[:4]}"


if __name__ == "__main__":

    threads = [
        "03582e05-8036-4f33-b235-013209dd9503",
        "0bf917ef-0531-4478-875c-41c5fb1e69a9",
        "21195e55-c28f-480a-a477-6e724f9df3fd",
        "2219b551-1103-4822-8bda-bce4e60e2bfb",
        "29c7cbbe-fad5-4509-a2b9-ba3f96e7a9ce",
        "2de9689f-e89f-4709-b1e8-848cd15558be",
        "2f2773ef-ce41-4c1f-af57-7feb95a938b5",
        "3e5f9d27-0046-4f6a-86bf-9a2f840a6392",
        "4267c349-d7c6-4e22-a466-677da2d090fc",
        "5e9c705f-4773-4be8-9f88-684c0b9bf48d",
        "6c9a521b-c80a-4fff-807c-96d950f4750b",
        "7115b38f-5dd1-4354-94b5-8ea296f9a2ec",
        "79a6c6de-b69a-4e3f-9a68-f9d91addbbc0",
        "806715a6-ad78-4fa8-9f00-56ccc54e626c",
        "a31b940d-0e8c-4e7a-b8ba-dbda38967e69",
        "a9c2b6fb-594d-480b-841d-c75f59bd5cea",
        "aa0dd8fa-33ea-4245-a2e8-49a1711fa690",
        "af298280-5dcf-4283-95cf-352504fc022e",
        "b2b048f5-d504-4bba-8baa-2e8bbdef8eee",
        "cd447cae-925d-4973-ba35-d807a2880a2a",
        "cfd2fc9f-1b6c-40b8-9f56-5d401b4fdd58",
        "e2f76c69-0307-4f70-8729-5f3048c8d1c6",
        "e6595cd7-6fba-402b-8523-f42886b0d2d8",
        "ebd73c55-8949-4cc9-ac33-6be4f43db9ba",
    ]

    for thread in threads:
        print(f"('{thread}', '{generate_readable_name(thread)}'),")
