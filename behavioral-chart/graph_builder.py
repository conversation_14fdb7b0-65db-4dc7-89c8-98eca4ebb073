import networkx as nx
from azure.ai.textanalytics import TextAnalyticsClient
from azure.core.credentials import AzureKeyCredential
from networkx.readwrite import json_graph

from config import log, settings as st


def authenticate_azure_text_analytics() -> TextAnalyticsClient:
    return TextAnalyticsClient(
        endpoint=st.TEXT_ANALYTICS_ENDPOINT,
        credential=AzureKeyCredential(st.TEXT_ANALYTICS_API_KEY),
    )


def azure_text_analytics(text, client: TextAnalyticsClient):
    try:
        documents = [text]
        key_phrase_result = client.extract_key_phrases(documents=documents)[0]
        sentiment_result = client.analyze_sentiment(documents=documents)[0]

        key_phrases = (
            key_phrase_result.key_phrases if not key_phrase_result.is_error else []
        )
        sentiment = (
            sentiment_result.sentiment if not sentiment_result.is_error else "unknown"
        )

        return {"key_phrases": key_phrases, "sentiment": sentiment}

    except Exception as e:
        log.exception(f"[Azure Error] {e}")
        return {"key_phrases": [], "sentiment": "error"}


def analyze_user_messages(messages):
    client = authenticate_azure_text_analytics()
    graph = nx.Graph()

    for message in messages:
        analytics = azure_text_analytics(message, client)
        key_phrases = analytics.get("key_phrases", [])
        sentiment = analytics.get("sentiment", "neutral")

        for i, phrase1 in enumerate(key_phrases):
            for phrase2 in key_phrases[i + 1 :]:
                p1, p2 = phrase1.lower(), phrase2.lower()
                graph.add_node(p1, type="phrase")
                graph.add_node(p2, type="phrase")
                graph.add_edge(p1, p2, relation="co_occurs", sentiment=sentiment)

    return json_graph.node_link_data(graph)
