[default]
    AZURE_OPENAI_API_KEY = ""
    AZURE_OPENAI_ENDPOINT = ""
    AZURE_DEPLOYMENT = ""

    TEXT_ANALYTICS_API_KEY = ""
    TEXT_ANALYTICS_ENDPOINT = ""

    DATABASE_PORT = ""
    DATABASE_USER = ""
    DATABASE_DB = ""
    DATABASE_PASSWORD = ""
    DATABASE_HOST = ""

    LOG_TO_SENTRY = ""

[development]
    AZURE_OPENAI_API_KEY = ""
    AZURE_OPENAI_ENDPOINT = ""
    AZURE_DEPLOYMENT = ""

    TEXT_ANALYTICS_API_KEY = ""
    TEXT_ANALYTICS_ENDPOINT = ""

    DATABASE_HOST = "postgres"
    DATABASE_PORT = 5432
    DATABASE_USER = "postgres"
    DATABASE_PASSWORD = "postgres"
    DATABASE_DB = "behav_graph"

[test]
    AZURE_OPENAI_API_KEY = ""
    AZURE_OPENAI_ENDPOINT = ""
    AZURE_DEPLOYMENT = ""

    TEXT_ANALYTICS_API_KEY = ""
    TEXT_ANALYTICS_ENDPOINT = ""

    DATABASE_PORT = ""
    DATABASE_USER = ""
    DATABASE_DB = ""
    DATABASE_PASSWORD = ""
    DATABASE_HOST = ""

[global]
    PROJECT_NAME = 'astrala-behavioral-graph'
    SENTRY_URL = "https://<EMAIL>/4508960938393600"
    LOG_TO_SENTRY = true
