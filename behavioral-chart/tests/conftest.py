import asyncio
from typing import Async<PERSON>enerator

import pytest
import pytest_asyncio

from models.base import Model
from httpx import ASGITransport, AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from config import settings as st
from db import get_session_dep
from main import app

db_url = (
    "postgresql+asyncpg://"
    f"{st.DATABASE_USER}:"
    f"{st.DATABASE_PASSWORD}@"
    f"{st.DATABASE_HOST}:"
    f"{st.DATABASE_PORT}/"
    f"{st.DATABASE_DB}"
)


@pytest.fixture(scope="session")
def event_loop():
    loop = asyncio.get_event_loop()
    yield loop
    loop.close()


def check_test_db():
    if (
        st.DATABASE_HOST not in ("localhost", "127.0.0.1", "postgres")
        or "supabase" in st.DATABASE_HOST
    ):
        print(db_url)
        raise Exception("Use local database only!")


@pytest_asyncio.fixture(scope="session")
async def engine():
    check_test_db()

    e = create_async_engine(db_url, echo=False, max_overflow=25)

    try:
        async with e.begin() as con:
            await con.run_sync(Model.metadata.create_all)

        yield e
    finally:
        pass
        # async with e.begin() as con:
        #     await con.run_sync(Model.metadata.drop_all)


@pytest_asyncio.fixture
async def dbsession(engine) -> AsyncSession:
    async with AsyncSession(bind=engine) as session:
        yield session


@pytest_asyncio.fixture
async def test_client_rest(
    dbsession: AsyncSession,
) -> AsyncGenerator[AsyncClient, None]:
    def override_get_db():
        test_db = dbsession
        yield test_db

    app.dependency_overrides[get_session_dep.dependency] = override_get_db

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as ac:
        yield ac
