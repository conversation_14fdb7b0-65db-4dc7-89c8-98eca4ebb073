from typing import Optional
from unittest.mock import AsyncMock, <PERSON><PERSON>, patch

import sqlalchemy as sa
from openai import BaseModel

import models as m
from api.thread import AUTO<PERSON><PERSON><PERSON><PERSON>, INITIAL__PROMPT
from openai_requests import Intelligence, Profile


class MockedOpenAiResponse(BaseModel):
    text: str
    profile_complete: bool = False
    intelligence_profile: Optional[Profile] = None


async def test_create(dbsession, test_client_rest):
    with patch("api.thread.make_openai_request", new_callable=AsyncMock) as mock_call:
        mock_call.return_value = MockedOpenAiResponse(text="mocked response")
        response = await test_client_rest.post("/threads/create")

    assert response.status_code == 200
    data = response.json()

    thread_id = await dbsession.scalar(sa.select(m.Thread.uid))

    assert thread_id is not None
    assert data["thread_id"] == thread_id
    assert len(data["messages"]) > 0
    assert data["messages"][0]["role"] == "system"
    assert data["messages"][0]["content"] == INITIAL__PROMPT
    assert data["messages"][1]["role"] == "assistant"
    assert data["messages"][1]["content"] == "mocked response"

    messages = (
        await dbsession.execute(
            sa.select(m.Message.role, m.Message.content)
            .where(m.Message.thread_id == thread_id)
            .order_by(m.Message.record_created)
        )
    ).fetchall()

    assert messages is not None
    assert len(messages) == 2
    assert messages[0].role == "system"
    assert messages[0].content == INITIAL__PROMPT
    assert messages[1].role == "assistant"
    assert messages[1].content == "mocked response"


async def test_chat(dbsession, test_client_rest):
    with patch("api.thread.make_openai_request", new_callable=AsyncMock) as mock_call:
        mock_call.return_value = MockedOpenAiResponse(text="mocked response")
        response = await test_client_rest.post("/threads/create")

        thread_id = response.json()["thread_id"]

        response = await test_client_rest.post(
            f"/threads/{thread_id}/chat",
            json={"message": "some text"},
            # json={"message": "I need to estimate my strengths and weaknesses in order to present it to potential employers"}
        )

    assert response.status_code == 200
    data = response.json()
    assert data["text"] == "mocked response"

    messages = (
        await dbsession.execute(
            sa.select(
                m.Message.id,
                m.Message.content,
                m.Message.role,
            )
            .where(m.Message.thread_id == thread_id)
            .order_by(m.Message.record_created)
        )
    ).fetchall()

    assert messages is not None
    assert len(messages) == 4

    assert messages[3].role == "assistant"
    assert messages[3].content == "mocked response"

    assert messages[2].role == "user"
    assert messages[2].content == "some text"


async def test_messages_list(dbsession, test_client_rest):
    with patch("api.thread.make_openai_request", new_callable=AsyncMock) as mock_call:
        mock_call.return_value = MockedOpenAiResponse(text="mocked response")
        response = await test_client_rest.post("/threads/create")

        thread_id = response.json()["thread_id"]

        response = await test_client_rest.get(f"/threads/{thread_id}/messages")

    assert response.status_code == 200
    assert len(response.json()) == 2


async def test_threads_list(dbsession, test_client_rest):
    with patch("api.thread.make_openai_request", new_callable=AsyncMock) as mock_call:
        mock_call.return_value = MockedOpenAiResponse(text="mocked response")
        response1 = await test_client_rest.post("/threads/create")
        response2 = await test_client_rest.post("/threads/create")
        response3 = await test_client_rest.post("/threads/create")

        thread_id1 = response1.json()["thread_id"]
        thread_id2 = response2.json()["thread_id"]
        thread_id3 = response3.json()["thread_id"]

        response = await test_client_rest.get(f"/threads")

    assert response.status_code == 200
    assert len(response.json()["threads"]) == 3
    for thread, thread_id in zip(
        response.json()["threads"], [thread_id3, thread_id2, thread_id1]
    ):
        assert thread["name"] is not None
        assert thread["uid"] == thread_id


async def test_profile_completed(dbsession, test_client_rest):
    with patch(
        "api.thread.make_openai_request", new_callable=AsyncMock
    ) as async_mock_call:
        with patch("api.thread.analyze_user_messages", new_callable=Mock) as mock_call:
            intelligence = Intelligence(rate=80, explanation="text")
            async_mock_call.return_value = MockedOpenAiResponse(
                text="mocked response",
                profile_complete=True,
                intelligence_profile=Profile(
                    emotional=intelligence,
                    symbolic=intelligence,
                    strategic=intelligence,
                    ethical=intelligence,
                    cognitive=intelligence,
                ),
            )
            mock_call.return_value = {}
            response = await test_client_rest.post("/threads/create")

            thread_id = response.json()["thread_id"]

            response = await test_client_rest.post(
                f"/threads/{thread_id}/chat",
                json={"message": "some text"},
            )

    assert response.status_code == 200
    data = response.json()
    assert data["text"] == "mocked response"

    messages = (
        await dbsession.execute(
            sa.select(m.Message.id)
            .where(m.Message.thread_id == thread_id)
            .order_by(m.Message.record_created)
        )
    ).fetchall()

    assert messages is not None
    assert len(messages) == 4

    profiles = (
        await dbsession.execute(
            sa.select(
                m.Profile.image,
                m.Profile.message_id,
                m.Profile.cognitive,
                m.Profile.symbolic,
                m.Profile.strategic,
                m.Profile.ethical,
                m.Profile.emotional,
                m.Profile.graph_data,
            )
            .select_from(
                sa.join(m.Profile, m.Message, m.Profile.message_id == m.Message.id)
            )
            .where(m.Message.thread_id == thread_id)
        )
    ).fetchall()
    assert len(profiles) == 1
    profile = profiles[0]

    assert profile.graph_data == {}

    assert profile.cognitive == 80
    assert profile.emotional == 80
    assert profile.ethical == 80
    assert profile.symbolic == 80
    assert profile.strategic == 80

    assert profile.image is not None

    response = await test_client_rest.post(
        f"/threads/{thread_id}/chat",
        json={"message": "some text"},
    )

    assert response.status_code == 200
    assert response.json()["text"] == AUTORESPONSE

    response = await test_client_rest.get(f"/threads/{thread_id}/messages")

    assert response.status_code == 200
