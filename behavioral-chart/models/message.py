from models.base import Model, RecordTimestampFields
import sqlalchemy as sa


class Message(Model, RecordTimestampFields):
    __tablename__ = "messages"

    id = sa.Column(sa.BigInteger, primary_key=True, autoincrement=True)

    thread_id = sa.Column(
        sa.Text,
        sa.ForeignKey("threads.uid", onupdate="CASCADE", ondelete="CASCADE"),
        index=True,
        nullable=True,
    )
    content = sa.Column(sa.Text, nullable=False)
    role = sa.Column(sa.Text, nullable=False)
