from models.base import Model, RecordTimestampFields
import sqlalchemy as sa


class Profile(Model, RecordTimestampFields):
    __tablename__ = "profiles"

    id = sa.Column(sa.BigInteger, primary_key=True)
    image = sa.Column(sa.LargeBinary, nullable=False)
    cognitive = sa.Column(sa.Integer, nullable=False, default=0, server_default="0")
    emotional = sa.Column(sa.Integer, nullable=False, default=0, server_default="0")
    ethical = sa.Column(sa.Integer, nullable=False, default=0, server_default="0")
    strategic = sa.Column(sa.Integer, nullable=False, default=0, server_default="0")
    symbolic = sa.Column(sa.Integer, nullable=False, default=0, server_default="0")
    graph_data = sa.Column(sa.JSON, nullable=False, default={}, server_default="{}")

    message_id = sa.Column(
        sa.BigInteger,
        sa.<PERSON>ey("messages.id", onupdate="CASCADE", ondelete="CASCADE"),
        index=True,
        nullable=True,
    )
