import asyncio
import time
from typing import Optional

from openai import (
    AsyncAzureOpenAI,
    LengthFinishReasonError,
    NOT_GIVEN,
    RateLimitError,
)
from pydantic import Field, BaseModel, conint

from api.exceptions import BadRequest
from config import log, settings

client = AsyncAzureOpenAI(
    api_key=settings.AZURE_OPENAI_API_KEY,
    api_version="2024-10-21",
    azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
    azure_deployment=settings.AZURE_DEPLOYMENT,
)


class Intelligence(BaseModel):
    rate: int = Field(
        ..., description="Users intelligence score. From 1 to 100, 100 is best"
    )
    explanation: str = Field(
        ..., description="Intelligence score explanation. Keep it less then 100 words"
    )


class Profile(BaseModel):
    emotional: Optional[Intelligence] = Field(
        None,
        description="Emotional intelligence reflects how well the user senses, understands, and responds to emotional dynamics—their own and others'. It includes empathy, emotional regulation, and the ability to form trusting, resilient relationships.",
    )
    symbolic: Optional[Intelligence] = Field(
        None,
        description="Symbolic intelligence captures the user's ability to use metaphor, imagery, and narrative to make meaning, inspire others, and connect the abstract to the personal. It's the imagination-driven layer of insight and communication.",
    )
    strategic: Optional[Intelligence] = Field(
        None,
        description="Strategic intelligence measures the user’s foresight, planning ability, and adaptability in complex, changing environments. It reflects how well they hold long-term vision while navigating real-world constraints.",
    )
    ethical: Optional[Intelligence] = Field(
        None,
        description="Ethical intelligence describes the user’s capacity to act according to internal values, fairness, and moral reasoning. It emerges in moments of tension, where integrity must be weighed against competing pressures.",
    )
    cognitive: Optional[Intelligence] = Field(
        None,
        description="Cognitive intelligence is the user’s ability to analyze, structure, and reason through information and problems. It includes logic, pattern recognition, and clear, structured decision-making under uncertainty.",
    )


class OpenAiResponse(BaseModel):
    text: str = Field(
        ...,
        description="Next question, reflection, or profile summary generated by Clara. Stay under 8000 characters.",
    )
    intelligence_profile: Profile = Field(
        ...,
        description="The user's current intelligence profile, updated with known insights. Fields may be None if incomplete.",
    )
    profile_complete: bool = Field(
        ...,
        description="True if Clara is ready to present the full profile. False if the evaluation is ongoing.",
    )
    answer_completeness: conint(ge=0, le=100) = Field(
        ...,
        description="How complete and relevant the user's latest answer was (100 = highly complete and relevant).",
    )
    questions_asked: int = Field(
        ..., description="Total number of questions Clara has asked the user so far."
    )


async def make_openai_request(
    messages: list[dict], retries: int = 3, delay: float = 2.0
) -> Optional[OpenAiResponse]:
    max_tokens = NOT_GIVEN
    for attempt in range(retries):
        try:
            start_time = time.time()

            completion = await client.beta.chat.completions.parse(
                model="gpt-4o-2024-08-06",
                messages=messages,
                temperature=0.7,
                max_tokens=max_tokens,
                response_format=OpenAiResponse,
            )

            event = completion.choices[0].message.parsed

            end_time = time.time()
            log.info(
                f"[Matching] AI request total time: {end_time - start_time:.2f} seconds"
            )

            return event

        except RateLimitError:
            if attempt < retries - 1:
                await asyncio.sleep(delay)
                continue
            else:
                log.error("Rate limit exceeded and retries exhausted.")
                raise

        except LengthFinishReasonError as e:
            max_tokens = 4000
            await asyncio.sleep(delay)
            log.info(f"Too long response: {str(e)}")
            continue

        except Exception as e:
            log.exception(e)
            raise BadRequest(f"Error while generating ai match description: {e}")
