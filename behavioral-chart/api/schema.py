from typing import List, Literal, Optional

from pydantic import BaseModel


class Message(BaseModel):
    role: Literal["user", "assistant", "system"]
    content: str
    image_url: Optional[bytes] = None


class MessageList(BaseModel):
    messages: List[Message]


class Thread(BaseModel):
    uid: str
    name: Optional[str] = None


class ThreadList(BaseModel):
    threads: List[Thread]


class ChatResponse(BaseModel):
    image_url: Optional[bytes] = None
    text: str


class ChatRequest(BaseModel):
    message: str


class CreateThreadResponse(BaseModel):
    thread_id: str
    thread_name: str
    messages: List[Message]
