-- AlterTable
ALTER TABLE "public"."Member" ADD COLUMN     "coinsBalance" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "public"."Reward" (
    "id" TEXT NOT NULL,
    "settingsId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "imageUrl" TEXT,
    "points" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "Reward_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."RewardRequest" (
    "workspaceId" TEXT NOT NULL,
    "id" TEXT NOT NULL,
    "memberId" TEXT NOT NULL,
    "rewardId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "comment" TEXT,
    "rejectReason" TEXT,
    "notifications" TEXT NOT NULL,
    "handledBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "RewardRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."RewardActivity" (
    "settingsId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "points" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."RewardSettings" (
    "workspaceId" TEXT NOT NULL,
    "sendPointsNotifications" BOOLEAN NOT NULL DEFAULT true
);

-- CreateIndex
CREATE UNIQUE INDEX "RewardActivity_settingsId_type_key" ON "public"."RewardActivity"("settingsId", "type");

-- CreateIndex
CREATE UNIQUE INDEX "RewardSettings_workspaceId_key" ON "public"."RewardSettings"("workspaceId");

-- AddForeignKey
ALTER TABLE "public"."Reward" ADD CONSTRAINT "Reward_settingsId_fkey" FOREIGN KEY ("settingsId") REFERENCES "public"."RewardSettings"("workspaceId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RewardRequest" ADD CONSTRAINT "RewardRequest_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "public"."Workspace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RewardRequest" ADD CONSTRAINT "RewardRequest_workspaceId_memberId_fkey" FOREIGN KEY ("workspaceId", "memberId") REFERENCES "public"."Member"("workspaceId", "id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RewardRequest" ADD CONSTRAINT "RewardRequest_rewardId_fkey" FOREIGN KEY ("rewardId") REFERENCES "public"."Reward"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RewardActivity" ADD CONSTRAINT "RewardActivity_settingsId_fkey" FOREIGN KEY ("settingsId") REFERENCES "public"."RewardSettings"("workspaceId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RewardSettings" ADD CONSTRAINT "RewardSettings_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "public"."Workspace"("id") ON DELETE CASCADE ON UPDATE CASCADE;
