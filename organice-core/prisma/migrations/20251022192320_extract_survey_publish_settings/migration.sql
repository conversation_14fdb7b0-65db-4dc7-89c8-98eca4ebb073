-- CreateTable
CREATE TABLE "public"."SurveyPublishSettings" (
    "timeOfPosting" TEXT NOT NULL,
    "timezone" TEXT NOT NULL,
    "frequency" "public"."SurveyPostingFrequency" NOT NULL,
    "day" INTEGER NOT NULL,
    "surveyId" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "SurveyPublishSettings_surveyId_key" ON "public"."SurveyPublishSettings"("surveyId");

-- AddForeignKey
ALTER TABLE "public"."SurveyPublishSettings" ADD CONSTRAINT "SurveyPublishSettings_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "public"."Survey"("id") ON DELETE CASCADE ON UPDATE CASCADE;

INSERT INTO "public"."SurveyPublishSettings" ("timeOfPosting", "timezone", "frequency", "day", "surveyId") SELECT "timeOfPosting", "timezone", "frequency", "day", "id" FROM "public"."Survey";

-- AlterTable
ALTER TABLE "public"."Survey" DROP COLUMN "day",
DROP COLUMN "frequency",
DROP COLUMN "timeOfPosting",
DROP COLUMN "timezone";
