-- AlterTable
ALTER TABLE "public"."Holidays" ADD COLUMN     "calendarId" TEXT,
ALTER COLUMN "country" DROP NOT NULL;

-- CreateTable
CREATE TABLE "public"."HolidayCalendar" (
    "workspaceId" TEXT NOT NULL,
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "country" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "HolidayCalendar_pkey" PRIMARY KEY ("workspaceId","id")
);


INSERT INTO "HolidayCalendar" ("workspaceId", "id", "title", "country") (
  SELECT
    "workspaceId",
    UNNEST("countries") as "id",
    UNNEST("countries") || ' Holidays' as "title",
    UNNEST("countries") as "country"
  FROM
    "HolidaysSettings"
);

UPDATE "Holidays"
SET "calendarId" = "country";

ALTER TABLE "public"."Holidays" ALTER COLUMN "calendarId" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "public"."HolidayCalendar" ADD CONSTRAINT "HolidayCalendar_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "public"."Workspace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Holidays" ADD CONSTRAINT "Holidays_workspaceId_calendarId_fkey" FOREIGN KEY ("workspaceId", "calendarId") REFERENCES "public"."HolidayCalendar"("workspaceId", "id") ON DELETE CASCADE ON UPDATE CASCADE;
