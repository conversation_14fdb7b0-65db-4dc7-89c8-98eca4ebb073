/* eslint-disable @typescript-eslint/unbound-method */
import { expect, test } from "@jest/globals";

import { createMember, createWorkspace } from "../utils/testUtils";

import {
  ActivityEventType,
  CoinsBalanceChangedType,
  NoOpActivityLog,
  RewardRequestChangedType,
  ActivityLog,
} from "./activityLog";
import {
  approveRewardRequest,
  deleteRewardRequest,
  rejectRewardRequest,
  requestReward,
  RewardNotActiveError,
  NotEnoughPointsError,
} from "./rewards";

import {
  Logger,
  Member,
  ReadonlyDeep,
  SlackAdapter,
  Workspace,
  RewardRequestStatus,
  Reward,
  RewardRequest,
  RewardActivityType,
  RewardNotificationType,
  DeletedRequestNotificationsType,
} from ".";

const initialRequester = createMember({
  overrides: {
    id: "U1",
    isAdmin: false,
    coinsBalance: 100,
  },
});

const initialAdmin = createMember({
  overrides: {
    id: "U2",
    isAdmin: true,
    isSlackWorkspaceAdmin: true,
    coinsBalance: 0,
  },
});

const initialWorkspace = createWorkspace({
  overrides: {
    id: "T1",
    members: [initialRequester, initialAdmin],
    rewardsSettings: {
      activities: [
        {
          type: RewardActivityType.Kudos,
          points: 10,
        },
      ],
      rewards: [
        {
          id: "REWARD1",
          name: "Reward 1",
          points: 50,
          description: "Description 1",
          isActive: true,
        },
        {
          id: "REWARD2",
          name: "Reward 2",
          points: 200,
          description: "Description 2",
          isActive: true,
        },
      ],
      sendPointsNotifications: true,
    },
    slackBotToken: {
      appId: "A1",
      botId: "UX",
      botMemberId: "UX",
      scopes: ["chat:write"],
      token: "xoxb-1",
    },
  },
});

describe("Rewards", () => {
  let workspace: ReadonlyDeep<Workspace>;
  let requester: ReadonlyDeep<Member>;
  let admin: ReadonlyDeep<Member>;
  let activityLog: ActivityLog;
  let slackAdapter: SlackAdapter;
  let logger: Logger;
  let now: Date;

  beforeEach(() => {
    workspace = initialWorkspace;
    requester = initialRequester;
    admin = initialAdmin;
    activityLog = new NoOpActivityLog();
    activityLog.add = jest.fn();

    slackAdapter = {
      sendRewardRequest: jest
        .fn()
        .mockResolvedValue({ ts: "111.000", channel: "D1" }),
      sendRequestReminder: jest
        .fn()
        .mockResolvedValue({ ts: "222.000", channel: "D1" }),
      updateRewardRequest: jest.fn().mockResolvedValue(undefined),
      notifyRequesterAboutRewardRequest: jest
        .fn()
        .mockResolvedValue({ ts: "333.000" }),
      notifyMemberAboutCoinsBalanceChange: jest
        .fn()
        .mockResolvedValue(undefined),
      deleteReminder: jest.fn().mockResolvedValue(undefined),
      notifyThatApprovedRequestWasDeleted: jest
        .fn()
        .mockResolvedValue(undefined),
    } as unknown as SlackAdapter;
    logger = { info: () => {}, error: () => {} } as Logger;
    now = new Date();
  });

  describe("when requesting a reward", () => {
    const onValidationPass = jest.fn().mockResolvedValue(undefined);

    test("should create a reward request", async () => {
      const updatedWorkspace = await requestReward({
        workspace,
        member: requester,
        input: { rewardId: "REWARD1" },
        onValidationPass,
        activitiesLog: activityLog,
        slackAdapter,
        logger,
      });

      expect(updatedWorkspace.rewardsRequests.length).toBe(1);
      const newRequest = updatedWorkspace.rewardsRequests[0];

      expect(newRequest.rewardId).toBe("REWARD1");
      expect(newRequest.memberId).toBe(requester.id);
      expect(newRequest.status).toBe(RewardRequestStatus.Pending);
    });

    test("should log activity", async () => {
      const updatedWorkspace = await requestReward({
        workspace,
        member: requester,
        input: { rewardId: "REWARD1" },
        onValidationPass,
        activitiesLog: activityLog,
        slackAdapter,
        logger,
      });
      const newRequest = updatedWorkspace.rewardsRequests[0];

      expect(activityLog.add).toHaveBeenCalledWith({
        type: ActivityEventType.rewardRequestChanged,
        workspaceId: updatedWorkspace.id,
        data: {
          memberId: requester.id,
          requestId: newRequest.id,
          updateType: RewardRequestChangedType.create,
          initiatorId: requester.id,
        },
      });
    });

    test("should notify admins", async () => {
      const updatedWorkspace = await requestReward({
        workspace,
        member: requester,
        input: { rewardId: "REWARD1" },
        onValidationPass,
        activitiesLog: activityLog,
        slackAdapter,
        logger,
      });

      const newRequest = updatedWorkspace.rewardsRequests[0];

      expect(slackAdapter.sendRewardRequest).toHaveBeenCalled();
      expect(slackAdapter.sendRequestReminder).toHaveBeenCalled();
      expect(newRequest.notifications).toEqual([
        {
          recipientId: "D1",
          ts: "111.000",
          type: RewardNotificationType.RewardRequest,
        },
        {
          recipientId: "D1",
          scheduleTime: expect.any(Date),
          ts: "222.000",
          type: RewardNotificationType.RewardReminder,
        },
      ]);
    });

    test("should throw an error if reward is not active", async () => {
      const inactiveReward: ReadonlyDeep<Reward> = {
        id: "REWARD3",
        name: "Inactive Reward",
        points: 10,
        isActive: false,
        description: "",
      };

      workspace = {
        ...workspace,
        rewardsSettings: {
          ...workspace.rewardsSettings,
          rewards: [...workspace.rewardsSettings.rewards, inactiveReward],
        },
      };

      await expect(
        requestReward({
          workspace,
          member: requester,
          input: { rewardId: "REWARD3" },
          onValidationPass,
          activitiesLog: activityLog,
          slackAdapter,
          logger,
        })
      ).rejects.toThrow(RewardNotActiveError);
    });

    test("should throw an error if user has not enough points", async () => {
      await expect(
        requestReward({
          workspace,
          member: requester,
          input: { rewardId: "REWARD2" },
          onValidationPass,
          activitiesLog: activityLog,
          slackAdapter,
          logger,
        })
      ).rejects.toThrow(NotEnoughPointsError);
    });
  });

  describe("when approving a reward request", () => {
    let rewardRequest: ReadonlyDeep<RewardRequest>;

    beforeEach(async () => {
      workspace = await requestReward({
        workspace,
        member: requester,
        input: { rewardId: "REWARD1" },
        onValidationPass: jest.fn().mockResolvedValue(undefined),
        activitiesLog: activityLog,
        slackAdapter,
        logger,
      });
      rewardRequest = workspace.rewardsRequests[0];
    });

    test("should approve the request", async () => {
      const [updatedWorkspace] = await approveRewardRequest({
        workspace,
        input: { requestId: rewardRequest.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
        now,
      });

      const updatedRequest = updatedWorkspace.rewardsRequests.find(
        (r) => r.id === rewardRequest.id
      );

      expect(updatedRequest?.status).toBe(RewardRequestStatus.Approved);
      expect(updatedRequest?.handledBy).toBe(admin.id);
    });

    test("should update member's coin balance", async () => {
      const [updatedWorkspace] = await approveRewardRequest({
        workspace,
        input: { requestId: rewardRequest.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
        now,
      });

      const updatedRequester = updatedWorkspace.members.find(
        (m) => m.id === requester.id
      );
      const reward = workspace.rewardsSettings.rewards.find(
        (r) => r.id === rewardRequest.rewardId
      )!;

      expect(updatedRequester?.coinsBalance).toBe(
        requester.coinsBalance - reward.points
      );
    });

    test("should log activities", async () => {
      const reward = workspace.rewardsSettings.rewards.find(
        (r) => r.id === rewardRequest.rewardId
      )!;

      const [updatedWorkspace, affectedRequests] = await approveRewardRequest({
        workspace,
        input: { requestId: rewardRequest.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
        now,
      });
      const updatedRequest = affectedRequests.find(
        (r) => r.id === rewardRequest.id
      )!;

      expect(activityLog.add).toHaveBeenCalledWith({
        type: ActivityEventType.coinsBalanceChanged,
        workspaceId: updatedWorkspace.id,
        data: {
          memberId: requester.id,
          increase: -reward.points,
          prevBalance: initialRequester.coinsBalance,
          updateType: CoinsBalanceChangedType.rewardApproved,
          initiatorId: admin.id,
        },
      });

      expect(activityLog.add).toHaveBeenCalledWith({
        type: ActivityEventType.rewardRequestChanged,
        workspaceId: updatedWorkspace.id,
        data: {
          memberId: requester.id,
          requestId: updatedRequest.id,
          updateType: RewardRequestChangedType.approve,
          initiatorId: admin.id,
        },
      });
    });

    test("should call slack adapter methods", async () => {
      const [, affectedRequests] = await approveRewardRequest({
        workspace,
        input: { requestId: rewardRequest.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
        now,
      });
      const updatedRequest = affectedRequests.find(
        (r) => r.id === rewardRequest.id
      )!;

      expect(slackAdapter.deleteReminder).toHaveBeenCalled();
      expect(
        slackAdapter.notifyMemberAboutCoinsBalanceChange
      ).toHaveBeenCalled();
      expect(slackAdapter.updateRewardRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          workspace: expect.any(Object),
          request: expect.objectContaining({
            id: updatedRequest.id,
            status: updatedRequest.status,
          }),
          channel: expect.any(String),
          ts: expect.any(String),
        })
      );
      expect(
        slackAdapter.notifyRequesterAboutRewardRequest
      ).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          id: updatedRequest.id,
          status: updatedRequest.status,
        })
      );
    });
  });

  describe("when rejecting a reward request", () => {
    let rewardRequest: ReadonlyDeep<RewardRequest>;

    beforeEach(async () => {
      workspace = await requestReward({
        workspace,
        member: requester,
        input: { rewardId: "REWARD1" },
        onValidationPass: jest.fn().mockResolvedValue(undefined),
        activitiesLog: activityLog,
        slackAdapter,
        logger,
      });
      rewardRequest = workspace.rewardsRequests[0];
    });

    test("should reject the request", async () => {
      const [, updatedRequest] = await rejectRewardRequest({
        workspace,
        input: {
          rewardRequestId: rewardRequest.id,
          initiatorId: admin.id,
          rejectReason: "Reason",
        },
        activityLog,
        slackAdapter,
        logger,
      });

      expect(updatedRequest.status).toBe(RewardRequestStatus.Rejected);
      expect(updatedRequest.handledBy).toBe(admin.id);
      expect(updatedRequest.rejectReason).toBe("Reason");
    });

    test("should log activity", async () => {
      const [updatedWorkspace, updatedRequest] = await rejectRewardRequest({
        workspace,
        input: {
          rewardRequestId: rewardRequest.id,
          initiatorId: admin.id,
          rejectReason: "Reason",
        },
        activityLog,
        slackAdapter,
        logger,
      });

      expect(activityLog.add).toHaveBeenCalledWith({
        type: ActivityEventType.rewardRequestChanged,
        workspaceId: updatedWorkspace.id,
        data: {
          memberId: requester.id,
          requestId: updatedRequest.id,
          updateType: RewardRequestChangedType.reject,
          initiatorId: admin.id,
        },
      });
    });

    test("should call slack adapter methods", async () => {
      const [updatedWorkspace] = await rejectRewardRequest({
        workspace,
        input: {
          rewardRequestId: rewardRequest.id,
          initiatorId: admin.id,
          rejectReason: "Reason",
        },
        activityLog,
        slackAdapter,
        logger,
      });

      const updatedRequest = updatedWorkspace.rewardsRequests.find(
        (r) => r.id === rewardRequest.id
      )!;

      expect(slackAdapter.updateRewardRequest).toHaveBeenCalledWith({
        workspace: expect.any(Object),
        request: expect.objectContaining({
          id: updatedRequest.id,
          status: updatedRequest.status,
        }),
        channel: expect.any(String),
        ts: expect.any(String),
      });
      expect(
        slackAdapter.notifyRequesterAboutRewardRequest
      ).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          id: updatedRequest.id,
          status: updatedRequest.status,
        })
      );
    });
  });

  describe("when deleting a reward request", () => {
    let pendingRequest: ReadonlyDeep<RewardRequest>;
    let approvedRequest: ReadonlyDeep<RewardRequest>;

    beforeEach(async () => {
      workspace = await requestReward({
        workspace,
        member: requester,
        input: { rewardId: "REWARD1" },
        onValidationPass: jest.fn().mockResolvedValue(undefined),
        activitiesLog: activityLog,
        slackAdapter,
        logger,
      });
      pendingRequest = workspace.rewardsRequests[0];

      let updatedWorkspace = await requestReward({
        workspace,
        member: requester,
        input: { rewardId: "REWARD1" },
        onValidationPass: jest.fn().mockResolvedValue(undefined),
        activitiesLog: activityLog,
        slackAdapter,
        logger,
      });
      const requestToApprove = updatedWorkspace.rewardsRequests.find(
        (r) => r.id !== pendingRequest.id
      )!;

      [updatedWorkspace] = await approveRewardRequest({
        workspace: updatedWorkspace,
        input: { requestId: requestToApprove.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
        now,
      });
      workspace = updatedWorkspace;
      approvedRequest = workspace.rewardsRequests.find(
        (r) => r.id === requestToApprove.id
      )!;
    });

    test("should delete a pending request", async () => {
      const updatedWorkspace = await deleteRewardRequest({
        workspace,
        input: {
          rewardRequestId: pendingRequest.id,
          initiatorId: requester.id,
        },
        activityLog,
        slackAdapter,
        logger,
      });

      expect(
        updatedWorkspace.rewardsRequests.find((r) => r.id === pendingRequest.id)
      ).toBeUndefined();
    });

    test("should log activity when deleting a pending request", async () => {
      const updatedWorkspace = await deleteRewardRequest({
        workspace,
        input: {
          rewardRequestId: pendingRequest.id,
          initiatorId: requester.id,
        },
        activityLog,
        slackAdapter,
        logger,
      });

      expect(activityLog.add).toHaveBeenCalledWith({
        type: ActivityEventType.rewardRequestChanged,
        workspaceId: updatedWorkspace.id,
        data: {
          memberId: requester.id,
          requestId: pendingRequest.id,
          updateType: RewardRequestChangedType.delete,
          initiatorId: requester.id,
        },
      });
    });

    test("should call slack adapter when deleting a pending request", async () => {
      await deleteRewardRequest({
        workspace,
        input: { rewardRequestId: pendingRequest.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
      });

      expect(slackAdapter.updateRewardRequest).toHaveBeenCalledWith({
        workspace: expect.any(Object),
        request: pendingRequest,
        channel: expect.any(String),
        ts: expect.any(String),
        deletedByMemberId: admin.id,
      });
    });

    test("should delete an approved request and refund points", async () => {
      const memberBefore = workspace.members.find(
        (m) => m.id === requester.id
      )!;
      const updatedWorkspace = await deleteRewardRequest({
        workspace,
        input: { rewardRequestId: approvedRequest.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
      });

      const memberAfter = updatedWorkspace.members.find(
        (m) => m.id === requester.id
      )!;
      const reward = workspace.rewardsSettings.rewards.find(
        (r) => r.id === approvedRequest.rewardId
      )!;

      expect(memberAfter.coinsBalance).toBe(
        memberBefore.coinsBalance + reward.points
      );
    });

    test("should log activities when deleting an approved request", async () => {
      const reward = workspace.rewardsSettings.rewards.find(
        (r) => r.id === approvedRequest.rewardId
      )!;

      const updatedWorkspace = await deleteRewardRequest({
        workspace,
        input: { rewardRequestId: approvedRequest.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
      });

      expect(activityLog.add).toHaveBeenCalledWith({
        type: ActivityEventType.coinsBalanceChanged,
        workspaceId: updatedWorkspace.id,
        data: {
          memberId: requester.id,
          increase: reward.points,
          prevBalance: initialRequester.coinsBalance - reward.points,
          updateType: CoinsBalanceChangedType.rewardDeleted,
          initiatorId: admin.id,
        },
      });
      expect(activityLog.add).toHaveBeenCalledWith({
        type: ActivityEventType.rewardRequestChanged,
        workspaceId: updatedWorkspace.id,
        data: {
          memberId: requester.id,
          requestId: approvedRequest.id,
          updateType: RewardRequestChangedType.delete,
          initiatorId: admin.id,
        },
      });
    });

    test("should call slack adapter when admin deletes an approved request", async () => {
      await deleteRewardRequest({
        workspace,
        input: { rewardRequestId: approvedRequest.id, initiatorId: admin.id },
        activityLog,
        slackAdapter,
        logger,
      });

      const notification = approvedRequest.notifications.find(
        (n) => n.type === RewardNotificationType.RewardStatusToRequester
      )!;

      expect(
        slackAdapter.notifyThatApprovedRequestWasDeleted
      ).toHaveBeenCalledWith(
        notification.recipientId,
        admin.id,
        DeletedRequestNotificationsType.ForRequesterMessage,
        notification.ts
      );
    });
  });
});
