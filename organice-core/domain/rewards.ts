// eslint-disable-next-line max-classes-per-file
import assert from "assert";
import crypto from "crypto";

import { addMinutes, isAfter } from "date-fns";

import {
  logCoinsBalanceChangeActivity,
  ActivityLog,
  CoinsBalanceChangedType,
  RewardRequestChangedType,
  logRewardRequestActivity,
} from "./activityLog";
import { getNotificationPeriod } from "./data-completion";

import {
  Workspace,
  ReadonlyDeep,
  buildReward,
  AddRewardInput,
  Member,
  replaceMember,
  RewardRequestStatus,
  RewardRequest,
  RewardActivity,
  RewardSettings,
  RewardActivityType,
  Logger,
  SlackAdapter,
  RewardNotification,
  RewardNotificationType,
  Schedule,
  DeletedRequestNotificationsType,
} from ".";

export class RewardNotActiveError extends Error {
  constructor() {
    super("Reward is not active");
  }
}

export class NotEnoughPointsError extends Error {
  constructor(public userPoints: number) {
    super("Not enough points");
  }
}

async function deleteRewardReminder(
  slackAdapter: SlackAdapter,
  now: Date,
  rewardRequest: ReadonlyDeep<RewardRequest>,
  rewardReminder: ReadonlyDeep<RewardNotification>
): Promise<ReadonlyDeep<RewardRequest>> {
  const rewardReminderIndex = rewardRequest.notifications.findIndex(
    (nt) => nt === rewardReminder
  );

  assert(rewardReminderIndex !== -1, "Expected reward reminder to exist");
  assert(
    rewardReminder.scheduleTime,
    "Expected reward reminder schedule time to exist"
  );

  if (
    !rewardReminder.deleted &&
    isAfter(rewardReminder.scheduleTime as Date, now)
  ) {
    await slackAdapter.deleteReminder(
      rewardReminder.recipientId,
      rewardReminder.ts
    );

    const newNotifications = [...rewardRequest.notifications];

    newNotifications[rewardReminderIndex] = {
      ...rewardReminder,
      deleted: true,
    };

    return {
      ...rewardRequest,
      notifications: newNotifications,
    };
  }

  return rewardRequest;
}

async function notifyThatApprovedRequestWasDeleted(
  slackAdapter: SlackAdapter,
  request: ReadonlyDeep<RewardRequest>,
  initiator: ReadonlyDeep<Member>
): Promise<void> {
  const requesterMessages = request.notifications.filter(
    (n) => n.type === RewardNotificationType.RewardStatusToRequester
  );

  for (const requesterMessage of requesterMessages) {
    await slackAdapter.notifyThatApprovedRequestWasDeleted(
      requesterMessage.recipientId,
      initiator.id,
      DeletedRequestNotificationsType.ForRequesterMessage,
      requesterMessage.ts
    );
  }
}

async function notifyThatRewardIsNoLongerAvailable({
  workspace,
  rewardId,
  slackAdapter,
  activityLog,
  logger,
}: {
  workspace: ReadonlyDeep<Workspace>;
  rewardId: string;
  slackAdapter: SlackAdapter;
  activityLog: ActivityLog;
  logger: Logger;
}): Promise<ReadonlyDeep<Workspace>> {
  const pendingRequests = workspace.rewardsRequests.filter(
    (rr) =>
      rr.rewardId === rewardId && rr.status === RewardRequestStatus.Pending
  );

  let updatedWorkspace = workspace;

  for (const pendingRequest of pendingRequests) {
    const rejectedResponse = await rejectRewardRequest({
      workspace: updatedWorkspace,
      input: {
        rewardRequestId: pendingRequest.id,
        rejectReason:
          "Auto-rejected because the reward is no longer available.",
      },
      activityLog,
      slackAdapter,
      logger,
    });

    updatedWorkspace = rejectedResponse[0];
  }

  return updatedWorkspace;
}

export async function deleteRewardReminders(
  request: ReadonlyDeep<RewardRequest>,
  slackAdapter: SlackAdapter,
  now: Date,
  logger: Logger
): Promise<ReadonlyDeep<RewardRequest>> {
  let updatedRequest = request;
  const reminders = request.notifications.filter(
    (nt) => nt.type === RewardNotificationType.RewardReminder
  );

  for (const reminder of reminders) {
    updatedRequest = await deleteRewardReminder(
      slackAdapter,
      now,
      updatedRequest,
      reminder
      // eslint-disable-next-line @typescript-eslint/no-loop-func
    ).catch((error) => {
      logger.error(error);

      return updatedRequest;
    });
  }

  return updatedRequest;
}

export function replaceRewardRequest(
  workspace: ReadonlyDeep<Workspace>,
  rewardRequest: ReadonlyDeep<RewardRequest>
): ReadonlyDeep<Workspace> {
  const index = workspace.rewardsRequests.findIndex(
    (r) => r.id === rewardRequest.id
  );

  if (index === -1) {
    return workspace;
  }

  const requests = [...workspace.rewardsRequests];

  requests[index] = rewardRequest;

  return {
    ...workspace,
    rewardsRequests: requests,
  };
}

export function getKudosRewardPoints(
  rewardsSettings: ReadonlyDeep<RewardSettings>,
  quantity: number
): number {
  const kudosActivity = rewardsSettings.activities.find(
    (a) => a.type === RewardActivityType.Kudos
  );

  if (!kudosActivity) {
    return 0;
  }

  return kudosActivity.points * quantity;
}

export function getSurveyRewardPoints(
  rewardsSettings: ReadonlyDeep<RewardSettings>
): number {
  const surveyActivity = rewardsSettings.activities.find(
    (a) => a.type === RewardActivityType.Survey
  );

  if (!surveyActivity) {
    return 0;
  }

  return surveyActivity.points;
}

export function getProfileCompletionRewardPoints(
  rewardsSettings: ReadonlyDeep<RewardSettings>
): number {
  const profileCompletionActivity = rewardsSettings.activities.find(
    (a) => a.type === RewardActivityType.Profile
  );

  if (!profileCompletionActivity) {
    return 0;
  }

  return profileCompletionActivity.points;
}

export async function addOrUpdateReward({
  workspace,
  input,
  slackAdapter,
  activityLog,
  logger,
}: {
  workspace: ReadonlyDeep<Workspace>;
  input: AddRewardInput;
  slackAdapter: SlackAdapter;
  activityLog: ActivityLog;
  logger: Logger;
}): Promise<ReadonlyDeep<Workspace>> {
  const rewards = input.id
    ? workspace.rewardsSettings.rewards.map((r) => {
        if (r.id === input.id) {
          return { ...r, ...input };
        }

        return r;
      })
    : [...workspace.rewardsSettings.rewards, buildReward(input)];

  if (input.id && !input.isActive) {
    workspace = await notifyThatRewardIsNoLongerAvailable({
      workspace,
      rewardId: input.id,
      slackAdapter,
      activityLog,
      logger,
    });
  }

  return {
    ...workspace,
    rewardsSettings: { ...workspace.rewardsSettings, rewards },
  };
}

export async function deleteReward({
  workspace,
  rewardId,
  slackAdapter,
  activityLog,
  logger,
}: {
  workspace: ReadonlyDeep<Workspace>;
  rewardId: string;
  slackAdapter: SlackAdapter;
  activityLog: ActivityLog;
  logger: Logger;
}): Promise<ReadonlyDeep<Workspace>> {
  workspace = await notifyThatRewardIsNoLongerAvailable({
    workspace,
    rewardId,
    slackAdapter,
    activityLog,
    logger,
  });

  return {
    ...workspace,
    rewardsSettings: {
      ...workspace.rewardsSettings,
      rewards: workspace.rewardsSettings.rewards.filter(
        (item) => item.id !== rewardId
      ),
    },
  };
}

export async function approveRewardRequest({
  workspace,
  input,
  activityLog,
  slackAdapter,
  logger,
  now = new Date(),
}: {
  workspace: ReadonlyDeep<Workspace>;
  input: {
    requestId: string;
    initiatorId: string;
  };
  activityLog: ActivityLog;
  slackAdapter: SlackAdapter;
  logger: Logger;
  now?: Date;
}): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<RewardRequest>[]]> {
  const { requestId, initiatorId } = input;
  const affectedRequests: ReadonlyDeep<RewardRequest>[] = [];

  const initiator = workspace.members.find((m) => m.id === initiatorId);

  assert(initiator, "Initiator not found");
  assert(initiator.isAdmin, "Only admins can approve reward requests");

  const rewardRequest = workspace.rewardsRequests.find(
    (rr) => rr.id === requestId
  );

  assert(rewardRequest, "Reward request not found");
  assert(
    rewardRequest.status === RewardRequestStatus.Pending,
    "Reward request is not pending"
  );

  const member = workspace.members.find((m) => m.id === rewardRequest.memberId);

  assert(member, "Member not found");

  const reward = workspace.rewardsSettings.rewards.find(
    (r) => r.id === rewardRequest.rewardId
  );

  assert(reward, "Reward not found");
  assert(
    member.coinsBalance >= reward.points,
    `Member does not have enough points ${member.coinsBalance} ${reward.points}`
  );

  let updatedRequest: ReadonlyDeep<RewardRequest> = {
    ...rewardRequest,
    status: RewardRequestStatus.Approved,
    handledBy: initiatorId,
    createdAt: new Date(rewardRequest.createdAt as Date),
    updatedAt: new Date(),
  };

  updatedRequest = await deleteRewardReminders(
    updatedRequest,
    slackAdapter,
    now,
    logger
  );

  let updatedWorkspace: ReadonlyDeep<Workspace> = replaceRewardRequest(
    workspace,
    updatedRequest
  );

  const [updatedMember, afterBalanceUpdateWorkspace] = updateCoinsBalance({
    workspace: updatedWorkspace,
    member,
    input: {
      increase: -reward.points,
      type: CoinsBalanceChangedType.rewardApproved,
      initiatorId,
    },
    slackAdapter,
    activityLog,
    logger,
  });

  updatedWorkspace = afterBalanceUpdateWorkspace;

  const pendingRequests = updatedWorkspace.rewardsRequests.filter(
    (rr) =>
      rr.memberId === member.id && rr.status === RewardRequestStatus.Pending
  );

  for (const pendingRequest of pendingRequests) {
    const pendingReward = updatedWorkspace.rewardsSettings.rewards.find(
      (r) => r.id === pendingRequest.rewardId
    );

    if (pendingReward && updatedMember.coinsBalance < pendingReward.points) {
      const rejectedResponse = await rejectRewardRequest({
        workspace: updatedWorkspace,
        input: {
          rewardRequestId: pendingRequest.id,
          rejectReason:
            "Auto-rejected due to insufficient funds after another reward approval.",
        },
        activityLog,
        slackAdapter,
        logger,
      });

      updatedWorkspace = rejectedResponse[0];
      const rejectedRequest = rejectedResponse[1];

      affectedRequests.push(rejectedRequest);
    }
  }

  void (async () => {
    try {
      await logRewardRequestActivity(activityLog, updatedWorkspace, {
        memberId: member.id,
        requestId,
        updateType: RewardRequestChangedType.approve,
        initiatorId,
      });
    } catch (error) {
      logger.error("Failed to log reward request approval activity", {
        error,
        rewardRequestId: requestId,
        workspaceId: updatedWorkspace.id,
      });
    }
  })();

  void (async () => {
    const adminNotifications = updatedRequest.notifications.filter(
      (nt) => nt.type === RewardNotificationType.RewardRequest
    );

    for (const adminNotification of adminNotifications) {
      await slackAdapter
        .updateRewardRequest({
          workspace: updatedWorkspace,
          request: updatedRequest,
          channel: adminNotification.recipientId,
          ts: adminNotification.ts,
        })
        .catch((error) => {
          logger.error(error);
        });
    }
  })();

  try {
    updatedRequest = await notifyRequesterAboutRequestChange(
      slackAdapter,
      updatedWorkspace,
      updatedRequest
    );

    updatedWorkspace = replaceRewardRequest(updatedWorkspace, updatedRequest);
  } catch (error) {
    logger.error(
      "Failed to send reward request approval notification to Slack",
      {
        error,
        rewardRequestId: updatedRequest.id,
        workspaceId: updatedWorkspace.id,
      }
    );
  }

  affectedRequests.push(updatedRequest);

  return [updatedWorkspace, affectedRequests];
}

export async function deleteRewardRequest({
  workspace,
  input,
  activityLog,
  slackAdapter,
  logger,
}: {
  workspace: ReadonlyDeep<Workspace>;
  input: {
    rewardRequestId: string;
    initiatorId: string;
  };
  activityLog: ActivityLog;
  slackAdapter: SlackAdapter;
  logger: Logger;
}): Promise<ReadonlyDeep<Workspace>> {
  const { rewardRequestId, initiatorId } = input;

  const rewardRequest = workspace.rewardsRequests.find(
    (rr) => rr.id === rewardRequestId
  );

  assert(rewardRequest, "Reward request not found");

  const initiator = workspace.members.find((m) => m.id === initiatorId);

  assert(initiator, "Initiator not found");

  assert(
    rewardRequest.memberId === initiatorId || initiator.isAdmin,
    "Only the request owner or an admin can delete a reward request"
  );

  const member = workspace.members.find((m) => m.id === rewardRequest.memberId);

  assert(member, "Member not found");

  let updatedWorkspace = workspace;

  if (rewardRequest.status === RewardRequestStatus.Approved) {
    const reward = updatedWorkspace.rewardsSettings.rewards.find(
      (r) => r.id === rewardRequest.rewardId
    );

    assert(reward, "Reward not found");

    const [, afterBalanceUpdateWorkspace] = updateCoinsBalance({
      workspace: updatedWorkspace,
      member,
      input: {
        increase: reward.points,
        type: CoinsBalanceChangedType.rewardDeleted,
        initiatorId,
      },
      slackAdapter,
      activityLog,
      logger,
    });

    updatedWorkspace = afterBalanceUpdateWorkspace;

    if (rewardRequest.memberId !== initiator.id) {
      await notifyThatApprovedRequestWasDeleted(
        slackAdapter,
        rewardRequest,
        initiator
      );
    }
  }

  if (rewardRequest.status === RewardRequestStatus.Pending) {
    const messages = rewardRequest.notifications.filter(
      (nt) => nt.type === RewardNotificationType.RewardRequest
    );

    for (const message of messages) {
      await slackAdapter
        .updateRewardRequest({
          workspace: updatedWorkspace,
          request: rewardRequest,
          channel: message.recipientId,
          ts: message.ts,
          deletedByMemberId: initiatorId,
        })
        .catch((error) => {
          logger.error(error);
        });
    }
  }

  void (async () => {
    try {
      await logRewardRequestActivity(activityLog, updatedWorkspace, {
        memberId: member.id,
        requestId: rewardRequestId,
        updateType: RewardRequestChangedType.delete,
        initiatorId,
      });
    } catch (error) {
      logger.error("Failed to log reward request deletion activity", {
        error,
        rewardRequestId,
        workspaceId: updatedWorkspace.id,
      });
    }
  })();

  updatedWorkspace = {
    ...updatedWorkspace,
    rewardsRequests: updatedWorkspace.rewardsRequests.filter(
      (r) => r.id !== rewardRequest.id
    ),
  };

  return updatedWorkspace;
}

export async function rejectRewardRequest({
  workspace,
  input,
  activityLog,
  slackAdapter,
  logger,
}: {
  workspace: ReadonlyDeep<Workspace>;
  input: {
    rewardRequestId: string;
    initiatorId?: string;
    rejectReason?: string;
  };
  activityLog: ActivityLog;
  slackAdapter: SlackAdapter;
  logger: Logger;
}): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<RewardRequest>]> {
  const { rewardRequestId, initiatorId, rejectReason } = input;

  if (initiatorId) {
    const initiator = workspace.members.find((m) => m.id === initiatorId);

    assert(initiator, "Initiator not found");
    assert(initiator.isAdmin, "Only admins can reject reward requests");
  }

  const rewardRequest = workspace.rewardsRequests.find(
    (rr) => rr.id === rewardRequestId
  );

  assert(rewardRequest, "Reward request not found");
  assert(
    rewardRequest.status === RewardRequestStatus.Pending,
    "Reward request is not pending"
  );

  const member = workspace.members.find((m) => m.id === rewardRequest.memberId);

  assert(member, "Member not found");

  let updatedRequest: ReadonlyDeep<RewardRequest> = {
    ...rewardRequest,
    status: RewardRequestStatus.Rejected,
    handledBy: initiatorId,
    rejectReason,
    createdAt: new Date(rewardRequest.createdAt.getTime()),
    updatedAt: new Date(),
  };

  updatedRequest = await deleteRewardReminders(
    updatedRequest,
    slackAdapter,
    new Date(),
    logger
  );

  let updatedWorkspace = replaceRewardRequest(workspace, updatedRequest);

  void (async () => {
    try {
      await logRewardRequestActivity(activityLog, updatedWorkspace, {
        memberId: member.id,
        requestId: rewardRequestId,
        updateType: RewardRequestChangedType.reject,
        initiatorId,
      });
    } catch (error) {
      logger.error("Failed to log reward request rejection activity", {
        error,
        rewardRequestId,
        workspaceId: updatedWorkspace.id,
      });
    }
  })();

  void (async () => {
    const adminNotifications = updatedRequest.notifications.filter(
      (nt) => nt.type === RewardNotificationType.RewardRequest
    );

    for (const adminNotification of adminNotifications) {
      await slackAdapter
        .updateRewardRequest({
          workspace: updatedWorkspace,
          request: updatedRequest,
          channel: adminNotification.recipientId,
          ts: adminNotification.ts,
        })
        .catch((error) => {
          logger.error(error);
        });
    }
  })();

  try {
    updatedRequest = await notifyRequesterAboutRequestChange(
      slackAdapter,
      updatedWorkspace,
      updatedRequest
    );

    updatedWorkspace = replaceRewardRequest(updatedWorkspace, updatedRequest);
  } catch (error) {
    logger.error(
      "Failed to send reward request approval notification to Slack",
      {
        error,
        rewardRequestId: updatedRequest.id,
        workspaceId: updatedWorkspace.id,
      }
    );
  }

  return [updatedWorkspace, updatedRequest];
}

interface UpdateRewardsSettingsInput {
  sendPointsNotifications: boolean;
  activities: RewardActivity[];
}

export function updateRewardsSettings(
  workspace: ReadonlyDeep<Workspace>,
  input: UpdateRewardsSettingsInput
): ReadonlyDeep<Workspace> {
  return {
    ...workspace,
    rewardsSettings: {
      ...workspace.rewardsSettings,
      ...input,
    },
  };
}

export function updateCoinsBalance({
  workspace,
  member,
  input,
  slackAdapter,
  activityLog,
  logger,
}: {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<Member>;
  input: {
    absolute?: number;
    increase?: number;
    type: CoinsBalanceChangedType;
    activityType?: RewardActivityType;
    initiatorId?: string;
  };
  slackAdapter: SlackAdapter;
  activityLog: ActivityLog;
  logger: Logger;
}): [ReadonlyDeep<Member>, ReadonlyDeep<Workspace>] {
  const { absolute, increase, initiatorId, activityType, type } = input;

  assert(
    typeof absolute === "number" || typeof increase === "number",
    "Expected either absolute or increase to be provided"
  );

  const activity = workspace.rewardsSettings.activities.find(
    (a) => a.type === activityType
  );

  if (activityType) {
    assert(activity, "Activity type not found");
  }

  const updatedBalance = absolute ?? member.coinsBalance + (increase ?? 0);

  if (updatedBalance === member.coinsBalance) {
    return [member, workspace];
  }

  const updatedMember = {
    ...member,
    coinsBalance: updatedBalance,
  };

  const updatedWorkspace = replaceMember(workspace, updatedMember);

  void (async () => {
    if (member.coinsBalance !== updatedMember.coinsBalance) {
      try {
        await logCoinsBalanceChangeActivity(activityLog, updatedWorkspace, {
          memberId: member.id,
          activityType,
          prevBalance: member.coinsBalance,
          absolute,
          increase,
          // NOTE: empty initiatorId means that the balance was changed by the system
          initiatorId,
          updateType: type,
        });
      } catch (error) {
        logger.error("Failed to log coins balance change activity", {
          error,
          memberId: member.id,
          workspaceId: updatedWorkspace.id,
        });
      }
    }
  })();

  if (workspace.rewardsSettings.sendPointsNotifications) {
    void slackAdapter.notifyMemberAboutCoinsBalanceChange({
      workspace: updatedWorkspace,
      member: updatedMember,
      activityType,
      increase,
      absolute,
    });
  }

  return [updatedMember, updatedWorkspace];
}

export interface MemberCoinsEntry {
  member: ReadonlyDeep<Member>;
  place: number;
  lastActivity: Date | null;
}

export function getMembersCoins(
  workspace: ReadonlyDeep<Workspace>
): MemberCoinsEntry[] {
  const sortedMembers = [...workspace.members].sort(
    (a, b) =>
      getMemberCoinsBalance(workspace, b.id).coinsEarned -
      getMemberCoinsBalance(workspace, a.id).coinsEarned
  );
  const rewardsRequests = workspace.rewardsRequests;

  const membersCoins = sortedMembers.map((member, index) => {
    const lastActivityDate = rewardsRequests.find(
      (r) => r.memberId === member.id
    )?.updatedAt;

    return {
      member,
      place: index + 1,
      lastActivity: lastActivityDate
        ? new Date(lastActivityDate as Date)
        : null,
    };
  });

  return membersCoins;
}

export function getMemberCoins(
  workspace: ReadonlyDeep<Workspace>,
  memberId: string
): MemberCoinsEntry | undefined {
  const sortedMembers = [...workspace.members].sort(
    (a, b) =>
      getMemberCoinsBalance(workspace, b.id).coinsEarned -
      getMemberCoinsBalance(workspace, a.id).coinsEarned
  );
  const rewardsRequests = workspace.rewardsRequests;

  const member = sortedMembers.find((m) => m.id === memberId);

  if (!member) return undefined;

  const lastActivityDate = rewardsRequests.find(
    (r) => r.memberId === member.id
  )?.updatedAt;

  return {
    member,
    place: sortedMembers.indexOf(member) + 1,
    lastActivity: lastActivityDate ? new Date(lastActivityDate as Date) : null,
  };
}

export function getMemberCoinsBalance(
  workspace: ReadonlyDeep<Workspace>,
  memberId: string
): {
  balance: number;
  coinsEarned: number;
  coinsSpent: number;
} {
  const member = workspace.members.find((m) => m.id === memberId);

  assert(member, "Member not found");

  const acceptedRewardRequests = workspace.rewardsRequests.filter(
    (request) =>
      request.memberId === member.id &&
      request.status === RewardRequestStatus.Approved
  );
  const rewards = workspace.rewardsSettings.rewards;

  const spentPoints = acceptedRewardRequests.reduce((sum, request) => {
    return sum + (rewards.find((r) => r.id === request.rewardId)?.points ?? 0);
  }, 0);

  return {
    coinsEarned: member.coinsBalance + spentPoints,
    coinsSpent: spentPoints,
    balance: member.coinsBalance,
  };
}

export function getTopCoinEarner(
  workspace: ReadonlyDeep<Workspace>
): { member: ReadonlyDeep<Member>; points: number } | undefined {
  const membersWithPoints = workspace.members.map((member) => ({
    member,
    points: getMemberCoinsBalance(workspace, member.id).coinsEarned,
  }));

  if (!membersWithPoints.length) {
    return undefined;
  }

  const topEarner = membersWithPoints.sort((a, b) => b.points - a.points)[0];

  return topEarner;
}

interface RequestRewardData {
  rewardId: string;
  comment?: string | null;
}

async function notifyAdminsAboutNewRewardRequest(
  workspace: ReadonlyDeep<Workspace>,
  slackAdapter: SlackAdapter,
  request: ReadonlyDeep<RewardRequest>
): Promise<ReadonlyDeep<RewardRequest>> {
  const member = workspace.members.find((m) => m.id === request.memberId);

  assert(member, "Member not found");
  assert(workspace.slackBotToken, "Expected Slack bot token to exist");

  const admins = workspace.members.filter((m) => m.isAdmin);

  const channels = admins.map((m) => m.id);

  for (const channel of channels) {
    const response = await slackAdapter.sendRewardRequest(
      workspace,
      request,
      channel
    );

    request = {
      ...request,
      notifications: [
        ...request.notifications,
        {
          ts: response.ts,
          recipientId: response.channel,
          type: RewardNotificationType.RewardRequest,
        },
      ],
    };

    const scheduleTime = addMinutes(
      request.createdAt as Date,
      getNotificationPeriod(Schedule.DAY) / 1000 / 60
    );

    const reminderResponse = await slackAdapter.sendRequestReminder(
      channel,
      scheduleTime,
      response.ts
    );

    request = {
      ...request,
      notifications: [
        ...request.notifications,
        {
          ts: reminderResponse.ts,
          recipientId: reminderResponse.channel,
          type: RewardNotificationType.RewardReminder,
          scheduleTime,
        },
      ],
    };
  }

  return request;
}

async function notifyRequesterAboutRequestChange(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  request: ReadonlyDeep<RewardRequest>
): Promise<ReadonlyDeep<RewardRequest>> {
  const response = await slackAdapter.notifyRequesterAboutRewardRequest(
    workspace,
    request
  );

  return {
    ...request,
    updatedAt: new Date(),
    notifications: [
      ...request.notifications,
      {
        type: RewardNotificationType.RewardStatusToRequester,
        ts: response.ts,
        recipientId: request.memberId,
      },
    ],
  };
}

export const requestReward = async ({
  workspace,
  member,
  input,
  onValidationPass,
  activitiesLog,
  slackAdapter,
  logger,
}: {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<Member>;
  input: RequestRewardData;
  onValidationPass: () => Promise<void>;
  activitiesLog: ActivityLog;
  slackAdapter: SlackAdapter;
  logger: Logger;
}): Promise<ReadonlyDeep<Workspace>> => {
  const reward = workspace.rewardsSettings.rewards.find(
    (r) => r.id === input.rewardId
  );

  if (!reward) {
    throw new Error("Reward not found");
  }

  if (!reward.isActive) {
    throw new RewardNotActiveError();
  }

  if (member.coinsBalance < reward.points) {
    throw new NotEnoughPointsError(member.coinsBalance);
  }

  await onValidationPass();

  const newRequest: RewardRequest = {
    id: crypto.randomBytes(10).toString("hex"),
    rewardId: input.rewardId,
    memberId: member.id,
    status: RewardRequestStatus.Pending,
    createdAt: new Date(),
    updatedAt: new Date(),
    comment: input.comment ?? undefined,
    notifications: [],
  };

  let updatedWorkspace: ReadonlyDeep<Workspace> = {
    ...workspace,
    rewardsRequests: [...workspace.rewardsRequests, newRequest],
  };

  logger.info(`Member ${member.id} requested reward ${input.rewardId}`);

  await logRewardRequestActivity(activitiesLog, updatedWorkspace, {
    memberId: member.id,
    requestId: newRequest.id,
    updateType: RewardRequestChangedType.create,
    initiatorId: member.id,
  });

  try {
    const request = await notifyAdminsAboutNewRewardRequest(
      updatedWorkspace,
      slackAdapter,
      newRequest
    );

    updatedWorkspace = replaceRewardRequest(updatedWorkspace, request);
  } catch (error) {
    logger.error("Failed to send reward request notification to Slack", {
      error,
      rewardRequestId: newRequest.id,
      workspaceId: updatedWorkspace.id,
    });
  }

  return updatedWorkspace;
};
