# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.idea/
.DS_Store
Thumbs.db

# Pulumi
/bin/
*.pem

# debug
npm-debug.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
**/tsconfig.tsbuildinfo
**/.tsbuildinfo

# dynaconf
*/.secrets.toml

__pycache__

# deployment
app-spec.yaml

.vscode

# Sentry Config File
.env.sentry-build-plugin
