/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/no-unused-vars */
import assert from 'node:assert';
import * as path from 'node:path';
import * as app from '@pulumi/azure-native/app';
import * as cognitiveservices from '@pulumi/azure-native/cognitiveservices';
import * as containerregistry from '@pulumi/azure-native/containerregistry';
import * as insights from '@pulumi/azure-native/insights';
import * as network from '@pulumi/azure-native/network';
import * as operationalinsights from '@pulumi/azure-native/operationalinsights';
import * as resources from '@pulumi/azure-native/resources';
import * as docker from '@pulumi/docker';
import * as pulumi from '@pulumi/pulumi';

assert(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  'Expected NEXT_PUBLIC_SUPABASE_URL env variable',
);
assert(
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  'Expected NEXT_PUBLIC_SUPABASE_ANON_KEY',
);
assert(
  process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY,
  'Expected NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY',
);
assert(
  process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL,
  'Expected NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL',
);
assert(
  process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY,
  'Expected NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY',
);
assert(
  process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL,
  'Expected NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL',
);
assert(
  process.env.NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY,
  'Expected NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY',
);
assert(
  process.env.NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL,
  'Expected NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL',
);
assert(process.env.STRIPE_SECRET_KEY, 'Expected STRIPE_SECRET_KEY');
assert(process.env.STRIPE_WEBHOOK_SECRET, 'Expected STRIPE_WEBHOOK_SECRET');
assert(process.env.SUPABASE_SERVICE_KEY, 'Expected SUPABASE_SERVICE_KEY');
assert(process.env.PARSING_API_TOKEN, 'Expected PARSING_API_TOKEN');
assert(process.env.PARSING_SENTRY_URL, 'Expected PARSING_SENTRY_URL');
assert(process.env.WORKER_SENTRY_URL, 'Expected WORKER_SENTRY_URL');
assert(process.env.POSTMARK_SERVER_API, 'Expected POSTMARK_SERVER_API');
assert(
  process.env.SUPABASE_POSTGRES_CONNECTION_URL,
  'Expected SUPABASE_POSTGRES_CONNECTION_URL',
);

const config = new pulumi.Config();
const suffix = config.require('azure_resource_suffix');
const containerPort = 3000;
const enableHttps = true; // set to false when deploying an environment for the first time
const supabasePostgresConnectionUrl = new URL(
  process.env.SUPABASE_POSTGRES_CONNECTION_URL,
);

const resourceGroup = new resources.ResourceGroup('resourceGroup', {
  location: 'uksouth',
  resourceGroupName: suffix,
});

const registry = new containerregistry.Registry('registry', {
  resourceGroupName: resourceGroup.name,
  sku: { name: 'Basic' },
  adminUserEnabled: true,
});

const registryCredentials = containerregistry.listRegistryCredentialsOutput({
  resourceGroupName: resourceGroup.name,
  registryName: registry.name,
});

const registryAdminUsername = registryCredentials.apply(
  credentials => credentials.username!,
);
const registryAdminPassword = registryCredentials.apply(
  credentials => credentials.passwords![0].value!,
);

const nextJsImage = new docker.Image('nextJsImage', {
  imageName: pulumi.interpolate`${registry.loginServer}/${suffix}:latest`,
  build: {
    context: path.resolve(__dirname),
    dockerfile: path.resolve(__dirname, 'astrala-site', 'Dockerfile'),
    platform: 'linux/amd64',
    args: {
      NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY:
        process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY,
      NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL:
        process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL,
      NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY:
        process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY,
      NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL:
        process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL,
      NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY:
        process.env.NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY,
      NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL:
        process.env.NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    },
  },
  registry: {
    server: registry.loginServer,
    username: registryAdminUsername,
    password: registryAdminPassword,
  },
});

const parsingImage = new docker.Image('parsingImage', {
  imageName: pulumi.interpolate`${registry.loginServer}/${suffix}-parsing:latest`,
  build: {
    context: path.resolve(__dirname, 'parsing'),
    dockerfile: path.resolve(__dirname, 'parsing', 'Dockerfile'),
    platform: 'linux/amd64',
  },
  registry: {
    server: registry.loginServer,
    username: registryAdminUsername,
    password: registryAdminPassword,
  },
});

const workerImage = new docker.Image('workerImage', {
  imageName: pulumi.interpolate`${registry.loginServer}/${suffix}-worker:latest`,
  build: {
    context: path.resolve(__dirname, 'parsing'),
    dockerfile: path.resolve(__dirname, 'parsing', 'Dockerfile-worker'),
    platform: 'linux/amd64',
  },
  registry: {
    server: registry.loginServer,
    username: registryAdminUsername,
    password: registryAdminPassword,
  },
});

const cronImage = new docker.Image(
  'cronImage',
  {
    imageName: pulumi.interpolate`${registry.loginServer}/${suffix}-cron:latest`,
    build: {
      context: path.resolve(__dirname),
      dockerfile: path.resolve(__dirname, 'astrala-cron', 'Dockerfile'),
      platform: 'linux/amd64',
    },
    registry: {
      server: registry.loginServer,
      username: registryAdminUsername,
      password: registryAdminPassword,
    },
  },
  {
    dependsOn: [nextJsImage, parsingImage, workerImage],
  },
);

const logsWorkspace = new operationalinsights.Workspace('logsWorkspace', {
  resourceGroupName: resourceGroup.name,
  workspaceName: pulumi.interpolate`${suffix}-logs`,
  location: resourceGroup.location,
  sku: {
    name: 'PerGB2018',
  },
  retentionInDays: 30,
});

const logsWorkspaceSharedKeys =
  operationalinsights.getWorkspaceSharedKeysOutput({
    resourceGroupName: resourceGroup.name,
    workspaceName: logsWorkspace.name,
  });

const environment = new app.ManagedEnvironment('environment', {
  resourceGroupName: resourceGroup.name,
  location: resourceGroup.location,
  environmentName: suffix,
  appLogsConfiguration: {
    destination: 'log-analytics',
    logAnalyticsConfiguration: {
      customerId: logsWorkspace.customerId,
      sharedKey:
        logsWorkspaceSharedKeys.primarySharedKey as pulumi.Output<string>,
    },
  },
});

const customDomainValidationRecord = new network.RecordSet(
  'customDomainValidationRecord',
  {
    resourceGroupName: config.require('azure_dns_domain_resource_group'),
    zoneName: config.require('azure_dns_domain_zone_name'),
    recordType: 'TXT',
    relativeRecordSetName: pulumi.interpolate`asuid.${config.require('azure_dns_domain').replace('.' + config.require('azure_dns_domain_zone_name'), '')}`,
    ttl: 300,
    txtRecords: [
      {
        value: [
          environment.customDomainConfiguration.apply(
            customDomainConfiguration =>
              customDomainConfiguration!.customDomainVerificationId,
          ),
        ],
      },
    ],
  },
);

let certificate: app.ManagedCertificate | null = null;
let certificateValidationRecord: network.RecordSet | null = null;

if (enableHttps) {
  certificate = new app.ManagedCertificate(
    'certificate',
    {
      resourceGroupName: resourceGroup.name,
      environmentName: environment.name,
      managedCertificateName: config.require('azure_dns_domain'),
      properties: {
        subjectName: config.require('azure_dns_domain'),
        domainControlValidation:
          app.ManagedCertificateDomainControlValidation.CNAME,
      },
    },
    { dependsOn: [customDomainValidationRecord] },
  );

  certificateValidationRecord = new network.RecordSet(
    'certificateValidationRecord',
    {
      resourceGroupName: config.require('azure_dns_domain_resource_group'),
      zoneName: config.require('azure_dns_domain_zone_name'),
      recordType: 'CNAME',
      relativeRecordSetName: pulumi.interpolate`_acme-challenge.${config.require('azure_dns_domain').replace('.' + config.require('azure_dns_domain_zone_name'), '')}`,
      ttl: 300,
      cnameRecord: {
        cname: certificate.properties.validationToken,
      },
    },
  );
}

const formRecognizer = new cognitiveservices.Account('formRecognizer', {
  resourceGroupName: resourceGroup.name,
  kind: 'FormRecognizer',
  sku: {
    name: config.require('azure_form_recoginer_sku'),
  },
  properties: {
    customSubDomainName: resourceGroup.name,
  },
});

const openAiAccount = new cognitiveservices.Account('openAiAccount', {
  resourceGroupName: resourceGroup.name,
  kind: 'OpenAI',
  sku: {
    name: 'S0',
  },
  properties: {
    customSubDomainName: resourceGroup.name,
  },
});

const openAiChatGpt4oDeployment = new cognitiveservices.Deployment(
  'openAiChatGpt4oDeployment',
  {
    resourceGroupName: resourceGroup.name,
    accountName: openAiAccount.name,
    properties: {
      model: {
        format: 'OpenAI',
        name: 'gpt-4o',
        version: '2024-08-06',
      },
    },
    sku: {
      name: 'GlobalStandard',
      capacity: 32,
    },
  },
);

const containerApp = new app.ContainerApp(
  'containerApp',
  {
    resourceGroupName: resourceGroup.name,
    managedEnvironmentId: environment.id,
    containerAppName: suffix,
    configuration: {
      ingress: {
        external: true,
        targetPort: containerPort,
        transport: 'http',
        customDomains: [
          certificate
            ? {
                bindingType: app.BindingType.SniEnabled,
                certificateId: certificate.id,
                name: config.require('azure_dns_domain'),
              }
            : {
                bindingType: app.BindingType.Disabled,
                name: config.require('azure_dns_domain'),
              },
        ],
      },
      registries: [
        {
          server: registry.loginServer,
          username: registryAdminUsername,
          passwordSecretRef: 'registry-password',
        },
      ],
      secrets: [
        {
          name: 'registry-password',
          value: registryAdminPassword,
        },
      ],
    },
    template: {
      scale: {
        minReplicas: 1,
        maxReplicas: 5,
      },
      containers: [
        {
          name: 'astrala-site',
          image: nextJsImage.repoDigest,
          resources: { cpu: 0.5, memory: '1.0Gi' },
          env: [
            {
              name: 'NEXT_PUBLIC_SUPABASE_URL',
              value: process.env.NEXT_PUBLIC_SUPABASE_URL,
            },
            {
              name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
              value: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
            },
            {
              name: 'PARSER_SERVICE_HOST',
              value: 'localhost',
            },
            {
              name: 'PARSER_SERVICE_PORT',
              value: '8000',
            },
            {
              name: 'POSTMARK_SERVER_API',
              value: process.env.POSTMARK_SERVER_API,
            },
            {
              name: 'STRIPE_SECRET_KEY',
              value: process.env.STRIPE_SECRET_KEY,
            },
            {
              name: 'STRIPE_WEBHOOK_SECRET',
              value: process.env.STRIPE_WEBHOOK_SECRET,
            },
            {
              name: 'NEXT_PUBLIC_STRIPE_EMPLOYER_PRICE',
              value: '5',
            },
            {
              name: 'NEXT_PUBLIC_STRIPE_JOB_SEEKER_PRICE',
              value: '5',
            },
            {
              name: 'STRIPE_CURRENCY',
              value: 'GBP',
            },
            {
              name: 'SUPABASE_SERVICE_KEY',
              value: process.env.SUPABASE_SERVICE_KEY,
            },
            {
              name: 'APP_URL',
              value: pulumi.interpolate`https://${config.require('azure_dns_domain')}`,
            },
            {
              name: 'PARSING_API_TOKEN',
              value: process.env.PARSING_API_TOKEN,
            },
          ],
        },
        {
          name: 'parsing',
          image: parsingImage.repoDigest,
          resources: { cpu: 0.5, memory: '1.0Gi' },
          env: [
            {
              name: 'DYNACONF_AZURE_FORM_RECOGNIZER_ENDPOINT',
              value: formRecognizer.properties.endpoint,
            },
            {
              name: 'DYNACONF_AZURE_FORM_RECOGNIZER_KEY',
              value: cognitiveservices.listAccountKeysOutput({
                resourceGroupName: resourceGroup.name,
                accountName: formRecognizer.name,
              }).key1 as pulumi.Output<string>,
            },
            {
              name: 'DYNACONF_AZURE_OPENAI_ENDPOINT',
              value: openAiAccount.properties.endpoint,
            },
            {
              name: 'DYNACONF_AZURE_OPENAI_API_KEY',
              value: cognitiveservices.listAccountKeysOutput({
                resourceGroupName: resourceGroup.name,
                accountName: openAiAccount.name,
              }).key1 as pulumi.Output<string>,
            },
            {
              name: 'DYNACONF_AZURE_DEPLOYMENT',
              value: openAiChatGpt4oDeployment.name,
            },
            {
              name: 'DYNACONF_DATABASE_DB',
              value: supabasePostgresConnectionUrl.pathname.slice(1),
            },
            {
              name: 'DYNACONF_DATABASE_HOST',
              value: supabasePostgresConnectionUrl.hostname,
            },
            {
              name: 'DYNACONF_DATABASE_PASSWORD',
              value: supabasePostgresConnectionUrl.password,
            },
            {
              name: 'DYNACONF_DATABASE_PORT',
              value: supabasePostgresConnectionUrl.port,
            },
            {
              name: 'DYNACONF_DATABASE_USER',
              value: supabasePostgresConnectionUrl.username,
            },
            {
              name: 'DYNACONF_SUPABASE_URL',
              value: process.env.NEXT_PUBLIC_SUPABASE_URL,
            },
            {
              name: 'DYNACONF_SUPABASE_KEY',
              value: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
            },
            {
              name: 'DYNACONF_API_TOKEN',
              value: process.env.PARSING_API_TOKEN,
            },
            {
              name: 'DYNACONF_SENTRY_URL',
              value: process.env.PARSING_SENTRY_URL,
            },
            {
              name: 'DYNACONF_MIN_SCORE',
              value: config.require('match_min_score'),
            },
          ],
        },
      ],
    },
  },
  {
    dependsOn: certificateValidationRecord ? [certificateValidationRecord] : [],
  },
);

const litDomain = config.get('azure_lit_dns_domain');

if (litDomain) {
  assert(
    process.env.LIT_SUPABASE_POSTGRES_CONNECTION_URL,
    'Expected LIT_SUPABASE_POSTGRES_CONNECTION_URL',
  );

  let litCertificate: app.ManagedCertificate | null =
    null as app.ManagedCertificate | null;
  let litCertificateValidationRecord: network.RecordSet | null = null;
  const litSupabasePostgresConnectionUrl = new URL(
    process.env.LIT_SUPABASE_POSTGRES_CONNECTION_URL,
  );

  const litCustomDomainValidationRecord = new network.RecordSet(
    'litCustomDomainValidationRecord',
    {
      resourceGroupName: config.require('azure_dns_domain_resource_group'),
      zoneName: config.require('azure_dns_domain_zone_name'),
      recordType: 'TXT',
      relativeRecordSetName: pulumi.interpolate`asuid.${litDomain.replace('.' + config.require('azure_dns_domain_zone_name'), '')}`,
      ttl: 300,
      txtRecords: [
        {
          value: [
            environment.customDomainConfiguration.apply(
              customDomainConfiguration =>
                customDomainConfiguration!.customDomainVerificationId,
            ),
          ],
        },
      ],
    },
  );

  if (enableHttps) {
    litCertificate = new app.ManagedCertificate(
      'litCertificate',
      {
        resourceGroupName: resourceGroup.name,
        environmentName: environment.name,
        managedCertificateName: litDomain,
        properties: {
          subjectName: litDomain,
          domainControlValidation:
            app.ManagedCertificateDomainControlValidation.CNAME,
        },
      },
      { dependsOn: [litCustomDomainValidationRecord] },
    );

    litCertificateValidationRecord = new network.RecordSet(
      'litCertificateValidationRecord',
      {
        resourceGroupName: config.require('azure_dns_domain_resource_group'),
        zoneName: config.require('azure_dns_domain_zone_name'),
        recordType: 'CNAME',
        relativeRecordSetName: pulumi.interpolate`_acme-challenge.${litDomain.replace('.' + config.require('azure_dns_domain_zone_name'), '')}`,
        ttl: 300,
        cnameRecord: {
          cname: litCertificate.properties.validationToken,
        },
      },
    );
  }

  const litImage = new docker.Image('litImage', {
    imageName: pulumi.interpolate`${registry.loginServer}/${suffix}-lit-nextjs:latest`,
    build: {
      context: __dirname,
      dockerfile: path.resolve(__dirname, 'assistant', 'Dockerfile'),
      platform: 'linux/amd64',
      args: {},
    },
    registry: {
      server: registry.loginServer,
      username: registryAdminUsername,
      password: registryAdminPassword,
    },
  });

  const litApiImage = new docker.Image('litApiImage', {
    imageName: pulumi.interpolate`${registry.loginServer}/${suffix}-lit:latest`,
    build: {
      context: path.resolve(__dirname, 'behavioral-chart'),
      dockerfile: path.resolve(__dirname, 'behavioral-chart', 'Dockerfile'),
      platform: 'linux/amd64',
      args: {},
    },
    registry: {
      server: registry.loginServer,
      username: registryAdminUsername,
      password: registryAdminPassword,
    },
  });

  const cognitiveServicesAccount = new cognitiveservices.Account(
    'cognitiveServicesAccount',
    {
      resourceGroupName: resourceGroup.name,
      kind: 'CognitiveServices',
      sku: {
        name: 'S0',
      },
      properties: {
        customSubDomainName: pulumi.concat(resourceGroup.name, '-lit'),
      },
    },
  );

  const litContainerApp = new app.ContainerApp(
    'litContainerApp',
    {
      resourceGroupName: resourceGroup.name,
      managedEnvironmentId: environment.id,
      containerAppName: 'lit-' + suffix,
      configuration: {
        ingress: {
          external: true,
          targetPort: 3000,
          transport: 'http',
          customDomains: [
            litCertificate
              ? {
                  bindingType: app.BindingType.SniEnabled,
                  certificateId: litCertificate.id,
                  name: litDomain,
                }
              : {
                  bindingType: app.BindingType.Disabled,
                  name: litDomain,
                },
          ],
        },
        registries: [
          {
            server: registry.loginServer,
            username: registryAdminUsername,
            passwordSecretRef: 'registry-password',
          },
        ],
        secrets: [
          {
            name: 'registry-password',
            value: registryAdminPassword,
          },
        ],
      },
      template: {
        scale: {
          minReplicas: 1,
          maxReplicas: 5,
        },
        containers: [
          {
            name: 'lit',
            image: litImage.repoDigest,
            resources: { cpu: 0.5, memory: '1.0Gi' },
            env: [
              {
                name: 'API_URL',
                value: 'http://localhost:8000',
              },
            ],
          },
          {
            name: 'lit-api',
            image: litApiImage.repoDigest,
            resources: { cpu: 0.5, memory: '1.0Gi' },
            env: [
              {
                name: 'DYNACONF_AZURE_FORM_RECOGNIZER_KEY',
                value: cognitiveservices.listAccountKeysOutput({
                  resourceGroupName: resourceGroup.name,
                  accountName: formRecognizer.name,
                }).key1 as pulumi.Output<string>,
              },
              {
                name: 'DYNACONF_AZURE_OPENAI_ENDPOINT',
                value: openAiAccount.properties.endpoint,
              },
              {
                name: 'DYNACONF_AZURE_OPENAI_API_KEY',
                value: cognitiveservices.listAccountKeysOutput({
                  resourceGroupName: resourceGroup.name,
                  accountName: openAiAccount.name,
                }).key1 as pulumi.Output<string>,
              },
              {
                name: 'DYNACONF_AZURE_DEPLOYMENT',
                value: openAiChatGpt4oDeployment.name,
              },
              {
                name: 'DYNACONF_DATABASE_DB',
                value: litSupabasePostgresConnectionUrl.pathname.slice(1),
              },
              {
                name: 'DYNACONF_DATABASE_HOST',
                value: litSupabasePostgresConnectionUrl.hostname,
              },
              {
                name: 'DYNACONF_DATABASE_PASSWORD',
                value: litSupabasePostgresConnectionUrl.password,
              },
              {
                name: 'DYNACONF_DATABASE_PORT',
                value: litSupabasePostgresConnectionUrl.port,
              },
              {
                name: 'DYNACONF_DATABASE_USER',
                value: litSupabasePostgresConnectionUrl.username,
              },
              {
                name: 'DYNACONF_TEXT_ANALYTICS_API_KEY',
                value: cognitiveservices.listAccountKeysOutput({
                  resourceGroupName: resourceGroup.name,
                  accountName: cognitiveServicesAccount.name,
                }).key1 as pulumi.Output<string>,
              },
              {
                name: 'DYNACONF_TEXT_ANALYTICS_ENDPOINT',
                value: cognitiveServicesAccount.properties.endpoint,
              },
            ],
          },
        ],
      },
    },
    {
      dependsOn: litCertificateValidationRecord
        ? [litCertificateValidationRecord]
        : [],
    },
  );

  const litDnsRecord = new network.RecordSet('litDnsRecord', {
    resourceGroupName: config.require('azure_dns_domain_resource_group'),
    zoneName: config.require('azure_dns_domain_zone_name'),
    recordType: 'CNAME',
    relativeRecordSetName: litDomain.replace(
      '.' + config.require('azure_dns_domain_zone_name'),
      '',
    ),
    ttl: 3600,
    cnameRecord: {
      cname: litContainerApp.configuration.apply(cfg => cfg!.ingress!.fqdn),
    },
  });
}

const workerContainerApp = new app.ContainerApp('workerContainerApp', {
  resourceGroupName: resourceGroup.name,
  managedEnvironmentId: environment.id,
  containerAppName: suffix + '-worker',
  configuration: {
    registries: [
      {
        server: registry.loginServer,
        username: registryAdminUsername,
        passwordSecretRef: 'registry-password',
      },
    ],
    secrets: [
      {
        name: 'registry-password',
        value: registryAdminPassword,
      },
    ],
  },
  template: {
    scale: {
      minReplicas: 1,
      maxReplicas: 1,
    },
    containers: [
      {
        name: 'worker',
        image: workerImage.repoDigest,
        resources: {
          cpu: config.requireNumber('azure_worker_cpu'),
          memory: config.require('azure_worker_ram'),
        },
        env: [
          {
            name: 'DYNACONF_AZURE_FORM_RECOGNIZER_ENDPOINT',
            value: formRecognizer.properties.endpoint,
          },
          {
            name: 'DYNACONF_AZURE_FORM_RECOGNIZER_KEY',
            value: cognitiveservices.listAccountKeysOutput({
              resourceGroupName: resourceGroup.name,
              accountName: formRecognizer.name,
            }).key1 as pulumi.Output<string>,
          },
          {
            name: 'DYNACONF_AZURE_OPENAI_ENDPOINT',
            value: openAiAccount.properties.endpoint,
          },
          {
            name: 'DYNACONF_AZURE_OPENAI_API_KEY',
            value: cognitiveservices.listAccountKeysOutput({
              resourceGroupName: resourceGroup.name,
              accountName: openAiAccount.name,
            }).key1 as pulumi.Output<string>,
          },
          {
            name: 'DYNACONF_AZURE_DEPLOYMENT',
            value: openAiChatGpt4oDeployment.name,
          },
          {
            name: 'DYNACONF_DATABASE_DB',
            value: supabasePostgresConnectionUrl.pathname.slice(1),
          },
          {
            name: 'DYNACONF_DATABASE_HOST',
            value: supabasePostgresConnectionUrl.hostname,
          },
          {
            name: 'DYNACONF_DATABASE_PASSWORD',
            value: supabasePostgresConnectionUrl.password,
          },
          {
            name: 'DYNACONF_DATABASE_PORT',
            value: supabasePostgresConnectionUrl.port,
          },
          {
            name: 'DYNACONF_DATABASE_USER',
            value: supabasePostgresConnectionUrl.username,
          },
          {
            name: 'DYNACONF_SUPABASE_URL',
            value: process.env.NEXT_PUBLIC_SUPABASE_URL,
          },
          {
            name: 'DYNACONF_SUPABASE_KEY',
            value: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
          },
          {
            name: 'DYNACONF_SENTRY_URL',
            value: process.env.WORKER_SENTRY_URL,
          },
          {
            name: 'DYNACONF_DEFAULT_STATUS',
            value: 'PENDING',
          },
          {
            name: 'DYNACONF_LOG_TO_SENTRY',
            value: 'true',
          },
          {
            name: 'DYNACONF_JOB_EXPERIENCE_MATCH_MIN_SCORE',
            value: '0.5',
          },
          {
            name: 'DYNACONF_MIN_SCORE',
            value: config.require('match_min_score'),
          },
          {
            name: 'DYNACONF_NEW_MATCHES_LIMIT',
            value: '10',
          },
        ],
      },
    ],
  },
});

const cronContainerApp = new app.ContainerApp('cronContainerApp', {
  resourceGroupName: resourceGroup.name,
  managedEnvironmentId: environment.id,
  containerAppName: suffix + '-cron',
  configuration: {
    registries: [
      {
        server: registry.loginServer,
        username: registryAdminUsername,
        passwordSecretRef: 'registry-password',
      },
    ],
    secrets: [
      {
        name: 'registry-password',
        value: registryAdminPassword,
      },
    ],
  },
  template: {
    scale: {
      minReplicas: 1,
      maxReplicas: 1,
    },
    containers: [
      {
        name: 'cron',
        image: cronImage.repoDigest,
        resources: {
          cpu: config.getNumber('azure_cron_cpu') || 0.25,
          memory: config.get('azure_cron_ram') || '0.5Gi',
        },
        env: [
          {
            name: 'SUPABASE_URL',
            value: process.env.NEXT_PUBLIC_SUPABASE_URL,
          },
          {
            name: 'SUPABASE_SERVICE_ROLE_KEY',
            value: process.env.SUPABASE_SERVICE_KEY,
          },
          {
            name: 'POSTMARK_SERVER_API',
            value: process.env.POSTMARK_SERVER_API,
          },
          {
            name: 'SITE_URL',
            value: pulumi.interpolate`https://${config.require('azure_dns_domain')}`,
          },
        ],
      },
    ],
  },
});

const dnsRecord = new network.RecordSet('dnsRecord', {
  resourceGroupName: config.require('azure_dns_domain_resource_group'),
  zoneName: config.require('azure_dns_domain_zone_name'),
  recordType: 'CNAME',
  relativeRecordSetName: config
    .require('azure_dns_domain')
    .replace('.' + config.require('azure_dns_domain_zone_name'), ''),
  ttl: 3600,
  cnameRecord: {
    cname: containerApp.configuration.apply(cfg => cfg!.ingress!.fqdn),
  },
});

// Export the URL
export const url = pulumi.interpolate`https://${config.require('azure_dns_domain')}`;

const alertActionGroup = new insights.ActionGroup('alertActionGroup', {
  resourceGroupName: resourceGroup.name,
  location: 'global',
  groupShortName: 'alerts',
  enabled: true,
  emailReceivers: [
    {
      name: 'emailReceiver',
      emailAddress: '<EMAIL>',
    },
  ],
});

const containerAppCpuAlert = new insights.MetricAlert('containerAppCpuAlert', {
  resourceGroupName: resourceGroup.name,
  location: 'global',
  scopes: [containerApp.id],
  description: 'High CPU usage on Container App (over 80%)',
  severity: 3,
  enabled: true,
  windowSize: 'PT5M',
  evaluationFrequency: 'PT1M',
  criteria: {
    allOf: [
      {
        criterionType: 'StaticThresholdCriterion',
        metricName: 'UsageNanoCores',
        metricNamespace: 'Microsoft.App/containerApps',
        name: 'CpuUsage',
        operator: 'GreaterThan',
        threshold: 0.8 * (0.5 + 0.5) * 1000000000, // 80% of allocated CPU for two containers
        timeAggregation: 'Average',
      },
    ],
    odataType: 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria',
  },
  actions: [
    {
      actionGroupId: alertActionGroup.id,
    },
  ],
});

const containerAppMemoryAlert = new insights.MetricAlert(
  'containerAppMemoryAlert',
  {
    resourceGroupName: resourceGroup.name,
    location: 'global',
    scopes: [containerApp.id],
    description: 'High Memory usage on Container App (over 90%)',
    severity: 3,
    enabled: true,
    windowSize: 'PT5M',
    evaluationFrequency: 'PT1M',
    criteria: {
      allOf: [
        {
          criterionType: 'StaticThresholdCriterion',
          metricName: 'WorkingSetBytes',
          metricNamespace: 'Microsoft.App/containerApps',
          name: 'MemoryUsage',
          operator: 'GreaterThan',
          threshold: 0.9 * (1 + 1) * 1024 ** 3, // 90% of allocated memory for two containers
          timeAggregation: 'Average',
        },
      ],
      odataType: 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria',
    },
    actions: [
      {
        actionGroupId: alertActionGroup.id,
      },
    ],
  },
);

const workerCpuAlert = new insights.MetricAlert('workerCpuAlert', {
  resourceGroupName: resourceGroup.name,
  location: 'global',
  scopes: [workerContainerApp.id],
  description: 'High CPU usage on Worker Container App (over 80%)',
  severity: 3,
  enabled: true,
  windowSize: 'PT5M',
  evaluationFrequency: 'PT1M',
  criteria: {
    allOf: [
      {
        criterionType: 'StaticThresholdCriterion',
        metricName: 'UsageNanoCores',
        metricNamespace: 'Microsoft.App/containerApps',
        name: 'CpuUsage',
        operator: 'GreaterThan',
        threshold: 0.8 * config.requireNumber('azure_worker_cpu') * 1000000000,
        timeAggregation: 'Average',
      },
    ],
    odataType: 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria',
  },
  actions: [
    {
      actionGroupId: alertActionGroup.id,
    },
  ],
});

const workerMemoryAlert = new insights.MetricAlert('workerMemoryAlert', {
  resourceGroupName: resourceGroup.name,
  location: 'global',
  scopes: [workerContainerApp.id],
  description: 'High Memory usage on Worker Container App (over 90%)',
  severity: 3,
  enabled: true,
  windowSize: 'PT5M',
  evaluationFrequency: 'PT1M',
  criteria: {
    allOf: [
      {
        criterionType: 'StaticThresholdCriterion',
        metricName: 'WorkingSetBytes',
        metricNamespace: 'Microsoft.App/containerApps',
        name: 'MemoryUsage',
        operator: 'GreaterThan',
        threshold: 0.9 * 4 * 1024 ** 3, // 90% of 4GB
        timeAggregation: 'Average',
      },
    ],
    odataType: 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria',
  },
  actions: [
    {
      actionGroupId: alertActionGroup.id,
    },
  ],
});
