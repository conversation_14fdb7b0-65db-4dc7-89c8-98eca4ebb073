# Database
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Application URL
SITE_URL=http://localhost:3000

# This one is only for local development. Will be sent automatically upon 'npm run dev'
LOCALTUNNEL_SUBDOMAIN=
LOCALTUNNEL_SITE_URL=

# Supabase - Local Development
# Get these by running: npx supabase status
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_SECRET_KEY=
SUPABASE_PROJECT_ID=

# Online Payment Platform (OPP)
OPP_API_KEY=<ask your tech lead to get it>
OPP_API_URL=https://api-sandbox.onlinebetaalplatform.nl/v1
OPP_FILES_API_URL=https://files-sandbox.onlinebetaalplatform.nl/v1
OPP_WEBHOOK_SECRET=<ask your tech lead to get it>
# flag true/false that enables widget that toggles compliance status in OPP sandbox environment
NEXT_PUBLIC_OPP_SANDBOX_COMPLIANCE_STATUS_CHANGE_ENABLED=true

#Resend
RESEND_API_KEY=

# Sentry - Error Monitoring
# Get DSN from: https://sentry.io/settings/[org]/projects/[project]/keys/
NEXT_PUBLIC_SENTRY_DSN=
NEXT_PUBLIC_SENTRY_ENVIRONMENT=development

# Sentry - Build-time (only for production)
SENTRY_ORG=renopay
SENTRY_PROJECT=
SENTRY_AUTH_TOKEN=
