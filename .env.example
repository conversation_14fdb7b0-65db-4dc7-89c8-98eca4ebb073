# Important
# Get from Supabase
# Project Settings > API
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_KEY=

# Get from Postmark
POSTMARK_SERVER_API=

# Misc
SITE_URL=http://localhost:3000

# Optional
# Needed to manually generate types
# Get from SUPABASE_URL: https://[project_id].supabase.co
SUPABASE_PROJECT_ID=

# Needed to pull schema and migrations from remote db
SUPABASE_DB_PASSWORD=
SUPABASE_ACCESS_TOKEN=

# Parser
PARSER_SERVICE_HOST=localhost
PARSER_SERVICE_PORT=8000

PARSING_API_TOKEN=

# Stripe
STRIPE_SECRET_KEY=sk_test_
STRIPE_WEBHOOK_SECRET=whsec_
NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY=price_
NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL=price_
NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY=price_
NEXT_PUBLIC_STRIPE_PRICE_PRO_ANNUAL=price_
NEXT_PUBLIC_STRIPE_PRICE_TEAM_MONTHLY=price_
NEXT_PUBLIC_STRIPE_PRICE_TEAM_ANNUAL=price_

STRIPE_CURRENCY=GBP
APP_URL=http://localhost:3000
