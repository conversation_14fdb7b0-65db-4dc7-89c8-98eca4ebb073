NEXT_PUBLIC_API_URL=http://localhost:8000

DYNACONF_OPENAI_API_KEY= # ask nikityy for it

DYNACONF_DATABASE_PORT=54322
DYNACONF_DATABASE_USER=postgres
DYNACONF_DATABASE_DB=postgres
DYNACONF_DATABASE_PASSWORD=postgres
DYNACONF_DATABASE_HOST=host.docker.internal

DYNACONF_FRONTEND_URL=http://localhost:3000

DYNACONF_SUPABASE_URL=http://host.docker.internal:54321
DYNACONF_SUPABASE_SECRET_KEY= # copy it from 'npx supabase start' output

DYNACONF_CODAIO_API_KEY= # ask nikityy for it

POSTMARK_SERVER_API= # ask nikityy for it
