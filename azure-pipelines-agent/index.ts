/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  DiskDeleteOptionTypes,
  VirtualMachine,
} from '@pulumi/azure-native/compute';
import {
  NetworkInterface,
  NetworkSecurityGroup,
  PublicIPAddress,
  Subnet,
  VirtualNetwork,
} from '@pulumi/azure-native/network';
import { ResourceGroup } from '@pulumi/azure-native/resources';
import * as pulumi from '@pulumi/pulumi';

const azureDevOpsUrl = 'https://dev.azure.com/BrownleeCale';
const azureDevOpsPat = process.env.AZURE_DEVOPS_PERSONAL_ACCESS_TOKEN;
const vmSize = 'Standard_B2s';
const agentName = 'azure-agent';
const poolName = 'Azure Cloud';

// Create an Azure Resource Group
const resourceGroup = new ResourceGroup('resourceGroup', {
  location: 'uksouth',
  resourceGroupName: 'azure-pipelines-runner',
});

// // Create Virtual Network
const vnet = new VirtualNetwork('vnet', {
  resourceGroupName: resourceGroup.name,
  addressSpace: { addressPrefixes: ['10.0.0.0/16'] },
});

// // Create Subnet
const subnet = new Subnet('subnet', {
  resourceGroupName: resourceGroup.name,
  virtualNetworkName: vnet.name,
  addressPrefix: '********/24',
});

const publicIp = new PublicIPAddress('publicIp', {
  resourceGroupName: resourceGroup.name,
  publicIPAllocationMethod: 'Dynamic',
});

const nsg = new NetworkSecurityGroup('nsg', {
  resourceGroupName: resourceGroup.name,
  securityRules: [
    {
      name: 'allow-ssh',
      priority: 100,
      direction: 'Inbound',
      access: 'Allow',
      protocol: 'Tcp',
      sourcePortRange: '*',
      destinationPortRange: '22',
      sourceAddressPrefix: '*',
      destinationAddressPrefix: '*',
    },
  ],
});

// Create Network Interface
const nic = new NetworkInterface('nic', {
  resourceGroupName: resourceGroup.name,
  ipConfigurations: [
    {
      name: 'ipconfig1',
      subnet: { id: subnet.id },
      publicIPAddress: { id: publicIp.id },
    },
  ],
  networkSecurityGroup: { id: nsg.id },
});

// // Create Virtual Machine
const vm = new VirtualMachine('vm', {
  resourceGroupName: resourceGroup.name,
  networkProfile: {
    networkInterfaces: [{ id: nic.id }],
  },
  hardwareProfile: { vmSize },
  osProfile: {
    computerName: 'pipelines-agent',
    adminUsername: 'azureuser',
    linuxConfiguration: {
      disablePasswordAuthentication: true,
      ssh: {
        publicKeys: [
          {
            keyData:
              'ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJVHogGOcYp5nYkssh0FKo0zyL9bBYHeNK8C42q0DEC2',
            path: '/home/<USER>/.ssh/authorized_keys',
          },
        ],
      },
    },
  },
  storageProfile: {
    imageReference: {
      publisher: 'Canonical',
      offer: '0001-com-ubuntu-server-jammy',
      sku: '22_04-lts',
      version: 'latest',
    },
    osDisk: {
      createOption: 'FromImage',
      name: 'osdisk',
      diskSizeGB: 30,
      deleteOption: DiskDeleteOptionTypes.Delete,
    },
  },
  userData: pulumi.all([azureDevOpsUrl, azureDevOpsPat]).apply(([url, pat]) =>
    Buffer.from(
      `#!/bin/bash
# Install dependencies
sudo apt-get update -y
sudo apt-get install -y git docker.io

# Create agent directory
mkdir -p /opt/azure-pipelines-agent
cd /opt/azure-pipelines-agent

# Download and extract agent
curl -O -L https://vstsagentpackage.azureedge.net/agent/4.252.0/vsts-agent-linux-x64-4.252.0.tar.gz
tar zxvf vsts-agent-linux-x64-4.252.0.tar.gz
sudo chown -R azureuser .
sudo usermod -aG docker \${USER}

# Configure agent
./config.sh --unattended \\
  --url "${url}" \\
  --auth pat \\
  --token "${pat}" \\
  --pool "${poolName}" \\
  --agent "${agentName}" \\
  --replace

# Install and start service
sudo ./svc.sh install
sudo ./svc.sh start
`,
    ).toString('base64'),
  ),
});
