name: easy-beauty-development
region: nyc

services:
  - name: web
    git:
      branch: development
      repo_clone_url: https://${REPO_USERNAME}:${REPO_PASSWORD}@github.com/${REPO_OWNER}/${REPO_NAME}.git

    build_command: npm run build
    run_command: npm start

    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs

    http_port: 3000

    envs:
      - key: NODE_ENV
        value: production

      - key: DATABASE_URL
        value: ${DATABASE_URL}
        type: SECRET

      - key: SITE_URL
        value: ${APP_URL}

      - key: SUPABASE_URL
        value: ${SUPABASE_URL}

      - key: SUPABASE_SECRET_KEY
        value: ${SUPABASE_SECRET_KEY}
        type: SECRET
