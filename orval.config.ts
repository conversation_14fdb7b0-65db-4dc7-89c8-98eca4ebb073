import { defineConfig } from "orval";

export default defineConfig({
  api: {
    input: {
      target: "http://localhost:8000/openapi.json",
    },
    output: {
      client: "fetch",
      mode: "single",
      namingConvention: "PascalCase",
      prettier: true,
      target: "src/api/generated.ts",
      override: {
        mutator: {
          path: "src/api/mutator.ts",
          name: "customFetch",
        },
      },
    },
  },
});
