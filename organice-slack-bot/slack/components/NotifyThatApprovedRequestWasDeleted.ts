import { DeletedRequestNotificationsType } from "@organice/core/domain";
import { Message, MessageBuilder, Section } from "slack-block-builder";

const getNotification = (
  requestDeletedBy: string,
  type: DeletedRequestNotificationsType
): string => {
  const map = {
    [DeletedRequestNotificationsType.ForRequesterMessage]: `Hey, your approved request was deleted by <@${requestDeletedBy}>. Contact them for more details.`,
    [DeletedRequestNotificationsType.ForManagerMessage]: `Hey, this approved request was deleted by <@${requestDeletedBy}>. Contact them for more details.`,
  };

  return map[type];
};

const NotifyThatApprovedRequestWasDeleted = (
  channel: string,
  deletedBy: string,
  type: DeletedRequestNotificationsType,
  threadTs: string
): MessageBuilder => {
  const text = getNotification(deletedBy, type);
  const message = Message().channel(channel).text(text).threadTs(threadTs);

  message.blocks(Section().text(text));

  return message;
};

export default NotifyThatApprovedRequestWasDeleted;
