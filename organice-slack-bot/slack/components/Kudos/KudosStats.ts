import {
  BlockBuilder,
  Context,
  Divider,
  Header,
  <PERSON>,
  Button,
} from "slack-block-builder";

import { kudosClicked } from "../Navigation";

export interface KudosStatsProps {
  cycleStart: Date;
  kudosLeftNumber: number;
  nextCycleReset: Date;
  totalReceivedNumber: number;
  totalSentNumber: number;
  showLearnMoreButton?: boolean;
}

function KudosStats({
  cycleStart,
  kudosLeftNumber,
  nextCycleReset,
  totalReceivedNumber,
  totalSentNumber,
  showLearnMoreButton,
}: KudosStatsProps): BlockBuilder[] {
  const cycleRange = `<!date^${
    cycleStart.getTime() / 1000
  }^{date_short} {time}|${cycleStart.toLocaleString()}> to <!date^${
    nextCycleReset.getTime() / 1000
  }^{date_short} {time}|${nextCycleReset.toLocaleString()}>`;
  const formattedResetDate = `<!date^${
    nextCycleReset.getTime() / 1000
  }^resets on {date_pretty} at {time}|${nextCycleReset.toLocaleString()}>`;

  const statsText = [
    `Kudos given: *${totalSentNumber}*`,
    `Kudos received : *${totalReceivedNumber}*`,
    `Kudos left: *${kudosLeftNumber}* (${formattedResetDate})`,
  ].join("\n");

  const learnMoreButton = Button({ text: "Learn More :arrow_right:" }).actionId(
    kudosClicked.actionId
  );

  return [
    Header().text(`:bar_chart: Kudos stats`),
    Context().elements(`from ${cycleRange}`),
    Divider(),
    Section({
      text: statsText,
    }).accessory(showLearnMoreButton ? learnMoreButton : undefined),
  ];
}

export default KudosStats;
