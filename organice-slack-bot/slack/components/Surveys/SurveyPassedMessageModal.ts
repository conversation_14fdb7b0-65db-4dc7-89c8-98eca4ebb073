import { ReadonlyDeep, Survey } from "@organice/core/domain";
import { Modal, ModalBuilder, Section } from "slack-block-builder";

interface Props {
  survey: ReadonlyDeep<Survey>;
}

function SurveyPassedMessageModal({ survey }: Props): ModalBuilder {
  return Modal({
    title: "Survey passed",
  })
    .blocks(
      Section({
        text: `Thank you for your answers in *${survey.title}* survey.`,
      })
    )
    .close("OK");
}

export default SurveyPassedMessageModal;
