import assert from "assert";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Reward,
  RewardsP<PERSON>,
  Workspace,
  Member,
} from "@organice/core/domain";
import {
  requestReward,
  RewardNotActiveError,
  NotEnoughPointsError,
} from "@organice/core/domain/rewards";
import { ModalView } from "@slack/web-api";
import pluralize from "pluralize";
import {
  Input,
  Modal,
  Option,
  StaticSelect,
  TextInput,
  OptionBuilder,
  Context,
  Divider,
} from "slack-block-builder";

// eslint-disable-next-line import/no-cycle
import { requireRewardsPageState } from "../../pages/RewardsPage";
import { declareViewCallback, loggerFromBoltContextAndBody } from "../_common";

import { getRewardsStatsText, RewardsStatsIncludes } from "./RewardsStats";

interface RequestRewardModalProps {
  workspace: ReadonlyDeep<Workspace>;
  currentMember: ReadonlyDeep<Member>;
  selectedRewardId?: string;
  privateMetadata: string;
}

function buildOption(reward: ReadonlyDeep<Reward>): OptionBuilder {
  return Option({
    text: `${reward.name} (${reward.points} ${pluralize(
      "Coin",
      reward.points
    )})`,
    value: reward.id,
  });
}

export const RequestRewardModal = ({
  workspace,
  currentMember,
  selectedRewardId,
  privateMetadata,
}: RequestRewardModalProps): ModalView => {
  const rewards = workspace.rewardsSettings.rewards.filter((r) => r.isActive);
  const rewardOptions = rewards.map((reward) => buildOption(reward));

  const selectElement = StaticSelect({
    actionId: "reward_select",
  }).options(rewardOptions);

  if (selectedRewardId) {
    const preselectedReward = rewards.find(
      (reward) => reward.id === selectedRewardId
    );

    if (preselectedReward) {
      selectElement.initialOption(buildOption(preselectedReward));
    }
  }

  const statsText = getRewardsStatsText(workspace, currentMember, [
    RewardsStatsIncludes.Balance,
    RewardsStatsIncludes.PendingRequests,
    RewardsStatsIncludes.ReceivedRewards,
  ]);

  return Modal({
    title: "Request Reward",
    submit: "Request",
    close: "Cancel",
    callbackId: "request_reward_modal",
  })
    .blocks(
      Context().elements(`:trophy: your stats:\n${statsText}`),
      Divider(),
      Input({ label: "Reward", blockId: "reward_selection" })
        .element(selectElement)
        .hint(
          "Once requested, an admin will review your request. You will be notified of the decision."
        ),
      Input({
        label: "Comments",
        blockId: "comments",
      })
        .element(
          TextInput({
            actionId: "comments_input",
          }).multiline(true)
        )
        .optional(true)
    )
    .privateMetaData(privateMetadata)
    .buildToObject();
};

export const requestRewardModalSubmit = declareViewCallback(
  {
    callback_id: "request_reward_modal",
    type: "view_submission",
  },
  async ({ ack, body, view, context, client }) => {
    const rewardsData = view.state.values;
    const selectedRewardId =
      rewardsData.reward_selection.reward_select.selected_option?.value;
    const comment = rewardsData.comments.comments_input.value;

    if (!selectedRewardId) {
      await ack({
        response_action: "errors",
        errors: {
          reward_selection: "Please select a reward.",
        },
      });

      return;
    }

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    let logger = loggerFromBoltContextAndBody(context, body);
    const workspaceId = body.team?.id;
    const memberId = body.user.id;
    const activitiesLog = context.getActivityLog();

    assert(workspaceId, "Expected workspace id to exist");

    let workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace not found`);
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);
    logger = logger.withContext({ member });

    try {
      workspace = await requestReward({
        slackAdapter,
        logger,
        workspace,
        member,
        input: {
          rewardId: selectedRewardId,
          comment,
        },
        activitiesLog,
        onValidationPass: async () => {
          await ack();
        },
      });
    } catch (error) {
      if (error instanceof NotEnoughPointsError) {
        await ack({
          response_action: "errors",
          errors: {
            reward_selection: `You don't have enough points to request this reward.`,
          },
        });
      } else if (error instanceof RewardNotActiveError) {
        await ack({
          response_action: "errors",
          errors: {
            reward_selection: `This reward is not active.`,
          },
        });
      } else {
        await ack({
          response_action: "errors",
          errors: {},
        });
      }

      return;
    }

    await repository.setWorkspace(workspace);

    const state = requireRewardsPageState(body, member.isAdmin);

    if (
      state.currentRewardsPage === RewardsPages.MyRequests ||
      state.currentRewardsPage === RewardsPages.IncomingRequests
    ) {
      await slackAdapter.renderActiveTab(
        workspace,
        member,
        requireRewardsPageState(body, member.isAdmin)
      );
    }
  }
);
