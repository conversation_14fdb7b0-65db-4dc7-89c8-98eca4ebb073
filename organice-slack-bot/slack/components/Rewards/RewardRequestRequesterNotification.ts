import assert from "assert";

import {
  <PERSON>only<PERSON>eep,
  RewardRequest,
  RewardRequestStatus,
  Workspace,
} from "@organice/core/domain";
import {
  Context,
  Divider,
  Message,
  MessageBuilder,
  Section,
  setIfTruthy,
} from "slack-block-builder";

import { RewardInfo } from "./RewardCard";
import { getRewardsStatsText, RewardsStatsIncludes } from "./RewardsStats";

interface Props {
  workspace: ReadonlyDeep<Workspace>;
  request: ReadonlyDeep<RewardRequest>;
}

export const RewardRequestRequesterNotification = ({
  workspace,
  request,
}: Props): MessageBuilder => {
  const reward = workspace.rewardsSettings.rewards.find(
    (r) => r.id === request.rewardId
  );

  if (!reward) {
    return Message({
      channel: request.memberId,
      text: "Error: Could not create reward request notification.",
    });
  }

  const member = workspace.members.find((m) => m.id === request.memberId);

  assert(member, "Member not found for the given reward request");

  const isApproved = request.status === RewardRequestStatus.Approved;

  const text = isApproved
    ? `Hey there, <@${request.handledBy}> approved your request :white_check_mark:`
    : "Hey there, your request was rejected :x:";

  const statsText = getRewardsStatsText(workspace, member, [
    RewardsStatsIncludes.Balance,
    RewardsStatsIncludes.PendingRequests,
    RewardsStatsIncludes.ReceivedRewards,
  ]);

  return Message({ channel: request.memberId, text }).blocks(
    Section({ text }),
    ...RewardInfo(
      reward,
      request.rejectReason
        ? `:speech_balloon: *Reason for rejection* \n${request.rejectReason}`
        : undefined
    ),

    setIfTruthy(request.status === RewardRequestStatus.Approved, [
      Divider(),
      Context().elements(`:trophy: your stats:\n${statsText}`),
    ])
  );
};
