import assert from "assert";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  RewardRequest,
  RewardRequestStatus,
  RewardsPages,
} from "@organice/core/domain";
import { deleteRewardRequest } from "@organice/core/domain/rewards";
import formatDate from "@organice/core/utils/formatDate";
import pluralize from "pluralize";
import {
  Actions,
  Button,
  Context,
  Divider,
  BlockBuilder,
  Header,
  Section,
  ButtonBuilder,
} from "slack-block-builder";

// eslint-disable-next-line import/no-cycle
import { requireRewardsPageState } from "../../pages/RewardsPage";
import { declareAction, loggerFromBoltContextAndBody } from "../_common";

import {
  approveRewardRequestAction,
  rejectRewardRequestAction,
} from "./RewardRequestMessage";

export type RewardRequestWithGroupTitle = ReadonlyDeep<RewardRequest> & {
  groupTitle: "Pending" | "Approved" | "Rejected" | null;
};

export const deleteRewardRequestClicked = declareAction(
  { action_id: "delete_reward_request_clicked", type: "block_actions" },
  async ({ ack, client, action, context, body }) => {
    await ack();
    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const logger = loggerFromBoltContextAndBody(context, body);
    const activityLog = context.getActivityLog();
    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");
    assert(action.type === "button", "Expected action to come from a button");

    const deletedRequestId = action.value;
    const memberId = body.user.id;

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace not found`);

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);

    let updatedWorkspace = await deleteRewardRequest({
      workspace,
      input: {
        rewardRequestId: deletedRequestId,
        initiatorId: memberId,
      },
      activityLog,
      slackAdapter,
      logger,
    });

    logger.info(`Reward Request - 'Delete' clicked`);

    updatedWorkspace = await repository.setWorkspace(updatedWorkspace);

    const state = requireRewardsPageState(body, member.isAdmin);

    await slackAdapter.renderRewardsHomeTab(updatedWorkspace, member, state);
  }
);

interface RewardRequestCardProps {
  request: ReadonlyDeep<RewardRequestWithGroupTitle>;
  reward: ReadonlyDeep<Reward>;
  isLast: boolean;
  currentRewardsPage: RewardsPages;
}

const RewardRequestCard = ({
  request,
  reward,
  isLast,
  currentRewardsPage,
}: RewardRequestCardProps): BlockBuilder[] => {
  const formattedDate = formatDate(request.createdAt as Date);

  const getAccessory = (): ButtonBuilder | undefined => {
    if (
      currentRewardsPage === RewardsPages.IncomingRequests &&
      request.status !== RewardRequestStatus.Pending
    ) {
      return Button()
        .text("Delete")
        .actionId(deleteRewardRequestClicked.actionId)
        .value(request.id);
    }

    if (
      currentRewardsPage === RewardsPages.MyRequests &&
      request.status === RewardRequestStatus.Pending
    ) {
      return Button()
        .text("Delete")
        .actionId(deleteRewardRequestClicked.actionId)
        .value(request.id);
    }

    return undefined;
  };

  const blocks: BlockBuilder[] = [
    Section().text(`*${reward.name}*`).accessory(getAccessory()),
    Context().elements(
      `:coin: ${reward.points} ${pluralize("Coin", reward.points)}`,
      `Requested ${
        formattedDate === "Today" ? formattedDate : `on ${formattedDate}`
      }`
    ),
  ];

  if (request.comment) {
    blocks.push(Section().text(`:speech_balloon: ${request.comment}`));
  }

  if (
    currentRewardsPage === RewardsPages.IncomingRequests &&
    request.status === RewardRequestStatus.Pending
  ) {
    blocks.push(
      Actions().elements(
        Button()
          .text("Approve")
          .actionId(approveRewardRequestAction.actionId)
          .value(request.id)
          .primary(),
        Button()
          .text("Reject")
          .actionId(rejectRewardRequestAction.actionId)
          .value(request.id)
          .danger()
      )
    );
  }

  if (request.status === RewardRequestStatus.Approved && request.handledBy) {
    blocks.push(
      Context().elements(`<@${request.handledBy}> :white_check_mark: Approved`)
    );
  }

  if (request.status === RewardRequestStatus.Rejected && request.rejectReason) {
    blocks.push(Context().elements(`*Reason:* ${request.rejectReason}`));
  }

  if (request.groupTitle) {
    blocks.unshift(Header().text(request.groupTitle), Divider());
  }

  if (!isLast) {
    blocks.push(Divider());
  }

  return blocks;
};

export default RewardRequestCard;
