import assert from "assert";

import {
  ActionsRewardsData,
  Member,
  ReadonlyDeep,
  RewardActivityType,
  Workspace,
} from "@organice/core/domain";
import {
  Actions,
  Button,
  Divider,
  Message,
  MessageBuilder,
  Section,
  Context,
  setIfTruthy,
} from "slack-block-builder";

import { viewLeaderboardClicked } from "../../pages/RewardsPage";

import { RewardsStatsIncludes, getRewardsStatsText } from "./RewardsStats";

const CoinsBalanceChangedMessage = ({
  workspace,
  member,
  increase,
  absolute,
  activityType,
  adminSiteUrl,
}: {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<Member>;
  increase?: number;
  absolute?: number;
  activityType?: RewardActivityType;
  adminSiteUrl: string;
}): MessageBuilder => {
  assert(
    typeof absolute === "number" ||
      (typeof increase === "number" && activityType),
    "Either absolute or both increase and activityType must be provided"
  );

  const getText = (): string => {
    if (absolute) {
      return `An admin has updated your coin balance.\n Your new balance is *${absolute}* coins.`;
    }

    const activity = workspace.rewardsSettings.activities.find(
      (a) => a.type === activityType
    );

    assert(activity, "Activity type must be valid");

    return `You've earned *${increase}* coins *${
      ActionsRewardsData[activity.type].postfix
    }* :tada:! \n Complete more activities to earn more coins!`;
  };

  const rewards = workspace.rewardsSettings.rewards;

  const hasActiveRewards = rewards.some((reward) => reward.isActive);

  const statsText = getRewardsStatsText(workspace, member, [
    RewardsStatsIncludes.Balance,
    RewardsStatsIncludes.CoinsEarned,
    RewardsStatsIncludes.CoinsSpent,
  ]);

  return Message()
    .channel(member.id)
    .blocks(
      Section().text(getText()),
      Actions().elements(
        setIfTruthy(
          hasActiveRewards,
          Button({ text: ":trophy: Request Reward" })
            .actionId("request_reward")
            .primary()
        ),
        Button()
          .text(":medal: View Leaderboard")
          .url(`${adminSiteUrl}/rewards/leaderboard`)
          .actionId(viewLeaderboardClicked.actionId)
      ),
      Divider(),
      Context().elements(`:trophy: your stats:\n${statsText}`)
    );
};

export default CoinsBalanceChangedMessage;
