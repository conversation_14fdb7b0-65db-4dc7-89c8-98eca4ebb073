import assert from "assert";

import { RewardActivityType, ActionsRewardsData } from "@organice/core/domain";
import { ModalView } from "@slack/web-api";
import pluralize from "pluralize";
import {
  <PERSON><PERSON>,
  ButtonBuilder,
  Divider,
  Modal,
  Section,
  setIfTruthy,
} from "slack-block-builder";

import { declareAction, loggerFromBoltContextAndBody } from "../_common";

export interface RewardActivitiesModalProps {
  activities: {
    type: RewardActivityType;
    points: number;
  }[];
}

const Activities = {
  [RewardActivityType.Kudos]: {
    emoji: ":clap:  ",
    ...ActionsRewardsData[RewardActivityType.Kudos],
  },
  [RewardActivityType.Profile]: {
    emoji: ":bust_in_silhouette:",
    ...ActionsRewardsData[RewardActivityType.Profile],
  },
  [RewardActivityType.Survey]: {
    emoji: ":memo:",
    ...ActionsRewardsData[RewardActivityType.Survey],
  },
};

function RewardActivitiesModal({
  activities,
}: RewardActivitiesModalProps): ModalView {
  return Modal({
    title: "Actions Rewards",
  })
    .blocks(
      activities.flatMap((item, idx) => {
        const activityData = Activities[item.type];

        return [
          setIfTruthy(idx > 0, Divider()),
          Section().text(
            `${activityData.emoji} *${activityData.title}* \n ${
              activityData.description
            } \n :coin: *${item.points}* ${pluralize("coin", item.points)} ${
              activityData.postfix
            }`
          ),
        ];
      })
    )
    .close("Close")
    .buildToObject();
}

export const viewRewardActivities = declareAction(
  {
    action_id: "view_reward_activities",
    type: "block_actions",
  },
  async ({ ack, body, client, action, context }) => {
    await ack();
    assert(action.type === "button", "Expected action to come from a button");

    const workspaceId = context.teamId ?? context.enterpriseId;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, "Workspace not found");

    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("'View Actions Rewards' button clicked");

    await client.views.open({
      view: RewardActivitiesModal({
        activities: workspace.rewardsSettings.activities.map((item) => ({
          type: item.type,
          points: item.points,
        })),
      }),
      trigger_id: body.trigger_id,
    });

    logger.info("'View Actions Rewards' modal opened");
  }
);

const ViewRewardActivitiesButton = (): ButtonBuilder => {
  return Button()
    .text(":small_blue_diamond: View Actions Rewards")
    .actionId(viewRewardActivities.actionId);
};

export default ViewRewardActivitiesButton;
