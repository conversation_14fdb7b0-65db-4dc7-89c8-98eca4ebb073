import {
  <PERSON>on<PERSON><PERSON><PERSON>,
  RewardRequestStatus,
  <PERSON><PERSON><PERSON><PERSON>,
  Member,
  Workspace,
} from "@organice/core/domain";
import { getMemberCoinsBalance } from "@organice/core/domain/rewards";
import pluralize from "pluralize";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Header,
  <PERSON>,
  But<PERSON>,
} from "slack-block-builder";

import { rewardsClicked } from "../Navigation";

export enum RewardsStatsIncludes {
  Balance,
  CoinsEarned,
  CoinsSpent,
  PendingRequests,
  ReceivedRewards,
}

export function getRewardsStatsText(
  workspace: ReadonlyDeep<Workspace>,
  currentMember: ReadonlyDeep<UIMember> | ReadonlyDeep<Member>,
  stats: RewardsStatsIncludes[] = [
    RewardsStatsIncludes.Balance,
    RewardsStatsIncludes.CoinsEarned,
    RewardsStatsIncludes.CoinsSpent,
    RewardsStatsIncludes.PendingRequests,
    RewardsStatsIncludes.ReceivedRewards,
  ]
): string {
  const pendingRequests = workspace.rewardsRequests.filter(
    (r) =>
      r.memberId === currentMember.id &&
      r.status === RewardRequestStatus.Pending
  ).length;

  const receivedRewards = workspace.rewardsRequests.filter(
    (r) =>
      r.memberId === currentMember.id &&
      r.status === RewardRequestStatus.Approved
  ).length;

  const { coinsEarned, coinsSpent, balance } = getMemberCoinsBalance(
    workspace,
    currentMember.id
  );

  const statsText = [];

  if (stats.includes(RewardsStatsIncludes.Balance)) {
    statsText.push(
      `Remaining: :coin: *${balance}* ${pluralize("Coin", balance)}`
    );
  }

  if (stats.includes(RewardsStatsIncludes.CoinsEarned)) {
    statsText.push(`Earned: ${coinsEarned} ${pluralize("Coin", coinsEarned)}`);
  }

  if (stats.includes(RewardsStatsIncludes.CoinsSpent)) {
    statsText.push(`Spent: ${coinsSpent} ${pluralize("Coin", coinsSpent)}`);
  }

  if (stats.includes(RewardsStatsIncludes.PendingRequests)) {
    statsText.push(`Pending requests: ${pendingRequests}`);
  }

  if (stats.includes(RewardsStatsIncludes.ReceivedRewards)) {
    statsText.push(`Received rewards: ${receivedRewards}`);
  }

  return statsText.join("\n");
}

interface RewardsStatsProps {
  workspace: ReadonlyDeep<Workspace>;
  currentMember: ReadonlyDeep<UIMember> | ReadonlyDeep<Member>;
  showLearnMoreButton?: boolean;
}

export default function RewardsStats(props: RewardsStatsProps): BlockBuilder[] {
  const { currentMember, workspace, showLearnMoreButton } = props;

  const statsText = getRewardsStatsText(workspace, currentMember);

  const learnMoreButton = Button({ text: "Learn More :arrow_right:" }).actionId(
    rewardsClicked.actionId
  );

  return [
    Header().text(`:trophy: Rewards stats`),
    Divider(),
    Section({
      text: statsText,
    }).accessory(showLearnMoreButton ? learnMoreButton : undefined),
  ];
}
