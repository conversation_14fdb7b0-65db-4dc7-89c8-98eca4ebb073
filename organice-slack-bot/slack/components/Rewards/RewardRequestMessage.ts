import assert from "assert";

import {
  RewardRequest,
  Workspace,
  ReadonlyDeep,
  RewardsHomePageState,
  RewardRequestStatus,
} from "@organice/core/domain";
import {
  approveRewardRequest,
  rejectRewardRequest,
} from "@organice/core/domain/rewards";
import {
  Actions,
  Button,
  Message,
  MessageBuilder,
  Section,
  Context,
  setIfTruthy,
  Attachment,
  Divider,
} from "slack-block-builder";

import { tryJsonParse } from "../../helpers";
// eslint-disable-next-line import/no-cycle
import { requireRewardsPageState } from "../../pages/RewardsPage";
import RequestRejectModal from "../RequestRejectModal";
import {
  declareAction,
  loggerFromBoltContextAndBody,
  declareViewCallback,
} from "../_common";

import { RewardInfo } from "./RewardCard";
import { getRewardsStatsText, RewardsStatsIncludes } from "./RewardsStats";

interface Props {
  workspace: <PERSON>only<PERSON>eep<Workspace>;
  request: ReadonlyDeep<RewardRequest>;
  channelId: string;
  deletedByMemberId?: string;
}

function getAttachmentColor(status: RewardRequestStatus): string {
  switch (status) {
    case RewardRequestStatus.Approved:
      return "#36a64f";
    case RewardRequestStatus.Rejected:
      return "#e01e5a";
    default:
      return "#e0e0e0";
  }
}

type RequestRewardRejectModalState = {
  requestId: string;
} & RewardsHomePageState;

export const approveRewardRequestAction = declareAction(
  { action_id: "approve_reward_request", type: "block_actions" },
  async ({ ack, body, client, context, action }) => {
    await ack();

    assert(action.type === "button", "Expected action to come from a button");
    const requestId = action.value;
    const initiatorId = body.user.id;

    const workspaceId = context.teamId ?? context.enterpriseId;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const logger = loggerFromBoltContextAndBody(context, body);
    const slackAdapter = context.getSlackAdapter(client);
    const activityLog = context.getActivityLog();

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, "Workspace not found");

    const [updatedWorkspace] = await approveRewardRequest({
      workspace,
      input: { requestId, initiatorId },
      activityLog,
      slackAdapter,
      logger,
    });

    const member = workspace.members.find((m) => m.id === initiatorId);

    assert(member, "Member not found for the given reward request");

    await repository.setWorkspace(updatedWorkspace);

    if (body.view) {
      const state = requireRewardsPageState(body, member.isAdmin);

      await slackAdapter.renderRewardsHomeTab(updatedWorkspace, member, state);
    }
  }
);

export const rejectRewardRequestActionConfirm = declareViewCallback(
  {
    callback_id: "reject_reward_request_confirm",
    type: "view_submission",
  },
  async ({ ack, body, client, context, view }) => {
    await ack();

    const state = tryJsonParse<RequestRewardRejectModalState>(
      body.view.private_metadata
    );

    assert(
      state,
      `Expected body.view.private_metadata to be a valid JSON, instead got "${body.view.private_metadata}"`
    );

    const { requestId, ...rewardsState } = state;

    const initiatorId = body.user.id;
    const workspaceId = context.teamId ?? context.enterpriseId;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const activityLog = context.getActivityLog();
    let logger = loggerFromBoltContextAndBody(context, body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace not found`);
    logger = logger.withContext({ workspace });

    logger.info(
      "Reward Request - Message tab: 'Reject Reward' modal submit clicked"
    );

    const [updatedWorkspace] = await rejectRewardRequest({
      workspace,
      input: {
        rewardRequestId: requestId,
        initiatorId,
        rejectReason: view.state.values.reason.typed_reason.value ?? undefined,
      },
      activityLog,
      slackAdapter,
      logger,
    });

    const member = workspace.members.find((m) => m.id === initiatorId);

    assert(member, "Member not found for the given reward request");

    await slackAdapter.renderRewardsHomeTab(workspace, member, rewardsState);

    await repository.setWorkspace(updatedWorkspace);
  }
);

export const rejectRewardRequestAction = declareAction(
  {
    action_id: "reject_reward_request",
    type: "block_actions",
  },
  async ({ ack, body, client, action, context }) => {
    await ack();

    assert(action.type === "button", "Expected action to come from a button");

    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("Reward Request - Message tab: 'Reject' clicked");

    await client.views.open({
      view: RequestRejectModal({
        callbackId: rejectRewardRequestActionConfirm.callbackId,
        metadata: {
          requestId: action.value,
        },
      }),
      trigger_id: body.trigger_id,
    });
  }
);

function RewardRequestMessage({
  workspace,
  request,
  channelId,
  deletedByMemberId,
}: Props): MessageBuilder {
  const reward = workspace.rewardsSettings.rewards.find(
    (r) => r.id === request.rewardId
  );

  assert(reward, "Reward not found for the given reward request");

  const member = workspace.members.find((m) => m.id === request.memberId);

  assert(member, "Member not found for the given reward request");

  const statsText = getRewardsStatsText(workspace, member, [
    RewardsStatsIncludes.Balance,
    RewardsStatsIncludes.PendingRequests,
    RewardsStatsIncludes.ReceivedRewards,
  ]);

  const notificationText =
    request.status === RewardRequestStatus.Pending
      ? "New Reward Request"
      : "Reward Request Updated";

  const attachments = [
    Attachment()
      .color(getAttachmentColor(request.status))
      .blocks(
        ...RewardInfo(
          reward,
          request.comment
            ? `:speech_balloon: *Comment* \n${request.comment}`
            : ""
        ),
        setIfTruthy(
          request.status === RewardRequestStatus.Pending && !deletedByMemberId,
          Actions().elements(
            Button({
              text: "Approve ",
              actionId: approveRewardRequestAction.actionId,
            })
              .primary()
              .value(request.id),
            Button({
              text: "Reject",
              actionId: rejectRewardRequestAction.actionId,
            })
              .danger()
              .value(request.id)
          )
        ),
        setIfTruthy(
          deletedByMemberId,
          Context().elements(`<@${deletedByMemberId!}> deleted this request`)
        ),
        setIfTruthy(
          request.status === RewardRequestStatus.Approved && request.handledBy,
          Context().elements(`<@${request.handledBy!}> approved this request`)
        ),
        setIfTruthy(
          request.status === RewardRequestStatus.Rejected && request.handledBy,
          Context().elements(`<@${request.handledBy!}> rejected this request`)
        )
      ),
  ];

  if (request.status === RewardRequestStatus.Pending && !deletedByMemberId) {
    attachments.push(
      Attachment()
        .color(getAttachmentColor(request.status))
        .blocks([
          Context().elements(`:trophy: ${member.name}'s stats:\n${statsText}`),
        ])
    );
  }

  return Message({ channel: channelId, text: notificationText })
    .blocks(
      Section({
        text: `Hey there, <@${member.id}> has requested a reward.\nOnce you or any of the Organice admins approve or reject this request, <@${member.id}> will be notified.`,
      }),
      Divider()
    )
    .attachments(...attachments);
}

export default RewardRequestMessage;
