import assert from "assert";

import { Reward } from "@organice/core/domain";
import pluralize from "pluralize";
import {
  Actions,
  BlockBuilder,
  Button,
  Divider,
  Image,
  Section,
  Context,
  setIfTruthy,
} from "slack-block-builder";

import { declareAction, loggerFromBoltContextAndBody } from "../_common";

// eslint-disable-next-line import/no-cycle
import { RequestRewardModal } from "./RequestRewardModal";

interface Props {
  reward: Reward;
  isLast: boolean;
}

export function RewardInfo(
  reward: Reward,
  extraInformation?: string
): BlockBuilder[] {
  return [
    Section()
      .text(
        `*${reward.name}* \n ${
          reward.description ? `${reward.description}` : ""
        }`
      )
      .accessory(
        reward.imageUrl
          ? Image({
              imageUrl: reward.imageUrl,
              altText: reward.name,
            })
          : undefined
      ),
    Context().elements(
      `:coin: ${reward.points} ${pluralize("Coin", reward.points)}`
    ),
    setIfTruthy(
      !!extraInformation,
      Section().text(extraInformation)
    ) as BlockBuilder,
  ];
}

export const requestReward = declareAction(
  { action_id: "request_reward", type: "block_actions" },
  async ({ ack, body, client, context, action }) => {
    await ack();

    assert(action.type === "button", "Expected action to come from a button");

    const workspaceId = context.teamId ?? context.enterpriseId;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const logger = loggerFromBoltContextAndBody(context, body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, "Workspace not found");

    const member = workspace.members.find((x) => x.id === body.user.id);

    assert(member, "Member not found");

    logger.info("RewardCard - 'Request Reward' button clicked");

    await client.views.open({
      trigger_id: body.trigger_id,
      view: RequestRewardModal({
        workspace,
        currentMember: member,
        selectedRewardId: action.value,
        privateMetadata: body.view?.private_metadata ?? "",
      }),
    });
  }
);

const RewardCard = ({ reward, isLast }: Props): BlockBuilder[] => {
  const blocks: BlockBuilder[] = [
    ...RewardInfo(reward),
    Actions().elements(
      Button({
        text: "Request",
        actionId: requestReward.actionId,
      }).value(reward.id)
    ),
  ];

  if (!isLast) {
    blocks.push(Divider());
  }

  return blocks;
};

export default RewardCard;
