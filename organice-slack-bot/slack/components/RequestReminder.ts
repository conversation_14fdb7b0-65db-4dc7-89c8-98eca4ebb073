import { Message, MessageBuilder } from "slack-block-builder";

interface Props {
  channel: string;
  channelType: "dm" | "private";
  threadTs?: string;
  postAt: string;
}

function RequestReminder({
  channel,
  channelType,
  threadTs,
  postAt,
}: Props): MessageBuilder {
  const text = `Hey ${
    channelType === "private" ? "<!channel>" : `<@${channel}>`
  }. Please react to this Request`;
  let message = Message().channel(channel).text(text).postAt(postAt);

  if (threadTs) {
    message = message.threadTs(threadTs);
  }

  return message;
}

export default RequestReminder;
