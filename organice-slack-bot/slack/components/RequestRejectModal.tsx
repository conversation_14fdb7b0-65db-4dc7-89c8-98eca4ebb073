import { ModalView } from "@slack/web-api";
import { Input, Modal, TextInput } from "slack-block-builder";

export interface Props {
  callbackId: string;
  metadata?: unknown;
}

function RequestRejectModal({ callbackId, metadata }: Props): ModalView {
  return Modal({
    title: "Reject Request",
    callbackId,
  })
    .blocks(
      Input({ label: "Reason" })
        .optional(true)
        .element(
          TextInput()
            .actionId("typed_reason")
            .multiline()
            .placeholder(
              "Only the requester and OrgaNice admins will have access to this information"
            )
        )
        .blockId("reason")
    )
    .close("Cancel")
    .submit("Submit")
    .privateMetaData(JSON.stringify(metadata))
    .buildToObject();
}

export default RequestRejectModal;
