import { Image, ImageBuilder } from "slack-block-builder";

export enum ImageTypes {
  Welcome = "welcome",
  Trial = "trial",
  Onboarding = "onboarding",
}

const Images = {
  [ImageTypes.Welcome]: "/png/slack/welcome.png",
  [ImageTypes.Trial]: "/png/slack/trial.png",
  [ImageTypes.Onboarding]: "/png/slack/onboarding.png",
};

function ImageBanner(
  adminSiteUrl: string,
  image: ImageTypes = ImageTypes.Welcome
): ImageBuilder {
  if (adminSiteUrl.includes("localhost")) {
    // Slack won't allow to send a image referencing to an image served on localhost
    adminSiteUrl = "https://dashboard.organice.app";
  }

  return Image()
    .imageUrl(`${adminSiteUrl}${Images[image]}`)
    .altText("inspiration");
}

export default ImageBanner;
