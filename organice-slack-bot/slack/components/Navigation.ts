import assert from "assert";

import {
  HomePages,
  ReadonlyDeep,
  SupportedFeature,
  HomePageState,
} from "@organice/core/domain";
import { <PERSON>s, But<PERSON>, Divider, BlockBuilder } from "slack-block-builder";

import { isFeatureEnabled } from "../domain";
// eslint-disable-next-line import/no-cycle
// import { calendarClicked } from "../pages/CalendarPage";
// eslint-disable-next-line import/no-cycle
import {
  tryJsonParse,
  inferHomePageState,
  getDefaultRewardsHomePageState,
  getDefaultCalendarHomePageState,
  getDefaultDashboardHomePageState,
  getDefaultKudosHomePageState,
  getDefaultOrgChartHomePageState,
  getDefaultSurveysHomePageState,
} from "../helpers";

import { declareAction, loggerFromBoltContextAndBody } from "./_common";

function createHomeTabAction<T extends unknown[]>({
  actionId,
  page,
  defaultStateFn,
  defaultStateFnArgs,
}: {
  actionId: string;
  page: HomePages;
  defaultStateFn: (...args: T) => HomePageState;
  defaultStateFnArgs?: T;
}): ReturnType<typeof declareAction> {
  return declareAction(
    { action_id: actionId, type: "block_actions" },
    async ({ ack, client, context, body, action }) => {
      await ack();
      assert(action.type === "button", "Expected action to come from a button");

      const workspaceId = (context.teamId ?? context.enterpriseId)!;
      const memberId = body.user.id;
      const repository = context.getRepository();
      const slackAdapter = context.getSlackAdapter(client);

      assert("view" in body && body.view, "Expected view to exist");
      assert(body.view.private_metadata, "Expected private metadata to exist");

      const state = tryJsonParse<HomePageState>(body.view.private_metadata);

      assert(
        state,
        `Expected body.view.private_metadata to be a valid JSON, instead got "${body.view.private_metadata}"`
      );

      const workspace = await repository.getWorkspace(workspaceId);

      assert(workspace, `Workspace not found`);

      let logger = loggerFromBoltContextAndBody(context, body);

      logger = logger.withContext({ workspace });

      const member = workspace.members.find((m) => m.id === memberId);

      assert(member, `Member not found`);

      logger = logger.withContext({ member });
      logger.info(`${page} - Home tab clicked`);

      await slackAdapter.renderActiveTab(
        workspace,
        member,
        inferHomePageState(state, page, () =>
          defaultStateFn(...(defaultStateFnArgs ?? ([] as unknown as T)))
        )
      );
    }
  );
}

export const rewardsClicked = createHomeTabAction({
  actionId: "rewards_clicked",
  page: HomePages.Rewards,
  defaultStateFn: getDefaultRewardsHomePageState,
});

export const calendarClicked = createHomeTabAction({
  actionId: "calendar_clicked",
  page: HomePages.Calendar,
  defaultStateFn: getDefaultCalendarHomePageState,
});

export const dashboardClicked = createHomeTabAction({
  actionId: "dashboard_clicked",
  page: HomePages.Dashboard,
  defaultStateFn: getDefaultDashboardHomePageState,
});

export const kudosClicked = createHomeTabAction({
  actionId: "kudos_clicked",
  page: HomePages.Kudos,
  defaultStateFn: getDefaultKudosHomePageState,
});

export const orgChartClicked = createHomeTabAction({
  actionId: "org_chart_clicked",
  page: HomePages.OrgChart,
  defaultStateFn: getDefaultOrgChartHomePageState,
});

export const surveysClicked = createHomeTabAction({
  actionId: "surveys_clicked",
  page: HomePages.Surveys,
  defaultStateFn: getDefaultSurveysHomePageState,
});

export function getHomeTabs(features: ReadonlyDeep<SupportedFeature[]>): {
  name: HomePages;
  label: string;
  isActive: (currentPage: HomePages) => boolean;
  actionId: string;
}[] {
  const allTabs = [
    {
      name: HomePages.Dashboard,
      label: "Dashboard",
      visible: [
        SupportedFeature.OrgChart,
        SupportedFeature.TimeOffs,
        SupportedFeature.Kudos,
      ].some((f) => isFeatureEnabled(features, f)),
      isActive: (currentPage: HomePages) => currentPage === HomePages.Dashboard,
      actionId: dashboardClicked.actionId,
    },
    {
      name: HomePages.OrgChart,
      label: "Organization",
      visible: isFeatureEnabled(features, SupportedFeature.OrgChart),
      isActive: (currentPage: HomePages) => currentPage === HomePages.OrgChart,
      actionId: orgChartClicked.actionId,
    },
    {
      name: HomePages.Calendar,
      label: "Calendar",
      visible:
        isFeatureEnabled(features, SupportedFeature.TimeOffs) ||
        isFeatureEnabled(features, SupportedFeature.Celebration),
      isActive: (currentPage: HomePages) => currentPage === HomePages.Calendar,
      actionId: calendarClicked.actionId,
    },
    {
      name: HomePages.Kudos,
      label: "Kudos",
      visible: isFeatureEnabled(features, SupportedFeature.Kudos),
      isActive: (currentPage: HomePages) => currentPage === HomePages.Kudos,
      actionId: kudosClicked.actionId,
    },
    {
      name: HomePages.Surveys,
      label: "Surveys",
      visible: isFeatureEnabled(features, SupportedFeature.Surveys),
      isActive: (currentPage: HomePages) => currentPage === HomePages.Surveys,
      actionId: surveysClicked.actionId,
    },
    {
      name: HomePages.Rewards,
      label: "Rewards",
      visible: isFeatureEnabled(features, SupportedFeature.Rewards),
      isActive: (currentPage: HomePages) => currentPage === HomePages.Rewards,
      actionId: rewardsClicked.actionId,
    },
  ];

  const visibleTabs = allTabs
    .filter((tab) => tab.visible)
    .map((tab) => ({
      name: tab.name,
      label: tab.label,
      isActive: tab.isActive,
      actionId: tab.actionId,
    }));

  return visibleTabs;
}

export function getHomeTabNavigation(
  currentPage: HomePages,
  enabledFeatures: ReadonlyDeep<SupportedFeature[]>
): BlockBuilder[] {
  const tabs = getHomeTabs(enabledFeatures);
  const elements = tabs.map((tab) =>
    Button()
      .text(
        tab.isActive(currentPage)
          ? `:large_green_circle: ${tab.label}`
          : tab.label
      )
      .actionId(tab.actionId)
  );
  const blocks =
    elements.length > 1
      ? [Actions().elements(elements).blockId("home_tab_actions"), Divider()]
      : [];

  return blocks;
}
