import assert from "assert";

import {
  ReadonlyDeep,
  Workspace,
  Position,
  Department,
  UIMember,
  UITeam,
  RootNode,
  Member,
  SupportedFeature,
} from "@organice/core/domain";
import { getNode } from "@organice/core/domain/org-chart";
import { differenceInCalendarDays } from "date-fns";

export function getMemberSubordinates(
  workspace: ReadonlyDeep<Workspace>,
  memberId: string
): ReadonlyDeep<(Department | Position)[]> {
  const memberPosition = getNode(
    workspace.orgTree.rootNode,
    (node): node is Position =>
      node.type === "position" && node.memberId === memberId
  );

  return memberPosition?.subordinates ?? [];
}

export interface PaginatorAction {
  action: string;
  buttonId: string;
  page: number;
  offset: number;
}

export function getNodeManager(
  workspace: ReadonlyDeep<Workspace>,
  element: ReadonlyDeep<Department | Position | RootNode>
): UIMember | null {
  if (element.type === "root" || !element.parentId) {
    return null;
  }

  const parentNode = getNode(
    workspace.orgTree.rootNode,
    (n): n is RootNode | Position | Department => n.id === element.parentId
  );

  if (!parentNode) {
    return null;
  }

  if (parentNode.type === "position" && parentNode.memberId) {
    return {
      ...getUIMemberById(workspace, parentNode.memberId),
      position: parentNode,
    } as UIMember;
  }

  return getNodeManager(workspace, parentNode);
}

function findDepartment(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): ReadonlyDeep<Department | undefined> {
  const node = getNode(
    workspace.orgTree.rootNode,
    (n): n is Position => n.type === "position" && n.memberId === member.id
  );
  let currentNode: ReadonlyDeep<RootNode | Position | Department | null> = node;

  while (
    currentNode !== null &&
    currentNode.type !== "department" &&
    currentNode.type !== "root"
  ) {
    const parentId = currentNode.parentId;

    currentNode = getNode(
      workspace.orgTree.rootNode,
      (n): n is RootNode | Position | Department => n.id === parentId
    );
  }

  if (currentNode?.type === "department") {
    return currentNode;
  }

  return undefined;
}

export function getUIMemberById(
  workspace: ReadonlyDeep<Workspace>,
  id: string
): ReadonlyDeep<UIMember> {
  const member = workspace.members.find((item) => item.id === id);

  assert(member, `Member not found`);

  const node = getNode(
    workspace.orgTree.rootNode,
    (n): n is Position => n.type === "position" && n.memberId === member.id
  );
  const teams: UITeam[] = (node?.teamIds ?? []).map((teamId) => {
    const team = workspace.teams.find((t) => t.id === teamId);

    assert(team, `Found a non-existing team (id: "${teamId}")`);

    return team;
  });

  const managerPosition = node && getNodeManager(workspace, node);

  const department = findDepartment(workspace, member);

  return {
    id: member.id,
    isAdmin: member.isAdmin,
    name: member.name,
    photoUrl: member.photoUrl,
    photo512Url: member.photo512Url,
    realName: member.realName,
    title: node?.title,
    email: member.email,
    updated: member.updated,
    manager: managerPosition ?? undefined,
    position: node ?? undefined,
    department: department
      ? {
          id: department.id,
          label: department.title,
        }
      : undefined,
    teams: teams.map((team) => ({
      id: team.id,
      label: team.label,
    })),
    howManyTimesHomeTabVisited: member.howManyTimesHomeTabVisited,
    coinsBalance: member.coinsBalance,
  };
}

export function getJoinedPeopleInLast30Days(
  workspace: ReadonlyDeep<Workspace>,
  time: Date
): ReadonlyDeep<UIMember>[] {
  const joinedPeopleSet = new Set<string>();

  if (
    workspace.installedAt &&
    differenceInCalendarDays(time, workspace.installedAt as Date) <= 30
  ) {
    workspace.members.forEach((m) => {
      if (m.joinedAt) {
        const diff = differenceInCalendarDays(
          workspace.installedAt as Date,
          m.joinedAt as Date
        );

        if (diff > 0 && diff <= 30) {
          joinedPeopleSet.add(m.id);
        }
      }
    });
  }

  workspace.members.forEach((m) => {
    if (
      m.joinedAt &&
      differenceInCalendarDays(time, m.joinedAt as Date) <= 30
    ) {
      joinedPeopleSet.add(m.id);
    }
  });

  return Array.from(joinedPeopleSet).map((memberId) =>
    getUIMemberById(workspace, memberId)
  );
}

export function isFeatureEnabled(
  enabledFeatures: ReadonlyDeep<SupportedFeature[]>,
  feature: SupportedFeature
): boolean {
  return enabledFeatures.includes(feature);
}
