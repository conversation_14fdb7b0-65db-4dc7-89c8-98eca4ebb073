import { expect, test } from "@jest/globals";
import { createMember, createWorkspace } from "@organice/core/utils/testUtils";

import { getJoinedPeopleInLast30Days } from ".";

const initialMember = createMember({
  overrides: {
    id: "2",
  },
});

const members = [initialMember];

const initialWorkspace = createWorkspace({
  overrides: {
    id: "T054D509V5J",
    members,
  },
});

describe("Joined people block on Home Tab", () => {
  describe("include people that joined to organization before app was installed", () => {
    test.each([
      {
        joinedAt: new Date("2023-10-05"),
        length: 1,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-10-09"),
        length: 1,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-10-10"),
        length: 1,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
    ] as const)("joinedAt on $joinedAt", testJoinedPeopleInLast30Days);
  });

  describe("include people that joined to organization after app was installed", () => {
    test.each([
      {
        joinedAt: new Date("2023-10-14"),
        length: 1,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-10-15"),
        length: 1,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-10-16"),
        length: 1,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-10-22"),
        length: 1,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-10-24"),
        length: 1,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
    ] as const)("joinedAt on $joinedAt", testJoinedPeopleInLast30Days);
  });

  describe("ignore people that joined before 30 days since app was installed", () => {
    test.each([
      {
        joinedAt: new Date("2023-09-08"),
        length: 0,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-09-09"),
        length: 0,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-09-10"),
        length: 0,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-09-11"),
        length: 0,
        currentDate: new Date("2023-10-12"),
        installedAt: new Date("2023-10-12"),
      },
    ] as const)("joinedAt on $joinedAt", testJoinedPeopleInLast30Days);
  });

  describe("ignore people that joined after 30 days", () => {
    test.each([
      {
        joinedAt: new Date("2023-10-12"),
        length: 0,
        currentDate: new Date("2023-11-16"),
        installedAt: new Date("2023-10-12"),
      },
      {
        joinedAt: new Date("2023-10-14"),
        length: 0,
        currentDate: new Date("2023-11-16"),
        installedAt: new Date("2023-10-12"),
      },
    ] as const)("joinedAt on $joinedAt", testJoinedPeopleInLast30Days);
  });

  function testJoinedPeopleInLast30Days(args: {
    joinedAt: Date;
    length: number;
    currentDate: Date;
    installedAt: Date;
  }): void {
    const workspace = { ...initialWorkspace };
    const member = { ...initialMember };

    workspace.installedAt = args.installedAt;
    member.joinedAt = args.joinedAt;

    workspace.members = [member];

    expect(
      getJoinedPeopleInLast30Days(workspace, args.currentDate).length
    ).toBe(args.length);
  }
});
