/* eslint-disable import/no-cycle */
import assert from "assert";

import {
  Department,
  HomePages,
  HOW_MANY_TIMES_SHOW_FEATURE_BANNER,
  OrgChartHomePageProps,
  OrgChartHomePageState,
  pickNRandom,
} from "@organice/core/domain";
import {
  createPosition,
  getMemberPosition,
  getNode,
  moveMemberToManager,
} from "@organice/core/domain/org-chart";
import { BlockAction } from "@slack/bolt";
import { HomeView } from "@slack/web-api";
import {
  Actions,
  Bits,
  BlockBuilder,
  Button,
  Context,
  ConversationSelect,
  Divider,
  Header,
  HomeTab,
  Md,
  Option,
  Section,
  StaticSelect,
} from "slack-block-builder";

import HelpNavigation from "../components/HelpNavigation";
import HiredSection from "../components/HiredSection";
import ImageBanner from "../components/ImageBanner";
import JoinedPeopleBlock from "../components/JoinedPeopleBlock";
import { getHomeTabNavigation } from "../components/Navigation";
import UpdateProfileButton from "../components/UpdateProfileButton";
import UserCard from "../components/UserCard";
import {
  declareAction,
  loggerFromBoltContextAndBody,
} from "../components/_common";
import { getUIMemberById } from "../domain";
import {
  getNewLineBlock,
  isValidArray,
  requirePageState,
  inferHomePageState,
  getDefaultOrgChartHomePageState,
} from "../helpers";

import NoFeaturesPage from "./NoFeaturesPage";

const USER_LIMIT_PER_PAGE = 10;

export const homeViewOnWebButton = declareAction(
  {
    action_id: "home_view_on_web_button",
  },
  async ({ ack, context, body }) => {
    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("'View on Web' button on home tab clicked");

    await ack();
  }
);

export const handleStateOfPeopleAreMissingBlock = declareAction(
  {
    action_id: "handle_state_of_people_are_missing_block",
    type: "block_actions",
  },
  async ({ ack, client, body, action, context }) => {
    await ack();
    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    assert(action.type === "button", "Expected action to come from a button");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const memberId = body.user.id;
    const peopleAreMissingOrgChartState = action.value as "hide" | "show";
    const logger = loggerFromBoltContextAndBody(context, body);
    const state = requireOrgChartPageState(body);
    const workspace = await repository.getWorkspace(workspaceId);

    logger.info(
      `Home tab - Manager section ${
        peopleAreMissingOrgChartState === "hide" ? "hided" : "showed"
      }`
    );

    assert(workspace, `Workspace not found`);

    const currentMember = getUIMemberById(workspace, memberId);

    state.showPeopleAreMissingOnOrgChartBlock =
      peopleAreMissingOrgChartState !== "hide";

    await slackAdapter.renderOrgChartHomeTab(workspace, currentMember, state);

    await repository.setWorkspace(workspace);
  }
);

export const selectMangerOnHomeTab = declareAction(
  { action_id: "select_manger_on_home_tab", type: "block_actions" },
  async ({ ack, client, body, action, context }) => {
    await ack();
    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    assert(
      action.type === "conversations_select",
      "Expected action to come from a static select"
    );

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const memberId = body.user.id;

    const managerId = action.selected_conversation;
    const subjectMemberId = action.block_id;
    const logger = loggerFromBoltContextAndBody(context, body);
    const state = requireOrgChartPageState(body);

    let workspace = await repository.getWorkspace(workspaceId);

    logger.info("Home tab - Manager assigned", managerId);

    assert(workspace, `Workspace not found`);

    const manager = workspace.members.find((m) => m.id === managerId);

    assert(manager, `Expect member with ${managerId} to exist`);

    const subjectMember = workspace.members.find(
      (m) => m.id === subjectMemberId
    );

    assert(subjectMember, `Expect member with ${subjectMemberId} to exist`);

    let position = getMemberPosition(workspace, subjectMember);

    if (!position) {
      [workspace, position] = createPosition(workspace, subjectMember.id);
    }

    const moveResult = moveMemberToManager(workspace, subjectMember, position, {
      memberId,
    });

    workspace = moveResult.workspace;

    const currentMember = getUIMemberById(workspace, memberId);

    await slackAdapter.renderOrgChartHomeTab(workspace, currentMember, state);

    await repository.setWorkspace(workspace);
  }
);

export const teamSelect = declareAction(
  { action_id: "team_select", type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();

    assert(
      action.type === "static_select",
      "Expected action to come from a static select"
    );

    const workspaceId = (context.teamId ?? context.enterpriseId)!;
    const memberId = body.user.id;
    const teamId = action.selected_option.value;
    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    let logger = loggerFromBoltContextAndBody(context, body);
    const state = requireOrgChartPageState(body);
    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace not found`);
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);
    logger = logger.withContext({ member });

    logger.info("Slack: Team selected", { teamId });

    const currentMember = getUIMemberById(workspace, memberId);

    const selectedTeam = workspace.teams.find(({ id }) => id === teamId);

    assert(selectedTeam, "Team not found");

    state.organizationSelectedTeam = {
      id: teamId,
      name: selectedTeam.label,
    };

    await slackAdapter.renderOrgChartHomeTab(workspace, currentMember, state);
  }
);

export const departmentSelect = declareAction(
  { action_id: "department_select", type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();

    assert(
      action.type === "static_select",
      "Expected action to come from a static select"
    );

    const workspaceId = (context.teamId ?? context.enterpriseId)!;
    const memberId = body.user.id;
    const departmentId = action.selected_option.value;
    const repository = context.getRepository();
    let logger = loggerFromBoltContextAndBody(context, body);
    const slackAdapter = context.getSlackAdapter(client);
    const state = requireOrgChartPageState(body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace not found`);
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);
    logger = logger.withContext({ member });

    logger.info("Slack: Department selected", { departmentId });

    const selectedDepartment = getNode(
      workspace.orgTree.rootNode,
      (x): x is Department => x.type === "department" && x.id === departmentId
    );

    assert(selectedDepartment, "Department not found");

    const currentMember = getUIMemberById(workspace, memberId);

    state.organizationSelectedDepartment = {
      id: selectedDepartment.id,
      name: selectedDepartment.title,
    };

    await slackAdapter.renderOrgChartHomeTab(workspace, currentMember, state);
  }
);

export const homeClearButton = declareAction(
  { action_id: "home_clear_button", type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();

    assert(action.type === "button", "Expected action to come from a button");

    const workspaceId = (context.teamId ?? context.enterpriseId)!;
    const memberId = body.user.id;
    const repository = context.getRepository();
    let logger = loggerFromBoltContextAndBody(context, body);
    const slackAdapter = context.getSlackAdapter(client);
    const state = requireOrgChartPageState(body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace not found`);
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);
    logger = logger.withContext({ member });

    logger.info("'Clear' button on home tab clicked");

    const currentMember = getUIMemberById(workspace, memberId);

    delete state.organizationSelectedDepartment;
    delete state.organizationSelectedTeam;

    await slackAdapter.renderOrgChartHomeTab(workspace, currentMember, state);
  }
);

const OrgChartPage = (
  props: OrgChartHomePageProps,
  state: OrgChartHomePageState,
  adminSiteUrl: string
): HomeView => {
  if (!props.enabledFeatures.length) {
    return NoFeaturesPage({
      adminSiteUrl,
      isAdmin: props.currentMember.isAdmin,
      currentPage: HomePages.OrgChart,
      enabledFeatures: props.enabledFeatures,
    });
  }

  const peopleAreMissingOrgChart = [
    Context().elements("Please select their managers"),
    ...pickNRandom(props.membersWithoutManager, 5).map((member) =>
      Section({ text: `<@${member.id}>` })
        .accessory(
          ConversationSelect()
            .placeholder("Select a manager")
            .actionId(selectMangerOnHomeTab.actionId)
            .filter("im")
            .excludeBotUsers(true)
            .excludeExternalSharedChannels(true)
        )
        .blockId(member.id)
    ),
  ];

  const departmentSelector = StaticSelect({
    placeholder: "Select a department",
  })
    .actionId(departmentSelect.actionId)
    .options(
      isValidArray(props.departments)
        ? props.departments.map((dep) =>
            Bits.Option({
              text: dep.title === "" ? "department" : dep.title,
              value: dep.id,
            })
          )
        : Bits.Option({
            text: "there is no department in your org-tree",
            value: "0",
          })
    );

  if (state.organizationSelectedDepartment) {
    departmentSelector.initialOption(
      Option({
        text: state.organizationSelectedDepartment.name,
        value: state.organizationSelectedDepartment.id,
      })
    );
  }

  const teamSelector = StaticSelect({ placeholder: "Select a team" })
    .actionId(teamSelect.actionId)
    .options(
      isValidArray(props.teams)
        ? props.teams.map((team) =>
            Bits.Option({ text: team.label, value: team.id })
          )
        : Bits.Option({
            text: "there is no team in your org-tree",
            value: "0",
          })
    );

  if (state.organizationSelectedTeam) {
    teamSelector.initialOption(
      Option({
        text: state.organizationSelectedTeam.name,
        value: state.organizationSelectedTeam.id,
      })
    );
  }

  const peopleLocatedOnOrgTree = props.assignedPositionNumber;

  let homeBlocks: BlockBuilder[] = [
    ...getHomeTabNavigation(HomePages.OrgChart, props.enabledFeatures),
    ...HelpNavigation(`${adminSiteUrl}?workspace=${props.workspaceId}`),
    getNewLineBlock(),
    Header().text(`:Office: ${props.companyName} chart`),
    Divider(),
    Section({
      text: `${peopleLocatedOnOrgTree} ${
        peopleLocatedOnOrgTree > 1 ? "people are" : "person is"
      } now located on your org chart`,
    }),
    Context().elements(
      "Use the filters below to see only people from a certain department or team"
    ),
    Actions().elements(
      departmentSelector,
      teamSelector,
      state.organizationSelectedDepartment || state.organizationSelectedTeam
        ? Button({ text: "Clear" }).actionId(homeClearButton.actionId)
        : undefined,
      UpdateProfileButton({ title: "Update Profile" })
    ),
  ];

  if (state.organizationSelectedDepartment || state.organizationSelectedTeam) {
    if (props.filteredMembersOnOrganizationTab.length) {
      homeBlocks = homeBlocks.concat(
        ...props.filteredMembersOnOrganizationTab
          .slice(0, USER_LIMIT_PER_PAGE)
          .map((user) => UserCard(user, adminSiteUrl, props.workspaceId, true)),
        Divider(),
        props.filteredMembersOnOrganizationTab.length > USER_LIMIT_PER_PAGE
          ? Actions().elements(
              Button({ text: `View on Web` })
                .url(`${adminSiteUrl}?workspace=${props.workspaceId}`)
                .actionId(homeViewOnWebButton.actionId)
            )
          : []
      );
    } else {
      homeBlocks.push(
        Section({
          text: Md.bold(
            "There are no people corresponding to the current filters"
          ),
        })
      );
    }
  }

  if (
    !state.hideAllBlockWithPeopleAreMissingOnOrgChart &&
    props.membersWithoutManager.length
  ) {
    homeBlocks.push(
      getNewLineBlock(),
      Header().text(":warning: Help wanted"),
      Divider(),
      Section({
        text: `${props.membersWithoutManager.length} ${
          props.membersWithoutManager.length > 1 ? "people are" : "person is"
        } missing from the org chart`,
      }).accessory(
        Button()
          .text(state.showPeopleAreMissingOnOrgChartBlock ? "Hide" : "Show")
          .actionId(handleStateOfPeopleAreMissingBlock.actionId)
          .value(state.showPeopleAreMissingOnOrgChartBlock ? "hide" : "show")
      )
    );

    if (state.showPeopleAreMissingOnOrgChartBlock) {
      homeBlocks.push(...peopleAreMissingOrgChart);
    }
  }

  if (props.joinedPeople.length) {
    homeBlocks.push(
      getNewLineBlock(),
      ...JoinedPeopleBlock(
        props.joinedPeople,
        props.companyName,
        adminSiteUrl,
        props.workspaceId
      )
    );
  }

  if (props.openPositions.length) {
    homeBlocks.push(
      getNewLineBlock(),
      ...HiredSection(
        adminSiteUrl,
        props.openPositions,
        props.currentMember,
        props.enableHiring,
        props.workspaceId,
        state.hiringBlockState
      )
    );
  }

  if (
    props.currentMember.howManyTimesHomeTabVisited <=
    HOW_MANY_TIMES_SHOW_FEATURE_BANNER
  ) {
    homeBlocks.unshift(ImageBanner(adminSiteUrl));
  }

  return HomeTab({
    callbackId: "home-tab",
  })
    .blocks(homeBlocks)
    .privateMetaData(JSON.stringify(state))
    .buildToObject();
};

export function requireOrgChartPageState(
  body: BlockAction
): OrgChartHomePageState {
  const metadata = requirePageState(body);

  assert(
    metadata.currentPage === HomePages.OrgChart,
    `Expected to receive metadata for org chart, instead got: "${metadata.currentPage}"`
  );

  return inferHomePageState(
    metadata,
    HomePages.OrgChart,
    getDefaultOrgChartHomePageState
  );
}

export default OrgChartPage;
