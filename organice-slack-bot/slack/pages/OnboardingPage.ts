import assert from "assert";

import { <PERSON><PERSON><PERSON><PERSON>, Member, <PERSON>onlyDeep } from "@organice/core/domain";
import { HomeView } from "@slack/web-api";
import { Button, Divider, HomeTab, Section } from "slack-block-builder";

import ImageBanner, { ImageTypes } from "../components/ImageBanner";
import {
  declareAction,
  loggerFromBoltContextAndBody,
} from "../components/_common";

export interface OnboardingPageProps {
  currentMember: ReadonlyDeep<UIMember> | ReadonlyDeep<Member>;
  installedBy: string | null;
}

export const completeOnboardingClicked = declareAction(
  { action_id: "complete_onboarding_clicked" },
  async ({ ack, action, body, context }) => {
    assert(
      action.type === "button",
      `Expected "complete_onboarding_clicked" action to be triggered on button click, instead got "${action.type}"`
    );

    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("'Complete onboarding' button clicked from the Home tab");

    await ack();
  }
);

const OnboardingPage = (
  props: OnboardingPageProps,
  adminSiteUrl: string
): HomeView => {
  const homeBlocks = [
    ImageBanner(adminSiteUrl, ImageTypes.Onboarding),
    Divider(),
  ];

  if (props.currentMember.isAdmin) {
    homeBlocks.push(
      Section()
        .text(
          `Hey <@${props.currentMember.id}> :wave:, \nPlease complete onboarding before using OrgaNice`
        )
        .accessory(
          Button()
            .primary()
            .text("Complete Onboarding")
            .actionId(completeOnboardingClicked.actionId)
            .url(`${adminSiteUrl}/onboarding`)
        )
    );
  } else {
    homeBlocks.push(
      Section().text(
        `Hey <@${
          props.currentMember.id
        }> :wave:, \nWe are waiting until <@${props.installedBy!}> completes onboarding. Feel free to remind them :smile:`
      )
    );
  }

  return HomeTab({
    callbackId: "home-tab",
  })
    .blocks(homeBlocks)
    .buildToObject();
};

export default OnboardingPage;
