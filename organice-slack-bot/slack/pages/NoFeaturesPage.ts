import {
  ReadonlyDeep,
  HomePages,
  SupportedFeature,
} from "@organice/core/domain";
import { HomeView } from "@slack/web-api";
import { HomeTab } from "slack-block-builder";

import { getHomeTabNavigation } from "../components/Navigation";
import NoFeaturesText from "../components/NoFeaturesText";

interface NoFeaturesPageProps {
  isAdmin: boolean;
  currentPage: HomePages;
  enabledFeatures: ReadonlyDeep<SupportedFeature[]>;
  adminSiteUrl: string;
}

const NoFeaturesPage = ({
  isAdmin,
  currentPage,
  enabledFeatures,
  adminSiteUrl,
}: NoFeaturesPageProps): HomeView => {
  const homeTab = HomeTab({
    callbackId: "home-tab",
  });

  const blocks = [
    ...getHomeTabNavigation(currentPage, enabledFeatures),
    NoFeaturesText(adminSiteUrl, isAdmin),
  ];

  return homeTab.blocks(blocks).buildToObject();
};

export default NoFeaturesPage;
