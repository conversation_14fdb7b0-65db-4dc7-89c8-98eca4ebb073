import { <PERSON><PERSON><PERSON><PERSON>, Member, <PERSON>only<PERSON>eep } from "@organice/core/domain";
import { HomeView } from "@slack/web-api";
import { Button, HomeTab, Section, BlockBuilder } from "slack-block-builder";

import ImageBanner, { ImageTypes } from "../components/ImageBanner";

export interface TrialOverPageProps {
  currentMember: ReadonlyDeep<UIMember> | ReadonlyDeep<Member>;
  installedBy: string | null;
}

const TrialExpiredPage = (
  props: TrialOverPageProps,
  adminSiteUrl: string
): HomeView => {
  const homeBlocks: BlockBuilder[] = [
    ImageBanner(adminSiteUrl, ImageTypes.Trial),
  ];

  if (props.currentMember.isAdmin) {
    homeBlocks.push(
      Section()
        .text(
          `Hi there <@${props.currentMember.id}>, your free plan is over. To continue enjoying the benefits of OrgaNice, we encourage you to upgrade your plan.`
        )
        .accessory(
          Button()
            .primary()
            .text("Upgrade Plan")
            .url(`${adminSiteUrl}/settings/pricing`)
        )
    );
  } else {
    homeBlocks.push(
      Section().text(
        `Hi there <@${
          props.currentMember.id
        }>, your free plan is over. We are waiting until <@${props.installedBy!}> upgrades the plan. Feel free to remind them :smile:`
      )
    );
  }

  return HomeTab({
    callbackId: "home-tab",
  })
    .blocks(homeBlocks)
    .buildToObject();
};

export default TrialExpiredPage;
