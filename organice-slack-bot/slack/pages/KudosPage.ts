/* eslint-disable import/no-cycle */
import assert from "assert";

import {
  Home<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Member,
  HOW_MANY_TIMES_SHOW_FEATURE_BANNER,
  KudosHomePageState,
  SupportedFeature,
} from "@organice/core/domain";
import { BlockAction } from "@slack/bolt";
import { HomeView } from "@slack/web-api";
import {
  Actions,
  Button,
  HomeTab,
  Option,
  StaticSelect,
  Context,
  ActionsBuilder,
  Paginator,
  BlockBuilder,
} from "slack-block-builder";

import GiveKudosModal from "../components/GiveKudosModal";
import HelpNavigation from "../components/HelpNavigation";
import ImageBanner from "../components/ImageBanner";
import KudosStats, { KudosStatsProps } from "../components/Kudos/KudosStats";
import KudosCard, { KudosCardProps } from "../components/KudosCard";
import { getHomeTabNavigation } from "../components/Navigation";
import ViewKudosValuesButton from "../components/ViewKudosValuesButton";
import {
  declareAction,
  loggerFromBoltContextAndBody,
} from "../components/_common";
import { PaginatorAction } from "../domain";
import {
  isValidArray,
  getSafePage,
  tryJsonParse,
  requirePageState,
  getNewLineBlock,
  inferHomePageState,
} from "../helpers";

import NoFeaturesPage from "./NoFeaturesPage";

export const KUDOS_PER_PAGE = 5;

export const renderKudos = declareAction(
  { action_id: /render_kudos/, type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();
    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);

    const actionValues = tryJsonParse<PaginatorAction>(action.action_id);

    assert(actionValues, "Expected action.action_id to be a valid JSON");

    const memberId = body.user.id;
    const state = requireKudosPageState(body);
    const page = actionValues.page;

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, "Expected workspace to exist");

    const member = workspace.members.find((m) => m.id === memberId);

    assert(member, `Member not found`);

    state.paginatorPage = page;

    await slackAdapter.renderKudosHomeTab(workspace, member, state);
  }
);

export const kudosPageSelected = declareAction(
  { action_id: "kudos_page_selected", type: "block_actions" },
  async ({ ack, client, body, context, action }) => {
    await ack();

    assert(
      action.type === "static_select",
      "Expected action to come from a static select"
    );

    const kudosPage = action.selected_option.value;

    assert(
      kudosPage === "given_kudos" || kudosPage === "received_kudos",
      "Expected kudos page to be selected"
    );

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const logger = loggerFromBoltContextAndBody(context, body);
    const state = requireKudosPageState(body);
    const workspaceId = body.team?.id;
    const memberId = body.user.id;

    assert(workspaceId, "Expected workspace id to exist");

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace with ${workspaceId} not found`);
    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);

    logger.info(`Kudos page "${kudosPage}" selected`);

    state.currentKudosPage = kudosPage;
    state.paginatorPage = 1;

    await slackAdapter.renderKudosHomeTab(workspace, member, state);
  }
);

export const giveKudos = declareAction(
  { action_id: "give_kudos", type: "block_actions" },
  async ({ ack, body, client, context, action }) => {
    assert(action.type === "button", "Expected action to come from a button");

    const workspaceId = context.teamId ?? context.enterpriseId;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    let logger = loggerFromBoltContextAndBody(context, body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, "Workspace not found");

    const member = workspace.members.find((x) => x.id === body.user.id);

    assert(member, "Member not found");

    logger = logger.withContext({ member });
    logger.info("Kudos - Home tab: 'Give Kudos' clicked");

    const queryHandler = context.getSlackBotQueryHandler();

    await client.views.open({
      view: GiveKudosModal(
        await queryHandler.getKudosModalProps(
          workspace,
          member,
          context.getTime()
        ),
        {
          withGIF: false,
        }
      ),
      trigger_id: body.trigger_id,
    });

    await ack();
  }
);

export const viewKudosInsightsClicked = declareAction(
  { action_id: "view_kudos_insights_clicked", type: "block_actions" },
  async ({ ack, body, context }) => {
    await ack();

    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("Kudos Tab - View Insights button clicked ");
  }
);

const buildKudosPageActions = (
  adminSiteUrl: string,
  currentKudosPage: "given_kudos" | "received_kudos"
): ActionsBuilder[] => {
  const options = {
    given_kudos: Option({ text: "Given Kudos", value: "given_kudos" }),
    received_kudos: Option({ text: "Received Kudos", value: "received_kudos" }),
  };

  const kudosSelect = StaticSelect()
    .actionId(kudosPageSelected.actionId)
    .options([options.given_kudos, options.received_kudos])
    .initialOption(options[currentKudosPage]);

  return [
    Actions().elements(
      kudosSelect,
      Button({ text: ":clap: Give Kudos" })
        .primary()
        .actionId(giveKudos.actionId),
      Button()
        .text(":eyes: View Insights")
        .actionId(viewKudosInsightsClicked.actionId)
        .url(
          `${adminSiteUrl}/kudos?stats_tab=${
            currentKudosPage === "given_kudos" ? "GIVEN" : "RECEIVED"
          }`
        ),
      ViewKudosValuesButton()
    ),
  ];
};

export interface KudosHomePageProps {
  currentMember: ReadonlyDeep<Member>;
  kudos: KudosCardProps[];
  totalKudosCount: number;
  workspaceId: string;
  enabledFeatures: ReadonlyDeep<SupportedFeature[]>;
  kudosStatsProps: KudosStatsProps;
}

export function requireKudosPageState(body: BlockAction): KudosHomePageState {
  const metadata = requirePageState(body);

  assert(
    metadata.currentPage === HomePages.Kudos,
    `Expected to receive metadata for kudos page, instead got: "${metadata.currentPage}"`
  );

  return inferHomePageState(
    metadata,
    HomePages.Kudos,
    getDefaultKudosHomePageState
  );
}

export function getDefaultKudosHomePageState(
  currentKudosPage: "given_kudos" | "received_kudos" = "given_kudos"
): KudosHomePageState {
  return {
    version: "v1",
    currentPage: HomePages.Kudos,
    currentKudosPage,
    paginatorPage: 1,
    showPeopleAreMissingOnOrgChartBlock: true,
    hiringBlockState: true,
    hideAllBlockWithPeopleAreMissingOnOrgChart: false,
  };
}

const KudosPage = (
  props: KudosHomePageProps,
  state: KudosHomePageState,
  adminSiteUrl: string
): HomeView => {
  if (!props.enabledFeatures.length) {
    return NoFeaturesPage({
      adminSiteUrl,
      currentPage: HomePages.Kudos,
      isAdmin: props.currentMember.isAdmin,
      enabledFeatures: props.enabledFeatures,
    });
  }

  const kudosBlocks: BlockBuilder[] = [
    ...getHomeTabNavigation(HomePages.Kudos, props.enabledFeatures),
    ...HelpNavigation(`${adminSiteUrl}/kudos?workspace=${props.workspaceId}`),
    getNewLineBlock(),
  ];

  kudosBlocks.push(
    Context().elements(
      "Unleash the power of appreciation with Kudos! Elevate your workplace by acknowledging employees' achievements and milestones."
    ),
    ...KudosStats(props.kudosStatsProps),
    ...buildKudosPageActions(adminSiteUrl, state.currentKudosPage)
  );

  if (
    props.currentMember.howManyTimesHomeTabVisited <=
    HOW_MANY_TIMES_SHOW_FEATURE_BANNER
  ) {
    kudosBlocks.unshift(ImageBanner(adminSiteUrl));
  }

  const homeTab = HomeTab({
    callbackId: "home-tab",
  }).blocks(kudosBlocks);

  const items = props.kudos;

  const currentPage = getSafePage(KUDOS_PER_PAGE, state.paginatorPage, [
    ...items,
  ]);

  homeTab.privateMetaData(
    JSON.stringify({
      ...state,
      paginatorPage: currentPage,
    })
  );

  const kudosPaginator = Paginator({
    perPage: KUDOS_PER_PAGE,
    page: currentPage,
    items: props.kudos,
    totalItems: props.totalKudosCount,
    actionId: ({ buttonId, page, offset }) =>
      JSON.stringify({
        action: "render_kudos",
        buttonId,
        page,
        offset,
      }),
    blocksForEach: ({ item }) => KudosCard(item),
  }).getBlocks();

  homeTab.blocks(
    isValidArray(props.kudos)
      ? kudosPaginator
      : [Context().elements("There are no kudos yet")]
  );

  return homeTab.buildToObject();
};

export default KudosPage;
