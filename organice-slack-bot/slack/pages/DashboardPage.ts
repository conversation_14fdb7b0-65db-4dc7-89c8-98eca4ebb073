/* eslint-disable import/no-cycle */
import assert from "assert";

import {
  DashboardHomePageState,
  GlobalContext,
  HomePages,
  Member,
  PresetPolicyId,
  ReadonlyDeep,
  SupportedFeature,
  Workspace,
} from "@organice/core/domain";
import {
  collectMemberData,
  formatFieldValueForSlack,
  formatPolicyFields,
  getAmountOfRequiredAndFilledFields,
  sortByProfileSections,
  FormattedPolicy,
} from "@organice/core/domain/data-completion";
import formatDate from "@organice/core/utils/formatDate";
import { HomeView } from "@slack/web-api";
import { chunk, differenceBy, times } from "lodash";
import {
  BlockBuilder,
  Divider,
  Header,
  HomeTab,
  Md,
  Section,
  SectionBuilder,
} from "slack-block-builder";

import HelpNavigation from "../components/HelpNavigation";
import KudosStats, { KudosStatsProps } from "../components/Kudos/KudosStats";
import { getHomeTabNavigation } from "../components/Navigation";
import RewardsStats from "../components/Rewards/RewardsStats";
import { MyTimeOffsBalance } from "../components/TimeOffStats";
import UpdateProfileButton, {
  getEditablePolicyFields,
} from "../components/UpdateProfileButton";
import { isFeatureEnabled } from "../domain";
import { truncateText, getNewLineBlock } from "../helpers";

import NoFeaturesPage from "./NoFeaturesPage";

export function getDefaultDashboardHomePageState(): DashboardHomePageState {
  return {
    version: "v1",
    currentPage: HomePages.Dashboard,
    hideAllBlockWithPeopleAreMissingOnOrgChart: false,
    showPeopleAreMissingOnOrgChartBlock: false,
    hiringBlockState: false,
  };
}

interface DashboardOrgChartInformationProps {
  currentMember: ReadonlyDeep<Member>;
}

function resortInTwoColumnsOrder<TItem>(array: TItem[]): TItem[] {
  const maxColumnSize = Math.ceil(array.length / 2);

  return array.sort((a, b) => {
    const idxA = array.indexOf(a);
    const modIdxA = idxA % maxColumnSize;
    const idxB = array.indexOf(b);
    const modIdxB = idxB % maxColumnSize;

    if (modIdxA === modIdxB) {
      return idxA - idxB;
    }

    return modIdxA - modIdxB;
  });
}

interface PopulatedProfileFieldsListProps {
  workspace: ReadonlyDeep<Workspace>;
  memberId: string;
}

export function PopulatedProfileFieldList({
  workspace,
  memberId,
}: PopulatedProfileFieldsListProps): [SectionBuilder[], FormattedPolicy[]] {
  const member = workspace.members.find((m) => m.id === memberId);

  assert(member, "Member not found");

  const fields = sortByProfileSections(
    getEditablePolicyFields(workspace, member),
    "id"
  );

  const memberData = collectMemberData(workspace, member);

  const resorted = resortInTwoColumnsOrder(
    fields.filter((f) => {
      const hasValue = Boolean(memberData[f.id]);

      if (f.id === PresetPolicyId.MANAGER) {
        return hasValue || memberData.managerPositionId === "withoutManager";
      }

      return hasValue;
    })
  );

  function SectionFields(array: FormattedPolicy[]): string[] {
    return array.map((f) => {
      const value = memberData[f.id];

      if (f.id === PresetPolicyId.MANAGER) {
        const withoutManager =
          value == null && memberData.managerPositionId === "withoutManager";

        return `*${f.label}*: ${
          withoutManager ? "Without manager" : `<@${value!}>`
        }`;
      }

      assert(value, "It should be non empty value for displaying");

      if (f.id === PresetPolicyId.COUNTRY) {
        const countryPolicy = formatPolicyFields(workspace).find(
          (p) => p.id === PresetPolicyId.COUNTRY
        );

        assert(countryPolicy, "Expected country policy field to exist");

        return `*${f.label}*: ${formatFieldValueForSlack(
          countryPolicy,
          value
        )}`;
      }

      if (f.type === "user") {
        return `*${f.label}*: <@${value}>`;
      }

      if (f.type === "date") {
        return `*${f.label}*: ${formatDate(new Date(value))}`;
      }

      if (f.type === "link") {
        return `*${f.label}*: <${value}|${truncateText(value, 25)}>`;
      }

      return `*${f.label}*: ${value}`;
    });
  }

  return [
    chunk(resorted, 10)
      .filter((item) => item.length > 0)
      .map((item) => Section().fields(SectionFields(item))),
    resorted,
  ];
}

function DashboardOrgChartInformation(
  props: DashboardOrgChartInformationProps,
  context: GlobalContext
): (BlockBuilder | undefined)[] {
  const fields = sortByProfileSections(
    getEditablePolicyFields(context.workspace, props.currentMember),
    "id"
  );

  const [populatedFieldsBlocks, populated] = PopulatedProfileFieldList({
    workspace: context.workspace,
    memberId: props.currentMember.id,
  });

  const progress = getAmountOfRequiredAndFilledFields(
    context.workspace,
    props.currentMember
  );

  const missing = differenceBy(fields, populated, (f) => f.id);

  const squares = times(10, (num) =>
    progress[1] / progress[0] < (num + 1) / 10
      ? ":white_square:"
      : ":large_green_square:"
  ).join(" ");

  const profileCompleteness = `*Profile completeness*:\n${squares}  ${progress[1]} / ${progress[0]}`;

  const missingFields =
    missing.length > 0
      ? `${Md.bold("Missing fields")}:\n${missing
          .map((f) => `— ${f.label} ${f.required ? "(required)" : ""}`)
          .join("\n")}`
      : "";

  return [
    Header().text(":bust_in_silhouette: My Profile"),
    Divider(),
    ...populatedFieldsBlocks,
    getNewLineBlock(),
    Section()
      .text(`${profileCompleteness} \n \n ${missingFields}`)
      .accessory(UpdateProfileButton({ title: "Update Profile" })),
  ];
}

interface DashboardPageProps {
  currentMember: ReadonlyDeep<Member>;
  enabledFeatures: ReadonlyDeep<SupportedFeature[]>;
  kudosStatsProps: KudosStatsProps;
}

const DashboardPage = (
  props: DashboardPageProps,
  state: DashboardHomePageState,
  adminSiteUrl: string,
  context: GlobalContext
): HomeView => {
  if (!props.enabledFeatures.length) {
    return NoFeaturesPage({
      adminSiteUrl,
      isAdmin: props.currentMember.isAdmin,
      currentPage: HomePages.Dashboard,
      enabledFeatures: props.enabledFeatures,
    });
  }

  return HomeTab({
    callbackId: "home-tab",
  })
    .blocks([
      ...getHomeTabNavigation(HomePages.Dashboard, props.enabledFeatures),
      ...HelpNavigation(`${adminSiteUrl}/?workspace=${context.workspace.id}`),
      getNewLineBlock(),
      ...(isFeatureEnabled(props.enabledFeatures, SupportedFeature.OrgChart)
        ? DashboardOrgChartInformation(
            { currentMember: props.currentMember },
            context
          )
        : []),

      ...(isFeatureEnabled(props.enabledFeatures, SupportedFeature.TimeOffs)
        ? MyTimeOffsBalance(props.currentMember.id, context, true)
        : []),

      ...(isFeatureEnabled(props.enabledFeatures, SupportedFeature.Kudos)
        ? KudosStats(props.kudosStatsProps)
        : []),
      ...(isFeatureEnabled(props.enabledFeatures, SupportedFeature.Rewards)
        ? RewardsStats({
            workspace: context.workspace,
            currentMember: props.currentMember,
            showLearnMoreButton: true,
          })
        : []),
    ])
    .privateMetaData(JSON.stringify(state))
    .buildToObject();
};

export default DashboardPage;
