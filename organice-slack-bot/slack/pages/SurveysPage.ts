/* eslint-disable import/no-cycle */
import assert from "assert";

import {
  HomePages,
  HOW_MANY_TIMES_SHOW_FEATURE_BANNER,
  SurveysHomePageProps,
  SurveysHomePageState,
} from "@organice/core/domain";
import { BlockAction } from "@slack/bolt";
import { HomeView } from "@slack/web-api";
import {
  Actions,
  Button,
  Divider,
  HomeTab,
  Option,
  StaticSelect,
  Context,
  ActionsBuilder,
  Paginator,
  BlockBuilder,
} from "slack-block-builder";

import HelpNavigation from "../components/HelpNavigation";
import ImageBanner from "../components/ImageBanner";
import { getHomeTabNavigation } from "../components/Navigation";
import SurveyCard from "../components/Surveys/SurveyCard";
import {
  declareAction,
  loggerFromBoltContextAndBody,
} from "../components/_common";
import { PaginatorAction } from "../domain";
import {
  isValidArray,
  getPageSlice,
  tryJsonParse,
  requirePageState,
  getNewLineBlock,
  inferHomePageState,
  getSafePage,
  getDefaultSurveysHomePageState,
} from "../helpers";

import NoFeaturesPage from "./NoFeaturesPage";

const SURVEYS_PER_PAGE = 5;

export const renderSurveys = declareAction(
  { action_id: /render_surveys/, type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();
    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);

    const actionValues = tryJsonParse<PaginatorAction>(action.action_id);

    assert(actionValues, "Expected action.action_id to be a valid JSON");

    const memberId = body.user.id;
    const state = requireSurveysPageState(body);
    const page = actionValues.page;

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, "Expected workspace to exist");

    const member = workspace.members.find((m) => m.id === memberId);

    assert(member, `Member not found`);

    state.paginatorPage = page;

    await slackAdapter.renderSurveysHomeTab(workspace, member, state);
  }
);

export const surveyPageSelected = declareAction(
  { action_id: "survey_page_selected", type: "block_actions" },
  async ({ ack, client, body, context, action }) => {
    await ack();

    assert(
      action.type === "static_select",
      "Expected action to come from a static select"
    );

    const surveyPage = action.selected_option.value;

    assert(
      surveyPage === "all_surveys" ||
        surveyPage === "closed_surveys" ||
        surveyPage === "in_progress_surveys" ||
        surveyPage === "not_published_surveys",
      "Expected surveys page to be selected"
    );

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const logger = loggerFromBoltContextAndBody(context, body);
    const state = requireSurveysPageState(body);
    const workspaceId = body.team?.id;
    const memberId = body.user.id;

    assert(workspaceId, "Expected workspace id to exist");

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace with ${workspaceId} not found`);
    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);

    logger.info(`Surveys page "${surveyPage}" selected`);

    state.currentSurveyPage = surveyPage;
    state.paginatorPage = 1;
    await slackAdapter.renderSurveysHomeTab(workspace, member, state);
  }
);

export const runSurveyClicked = declareAction(
  { action_id: "run_survey_clicked" },
  async ({ ack, action, body, context }) => {
    assert(
      action.type === "button",
      `Expected "run_survey_clicked" action to be triggered on button click, instead got "${action.type}"`
    );

    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("'Run survey' button clicked from the Home tab");

    await ack();
  }
);

export const surveyTemplatesClicked = declareAction(
  { action_id: "survey_templates_clicked" },
  async ({ ack, action, body, context }) => {
    assert(
      action.type === "button",
      `Expected "survey_templates_clicked" action to be triggered on button click, instead got "${action.type}"`
    );

    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("Survey 'Templates' button clicked from the Home tab");

    await ack();
  }
);

const buildSurveysPageActions = (
  adminSiteUrl: string,
  currentSurveyPage:
    | "all_surveys"
    | "closed_surveys"
    | "in_progress_surveys"
    | "not_published_surveys",
  isAdmin: boolean
): ActionsBuilder[] => {
  if (!isAdmin) {
    return [];
  }
  const options = {
    all_surveys: Option({ text: "All surveys", value: "all_surveys" }),
    closed_surveys: Option({ text: "Closed surveys", value: "closed_surveys" }),
    in_progress_surveys: Option({
      text: "Surveys in progress",
      value: "in_progress_surveys",
    }),
    not_published_surveys: Option({
      text: "Not published surveys",
      value: "not_published_surveys",
    }),
  };

  const surveysSelect = StaticSelect()
    .actionId(surveyPageSelected.actionId)
    .options([
      options.all_surveys,
      options.closed_surveys,
      options.in_progress_surveys,
      options.not_published_surveys,
    ])
    .initialOption(options[currentSurveyPage]);

  return [
    Actions().elements(
      surveysSelect,
      Button({ text: ":zap: Run Survey" })
        .primary()
        .actionId(runSurveyClicked.actionId)
        .url(`${adminSiteUrl}/surveys`),
      Button()
        .text(":memo: Templates")
        .actionId(surveyTemplatesClicked.actionId)
        .url(`${adminSiteUrl}/settings/surveys`)
    ),
  ];
};

export function requireSurveysPageState(
  body: BlockAction
): SurveysHomePageState {
  const metadata = requirePageState(body);

  assert(
    metadata.currentPage === HomePages.Surveys,
    `Expected to receive metadata for surveys page, instead got: "${metadata.currentPage}"`
  );

  return inferHomePageState(
    metadata,
    HomePages.Surveys,
    getDefaultSurveysHomePageState
  );
}

const SurveysPage = (
  props: SurveysHomePageProps,
  state: SurveysHomePageState,
  adminSiteUrl: string
): HomeView => {
  if (!props.enabledFeatures.length) {
    return NoFeaturesPage({
      adminSiteUrl,
      isAdmin: props.currentMember.isAdmin,
      currentPage: HomePages.Surveys,
      enabledFeatures: props.enabledFeatures,
    });
  }

  const homeTab = HomeTab({
    callbackId: "home-tab",
  });

  const surveysBlocks: BlockBuilder[] = [
    ...getHomeTabNavigation(HomePages.Surveys, props.enabledFeatures),
    ...HelpNavigation(`${adminSiteUrl}/surveys`),
    getNewLineBlock(),
  ];

  if (props.currentMember.isAdmin) {
    surveysBlocks.push(
      Context().elements(
        "Run surveys on diverse topics, track results and enhance employee experience."
      )
    );
    surveysBlocks.push(Divider());
  }

  surveysBlocks.push(
    ...buildSurveysPageActions(
      adminSiteUrl,
      state.currentSurveyPage,
      props.currentMember.isAdmin
    )
  );

  if (
    props.currentMember.howManyTimesHomeTabVisited <=
    HOW_MANY_TIMES_SHOW_FEATURE_BANNER
  ) {
    surveysBlocks.unshift(ImageBanner(adminSiteUrl));
  }

  homeTab.blocks(surveysBlocks);

  const items = props.surveys;

  const currentPage = getSafePage(SURVEYS_PER_PAGE, state.paginatorPage, [
    ...items,
  ]);

  homeTab.privateMetaData(
    JSON.stringify({
      ...state,
      paginatorPage: currentPage,
    })
  );

  const itemsPart = getPageSlice(SURVEYS_PER_PAGE, currentPage, [...items]);

  const surveysPaginator = Paginator({
    perPage: SURVEYS_PER_PAGE,
    page: currentPage,
    items: itemsPart,
    totalItems: items.length,
    actionId: ({ buttonId, page, offset }) =>
      JSON.stringify({
        action: "render_surveys",
        buttonId,
        page,
        offset,
      }),
    blocksForEach: ({ item }) => [
      ...SurveyCard({
        survey: item,
        adminSiteUrl,
        currentMember: props.currentMember,
      }),
    ],
  }).getBlocks();

  homeTab.blocks(
    isValidArray(items)
      ? surveysPaginator
      : [Context().elements("There are no surveys yet")]
  );

  return homeTab.buildToObject();
};

export default SurveysPage;
