/* eslint-disable import/no-cycle */
import assert from "assert";

import {
  RewardsHomePageState,
  RewardsHomePageProps,
  HOW_MANY_TIMES_SHOW_FEATURE_BANNER,
  HomePages,
  RewardsPages,
  RewardRequest,
  ReadonlyDeep,
  RewardRequestStatus,
} from "@organice/core/domain";
import { BlockAction, HomeView, SlackViewAction } from "@slack/bolt";
import {
  Actions,
  Button,
  Divider,
  HomeTab,
  BlockBuilder,
  Option,
  StaticSelect,
  ActionsBuilder,
  Context,
  Paginator,
  setIfTruthy,
} from "slack-block-builder";

import HelpNavigation from "../components/HelpNavigation";
import ImageBanner from "../components/ImageBanner";
import { getHomeTabNavigation } from "../components/Navigation";
import ViewRewardActivitiesButton from "../components/Rewards/ActionsRewardsButton";
import RewardCard from "../components/Rewards/RewardCard";
import RewardRequestCard, {
  RewardRequestWithGroupTitle,
} from "../components/Rewards/RewardRequestCard";
import RewardsStats from "../components/Rewards/RewardsStats";
import {
  declareAction,
  loggerFromBoltContextAndBody,
} from "../components/_common";
import { PaginatorAction } from "../domain";
import {
  getNewLineBlock,
  requirePageState,
  tryJsonParse,
  isValidArray,
  getPageSlice,
  getSafePage,
  inferHomePageState,
  getDefaultRewardsHomePageState,
} from "../helpers";

import NoFeaturesPage from "./NoFeaturesPage";

const REWARDS_PER_PAGE = 5;
const REQUESTS_PER_PAGE = 10;

const applyGroupTitle = (
  request: ReadonlyDeep<RewardRequest>,
  index: number,
  groupTitle: "Pending" | "Approved" | "Rejected"
): ReadonlyDeep<RewardRequestWithGroupTitle> => ({
  ...request,
  groupTitle: index === 0 ? groupTitle : null,
});

function getSortedRewardRequests(
  requests: readonly ReadonlyDeep<RewardRequest>[]
): ReadonlyDeep<RewardRequestWithGroupTitle>[] {
  const pending = requests
    .filter((r) => r.status === RewardRequestStatus.Pending)
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .map((r, i) => applyGroupTitle(r, i, "Pending"));

  const approved = requests
    .filter((r) => r.status === RewardRequestStatus.Approved)
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .map((r, i) => applyGroupTitle(r, i, "Approved"));
  const rejected = requests
    .filter((r) => r.status === RewardRequestStatus.Rejected)
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .map((r, i) => applyGroupTitle(r, i, "Rejected"));

  return [...pending, ...approved, ...rejected];
}

export function requireRewardsPageState(
  body: BlockAction | SlackViewAction,
  isAdmin: boolean
): RewardsHomePageState {
  const metadata = requirePageState(body);

  assert(
    metadata.currentPage === HomePages.Rewards,
    `Expected to receive metadata for rewards, instead got: "${metadata.currentPage}"`
  );

  return inferHomePageState(metadata, HomePages.Rewards, () =>
    getDefaultRewardsHomePageState(isAdmin)
  );
}

export const renderRewards = declareAction(
  { action_id: /render_rewards/, type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();
    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);

    const actionValues = tryJsonParse<PaginatorAction>(action.action_id);

    assert(actionValues, "Expected action.action_id to be a valid JSON");

    const memberId = body.user.id;
    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, "Expected workspace to exist");

    const member = workspace.members.find((m) => m.id === memberId);

    assert(member, `Member not found`);
    const state = requireRewardsPageState(body, member.isAdmin);
    const page = actionValues.page;

    state.paginatorPage = page;

    await slackAdapter.renderRewardsHomeTab(workspace, member, state);
  }
);

export const rewardsPageSelected = declareAction(
  { action_id: "rewards_page_selected", type: "block_actions" },
  async ({ ack, client, body, context, action }) => {
    await ack();

    assert(
      action.type === "static_select",
      "Expected action to come from a static select"
    );

    const rewardsPage = action.selected_option.value;

    assert(
      rewardsPage === RewardsPages.AvailableRewards ||
        rewardsPage === RewardsPages.MyRequests ||
        rewardsPage === RewardsPages.IncomingRequests,
      "Expected rewards page to be selected"
    );

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const logger = loggerFromBoltContextAndBody(context, body);
    const workspaceId = body.team?.id;
    const memberId = body.user.id;

    assert(workspaceId, "Expected workspace id to exist");

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace with ${workspaceId} not found`);
    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);

    const state = requireRewardsPageState(body, member.isAdmin);

    logger.info(`Rewards page "${rewardsPage}" selected`);

    state.currentRewardsPage = rewardsPage;
    state.paginatorPage = 1;

    await slackAdapter.renderActiveTab(workspace, member, state);
  }
);

export const viewLeaderboardClicked = declareAction(
  { action_id: "view_rewards_leaderboard_clicked", type: "block_actions" },
  async ({ ack, body, context }) => {
    await ack();

    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("Rewards Tab - View Rewards Leaderboard clicked");
  }
);

const buildRewardsPageActions = (
  currentRewardsPage: RewardsPages,
  adminSiteUrl: string,
  isAdmin: boolean,
  hasActiveRewards: boolean
): ActionsBuilder[] => {
  const options = {
    [RewardsPages.AvailableRewards]: Option({
      text: "Available Rewards",
      value: RewardsPages.AvailableRewards,
    }),
    [RewardsPages.MyRequests]: Option({
      text: "My Requests",
      value: RewardsPages.MyRequests,
    }),
    [RewardsPages.IncomingRequests]: Option({
      text: "Incoming Requests",
      value: RewardsPages.IncomingRequests,
    }),
  };

  const selectOptions = [
    options[RewardsPages.AvailableRewards],
    options[RewardsPages.MyRequests],
  ];

  if (isAdmin) {
    selectOptions.push(options[RewardsPages.IncomingRequests]);
  }

  const rewardsSelect = StaticSelect()
    .actionId(rewardsPageSelected.actionId)
    .options(selectOptions)
    .initialOption(options[currentRewardsPage]);

  const elements = [
    rewardsSelect,
    setIfTruthy(
      hasActiveRewards,
      Button({ text: ":trophy: Request Reward" })
        .actionId("request_reward")
        .primary()
    ),
    Button()
      .text(":medal: View Leaderboard")
      .actionId(viewLeaderboardClicked.actionId)
      .url(`${adminSiteUrl}/rewards/leaderboard`),
    ViewRewardActivitiesButton(),
  ];

  return [Actions().elements(elements)];
};

const RewardsPage = (
  props: RewardsHomePageProps,
  state: RewardsHomePageState,
  adminSiteUrl: string
): HomeView => {
  const rewards = props.workspace.rewardsSettings.rewards;
  const rewardsRequests =
    state.currentRewardsPage === RewardsPages.IncomingRequests
      ? props.workspace.rewardsRequests
      : props.workspace.rewardsRequests.filter(
          (request) => request.memberId === props.currentMember.id
        );

  const enabledFeatures = props.workspace.onboarding.bookmarkedFeatures;

  if (!enabledFeatures.length) {
    return NoFeaturesPage({
      adminSiteUrl,
      isAdmin: props.currentMember.isAdmin,
      currentPage: HomePages.Rewards,
      enabledFeatures,
    });
  }

  const hasActiveRewards = rewards.some((reward) => reward.isActive);

  const rewardsBlocks: BlockBuilder[] = [
    ...getHomeTabNavigation(HomePages.Rewards, enabledFeatures),
    ...HelpNavigation(`${adminSiteUrl}?workspace=${props.workspace.id}`),
    getNewLineBlock(),
    Context().elements(
      "Redeem your hard-earned coins for exciting rewards. Your contributions deserve to be celebrated!"
    ),
    ...RewardsStats({
      workspace: props.workspace,
      currentMember: props.currentMember,
    }),
    Divider(),
    ...buildRewardsPageActions(
      state.currentRewardsPage,
      adminSiteUrl,
      props.currentMember.isAdmin,
      hasActiveRewards
    ),
  ];

  if (
    props.currentMember.howManyTimesHomeTabVisited <=
    HOW_MANY_TIMES_SHOW_FEATURE_BANNER
  ) {
    rewardsBlocks.unshift(ImageBanner(adminSiteUrl));
  }

  const homeTab = HomeTab({
    callbackId: "home-tab",
  }).blocks(rewardsBlocks);

  if (state.currentRewardsPage === RewardsPages.AvailableRewards) {
    const items = rewards.filter((reward) => reward.isActive);

    const currentPage = getSafePage(
      REWARDS_PER_PAGE,
      state.paginatorPage,
      items
    );

    homeTab.privateMetaData(
      JSON.stringify({
        ...state,
        paginatorPage: currentPage,
      })
    );

    const itemsPart = getPageSlice(REWARDS_PER_PAGE, currentPage, items);

    const rewardsPaginator = Paginator({
      perPage: REWARDS_PER_PAGE,
      page: currentPage,
      items: itemsPart,
      totalItems: items.length,
      actionId: ({ buttonId, page, offset }) =>
        JSON.stringify({
          action: "render_rewards",
          buttonId,
          page,
          offset,
        }),
      blocksForEach: ({ item }) =>
        RewardCard({
          reward: item,
          isLast: itemsPart.indexOf(item) === itemsPart.length - 1,
        }),
    }).getBlocks();

    homeTab.blocks(
      isValidArray(items)
        ? rewardsPaginator
        : [Context().elements("There are no rewards yet")]
    );
  } else {
    const allRequests = getSortedRewardRequests(rewardsRequests);

    const currentPage = getSafePage(
      REQUESTS_PER_PAGE,
      state.paginatorPage,
      allRequests
    );

    homeTab.privateMetaData(
      JSON.stringify({
        ...state,
        paginatorPage: currentPage,
      })
    );

    const itemsPart = getPageSlice(REQUESTS_PER_PAGE, currentPage, [
      ...allRequests,
    ]);

    const rewardsPaginator = Paginator({
      perPage: REQUESTS_PER_PAGE,
      page: currentPage,
      items: itemsPart,
      totalItems: allRequests.length,
      actionId: ({ buttonId, page, offset }) =>
        JSON.stringify({
          action: "render_rewards",
          buttonId,
          page,
          offset,
        }),
      blocksForEach: ({ item }) => {
        const reward = rewards.find((r) => r.id === item.rewardId);

        assert(reward, `Reward with id ${item.rewardId} not found`);

        const currentIndex = itemsPart.indexOf(item);
        const isLastInArray = currentIndex === itemsPart.length - 1;
        const nextItemHasGroupTitle =
          !isLastInArray && !!itemsPart[currentIndex + 1].groupTitle;
        const isLast = isLastInArray || nextItemHasGroupTitle;

        return RewardRequestCard({
          request: item,
          reward,
          isLast,
          currentRewardsPage: state.currentRewardsPage,
        });
      },
    }).getBlocks();

    homeTab.blocks(
      isValidArray(allRequests)
        ? rewardsPaginator
        : [Context().elements("You have no reward requests yet.")]
    );
  }

  return homeTab.buildToObject();
};

export default RewardsPage;
