/* eslint-disable import/no-cycle */
import assert from "assert";

import {
  HomePages,
  <PERSON><PERSON>lyDeep,
  Member,
  CalendarHomePageState,
  CalendarHomePageProps,
  SlackAdapter,
  Position,
  TimeOffRequest,
  Workspace,
  Department,
  UIMember,
  RootNode,
  SlackAppHomeGroupedCalendarEvent,
  GlobalContext,
  SupportedFeature,
  SlackCalendarEvent,
  SlackCelebration,
  CelebrationType,
  SlackHoliday,
  PresetPolicyId,
  HOW_MANY_TIMES_SHOW_FEATURE_BANNER,
} from "@organice/core/domain";
import {
  getAnniversaryDateThisYear,
  getBirthdayDateThisYear,
} from "@organice/core/domain/announcements";
import { collectMemberData } from "@organice/core/domain/data-completion";
import { getNode } from "@organice/core/domain/org-chart";
import { BlockAction, SlackViewAction } from "@slack/bolt";
import { HomeView, WebClient } from "@slack/web-api";
import {
  addDays,
  isWithinInterval,
  isSameDay,
  addMonths,
  isBefore,
} from "date-fns";
import {
  Actions,
  Button,
  HomeTab,
  Option,
  StaticSelect,
  Context,
  StaticSelectBuilder,
  ActionsBuilder,
  ConversationSelect,
  Paginator,
  BlockBuilder,
  setIfTruthy,
  OptionBuilder,
} from "slack-block-builder";

import CalendarEventCard from "../components/CalendarEventCard";
import CelebrationEventsSection from "../components/CelebrationEventsSection";
import HelpNavigation from "../components/HelpNavigation";
import ImageBanner from "../components/ImageBanner";
import { getHomeTabNavigation } from "../components/Navigation";
import RequestTimeOffModal from "../components/RequestTimeOffModal";
import { MyTimeOffsBalance } from "../components/TimeOffStats";
import {
  declareAction,
  loggerFromBoltContextAndBody,
} from "../components/_common";
import { getUIMemberById, isFeatureEnabled, PaginatorAction } from "../domain";
import {
  tryJsonParse,
  requirePageState,
  getPageSlice,
  isValidArray,
  getNewLineBlock,
  inferHomePageState,
  getSafePage,
  getDefaultCalendarHomePageState,
} from "../helpers";

import NoFeaturesPage from "./NoFeaturesPage";

const TIME_OFFS_PER_PAGE = 10;

export const renderRequests = declareAction(
  { action_id: /render_requests/, type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();
    const workspaceId = body.team?.id;
    const memberId = body.user.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);

    const actionValues = tryJsonParse<PaginatorAction>(action.action_id);

    assert(actionValues, "Expected action.action_id to be a valid JSON");

    const state = requireCalendarPageState(body);
    const page = actionValues.page;
    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, "Expected workspace to exist");

    state.paginatorPage = page;

    await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
  }
);

export const timeOffCalendarPageSelected = declareAction(
  { action_id: "time_off_calendar_page_selected", type: "block_actions" },
  async ({ ack, client, body, context, action }) => {
    await ack();

    assert(
      action.type === "static_select",
      "Expected action to come from a static select"
    );

    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const memberId = body.user.id;
    const selectedPage = action.selected_option.value;
    let logger = loggerFromBoltContextAndBody(context, body);
    const state = requireCalendarPageState(body);

    if (selectedPage === "my_calendar") {
      const workspace = await repository.getWorkspace(workspaceId);

      assert(workspace, `Workspace with ${workspaceId} not found`);
      logger = logger.withContext({ workspace });

      const member = workspace.members.find((x) => x.id === memberId);

      assert(member, `Member not found`);
      logger = logger.withContext({ member });

      const subordinates = getMemberSubordinates(workspace, memberId);

      logger.info("Calendar - 'My Calendar' selected", {
        role: member.isAdmin ? "Admin" : "User",
        isSomeonesManager: !!subordinates.length,
      });

      state.currentCalendarPage = "my_calendar";
      state.paginatorPage = 1;
      delete state.selectedUser;
      delete state.selectedEventType;

      await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
    }

    if (selectedPage === "incoming_requests") {
      const workspace = await repository.getWorkspace(workspaceId);

      assert(workspace, `Workspace with ${workspaceId} not found`);
      logger = logger.withContext({ workspace });

      const member = workspace.members.find((x) => x.id === memberId);

      assert(member, `Member not found`);
      logger = logger.withContext({ member });

      const subordinates = getMemberSubordinates(workspace, memberId);

      logger.info("Calendar - 'Incoming' selected", {
        role: member.isAdmin ? "Admin" : "User",
        isSomeonesManager: !!subordinates.length,
      });

      state.currentCalendarPage = "incoming_requests";
      state.paginatorPage = 1;
      delete state.selectedUser;
      delete state.selectedEventType;

      await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
    }

    if (selectedPage === "organization_calendar") {
      const workspace = await repository.getWorkspace(workspaceId);

      assert(workspace, `Workspace with ${workspaceId} not found`);

      const subordinates = getMemberSubordinates(workspace, memberId);
      const member = workspace.members.find((x) => x.id === memberId);

      assert(member, `Member not found`);

      logger.info("Calendar - 'Organization' selected", {
        role: member.isAdmin ? "Admin" : "User",
        isSomeonesManager: !!subordinates.length,
      });

      state.currentCalendarPage = "organization_calendar";
      state.paginatorPage = 1;
      delete state.selectedUser;
      delete state.selectedEventType;

      await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
    }
  }
);

export const requestTimeOffClicked = declareAction(
  { action_id: "request_time_off_clicked", type: "block_actions" },
  async ({ ack, body, client, context, action }) => {
    await ack();

    assert(action.type === "button", "Expected action to come from a button");

    const workspaceId = body.team?.id;
    const memberId = body.user.id;
    const triggerId = body.trigger_id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    let logger = loggerFromBoltContextAndBody(context, body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace not found`);
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);
    logger = logger.withContext({ member });

    logger.info("Calendar - Home tab: 'Request Time Off' clicked");

    let calendarHomePageState: CalendarHomePageState | undefined;

    try {
      calendarHomePageState = requireCalendarPageState(body);
    } catch {
      // Intentionally ignored
    }

    await openRequestTimeOffModal(
      slackAdapter,
      client,
      triggerId,
      workspace,
      member,
      calendarHomePageState
    );
  }
);

export const clearTimeOffFilters = declareAction(
  { action_id: "clear_time_off_filters", type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();

    assert(
      action.type === "button",
      "Expected action to come from a static select"
    );

    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const memberId = body.user.id;
    const state = requireCalendarPageState(body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace with ${workspaceId} not found`);

    state.paginatorPage = 1;
    delete state.selectedUser;

    await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
  }
);

export const timeOffUserSelect = declareAction(
  { action_id: "time_off_user_select", type: "block_actions" },
  async ({ ack, client, body, context, action }) => {
    await ack();

    assert(
      action.type === "conversations_select",
      "Expected action to come from a static select"
    );

    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const memberId = body.user.id;
    let logger = loggerFromBoltContextAndBody(context, body);
    const state = requireCalendarPageState(body);
    const selectedUserId = action.selected_conversation;

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace with ${workspaceId} not found`);
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);
    logger = logger.withContext({ member });

    const subordinates = getMemberSubordinates(workspace, memberId);

    logger.info(`Time Off - User selected (${state.currentCalendarPage})`, {
      role: member.isAdmin ? "Admin" : "User",
      isSomeonesManager: !!subordinates.length,
      tab: state.currentCalendarPage,
      selectedUser: selectedUserId,
    });

    state.selectedUser = selectedUserId;
    state.paginatorPage = 1;

    await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
  }
);

export const clearCalendarEventFilters = declareAction(
  { action_id: "clear_calendar_event_filters", type: "block_actions" },
  async ({ ack, client, context, body, action }) => {
    await ack();

    assert(
      action.type === "button",
      "Expected action to come from a static select"
    );

    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const memberId = body.user.id;
    const state = requireCalendarPageState(body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace with ${workspaceId} not found`);

    state.paginatorPage = 1;
    delete state.selectedEventType;

    await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
  }
);

export const calendarEventSelect = declareAction(
  { action_id: "calendar_event_select", type: "block_actions" },
  async ({ ack, client, body, context, action }) => {
    await ack();

    assert(
      action.type === "static_select",
      "Expected action to come from a static select"
    );

    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const memberId = body.user.id;
    const selectedEventType = action.selected_option.value;
    let logger = loggerFromBoltContextAndBody(context, body);
    const state = requireCalendarPageState(body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace with ${workspaceId} not found`);
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);
    logger = logger.withContext({ member });

    const subordinates = getMemberSubordinates(workspace, memberId);

    logger.info("Calendar - 'Event type' filter selected", {
      role: member.isAdmin ? "Admin" : "User",
      isSomeonesManager: !!subordinates.length,
    });

    if (
      selectedEventType === "time_off" ||
      selectedEventType === "holiday" ||
      selectedEventType === "celebration"
    ) {
      state.paginatorPage = 1;
      state.selectedEventType = selectedEventType;
      await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
    }
  }
);

export const timeOffStatusSelect = declareAction(
  { action_id: "time_off_status_select", type: "block_actions" },
  async ({ ack, client, body, context, action }) => {
    await ack();

    assert(
      action.type === "static_select",
      "Expected action to come from a static select"
    );

    const workspaceId = body.team?.id;

    assert(workspaceId, "Expected workspace id to exist");

    const repository = context.getRepository();
    const slackAdapter = context.getSlackAdapter(client);
    const memberId = body.user.id;
    let logger = loggerFromBoltContextAndBody(context, body);
    const state = requireCalendarPageState(body);

    const workspace = await repository.getWorkspace(workspaceId);

    assert(workspace, `Workspace with ${workspaceId} not found`);
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((x) => x.id === memberId);

    assert(member, `Member not found`);
    logger = logger.withContext({ member });

    const subordinates = getMemberSubordinates(workspace, memberId);

    logger.info(`Time Off - Status selected (${state.currentCalendarPage})`, {
      role: member.isAdmin ? "Admin" : "User",
      isSomeonesManager: !!subordinates.length,
      tab: state.currentCalendarPage,
    });

    state.paginatorPage = 1;

    await slackAdapter.renderCalendarHomeTab(workspace, memberId, state);
  }
);

const getCalendarNavigation = (
  state: CalendarHomePageState
): StaticSelectBuilder => {
  const options = [
    Option({ text: "My Calendar", value: "my_calendar" }),
    Option({ text: "Incoming Requests", value: "incoming_requests" }),
    Option({ text: "Org Calendar", value: "organization_calendar" }),
  ];

  const defaultOption = {
    my_calendar: options[0],
    incoming_requests: options[1],
    organization_calendar: options[2],
  };

  return StaticSelect()
    .actionId(timeOffCalendarPageSelected.actionId)
    .options(...options)
    .initialOption(defaultOption[state.currentCalendarPage]);
};

const getCalendarPageActions = (
  state: CalendarHomePageState,
  adminSiteUrl: string,
  isAdmin: boolean,
  features: ReadonlyDeep<SupportedFeature[]>
): ActionsBuilder[] => {
  const requestTimeOffButton = Button()
    .text(":hand: Request Time Off")
    .actionId(requestTimeOffClicked.actionId)
    .primary();

  const ReportsButton = Button()
    .text(":eyes: View Reports")
    .url(`${adminSiteUrl}/calendar/reports`);

  const actions = [
    Actions().elements(
      getCalendarNavigation(state),
      requestTimeOffButton,
      setIfTruthy(isAdmin, ReportsButton)
    ),
  ];

  if (
    state.currentCalendarPage === "incoming_requests" ||
    state.currentCalendarPage === "organization_calendar"
  ) {
    const userSelect = ConversationSelect()
      .placeholder("Select a user")
      .actionId(timeOffUserSelect.actionId)
      .filter("im")
      .excludeBotUsers(true)
      .excludeExternalSharedChannels(true)
      .initialConversation(
        typeof state.selectedUser === "string" ? state.selectedUser : undefined
      );

    const clearButton = Button()
      .text("Clear")
      .actionId(clearTimeOffFilters.actionId);

    actions.push(Actions().elements(userSelect, clearButton));
  }

  if (state.currentCalendarPage === "organization_calendar") {
    const options: Partial<
      Record<"time_off" | "holiday" | "celebration", OptionBuilder>
    > = {};

    if (isFeatureEnabled(features, SupportedFeature.TimeOffs)) {
      options.time_off = Option({ text: "Time Off", value: "time_off" });
      options.holiday = Option({ text: "Holiday", value: "holiday" });
    }

    if (isFeatureEnabled(features, SupportedFeature.Celebration)) {
      options.celebration = Option({
        text: "Celebration",
        value: "celebration",
      });
    }

    const eventSelect = StaticSelect()
      .placeholder("Select an event type")
      .actionId(calendarEventSelect.actionId)
      .options(...Object.values(options))
      .initialOption(
        state.selectedEventType && options[state.selectedEventType]
      );

    const clearButton = Button()
      .text("Clear")
      .actionId(clearCalendarEventFilters.actionId);

    actions.push(Actions().elements(eventSelect, clearButton));
  }

  return actions;
};

const CalendarPage = (
  props: CalendarHomePageProps,
  state: CalendarHomePageState,
  adminSiteUrl: string,
  context: GlobalContext
): HomeView => {
  if (!props.enabledFeatures.length) {
    return NoFeaturesPage({
      adminSiteUrl,
      isAdmin: props.currentMember.isAdmin,
      currentPage: HomePages.Calendar,
      enabledFeatures: props.enabledFeatures,
    });
  }

  let calendarBlocks: BlockBuilder[] = [
    ...getHomeTabNavigation(HomePages.Calendar, props.enabledFeatures),
    ...HelpNavigation(
      `${adminSiteUrl}/calendar/organization?workspace=${props.workspaceId}`
    ),
    getNewLineBlock(),
  ];

  calendarBlocks = calendarBlocks.concat(
    getCalendarPageActions(
      state,
      adminSiteUrl,
      props.currentMember.isAdmin,
      props.enabledFeatures
    )
  );

  if (
    props.currentMember.howManyTimesHomeTabVisited <=
    HOW_MANY_TIMES_SHOW_FEATURE_BANNER
  ) {
    calendarBlocks.unshift(ImageBanner(adminSiteUrl));
  }

  const homeTab = HomeTab({
    callbackId: "home-tab",
  }).blocks(calendarBlocks);

  if (state.currentCalendarPage === "my_calendar") {
    homeTab.blocks([
      ...CelebrationEventsSection({
        currentMember: props.currentMember,
        workspaceMembers: [...props.workspaceMembers],
      }),
      ...MyTimeOffsBalance(props.currentMember.id, context),
    ]);
  }

  const calendarEvents = [...props.events];
  const manager = props.memberData
    ? props.memberData[PresetPolicyId.MANAGER]
    : null;

  const currentPage = getSafePage(
    TIME_OFFS_PER_PAGE,
    state.paginatorPage,
    calendarEvents
  );

  homeTab.privateMetaData(
    JSON.stringify({
      ...state,
      paginatorPage: currentPage,
    })
  );

  const paginatorItems = getPageSlice(
    TIME_OFFS_PER_PAGE,
    currentPage,
    calendarEvents
  );

  const timeOffPaginator = Paginator({
    perPage: TIME_OFFS_PER_PAGE,
    items: paginatorItems,
    totalItems: calendarEvents.length,
    page: currentPage,
    actionId: ({ buttonId, page, offset }) =>
      JSON.stringify({
        action: "render_requests",
        buttonId,
        page,
        offset,
      }),
    blocksForEach: ({ item }) => [
      ...CalendarEventCard({
        request: item,
        timeOffTypes: props.timeOffTypes,
        isSubordinate:
          state.currentCalendarPage === "my_calendar"
            ? false
            : isMySubordinateTimeOff(
                item as ReadonlyDeep<TimeOffRequest>,
                props.memberSubordinates
              ),
        workspaceMembers: props.workspaceMembers,
        currentMember: props.currentMember,
        allTimeOffsRequests: props.allTimeOffsRequests,
        showTimeOffsSummary: state.currentCalendarPage === "incoming_requests",
        context,
      }),
    ],
  }).getBlocks();

  if (state.currentCalendarPage === "incoming_requests") {
    const showIsManagedBy = props.selectedUser
      ? calendarEvents.find(
          (r) =>
            !isMySubordinateTimeOff(
              r as ReadonlyDeep<TimeOffRequest>,
              props.memberSubordinates
            )
        )
      : null;

    const selectedUserName =
      props.selectedUser && props.selectedUser.realName?.trim() !== ""
        ? props.selectedUser.realName!
        : "This user";

    const subordinatesBlocks = isValidArray(calendarEvents)
      ? [
          showIsManagedBy && manager
            ? [
                Context().elements(
                  `${selectedUserName} is managed by <@${manager}>`
                ),
              ]
            : undefined,
          ...timeOffPaginator,
        ]
      : [Context().elements("There are no Time Offs yet")];

    homeTab.blocks(...subordinatesBlocks);
  } else {
    const subordinatesBlocks = isValidArray(calendarEvents)
      ? timeOffPaginator
      : [Context().elements("There are no events yet")];

    homeTab.blocks(subordinatesBlocks);
  }

  return homeTab.buildToObject();
};

export function requireCalendarPageState(
  body: BlockAction | SlackViewAction
): CalendarHomePageState {
  const metadata = requirePageState(body);

  assert(
    metadata.currentPage === HomePages.Calendar,
    `Expected to receive metadata for calendar page, instead got: "${metadata.currentPage}"`
  );

  return inferHomePageState(
    metadata,
    HomePages.Calendar,
    getDefaultCalendarHomePageState
  );
}

export async function openRequestTimeOffModal(
  slackAdapter: SlackAdapter,
  client: WebClient,
  triggerId: string,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  calendarHomePageState?: CalendarHomePageState
): Promise<void> {
  if (!workspace.billing.subscription.ok) {
    await slackAdapter.openTrialExpiredModal({
      triggerId,
      me: member,
      members: workspace.members,
      trialExpired: workspace.billing.subscription.type === "trial",
    });
  } else {
    const previousTimeOffRequest = workspace.timeOffs.requests
      .filter((r) => r.memberId === member.id)
      .sort((a, b) => b.createdAt.valueOf() - a.createdAt.valueOf())
      .at(0);
    const memberData = collectMemberData(workspace, member);
    const managerId = memberData[PresetPolicyId.MANAGER];
    const manager = workspace.members.find((m) => {
      return m.id === managerId;
    });

    await client.views.open({
      view: RequestTimeOffModal(
        {
          defaultApproversIds:
            previousTimeOffRequest?.approversIds ??
            (manager ? [manager.id] : []),
          memberId: member.id,
          context: { workspace },
        },
        {
          calendarHomePageState,
        }
      ),
      trigger_id: triggerId,
    });
  }
}

export function groupCalendarEvents(
  calendarEvents: ReadonlyDeep<SlackCalendarEvent>[],
  now: Date,
  currentMember?: ReadonlyDeep<Member>
): ReadonlyDeep<SlackAppHomeGroupedCalendarEvent[]> {
  const isTodayRequests = (
    event: ReadonlyDeep<SlackCalendarEvent>
  ): boolean => {
    if (isPastRequests(event)) {
      return false;
    }

    return isWithinInterval(now, {
      start: event.startDate as Date,
      end: event.endDate as Date,
    });
  };

  const isPendingRequests = (
    event: ReadonlyDeep<SlackCalendarEvent>
  ): boolean => {
    if (isPastRequests(event) || isTodayRequests(event) || !currentMember) {
      return false;
    }

    return (
      isTimeOffRequest(event) &&
      event.status === "PENDING" &&
      (currentMember.isAdmin ||
        event.approversIds.includes(currentMember.id) ||
        event.memberId === currentMember.id)
    );
  };

  const isUpcomingRequests = (
    event: ReadonlyDeep<SlackCalendarEvent>
  ): boolean => {
    if (
      isPastRequests(event) ||
      isTodayRequests(event) ||
      isPendingRequests(event)
    ) {
      return false;
    }

    const toDate = addMonths(now, 3);

    return (
      (event.startDate > now || event.endDate > now) &&
      isBefore(event.startDate as Date, toDate)
    );
  };

  const isPastRequests = (event: ReadonlyDeep<SlackCalendarEvent>): boolean => {
    return event.endDate < now;
  };

  const sortByEventOrder = (
    a: ReadonlyDeep<SlackCalendarEvent>,
    b: ReadonlyDeep<SlackCalendarEvent>
  ): number => {
    const getPriority = (event: ReadonlyDeep<SlackCalendarEvent>): number => {
      if (isCelebrationEvent(event)) return event.type === "BIRTHDAY" ? 1 : 2;

      if (isTimeOffRequest(event)) return 3;

      if (isHolidayEvent(event)) return 4;

      return 5;
    };

    return getPriority(a) - getPriority(b);
  };

  const sortByDate = (
    a: ReadonlyDeep<SlackCalendarEvent>,
    b: ReadonlyDeep<SlackCalendarEvent>
  ): number => {
    if (a.startDate > now || a.endDate > now) {
      return a.startDate.valueOf() - b.startDate.valueOf();
    }

    return b.startDate.valueOf() - a.startDate.valueOf();
  };

  const groupByDay = (
    acc: ReadonlyDeep<SlackCalendarEvent>[][],
    cur: ReadonlyDeep<SlackCalendarEvent>
  ): ReadonlyDeep<SlackCalendarEvent>[][] => {
    const dayGroup = acc.find(
      (group) =>
        !isWithinInterval(now, {
          start: group[0].startDate as Date,
          end: group[0].endDate as Date,
        }) && isSameDay(group[0].startDate as Date, cur.startDate as Date)
    );

    if (dayGroup) {
      dayGroup.push(cur);
    } else {
      acc.push([cur]);
    }

    return acc;
  };

  const applyGroupTitle = (
    event: ReadonlyDeep<SlackCalendarEvent>,
    index: number,
    groupTitle: ReadonlyDeep<"Past" | "Today" | "Pending" | "Upcoming" | null>
  ): ReadonlyDeep<SlackAppHomeGroupedCalendarEvent> => ({
    ...event,
    groupTitle: index === 0 ? groupTitle : null,
  });

  const todayRequests: ReadonlyDeep<SlackAppHomeGroupedCalendarEvent>[] =
    calendarEvents
      .filter(isTodayRequests)
      .sort(sortByEventOrder)
      .map((e, i) => applyGroupTitle(e, i, "Today"));

  const pendingRequests: ReadonlyDeep<SlackAppHomeGroupedCalendarEvent>[] =
    calendarEvents
      .filter(isPendingRequests)
      .sort(sortByDate)
      .map((e, i) => applyGroupTitle(e, i, "Pending"));

  const upcomingRequests: ReadonlyDeep<SlackAppHomeGroupedCalendarEvent>[] =
    calendarEvents
      .filter(isUpcomingRequests)
      .sort(sortByDate)
      .reduce(groupByDay, [])
      .flatMap((group) => group.sort(sortByEventOrder))
      .map((e, i) => applyGroupTitle(e, i, "Upcoming"));

  const pastRequests: ReadonlyDeep<SlackAppHomeGroupedCalendarEvent>[] =
    calendarEvents
      .filter(isPastRequests)
      .sort(sortByDate)
      .reduce(groupByDay, [])
      .flatMap((group) => group.sort(sortByEventOrder))
      .map((e, i) => applyGroupTitle(e, i, "Past"));

  const mergedRequests = [
    ...todayRequests,
    ...pendingRequests,
    ...upcomingRequests,
    ...pastRequests,
  ];

  return mergedRequests;
}

function getTimeOffEvents(
  timeOffs: readonly ReadonlyDeep<TimeOffRequest>[]
): TimeOffRequest[] {
  return timeOffs.map((r) => ({
    ...r,
  })) as TimeOffRequest[];
}

export function getCelebrationEvents(
  members: readonly ReadonlyDeep<Member>[],
  isAdmin = false
): SlackCelebration[] {
  const now = new Date();

  return members.flatMap((member) => {
    const events: SlackCelebration[] = [];
    const birthday = getBirthdayDateThisYear(
      member,
      now.getFullYear(),
      isAdmin
    );
    const anniversary = getAnniversaryDateThisYear(member, now.getFullYear());

    if (birthday) {
      events.push({
        memberId: member.id,
        startDate: birthday,
        endDate: addDays(birthday, 1),
        type: CelebrationType.Birthday,
      });
    }

    if (anniversary) {
      events.push({
        memberId: member.id,
        startDate: anniversary,
        endDate: addDays(anniversary, 1),
        type: CelebrationType.Anniversary,
      });
    }

    return events;
  });
}

export function getCalendarEventsForCurrentCalendarPage(
  workspace: ReadonlyDeep<Workspace>,
  currentMember: ReadonlyDeep<Member>,
  state: CalendarHomePageState,
  now: Date
): ReadonlyDeep<SlackAppHomeGroupedCalendarEvent[]> {
  let events: ReadonlyDeep<SlackCalendarEvent>[];

  switch (state.currentCalendarPage) {
    case "my_calendar": {
      const timeOffEvents = getTimeOffEvents(
        workspace.timeOffs.requests
      ).filter((r) => r.memberId === currentMember.id);

      const holidayEvents = workspace.timeOffs.holidayCalendars
        .filter(
          (holidayCalendar) =>
            currentMember.country != null &&
            holidayCalendar.country === currentMember.country
        )
        .flatMap((calendar) =>
          calendar.holidays
            .filter(
              (h) =>
                isWithinInterval(now, {
                  start: h.startDate as Date,
                  end: h.endDate as Date,
                }) || h.startDate >= now
            )
            .map((h) => ({
              ...h,
              country: calendar.country,
            }))
        );

      events = [...timeOffEvents, ...holidayEvents].sort(
        (a, b) => a.startDate.valueOf() - b.startDate.valueOf()
      );
      break;
    }
    case "incoming_requests": {
      const position = getNode(
        workspace.orgTree.rootNode,
        (node): node is Position =>
          node.type === "position" && node.memberId === currentMember.id
      );
      const subordinates = position?.subordinates ?? [];

      events = getTimeOffEvents(workspace.timeOffs.requests).filter(
        (r) =>
          r.status === "PENDING" &&
          (r.approversIds.includes(currentMember.id) ||
            isMySubordinateTimeOff(r, subordinates))
      );
      break;
    }
    case "organization_calendar":
    default: {
      const timeOffEvents = getTimeOffEvents(workspace.timeOffs.requests);

      events = currentMember.isAdmin
        ? [...timeOffEvents]
        : timeOffEvents.filter(
            (timeOff) =>
              timeOff.status === "APPROVED" ||
              timeOff.memberId === currentMember.id ||
              timeOff.approversIds.includes(currentMember.id)
          );

      const holidays = workspace.timeOffs.holidayCalendars.flatMap((calendar) =>
        calendar.holidays
          .filter(
            (h) =>
              isWithinInterval(now, {
                start: h.startDate as Date,
                end: h.endDate as Date,
              }) || h.startDate >= now
          )
          .map((h) => ({
            ...h,
            country: calendar.country,
          }))
      );

      const celebrationEvent = getCelebrationEvents(
        workspace.members.filter((m) => m.id !== currentMember.id),
        currentMember.isAdmin
      );

      events = [...events, ...holidays, ...celebrationEvent];

      break;
    }
  }

  if (state.selectedUser) {
    const selectedUser = workspace.members.find(
      (m) => m.id === state.selectedUser
    );

    if (selectedUser) {
      events = events.filter(
        (e) =>
          (isTimeOffRequest(e) && e.memberId === state.selectedUser) ||
          (isHolidayEvent(e) && e.country === selectedUser.country) ||
          (isCelebrationEvent(e) && e.memberId === state.selectedUser)
      );
    }
  }

  events = events.filter((event) => {
    if (state.selectedEventType === "time_off") {
      return isTimeOffRequest(event);
    }

    if (state.selectedEventType === "holiday") {
      return isHolidayEvent(event);
    }

    if (state.selectedEventType === "celebration") {
      return isCelebrationEvent(event);
    }

    return true;
  });

  if (state.currentCalendarPage === "incoming_requests") {
    return events.map((e) => ({ ...e, groupTitle: null }));
  }

  return groupCalendarEvents(events, now, currentMember);
}

function isMySubordinateTimeOff(
  timeOff: ReadonlyDeep<TimeOffRequest>,
  subordinates: ReadonlyDeep<(Department | Position)[]>
): boolean {
  return subordinates.some(
    (sub) => sub.type === "position" && timeOff.memberId === sub.memberId
  );
}

export function getNodeManager(
  workspace: ReadonlyDeep<Workspace>,
  element: ReadonlyDeep<Department | Position | RootNode>
): UIMember | null {
  if (element.type === "root" || !element.parentId) {
    return null;
  }

  const parentNode = getNode(
    workspace.orgTree.rootNode,
    (n): n is RootNode | Position | Department => n.id === element.parentId
  );

  if (!parentNode) {
    return null;
  }

  if (parentNode.type === "position" && parentNode.memberId) {
    return {
      ...getUIMemberById(workspace, parentNode.memberId),
      position: parentNode,
    } as UIMember;
  }

  return getNodeManager(workspace, parentNode);
}

function getMemberSubordinates(
  workspace: ReadonlyDeep<Workspace>,
  memberId: string
): ReadonlyDeep<(Department | Position)[]> {
  const memberPosition = getNode(
    workspace.orgTree.rootNode,
    (node): node is Position =>
      node.type === "position" && node.memberId === memberId
  );

  return memberPosition?.subordinates ?? [];
}

function isTimeOffRequest(
  event: ReadonlyDeep<SlackCalendarEvent>
): event is TimeOffRequest {
  return "status" in event;
}

function isHolidayEvent(
  event: ReadonlyDeep<SlackCalendarEvent>
): event is SlackHoliday {
  return "country" in event;
}

function isCelebrationEvent(
  event: ReadonlyDeep<SlackCalendarEvent>
): event is SlackCelebration {
  return (
    (event as SlackCelebration).type === "BIRTHDAY" ||
    (event as SlackCelebration).type === "ANNIVERSARY"
  );
}

export default CalendarPage;
