import assert from "node:assert";

import type { BankAccountFormData } from "@/app/(dashboard)/compliance/components/BankAccountForm";
import { type ContactFormData } from "@/app/(dashboard)/compliance/components/ContactForm";
import { type UltimateBeneficialOwnerData } from "@/app/(dashboard)/compliance/components/UltimateBeneficialOwnerForm";
import type {
  CompliancePageData,
  GetMerchantComplianceRequirementsResponse,
} from "@/app/(dashboard)/compliance/page";
import { type FileSpec } from "@/app/(dashboard)/compliance/types";
import {
  OppMerchantComplianceStatus,
  OppMerchantStatus,
} from "@/domain/organization";

import OppClient, {
  GetObjectsResponse,
  PostMerchantResponse,
} from "./OppClient";

export type CreateMerchantParams = {
  email: string;
  chamberOfCommerceNumber: string;
  phoneNumber: string;
  entityType: string;
  city: string;
  zipCode: string;
  addressLine1: string;
  addressLine2?: string;
};

export type CreateMerchantResponse = {
  merchantUid: string;
  merchantStatus: OppMerchantStatus;
  merchantComplianceStatus: OppMerchantComplianceStatus;
  phoneNumber: string;
  compliance: {
    status: "unverified" | "pending" | "verified";
    overview_url: string;
    requirements: {
      type: string;
      status: "unverified" | "pending" | "verified";
      objectType: string | null;
      objectUid: string | null;
      objectUrl: string | null;
      objectRedirectUrl: string;
    }[];
  };
};

export type CreateBankAccountParams = {
  merchantUid: string;
  accountName: string;
  accountIban: string;
  accountBic: string;
};

export type CreateBankAccountResponse = {
  uid: string;
  status: "new" | "pending" | "approved" | "disapproved";
};

export type CreateUBOParams = {
  merchantUid: string;
  namesFirst: string;
  namePrefix?: string;
  nameLast: string;
  dateOfBirth: string;
  countryOfResidence: string;
  isDecisionMaker?: boolean;
  decisionMakerPercentage?: number;
  isShareholder?: boolean;
  shareholderPercentage?: number;
  isPep?: boolean;
};

export type UBOResponse = {
  uid: string;
  status: "new" | "pending" | "verified" | "unverified";
  namesFirst: string;
  namePrefix: string;
  nameLast: string;
  dateOfBirth: string;
  countryOfResidence: string;
  isDecisionMaker: boolean;
  decisionMakerPercentage: number;
  isShareholder: boolean;
  shareholderPercentage: number;
  isPep: boolean;
};

export type UpdateUBOParams = {
  merchantUid: string;
  uboUid: string;
} & Partial<CreateUBOParams>;

export type CreateContactParams = {
  merchantUid: string;
  title: "mr" | "mrs";
  nameInitials: string;
  nameNamesGiven: string;
  nameFirst: string;
  nameLast: string;
  birthdate: string;
  emailaddresses: Array<{
    emailaddress: string;
  }>;
  phonenumbers: Array<{
    phonenumber: string;
  }>;
};

export type UpdateContactParams = {
  merchantUid: string;
  contactUid: string;
  title?: "mr" | "mrs";
  nameInitials?: string;
  nameNamesGiven?: string;
  nameFirst?: string;
  nameLast?: string;
  birthdate?: string;
  emailaddresses?: Array<{
    emailaddress: string;
  }>;
  phonenumbers?: Array<{
    phonenumber: string;
  }>;
};

type LegalEntity = {
  code: string;
  name: string;
};

type UploadLink = {
  headers: Record<string, string>;
  url: string;
};

export type CreateContactResponse = {
  uid: string;
  status: "pending" | "verified" | "unverified";
};

export default class OppQueryHandler {
  private readonly oppClient: OppClient;
  private readonly notifyUrl: string;
  private readonly returnUrl: string;

  constructor(options: {
    oppClient: OppClient;
    notifyUrl: string;
    returnUrl: string;
  }) {
    this.oppClient = options.oppClient;
    this.notifyUrl = options.notifyUrl;
    this.returnUrl = options.returnUrl;
  }

  async createMerchant(
    params: CreateMerchantParams
  ): Promise<CreateMerchantResponse> {
    const merchant = await this.oppClient.createMerchant({
      coc_nr: params.chamberOfCommerceNumber,
      country: "GBR",
      emailaddress: params.email,
      phone: params.phoneNumber,
      notify_url: this.notifyUrl,
      legal_entity: params.entityType,
      addresses: [
        {
          country: "GBR",
          zipcode: params.zipCode,
          city: params.city,
          address_line_1: params.addressLine1,
          address_line_2: params.addressLine2,
        },
      ],
      type: "business",
    });

    return {
      merchantUid: merchant.uid,
      merchantStatus:
        oppMerchantStatusToDomain[merchant.status] ?? OppMerchantStatus.NEW,
      merchantComplianceStatus:
        oppMerchantComplianceStatusToDomain[merchant.compliance.status] ??
        OppMerchantComplianceStatus.UNVERIFIED,
      phoneNumber: merchant.phone,
      compliance: {
        status: merchant.compliance.status,
        overview_url: merchant.compliance.overview_url,
        requirements: merchant.compliance.requirements.map((requirement) => ({
          type: requirement.type,
          status: requirement.status,
          objectType: requirement.object_type,
          objectUid: requirement.object_uid,
          objectUrl: requirement.object_url,
          objectRedirectUrl: requirement.object_redirect_url,
        })),
      },
    };
  }

  async createBankAccount(
    params: CreateBankAccountParams
  ): Promise<CreateBankAccountResponse> {
    const bankAccount = await this.oppClient.createBankAccount(
      params.merchantUid,
      {
        account_name: params.accountName,
        account_bic: params.accountBic,
        account_iban: params.accountIban,
        notify_url: this.notifyUrl,
        return_url: this.returnUrl,
      }
    );

    return {
      uid: bankAccount.uid,
      status: bankAccount.status,
    };
  }

  private async _getUBOsFormData(
    merchantUid: string
  ): Promise<UltimateBeneficialOwnerData> {
    const ubos = await this.oppClient.getUBOs(merchantUid);

    return {
      ubos: ubos.data.map((ubo) => ({
        uid: ubo.uid,
        status: ubo.status,
        namesFirst: ubo.names_first,
        namePrefix: ubo.name_prefix,
        nameLast: ubo.name_last,
        dateOfBirth: ubo.date_of_birth,
        countryOfResidence: ubo.country_of_residence,
        isDecisionMaker: ubo.is_decision_maker,
        decisionMakerPercentage: ubo.decision_maker_percentage,
        isShareholder: ubo.is_shareholder,
        shareholderPercentage: ubo.shareholder_percentage,
        isPep: ubo.is_pep,
      })),
    };
  }

  async createUBO(params: CreateUBOParams): Promise<UBOResponse> {
    const ubo = await this.oppClient.createUBO(params.merchantUid, {
      names_first: params.namesFirst,
      name_prefix: params.namePrefix,
      name_last: params.nameLast,
      date_of_birth: params.dateOfBirth,
      country_of_residence: params.countryOfResidence,
      is_decision_maker: params.isDecisionMaker,
      decision_maker_percentage: params.decisionMakerPercentage,
      is_shareholder: params.isShareholder,
      shareholder_percentage: params.shareholderPercentage,
      is_pep: params.isPep,
    });

    return {
      uid: ubo.uid,
      status: ubo.status,
      namesFirst: ubo.names_first,
      namePrefix: ubo.name_prefix,
      nameLast: ubo.name_last,
      dateOfBirth: ubo.date_of_birth,
      countryOfResidence: ubo.country_of_residence,
      isDecisionMaker: ubo.is_decision_maker,
      decisionMakerPercentage: ubo.decision_maker_percentage,
      isShareholder: ubo.is_shareholder,
      shareholderPercentage: ubo.shareholder_percentage,
      isPep: ubo.is_pep,
    };
  }

  async updateUBO(params: UpdateUBOParams): Promise<UBOResponse> {
    const ubo = await this.oppClient.updateUBO(
      params.merchantUid,
      params.uboUid,
      {
        names_first: params.namesFirst,
        name_prefix: params.namePrefix,
        name_last: params.nameLast,
        date_of_birth: params.dateOfBirth,
        country_of_residence: params.countryOfResidence,
        is_decision_maker: params.isDecisionMaker,
        decision_maker_percentage: params.decisionMakerPercentage,
        is_shareholder: params.isShareholder,
        shareholder_percentage: params.shareholderPercentage,
        is_pep: params.isPep,
      }
    );

    return {
      uid: ubo.uid,
      status: ubo.status,
      namesFirst: ubo.names_first,
      namePrefix: ubo.name_prefix,
      nameLast: ubo.name_last,
      dateOfBirth: ubo.date_of_birth,
      countryOfResidence: ubo.country_of_residence,
      isDecisionMaker: ubo.is_decision_maker,
      decisionMakerPercentage: ubo.decision_maker_percentage,
      isShareholder: ubo.is_shareholder,
      shareholderPercentage: ubo.shareholder_percentage,
      isPep: ubo.is_pep,
    };
  }

  async createContact(
    params: CreateContactParams
  ): Promise<CreateContactResponse> {
    const contact = await this.oppClient.createContact(params.merchantUid, {
      type: "representative",
      title: params.title,
      name_initials: params.nameInitials,
      name_names_given: params.nameNamesGiven,
      name_first: params.nameFirst,
      name_last: params.nameLast,
      birthdate: params.birthdate,
      emailaddresses: params.emailaddresses,
      phonenumbers: params.phonenumbers,
    });

    return {
      uid: contact.uid,
      status: contact.status,
    };
  }

  async updateContact(
    params: UpdateContactParams
  ): Promise<CreateContactResponse> {
    const contact = await this.oppClient.updateContact(
      params.merchantUid,
      params.contactUid,
      {
        title: params.title,
        name_initials: params.nameInitials,
        name_names_given: params.nameNamesGiven,
        name_first: params.nameFirst,
        name_last: params.nameLast,
        birthdate: params.birthdate,
        emailaddresses: params.emailaddresses,
        phonenumbers: params.phonenumbers,
      }
    );

    return {
      uid: contact.uid,
      status: contact.status,
    };
  }

  async getMerchantStatus(merchantUid: string): Promise<OppMerchantStatus> {
    const rawMerchant = await this.oppClient.getMerchantByUid(merchantUid);

    return (
      oppMerchantStatusToDomain[rawMerchant.status] ?? OppMerchantStatus.NEW
    );
  }

  async getMerchantComplianceStatus(
    merchantUid: string
  ): Promise<OppMerchantComplianceStatus> {
    const merchant = await this.oppClient.getMerchantByUid(merchantUid);

    return (
      oppMerchantComplianceStatusToDomain[merchant.compliance.status] ??
      OppMerchantComplianceStatus.UNVERIFIED
    );
  }

  private _getMerchantComplianceRequirements(
    merchant: PostMerchantResponse
  ): GetMerchantComplianceRequirementsResponse {
    function chooseWorseComplianceRequirementStatus(
      a: PostMerchantResponse["compliance"]["requirements"][number]["status"],
      b: PostMerchantResponse["compliance"]["requirements"][number]["status"]
    ): PostMerchantResponse["compliance"]["requirements"][number]["status"] {
      return a === "unverified" || b === "unverified"
        ? "unverified"
        : // eslint-disable-next-line unicorn/no-nested-ternary
          a === "pending" || b === "pending"
          ? "pending"
          : "verified";
    }

    function collectWorseComplianceRequirementStatus(
      types: PostMerchantResponse["compliance"]["requirements"][number]["type"][]
    ): PostMerchantResponse["compliance"]["requirements"][number]["status"] {
      return merchant.compliance.requirements
        .filter((requirement) => types.includes(requirement.type))
        .map((requirement) => requirement.status)
        .reduce(chooseWorseComplianceRequirementStatus, "verified");
    }

    return {
      bankAccount: collectWorseComplianceRequirementStatus([
        "bank_account.required",
        "bank_account.verification.required",
      ]),
      chamberOfCommerceExtract: collectWorseComplianceRequirementStatus([
        "coc_extract.required",
      ]),
      contact: collectWorseComplianceRequirementStatus([
        "contact.verification.required",
        "contact.phonenumber.required",
        "contact.phonenumber.verification.required",
      ]),
      organizationStructure: collectWorseComplianceRequirementStatus([
        "organization_structure.required",
      ]),
      ultimateBenificialOwner: collectWorseComplianceRequirementStatus([
        "ubo.required",
        "ubo.verification.required",
      ]),
      other: {
        status: collectWorseComplianceRequirementStatus([
          "source_of_funds.required",
          "right_to_work.required",
        ]),
        url: merchant.compliance.overview_url,
      },
    };
  }

  async getCompliacePageData(merchantUid: string): Promise<CompliancePageData> {
    // requirements
    const requirementsPromise = this.oppClient
      .getMerchantByUid(merchantUid)
      .then((merchant) => this._getMerchantComplianceRequirements(merchant));
    // bankAccount
    const objectsPromise = this.oppClient.getObjects();
    const bankAccoutDataPromise = Promise.all([
      objectsPromise,
      this._getBankAccountFormData(merchantUid),
    ]).then(([objects, account]) => {
      const bankStatementFrontFileSpec = this._findFileSpecByPurpose(
        objects,
        "bank_account_bank_statement"
      );
      const bankStatementBackFileSpec = this._findFileSpecByPurpose(
        objects,
        "bank_account_bank_statement_backside"
      );

      assert(
        bankStatementFrontFileSpec,
        "Bank statement front file spec not found"
      );
      assert(
        bankStatementBackFileSpec,
        "Bank statement back file spec not found"
      );

      return {
        account,
        bankStatementFrontFileSpec: {
          allowedMimeTypes: bankStatementFrontFileSpec.allowed_mime_types,
          maxFileSizeInBytes: this._parseFileSize(
            bankStatementFrontFileSpec.allowed_upload_size
          ),
        },
        bankStatementBackFileSpec: {
          allowedMimeTypes: bankStatementBackFileSpec.allowed_mime_types,
          maxFileSizeInBytes: this._parseFileSize(
            bankStatementBackFileSpec.allowed_upload_size
          ),
        },
      };
    });
    // chamberOfCommerceExtract
    const chamberOfCommerceExtractPromise = objectsPromise.then((objects) =>
      this._getChamberOfCommerceExtractFormData(objects)
    );
    // contacts
    const contactsDataPromise = Promise.all([
      objectsPromise,
      this._getContactFormData(merchantUid),
    ]).then(([objects, contacts]) => {
      const passportFileSpec = this._findFileSpecByPurpose(
        objects,
        "representative_passport"
      );

      assert(passportFileSpec, "Passport file spec not found");
      const driversLicenseFrontFileSpec = this._findFileSpecByPurpose(
        objects,
        "representative_drivers_license_front"
      );
      assert(
        driversLicenseFrontFileSpec,
        "Driver license front file spec not found"
      );
      const driversLicenseBackFileSpec = this._findFileSpecByPurpose(
        objects,
        "representative_drivers_license_backside"
      );
      assert(
        driversLicenseBackFileSpec,
        "Driver license back file spec not found"
      );

      return {
        contacts,
        passportFileSpec: {
          allowedMimeTypes: passportFileSpec.allowed_mime_types,
          maxFileSizeInBytes: this._parseFileSize(
            passportFileSpec.allowed_upload_size
          ),
        },
        driversLicenseFrontFileSpec: {
          allowedMimeTypes: driversLicenseFrontFileSpec.allowed_mime_types,
          maxFileSizeInBytes: this._parseFileSize(
            driversLicenseFrontFileSpec.allowed_upload_size
          ),
        },
        driversLicenseBackFileSpec: {
          allowedMimeTypes: driversLicenseBackFileSpec.allowed_mime_types,
          maxFileSizeInBytes: this._parseFileSize(
            driversLicenseBackFileSpec.allowed_upload_size
          ),
        },
      };
    });
    // organizationStructure
    const organizationStructurePromise = objectsPromise.then((objects) =>
      this._getOrganizationStructureFormData(objects)
    );
    // ultimateBeneficialOwners
    const ultimateBeneficialOwnersPromise = this._getUBOsFormData(merchantUid);

    const [
      requirements,
      bankAccount,
      chamberOfCommerceExtract,
      contact,
      organizationStructure,
      ultimateBeneficialOwners,
    ] = await Promise.all([
      requirementsPromise,
      bankAccoutDataPromise,
      chamberOfCommerceExtractPromise,
      contactsDataPromise,
      organizationStructurePromise,
      ultimateBeneficialOwnersPromise,
    ]);

    return {
      requirements,
      bankAccount,
      chamberOfCommerceExtract,
      contact,
      organizationStructure,
      ultimateBeneficialOwners,
    };
  }

  async getLegalEntities(): Promise<LegalEntity[]> {
    const legalEntities = await this.oppClient.getLegalEntities();

    return legalEntities.data.map((legalEntity) => ({
      code: legalEntity.legal_entity_code,
      name: legalEntity.legal_entity_name,
    }));
  }

  private async _getBankAccountFormData(
    merchantUid: string
  ): Promise<BankAccountFormData["account"]> {
    const bankAccounts = await this.oppClient.getBankAccounts(merchantUid);
    const bankAccount = bankAccounts.data[0];

    if (!bankAccount) {
      return {
        uid: null,
        status: null,
        accountName: null,
        accountIban: null,
        accountBic: null,
      };
    }

    return {
      uid: bankAccount.uid,
      status: bankAccount.status,
      accountName: bankAccount.account.account_name,
      accountIban: bankAccount.account.account_iban,
      accountBic: bankAccount.account.account_bic,
    };
  }

  private _getChamberOfCommerceExtractFormData(objects: GetObjectsResponse): {
    fileSpec: FileSpec;
  } {
    const fileSpec = this._findFileSpecByPurpose(objects, "coc_extract");
    assert(fileSpec, "Chamber of commerce extract file spec not found");

    return {
      fileSpec: {
        allowedMimeTypes: fileSpec.allowed_mime_types,
        maxFileSizeInBytes: this._parseFileSize(fileSpec.allowed_upload_size),
      },
    };
  }

  private async _getContactFormData(
    merchantUid: string
  ): Promise<ContactFormData["contacts"]> {
    const contactsResponse = await this.oppClient.getContacts(merchantUid);

    return contactsResponse.data.map((contact) => ({
      uid: contact.uid,
      status: contact.status,
      title: contact.title,
      name: {
        initials: contact.name_initials,
        namesGiven: contact.names_given,
        first: contact.name_first,
        last: contact.name_last,
      },
      dateOfBirth: contact.birthdate,
      emailaddress: contact.emailaddresses[0].emailaddress,
      phonenumber: contact.phonenumbers[0].phonenumber,
      isPep: contact.is_pep,
    }));
  }

  private _getOrganizationStructureFormData(objects: GetObjectsResponse): {
    fileSpec: FileSpec;
  } {
    const fileSpec = this._findFileSpecByPurpose(
      objects,
      "organization_structure"
    );

    assert(fileSpec, "Organization structure file spec not found");

    return {
      fileSpec: {
        allowedMimeTypes: fileSpec.allowed_mime_types,
        maxFileSizeInBytes: this._parseFileSize(fileSpec.allowed_upload_size),
      },
    };
  }

  async getUploadLinksForContactForm(
    merchantUid: string,
    contactUid: string
  ): Promise<{
    passportUploadLink: UploadLink;
    driversLicenseFrontUploadLink: UploadLink;
    driversLicenseBackUploadLink: UploadLink;
  }> {
    const [
      passportUploadLink,
      driversLicenseFrontUploadLink,
      driversLicenseBackUploadLink,
    ] = await Promise.all([
      this._createUploadLink({
        merchantUid,
        objectUid: contactUid,
        purpose: "representative_passport",
      }),
      this._createUploadLink({
        merchantUid,
        objectUid: contactUid,
        purpose: "representative_drivers_license_front",
      }),
      this._createUploadLink({
        merchantUid,
        objectUid: contactUid,
        purpose: "representative_drivers_license_backside",
      }),
    ]);

    return {
      passportUploadLink,
      driversLicenseFrontUploadLink,
      driversLicenseBackUploadLink,
    };
  }

  async getUploadLinkForCoC(merchantUid: string): Promise<UploadLink> {
    return await this._createUploadLink({
      merchantUid,
      objectUid: merchantUid,
      purpose: "coc_extract",
    });
  }

  async getUploadLinkForOrganizationStructure(
    merchantUid: string
  ): Promise<UploadLink> {
    return await this._createUploadLink({
      merchantUid,
      objectUid: merchantUid,
      purpose: "organization_structure",
    });
  }

  async getUploadLinksForBankAccount(
    merchantUid: string,
    bankAccountUid: string
  ): Promise<{
    bankStatementFrontUploadLink: UploadLink;
    bankStatementBackUploadLink: UploadLink;
  }> {
    const [bankStatementFrontUploadLink, bankStatementBackUploadLink] =
      await Promise.all([
        this._createUploadLink({
          merchantUid,
          objectUid: bankAccountUid,
          purpose: "bank_account_bank_statement",
        }),
        this._createUploadLink({
          merchantUid,
          objectUid: bankAccountUid,
          purpose: "bank_account_bank_statement_backside",
        }),
      ]);

    return {
      bankStatementFrontUploadLink,
      bankStatementBackUploadLink,
    };
  }

  private _findFileSpecByPurpose(
    allowedObjects: GetObjectsResponse,
    purpose: string
  ):
    | GetObjectsResponse["data"][number]["file_groups"][number]["files"][number]
    | null {
    for (const objectType of allowedObjects.data) {
      for (const fileGroup of objectType.file_groups) {
        for (const file of fileGroup.files) {
          if (file.purpose === purpose) {
            return file;
          }
        }
      }
    }
    return null;
  }

  private async _createUploadLink({
    merchantUid,
    objectUid,
    purpose,
  }: {
    merchantUid: string;
    objectUid: string;
    purpose: string;
  }): Promise<UploadLink> {
    const uploadLinkResult = await this.oppClient.createUpload({
      merchant_uid: merchantUid,
      purpose,
      object_uid: objectUid,
    });

    return {
      headers: {
        "x-opp-files-token": uploadLinkResult.token,
      },
      url: uploadLinkResult.url,
    };
  }

  private _parseFileSize(sizeString: string): number {
    // Parse sizes like "7M" to bytes
    const match = sizeString.match(/^(\d+)([KMG])$/);
    if (!match) {
      throw new Error(`Invalid file size format: ${sizeString}`);
    }

    const value = Number.parseInt(match[1], 10);
    const unit = match[2];

    const multipliers: Record<string, number> = {
      K: 1024,
      M: 1024 * 1024,
      G: 1024 * 1024 * 1024,
    };

    return value * multipliers[unit];
  }
}

const oppMerchantStatusToDomain: Record<
  PostMerchantResponse["status"],
  OppMerchantStatus
> = {
  new: OppMerchantStatus.NEW,
  pending: OppMerchantStatus.PENDING,
  live: OppMerchantStatus.LIVE,
  suspended: OppMerchantStatus.SUSPENDED,
  terminated: OppMerchantStatus.TERMINATED,
  blocked: OppMerchantStatus.BLOCKED,
  deleted: OppMerchantStatus.DELETED,
};

const oppMerchantComplianceStatusToDomain: Record<
  PostMerchantResponse["compliance"]["status"],
  OppMerchantComplianceStatus
> = {
  unverified: OppMerchantComplianceStatus.UNVERIFIED,
  pending: OppMerchantComplianceStatus.PENDING,
  verified: OppMerchantComplianceStatus.VERIFIED,
};
