export default class OppClient {
  private readonly apiKey: string;
  private readonly apiUrl: string;
  private readonly filesApiUrl: string;

  constructor(options: {
    apiKey: string;
    apiUrl: string;
    filesApiUrl: string;
  }) {
    this.apiKey = options.apiKey;
    this.apiUrl = options.apiUrl;
    this.filesApiUrl = options.filesApiUrl;
  }

  async createMerchant(body: PostMerchantBody): Promise<PostMerchantResponse> {
    const bodyParams = new URLSearchParams();
    bodyParams.append("coc_nr", body.coc_nr);
    bodyParams.append("country", body.country);
    bodyParams.append("emailaddress", body.emailaddress);
    bodyParams.append("phone", body.phone);
    bodyParams.append("notify_url", body.notify_url);
    bodyParams.append("type", body.type);
    bodyParams.append("legal_entity", body.legal_entity);
    bodyParams.append("addresses[0][city]", body.addresses[0].city);
    bodyParams.append("addresses[0][country]", body.addresses[0].country);
    bodyParams.append("addresses[0][zipcode]", body.addresses[0].zipcode);
    bodyParams.append(
      "addresses[0][address_line_1]",
      body.addresses[0].address_line_1
    );

    bodyParams.append(
      "addresses[0][address_line_2]",
      body.addresses[0].address_line_2 ?? ""
    );

    const response = await fetch(`${this.apiUrl}/merchants`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
      },
      body: bodyParams,
    });

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async createBankAccount(
    merchantUid: string,
    body: PostBankAccountBody
  ): Promise<PostBankAccountResponse> {
    const response = await fetch(
      `${this.apiUrl}/merchants/${merchantUid}/bank_accounts`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: new URLSearchParams(body),
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async getBankAccounts(merchantUid: string): Promise<GetBankAccountsResponse> {
    const response = await fetch(
      `${this.apiUrl}/merchants/${merchantUid}/bank_accounts`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async getUBOs(merchantUid: string): Promise<GetUBOsResponse> {
    const response = await fetch(
      `${this.apiUrl}/merchants/${merchantUid}/ubos`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async createUBO(
    merchantUid: string,
    body: CreateUBOBody
  ): Promise<CreateUBOResponse> {
    const bodyParams = new URLSearchParams();
    bodyParams.append("names_first", body.names_first);
    if (body.name_prefix) {
      bodyParams.append("name_prefix", body.name_prefix);
    }
    bodyParams.append("name_last", body.name_last);
    bodyParams.append("date_of_birth", body.date_of_birth);
    bodyParams.append("country_of_residence", body.country_of_residence);

    if ("is_decision_maker" in body) {
      bodyParams.append(
        "is_decision_maker",
        body.is_decision_maker ? "true" : "false"
      );
    }
    if (body.decision_maker_percentage) {
      bodyParams.append(
        "decision_maker_percentage",
        `${body.decision_maker_percentage}`
      );
    }

    if ("is_shareholder" in body) {
      bodyParams.append(
        "is_shareholder",
        body.is_shareholder ? "true" : "false"
      );
    }

    if (body.shareholder_percentage) {
      bodyParams.append(
        "shareholder_percentage",
        `${body.shareholder_percentage}`
      );
    }

    if ("is_pep" in body) {
      bodyParams.append("is_pep", body.is_pep ? "true" : "false");
    }

    const response = await fetch(
      `${this.apiUrl}/merchants/${merchantUid}/ubos`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: bodyParams,
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async updateUBO(
    merchantUid: string,
    uboUid: string,
    body: Partial<CreateUBOBody>
  ): Promise<CreateUBOResponse> {
    const bodyParams = new URLSearchParams();

    if (body.names_first) {
      bodyParams.append("names_first", body.names_first);
    }

    if (body.name_prefix) {
      bodyParams.append("name_prefix", body.name_prefix);
    }

    if (body.name_last) {
      bodyParams.append("name_last", body.name_last);
    }

    if (body.date_of_birth) {
      bodyParams.append("date_of_birth", body.date_of_birth);
    }

    if (body.country_of_residence) {
      bodyParams.append("country_of_residence", body.country_of_residence);
    }

    if ("is_decision_maker" in body) {
      bodyParams.append(
        "is_decision_maker",
        body.is_decision_maker ? "true" : "false"
      );
    }

    if ("decision_maker_percentage" in body) {
      bodyParams.append(
        "decision_maker_percentage",
        `${body.decision_maker_percentage}`
      );
    }

    if ("is_shareholder" in body) {
      bodyParams.append(
        "is_shareholder",
        body.is_shareholder ? "true" : "false"
      );
    }

    if ("shareholder_percentage" in body) {
      bodyParams.append(
        "shareholder_percentage",
        `${body.shareholder_percentage}`
      );
    }

    if ("is_pep" in body) {
      bodyParams.append("is_pep", body.is_pep ? "true" : "false");
    }

    const response = await fetch(
      `${this.apiUrl}/merchants/${merchantUid}/ubos/${uboUid}`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: bodyParams,
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async getContacts(merchantUid: string): Promise<GetContactsResponse> {
    const response = await fetch(
      `${this.apiUrl}/merchants/${merchantUid}/contacts`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async createUpload(body: PostUploadBody): Promise<PostUploadResponse> {
    const response = await fetch(`${this.filesApiUrl}/uploads`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
      },
      body: new URLSearchParams(body),
    });

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async getMerchantByUid(merchantUid: string): Promise<PostMerchantResponse> {
    const response = await fetch(`${this.apiUrl}/merchants/${merchantUid}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
      },
    });

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async getObjects(): Promise<GetObjectsResponse> {
    const response = await fetch(`${this.filesApiUrl}/objects`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
      },
    });

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async createContact(
    merchantUid: string,
    body: CreateContactBody
  ): Promise<CreateContactResponse> {
    const bodyParams = new URLSearchParams();

    bodyParams.append("type", body.type);
    bodyParams.append("title", body.title);
    bodyParams.append("gender", body.title === "mr" ? "m" : "f");
    bodyParams.append("name[initials]", body.name_initials);
    bodyParams.append("name[names_given]", body.name_names_given);
    bodyParams.append("name[first]", body.name_first);
    bodyParams.append("name[last]", body.name_last);

    if (body.birthdate) {
      bodyParams.append("birthdate", body.birthdate);
    }
    bodyParams.append(
      "emailaddresses[0][emailaddress]",
      body.emailaddresses[0].emailaddress
    );
    bodyParams.append(
      "phonenumbers[0][phonenumber]",
      body.phonenumbers[0].phonenumber
    );

    const response = await fetch(
      `${this.apiUrl}/merchants/${merchantUid}/contacts`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: bodyParams,
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async updateContact(
    merchantUid: string,
    contactUid: string,
    body: Partial<CreateContactBody>
  ): Promise<CreateContactResponse> {
    const bodyParams = new URLSearchParams();

    if (body.title) {
      bodyParams.append("title", body.title);
    }

    if (body.name_initials) {
      bodyParams.append("name[initials]", body.name_initials);
    }

    if (body.name_names_given) {
      bodyParams.append("name[names_given]", body.name_names_given);
    }

    if (body.name_first) {
      bodyParams.append("name[first]", body.name_first);
    }

    if (body.name_last) {
      bodyParams.append("name[last]", body.name_last);
    }

    if (body.birthdate) {
      bodyParams.append("birthdate", body.birthdate);
    }

    if (body.emailaddresses) {
      bodyParams.append(
        "emailaddresses[0][emailaddress]",
        body.emailaddresses[0].emailaddress
      );
    }

    if (body.phonenumbers) {
      bodyParams.append(
        "phonenumbers[0][phonenumber]",
        body.phonenumbers[0].phonenumber
      );
    }

    const response = await fetch(
      `${this.apiUrl}/merchants/${merchantUid}/contacts/${contactUid}`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: bodyParams,
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  async getLegalEntities(): Promise<GetLegalEntitiesResponse> {
    const bodyParams = new URLSearchParams();
    bodyParams.append("filter[0][name]", "country");
    bodyParams.append("filter[0][operator]", "eq");
    bodyParams.append("filter[0][value]", "GBR");
    bodyParams.append("page", "1");
    bodyParams.append("perpage", "50");

    const response = await fetch(
      `${this.apiUrl}/legal-entities?${bodyParams.toString()}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      }
    );

    if (!response.ok) {
      await this.parseErrorResponse(response);
    }

    return await response.json();
  }

  private async parseErrorResponse(response: Response): Promise<never> {
    const rawBody = await response.text();
    try {
      const error = JSON.parse(rawBody) as ApiErrorResponse;
      throw new OppApiError(error.message, error.status_code);
    } catch (error) {
      throw new Error(rawBody);
    }
  }
}

export type ApiErrorResponse = {
  error: string;
  message: string;
  status_code: number;
};

export class OppApiError extends Error {
  constructor(
    message: string,
    public statusCode: number
  ) {
    super(message);
    this.name = "OppApiError";
  }
}

export type PostMerchantBody = {
  coc_nr: string; // Company number
  country: string; // ISO 3166-1 alpha-3
  emailaddress: string;
  phone: string;
  notify_url: string;
  type: "business";
  legal_entity: string;
  addresses: Array<{
    country: string;
    zipcode: string;
    city: string;
    address_line_1: string;
    address_line_2?: string;
  }>;
};

export type PostMerchantResponse = {
  uid: string;
  status:
    | "new"
    | "pending"
    | "live"
    | "suspended"
    | "terminated"
    | "blocked"
    | "deleted";
  compliance: {
    status: "unverified" | "pending" | "verified";
    level: number;
    overview_url: string;
    requirements: {
      type:
        | "bank_account.required"
        | "bank_account.verification.required"
        | "contact.verification.required"
        | "contact.phonenumber.required"
        | "contact.phonenumber.verification.required"
        | "source_of_funds.required"
        | "coc_extract.required"
        | "organization_structure.required"
        | "ubo.required"
        | "ubo.verification.required"
        | "right_to_work.required";
      status: "unverified" | "pending" | "verified";
      object_type: string | null;
      object_uid: string | null;
      object_url: string | null;
      object_redirect_url: string;
    }[];
  };
  phone: string;
  country: string;
  emailaddress: string[];
};

export type PostBankAccountBody = {
  account_bic: string;
  account_iban: string;
  account_name: string;
  notify_url: string;
  return_url: string;
};

export type PostBankAccountResponse = {
  uid: string;
  object: "bank_account";
  created: number;
  updated: number;
  verified: boolean | null;
  verified_with: null;
  verification_url: string;
  status: "new" | "pending" | "approved" | "disapproved";
  account: {
    account_iban: string;
    account_name: string;
  };
  bank: {
    bic: string;
  };
  reference: null;
  return_url: string;
  notify_url: string;
  is_default: boolean;
  disapprovals: {
    name: string;
    created: number;
  }[];
};

export type GetMerchantResponse = PostMerchantResponse;

export type GetBankAccountsResponse = {
  object: "list";
  url: string;
  has_more: boolean;
  total_item_count: number;
  items_per_page: number;
  current_page: number;
  last_page: number;
  data: {
    uid: string;
    object: "bank_account";
    created: number;
    updated: number;
    verified: null;
    verified_with: null;
    verification_url: "https://sandbox.onlinebetaalplatform.nl/en/renopay/merchants/mer_8034e1b0b0c5/verifications/bank-details/bnk_7521c635d77d/3192bc0bcccda32195129d57f13a976b06fef59c";
    status: "new" | "pending" | "approved" | "disapproved";
    account: {
      account_name: string;
      account_iban: string;
      account_number: string;
      account_bic: string;
      account_sort_code: string;
      account_country: string;
    };
    bank: unknown[];
    reference: null;
    return_url: string;
    notify_url: string;
    is_default: boolean;
    disapprovals: unknown[];
  }[];
};

export type PostUploadBody = {
  merchant_uid: string;
  purpose: string;
  object_uid: string;
};

export type PostUploadResponse = {
  uid: string;
  created: number;
  updated: number;
  expired: number;
  merchant_uid: string;
  object_uid: string;
  purpose: string;
  token: string;
  url: string;
};

export type GetObjectsResponse = {
  object: "list";
  url: string;
  has_more: boolean;
  total_item_count: number;
  items_per_page: number;
  current_page: number;
  last_page: number;
  data: {
    type: string;
    file_groups: {
      name: string;
      restricted_to: string;
      number_of_files_required: number;
      files: {
        purpose: string;
        allowed_upload_size: string;
        allowed_mime_types: string[];
      }[];
    }[];
  }[];
};

export type CreateUBOBody = {
  names_first: string; // required - The first names of the UBO
  name_prefix?: string; // Prefix of the UBO
  name_last: string; // required - Last name of the UBO
  date_of_birth: string; // required - Date of birth formatted as yyyy-mm-dd
  country_of_residence: string; // required - ISO 3166-1 alpha-3 country code
  is_decision_maker?: boolean; // Whether the UBO is a decision maker
  decision_maker_percentage?: number; // How much decision power the UBO has, expressed as a percentage
  is_shareholder?: boolean; // Whether the UBO is a shareholder
  shareholder_percentage?: number; // The percentage of shares the UBO holds in the company
  is_pep?: boolean; // Whether the UBO is a Politically Exposed Persons (PEP)
};

export type CreateUBOResponse = {
  livemode: boolean;
  uid: string;
  object: "ubo";
  status: "pending" | "verified" | "unverified";
  created: number;
  updated: number;
  verified: null;
  names_first: string;
  name_prefix: string;
  name_last: string;
  date_of_birth: string;
  country_of_residence: string;
  share: number;
  is_decision_maker: boolean;
  decision_maker_percentage: number;
  is_shareholder: boolean;
  shareholder_percentage: number;
  is_pep: boolean;
};

export type GetUBOsResponse = {
  object: "list";
  url: string;
  has_more: boolean;
  total_item_count: number;
  items_per_page: number;
  current_page: number;
  last_page: number;
  data: {
    livemode: boolean;
    uid: string;
    object: "ubo";
    status: "new" | "pending" | "verified" | "unverified";
    created: number;
    updated: number;
    verified: null;
    names_first: string;
    name_prefix: string;
    name_last: string;
    date_of_birth: string;
    country_of_residence: string;
    share: number;
    is_decision_maker: boolean;
    decision_maker_percentage: number;
    is_shareholder: boolean;
    shareholder_percentage: number;
    is_pep: boolean;
  }[];
};

export type CreateContactBody = {
  type: "representative";
  title: "mr" | "mrs";
  name_initials: string;
  name_names_given: string;
  name_first: string;
  name_last: string;
  birthdate?: string; // Date formatted as yyyy-mm-dd
  emailaddresses: Array<{
    emailaddress: string;
  }>;
  phonenumbers: Array<{
    phonenumber: string;
  }>;
};

export type CreateContactResponse = {
  uid: string;
  status: "pending" | "verified" | "unverified";
  [key: string]: unknown; // Add more specific fields as needed based on API response
};

export type GetContactsResponse = {
  object: "list";
  url: string;
  has_more: boolean;
  total_item_count: number;
  items_per_page: number;
  current_page: number;
  last_page: number;
  data: {
    uid: string;
    object: "contact";
    created: number;
    updated: number;
    verified: null;
    verified_with: null;
    verification_url: string;
    type: "representative";
    status: "unverified" | "pending" | "verified";
    title: "mr" | "mrs";
    name_initials: string;
    name_first: string;
    name_last: string;
    names_given: string;
    birthdate: string | null;
    partner_name_last: string | null;
    emailaddresses: Array<{
      emailaddress: string;
    }>;
    phonenumbers: Array<{
      phonenumber: string;
    }>;
    is_pep: boolean | null;
  }[];
};

export type GetLegalEntitiesResponse = {
  object: "list";
  url: string;
  has_more: boolean;
  total_item_count: number;
  items_per_page: number;
  current_page: number;
  last_page: number;
  data: {
    object: "legal_entity";
    country: string;
    language: string;
    legal_entity_code: string;
    legal_entity_name: string;
    registry_number: string | null;
    registry_name: string | null;
    registry_file: string | null;
    bank_account_type: string;
  }[];
};
