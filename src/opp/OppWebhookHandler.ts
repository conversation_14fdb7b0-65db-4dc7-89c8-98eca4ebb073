import { createHmac, timingSafeEqual } from "crypto";
import { NextRequest } from "next/server";

export default class OppWebhookHandler {
  private readonly secret: string;

  constructor(options: { secret: string }) {
    this.secret = options.secret;
  }

  async readEvent(request: NextRequest): Promise<OppWebhookNotification> {
    const body = await request.text();

    if (!verifySignature(request, body, this.secret)) {
      throw new Error("Invalid signature");
    }

    const payload: OppWebhookNotification = JSON.parse(body);

    return payload;
  }
}

type OppNotificationType =
  | "bank_account.status.changed"
  | "contact.status.changed"
  | "dispute.message.added"
  | "dispute.status.changed"
  | "ideal_qr.status.changed"
  | "mandate.status.changed"
  | "merchant.compliance_level.changed"
  | "merchant.compliance_requirement.created"
  | "merchant.compliance_requirement.status.changed"
  | "merchant.compliance_status.changed"
  | "merchant.contact.phonenumber.updated"
  | "merchant.profile.balance.changed"
  | "merchant.profile.status.changed"
  | "merchant.status.changed"
  | "merchant.withdrawal.status.changed"
  | "multi_transaction.status.changed"
  | "multi_transaction_import.status.changed"
  | "payment_reference.transaction.denied"
  | "payment_reference.transaction.registered"
  | "signup.status.changed"
  | "transaction.amount.changed"
  | "transaction.status.changed"
  | "virtual_iban.transaction.registered"
  | "ubo.status.updated"
  | "withdrawal.status.changed"
  | "compliance_requirement.about-to-expire";

type OppWebhookNotification = {
  uid: string;
  type: OppNotificationType;
  created: number;
  object_uid: string;
  object_type: string;
  object_url: string;
  parent_uid?: string;
  parent_type?: string;
  verification_hash?: string;
};

function verifySignature(
  request: NextRequest,
  body: string,
  secret: string
): boolean {
  const signatureHeader = request.headers.get("signature");
  const digestHeader = request.headers.get("digest");

  if (!signatureHeader || !digestHeader) {
    return false;
  }

  const { algorithm, headers, signature } =
    parseSignatureHeader(signatureHeader);

  if (algorithm !== "hmac-sha256") {
    return false;
  }

  const signingString = buildSigningString(request, headers, digestHeader);

  const hmac = createHmac("sha256", secret);
  hmac.update(signingString);
  const calculatedSignature = hmac.digest("base64");

  const providedSignature = Buffer.from(signature, "base64");
  const expectedSignature = Buffer.from(calculatedSignature, "base64");

  if (providedSignature.length !== expectedSignature.length) {
    return false;
  }

  return timingSafeEqual(providedSignature, expectedSignature);
}

function buildSigningString(
  request: NextRequest,
  headers: string[],
  digest: string
): string {
  const lines: string[] = [];

  for (const header of headers) {
    if (header === "(request-target)") {
      const method = request.method.toLowerCase();
      const path = request.nextUrl.pathname;
      lines.push(`(request-target): ${method} ${path}`);
    } else if (header === "host") {
      lines.push(`host: ${request.headers.get("host")}`);
    } else if (header === "date") {
      lines.push(`date: ${request.headers.get("date")}`);
    } else if (header === "digest") {
      lines.push(`digest: ${digest}`);
    }
  }

  return lines.join("\n");
}

function parseSignatureHeader(signatureHeader: string): {
  keyId: string;
  algorithm: string;
  headers: string[];
  signature: string;
} {
  const parts = signatureHeader.split(",");
  const result: Record<string, string> = {};

  for (const part of parts) {
    const [key, value] = part.split("=");
    result[key.trim()] = value.replace(/^"|"$/g, "");
  }

  return {
    keyId: result.keyId,
    algorithm: result.algorithm,
    headers: result.headers.split(" "),
    signature: result.signature,
  };
}
