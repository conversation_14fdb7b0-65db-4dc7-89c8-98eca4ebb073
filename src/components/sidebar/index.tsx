import { AppSidebar, AppSidebarProps } from "@/components/sidebar/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

export type SidebarProps = {
  data: AppSidebarProps["data"];
  children: React.ReactNode;
};

export default function Sidebar({ data, children }: SidebarProps) {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar data={data} />
      <SidebarInset>{children}</SidebarInset>
    </SidebarProvider>
  );
}
