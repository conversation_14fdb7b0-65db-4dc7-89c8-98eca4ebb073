"use client";

import { Search } from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import { useEffect } from "react";

import { AppSidebarProps } from "@/components/sidebar/app-sidebar";
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { SidebarHeader } from "@/components/ui/sidebar";

type NavHeaderProps = {
  data: AppSidebarProps["data"];
};

export function NavHeader({ data }: NavHeaderProps) {
  const [open, setOpen] = React.useState(false);
  const router = useRouter();

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };
    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []);

  return (
    <>
      <SidebarHeader>
        <button
          className="flex items-center justify-between px-2 pb-0 pt-1 cursor-pointer"
          onClick={() => setOpen(true)}
        >
          <div className="flex items-center flex-1 gap-3">
            <Search className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground font-normal">
              Search
            </span>
          </div>
          <div className="flex items-center justify-center px-2 py-1 border border-border rounded-md">
            <kbd className="text-muted-foreground inline-flex font-[inherit] text-xs font-medium">
              <span className="opacity-70">⌘K</span>
            </kbd>
          </div>
        </button>
      </SidebarHeader>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput placeholder="Search everything..." />
        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          {data.navMain.length > 0 && (
            <>
              <CommandGroup heading="Navigation">
                {data.navMain.map((item) => (
                  <CommandItem
                    key={item.id}
                    className="py-2!"
                    onSelect={() => {
                      setOpen(false);
                      if (item.url) {
                        router.push(item.url);
                      }
                    }}
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    <span>{item.title}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            </>
          )}
        </CommandList>
      </CommandDialog>
    </>
  );
}
