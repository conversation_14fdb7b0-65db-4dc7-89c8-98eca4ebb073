"use client";

import { ChevronDownIcon } from "lucide-react";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import formatDate from "@/lib/utils/formatDate";

export type DatepickerProps = {
  label?: React.ReactNode;
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  id?: string;
  disabled?: boolean;
  placeholder?: string;
  disabledFilter?: (date: Date) => boolean;
};

export function Datepicker({
  label,
  value,
  onChange,
  id,
  disabled = false,
  placeholder = "Select date",
  disabledFilter,
}: DatepickerProps) {
  const [open, setOpen] = React.useState(false);

  const handleSelect = (date: Date | undefined) => {
    onChange?.(date);
    setOpen(false);
  };

  const defaultId = React.useId();
  const buttonId = id || defaultId;

  return (
    <div className="w-full flex flex-col gap-3">
      {label && (
        <Label htmlFor={buttonId} className="px-1 text-sm leading-snug">
          {label}
        </Label>
      )}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            id={buttonId}
            disabled={disabled}
            className="w-full justify-between font-normal"
          >
            {value ? formatDate(value.toISOString()) : placeholder}
            <ChevronDownIcon />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto overflow-hidden p-0" align="start">
          <Calendar
            mode="single"
            selected={value}
            captionLayout="dropdown"
            onSelect={handleSelect}
            disabled={disabledFilter}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
