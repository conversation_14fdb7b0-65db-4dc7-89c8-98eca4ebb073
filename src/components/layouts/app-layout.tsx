"use client";

import { Folder<PERSON><PERSON>ban, LayoutDashboard, MessageCircle } from "lucide-react";
import React from "react";

import Sidebar from "@/components/sidebar";
import { AppSidebarProps } from "@/components/sidebar/app-sidebar";
import { SiteHeader } from "@/components/site-header";

type PageLayoutProps = {
  heading: React.ReactNode;
  user: AppSidebarProps["data"]["user"];
  children: React.ReactNode;
};

export default function PageLayout({
  heading,
  user,
  children,
}: PageLayoutProps) {
  const sidebarData: AppSidebarProps["data"] = {
    user: {
      name: user.name,
      email: user.email,
      avatar: user.avatar,
      canViewOrganization: user.canViewOrganization,
    },
    navMain: [
      {
        id: "dashboard",
        title: "Dashboard",
        url: "/",
        icon: LayoutDashboard,
        isActive: false,
      },
      {
        id: "projects",
        title: "Projects",
        url: "/projects",
        icon: Folder<PERSON><PERSON>ban,
        isActive: false,
      },
      {
        id: "clients-and-chats",
        title: "Clients & Chats",
        url: "/clients-and-chats",
        icon: MessageCircle,
        isActive: false,
      },
    ],
  };

  return (
    <Sidebar data={sidebarData}>
      <SiteHeader heading={heading} />

      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            {children}
          </div>
        </div>
      </div>
    </Sidebar>
  );
}
