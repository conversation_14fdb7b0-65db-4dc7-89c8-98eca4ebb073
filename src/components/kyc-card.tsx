import { <PERSON><PERSON><PERSON>rian<PERSON> } from "lucide-react";
import Link from "next/link";

import { Card, CardDescription, CardHeader, CardTitle } from "./ui/card";

export default function KYCCard() {
  return (
    <Card className="border-yellow-500/50 bg-yellow-50/50 dark:bg-yellow-950/20">
      <CardHeader>
        <div className="flex items-start gap-3">
          <AlertTriangle className="mt-0.5 size-5 text-yellow-600 dark:text-yellow-500" />
          <div className="flex-1">
            <CardTitle className="text-base font-semibold text-yellow-900 dark:text-yellow-100">
              KYC Verification Not Completed
            </CardTitle>
            <CardDescription className="mt-1.5 text-yellow-800 dark:text-yellow-200">
              Please complete your KYC verification to access all features.{" "}
              <Link
                href="/compliance"
                className="font-medium underline underline-offset-4 hover:no-underline"
              >
                Complete verification
              </Link>
            </CardDescription>
          </div>
        </div>
      </CardHeader>
    </Card>
  );
}
