/**
 * Generated by orval v7.16.0 🍺
 * Do not edit manually.
 * scopezilla
 * OpenAPI spec version: 0.1.0
 */
import { customFetch } from "./mutator";
export interface ConversationResponse {
  id: string;
  type: ConversationTypes;
}

export type ConversationTypes =
  (typeof ConversationTypes)[keyof typeof ConversationTypes];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ConversationTypes = {
  INITIAL: "INITIAL",
  OVERVIEW: "OVERVIEW",
  SCOPES: "SCOPES",
} as const;

export interface EstimatesResponse {
  project_id: string;
  coda_doc_id: string;
  coda_doc_link: string;
  coda_doc_page_id: string;
  coda_doc_page_link: string;
}

export interface HTTPValidationError {
  detail?: ValidationError[];
}

export interface HealthCheckResponse {
  status: string;
}

export interface ManyProjectsResponse {
  projects: ProjectResponse[];
}

export interface MessageRequest {
  message: string;
}

export type ProjectRequestUserId = string | null;

export interface ProjectRequest {
  user_id?: ProjectRequestUserId;
}

export type ProjectResponseCodaDocId = string | null;

export type ProjectResponseCodaDocLink = string | null;

export type ProjectResponseCodaDocPageId = string | null;

export type ProjectResponseCodaDocPageLink = string | null;

export interface ProjectResponse {
  project_id: string;
  coda_doc_id?: ProjectResponseCodaDocId;
  coda_doc_link?: ProjectResponseCodaDocLink;
  coda_doc_page_id?: ProjectResponseCodaDocPageId;
  coda_doc_page_link?: ProjectResponseCodaDocPageLink;
  conversations: ConversationResponse[];
}

export type ProjectUpdateRequestUserId = string | null;

export type ProjectUpdateRequestProjectOverview = string | null;

export type ProjectUpdateRequestFeaturesAndScopes = string | null;

export interface ProjectUpdateRequest {
  user_id?: ProjectUpdateRequestUserId;
  project_overview?: ProjectUpdateRequestProjectOverview;
  features_and_scopes?: ProjectUpdateRequestFeaturesAndScopes;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface SignInApiResponse {
  user_id: string;
}

export interface SignInInitRequest {
  email: string;
  subscribe: boolean;
}

export interface SignInRequest {
  email: string;
  otp: string;
}

export type ValidationErrorLocItem = string | number;

export interface ValidationError {
  loc: ValidationErrorLocItem[];
  msg: string;
  type: string;
}

export type CreateProjectBody = ProjectRequest | null;

export type GetManyProjectsForUserParams = {
  user_id: string;
};

/**
 * @summary Healthcheck
 */
export type healthcheckResponse200 = {
  data: HealthCheckResponse;
  status: 200;
};

export type healthcheckResponseSuccess = healthcheckResponse200 & {
  headers: Headers;
};
export type healthcheckResponse = healthcheckResponseSuccess;

export const getHealthcheckUrl = () => {
  return `/healthcheck`;
};

export const healthcheck = async (
  options?: RequestInit,
): Promise<healthcheckResponse> => {
  return customFetch<healthcheckResponse>(getHealthcheckUrl(), {
    ...options,
    method: "GET",
  });
};

/**
 * @summary Initiate Sign In
 */
export type initiateSignInResponse200 = {
  data: unknown;
  status: 200;
};

export type initiateSignInResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type initiateSignInResponseSuccess = initiateSignInResponse200 & {
  headers: Headers;
};
export type initiateSignInResponseError = initiateSignInResponse422 & {
  headers: Headers;
};

export type initiateSignInResponse =
  | initiateSignInResponseSuccess
  | initiateSignInResponseError;

export const getInitiateSignInUrl = () => {
  return `/auth/initiate-sign-in`;
};

export const initiateSignIn = async (
  signInInitRequest: SignInInitRequest,
  options?: RequestInit,
): Promise<initiateSignInResponse> => {
  return customFetch<initiateSignInResponse>(getInitiateSignInUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(signInInitRequest),
  });
};

/**
 * @summary Sign In With Otp
 */
export type signInWithOtpResponse200 = {
  data: SignInApiResponse;
  status: 200;
};

export type signInWithOtpResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type signInWithOtpResponseSuccess = signInWithOtpResponse200 & {
  headers: Headers;
};
export type signInWithOtpResponseError = signInWithOtpResponse422 & {
  headers: Headers;
};

export type signInWithOtpResponse =
  | signInWithOtpResponseSuccess
  | signInWithOtpResponseError;

export const getSignInWithOtpUrl = () => {
  return `/auth/sign-in-with-otp`;
};

export const signInWithOtp = async (
  signInRequest: SignInRequest,
  options?: RequestInit,
): Promise<signInWithOtpResponse> => {
  return customFetch<signInWithOtpResponse>(getSignInWithOtpUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(signInRequest),
  });
};

/**
 * @summary Refresh Token
 */
export type refreshTokenResponse200 = {
  data: unknown;
  status: 200;
};

export type refreshTokenResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type refreshTokenResponseSuccess = refreshTokenResponse200 & {
  headers: Headers;
};
export type refreshTokenResponseError = refreshTokenResponse422 & {
  headers: Headers;
};

export type refreshTokenResponse =
  | refreshTokenResponseSuccess
  | refreshTokenResponseError;

export const getRefreshTokenUrl = () => {
  return `/auth/refresh-token`;
};

export const refreshToken = async (
  refreshTokenRequest: RefreshTokenRequest,
  options?: RequestInit,
): Promise<refreshTokenResponse> => {
  return customFetch<refreshTokenResponse>(getRefreshTokenUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(refreshTokenRequest),
  });
};

/**
 * @summary Create Project
 */
export type createProjectResponse200 = {
  data: ProjectResponse;
  status: 200;
};

export type createProjectResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type createProjectResponseSuccess = createProjectResponse200 & {
  headers: Headers;
};
export type createProjectResponseError = createProjectResponse422 & {
  headers: Headers;
};

export type createProjectResponse =
  | createProjectResponseSuccess
  | createProjectResponseError;

export const getCreateProjectUrl = () => {
  return `/project/`;
};

export const createProject = async (
  createProjectBody: CreateProjectBody,
  options?: RequestInit,
): Promise<createProjectResponse> => {
  return customFetch<createProjectResponse>(getCreateProjectUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(createProjectBody),
  });
};

/**
 * @summary Get Many Projects
 */
export type getManyProjectsForUserResponse200 = {
  data: ManyProjectsResponse;
  status: 200;
};

export type getManyProjectsForUserResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type getManyProjectsForUserResponseSuccess =
  getManyProjectsForUserResponse200 & {
    headers: Headers;
  };
export type getManyProjectsForUserResponseError =
  getManyProjectsForUserResponse422 & {
    headers: Headers;
  };

export type getManyProjectsForUserResponse =
  | getManyProjectsForUserResponseSuccess
  | getManyProjectsForUserResponseError;

export const getGetManyProjectsForUserUrl = (
  params: GetManyProjectsForUserParams,
) => {
  const normalizedParams = new URLSearchParams();

  Object.entries(params || {}).forEach(([key, value]) => {
    if (value !== undefined) {
      normalizedParams.append(key, value === null ? "null" : value.toString());
    }
  });

  const stringifiedParams = normalizedParams.toString();

  return stringifiedParams.length > 0
    ? `/project/?${stringifiedParams}`
    : `/project/`;
};

export const getManyProjectsForUser = async (
  params: GetManyProjectsForUserParams,
  options?: RequestInit,
): Promise<getManyProjectsForUserResponse> => {
  return customFetch<getManyProjectsForUserResponse>(
    getGetManyProjectsForUserUrl(params),
    {
      ...options,
      method: "GET",
    },
  );
};

/**
 * @summary Get Project
 */
export type getProjectResponse200 = {
  data: ProjectResponse;
  status: 200;
};

export type getProjectResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type getProjectResponseSuccess = getProjectResponse200 & {
  headers: Headers;
};
export type getProjectResponseError = getProjectResponse422 & {
  headers: Headers;
};

export type getProjectResponse =
  | getProjectResponseSuccess
  | getProjectResponseError;

export const getGetProjectUrl = (projectId: string) => {
  return `/project/${projectId}`;
};

export const getProject = async (
  projectId: string,
  options?: RequestInit,
): Promise<getProjectResponse> => {
  return customFetch<getProjectResponse>(getGetProjectUrl(projectId), {
    ...options,
    method: "GET",
  });
};

/**
 * @summary Update Project
 */
export type updateProjectWithUserIdResponse200 = {
  data: unknown;
  status: 200;
};

export type updateProjectWithUserIdResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type updateProjectWithUserIdResponseSuccess =
  updateProjectWithUserIdResponse200 & {
    headers: Headers;
  };
export type updateProjectWithUserIdResponseError =
  updateProjectWithUserIdResponse422 & {
    headers: Headers;
  };

export type updateProjectWithUserIdResponse =
  | updateProjectWithUserIdResponseSuccess
  | updateProjectWithUserIdResponseError;

export const getUpdateProjectWithUserIdUrl = (projectId: string) => {
  return `/project/${projectId}`;
};

export const updateProjectWithUserId = async (
  projectId: string,
  projectUpdateRequest: ProjectUpdateRequest,
  options?: RequestInit,
): Promise<updateProjectWithUserIdResponse> => {
  return customFetch<updateProjectWithUserIdResponse>(
    getUpdateProjectWithUserIdUrl(projectId),
    {
      ...options,
      method: "PATCH",
      headers: { "Content-Type": "application/json", ...options?.headers },
      body: JSON.stringify(projectUpdateRequest),
    },
  );
};

/**
 * @summary Open Conversation Sse
 */
export type openConversationSseResponse200 = {
  data: unknown;
  status: 200;
};

export type openConversationSseResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type openConversationSseResponseSuccess =
  openConversationSseResponse200 & {
    headers: Headers;
  };
export type openConversationSseResponseError =
  openConversationSseResponse422 & {
    headers: Headers;
  };

export type openConversationSseResponse =
  | openConversationSseResponseSuccess
  | openConversationSseResponseError;

export const getOpenConversationSseUrl = (
  projectId: string,
  conversationId: string,
) => {
  return `/project/${projectId}/conversation/${conversationId}`;
};

export const openConversationSse = async (
  projectId: string,
  conversationId: string,
  options?: RequestInit,
): Promise<openConversationSseResponse> => {
  return customFetch<openConversationSseResponse>(
    getOpenConversationSseUrl(projectId, conversationId),
    {
      ...options,
      method: "POST",
    },
  );
};

/**
 * @summary Post Message
 */
export type postMessageToConversationResponse200 = {
  data: unknown;
  status: 200;
};

export type postMessageToConversationResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type postMessageToConversationResponseSuccess =
  postMessageToConversationResponse200 & {
    headers: Headers;
  };
export type postMessageToConversationResponseError =
  postMessageToConversationResponse422 & {
    headers: Headers;
  };

export type postMessageToConversationResponse =
  | postMessageToConversationResponseSuccess
  | postMessageToConversationResponseError;

export const getPostMessageToConversationUrl = (
  projectId: string,
  conversationId: string,
) => {
  return `/project/${projectId}/conversation/${conversationId}/message`;
};

export const postMessageToConversation = async (
  projectId: string,
  conversationId: string,
  messageRequest: MessageRequest,
  options?: RequestInit,
): Promise<postMessageToConversationResponse> => {
  return customFetch<postMessageToConversationResponse>(
    getPostMessageToConversationUrl(projectId, conversationId),
    {
      ...options,
      method: "POST",
      headers: { "Content-Type": "application/json", ...options?.headers },
      body: JSON.stringify(messageRequest),
    },
  );
};

/**
 * @summary Open Project Overview Sse
 */
export type openProjectOverviewSseResponse200 = {
  data: unknown;
  status: 200;
};

export type openProjectOverviewSseResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type openProjectOverviewSseResponseSuccess =
  openProjectOverviewSseResponse200 & {
    headers: Headers;
  };
export type openProjectOverviewSseResponseError =
  openProjectOverviewSseResponse422 & {
    headers: Headers;
  };

export type openProjectOverviewSseResponse =
  | openProjectOverviewSseResponseSuccess
  | openProjectOverviewSseResponseError;

export const getOpenProjectOverviewSseUrl = (projectId: string) => {
  return `/project/${projectId}/overview_sse`;
};

export const openProjectOverviewSse = async (
  projectId: string,
  options?: RequestInit,
): Promise<openProjectOverviewSseResponse> => {
  return customFetch<openProjectOverviewSseResponse>(
    getOpenProjectOverviewSseUrl(projectId),
    {
      ...options,
      method: "POST",
    },
  );
};

/**
 * @summary Generate Project Overview
 */
export type generateProjectOverviewResponse200 = {
  data: ProjectResponse;
  status: 200;
};

export type generateProjectOverviewResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type generateProjectOverviewResponseSuccess =
  generateProjectOverviewResponse200 & {
    headers: Headers;
  };
export type generateProjectOverviewResponseError =
  generateProjectOverviewResponse422 & {
    headers: Headers;
  };

export type generateProjectOverviewResponse =
  | generateProjectOverviewResponseSuccess
  | generateProjectOverviewResponseError;

export const getGenerateProjectOverviewUrl = (projectId: string) => {
  return `/project/${projectId}/generate_project_overview`;
};

export const generateProjectOverview = async (
  projectId: string,
  options?: RequestInit,
): Promise<generateProjectOverviewResponse> => {
  return customFetch<generateProjectOverviewResponse>(
    getGenerateProjectOverviewUrl(projectId),
    {
      ...options,
      method: "POST",
    },
  );
};

/**
 * @summary Open Project Scopes Sse
 */
export type openProjectScopesSseResponse200 = {
  data: unknown;
  status: 200;
};

export type openProjectScopesSseResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type openProjectScopesSseResponseSuccess =
  openProjectScopesSseResponse200 & {
    headers: Headers;
  };
export type openProjectScopesSseResponseError =
  openProjectScopesSseResponse422 & {
    headers: Headers;
  };

export type openProjectScopesSseResponse =
  | openProjectScopesSseResponseSuccess
  | openProjectScopesSseResponseError;

export const getOpenProjectScopesSseUrl = (projectId: string) => {
  return `/project/${projectId}/scopes_sse`;
};

export const openProjectScopesSse = async (
  projectId: string,
  options?: RequestInit,
): Promise<openProjectScopesSseResponse> => {
  return customFetch<openProjectScopesSseResponse>(
    getOpenProjectScopesSseUrl(projectId),
    {
      ...options,
      method: "POST",
    },
  );
};

/**
 * @summary Generate Project Scopes
 */
export type generateProjectScopesResponse200 = {
  data: ProjectResponse;
  status: 200;
};

export type generateProjectScopesResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type generateProjectScopesResponseSuccess =
  generateProjectScopesResponse200 & {
    headers: Headers;
  };
export type generateProjectScopesResponseError =
  generateProjectScopesResponse422 & {
    headers: Headers;
  };

export type generateProjectScopesResponse =
  | generateProjectScopesResponseSuccess
  | generateProjectScopesResponseError;

export const getGenerateProjectScopesUrl = (projectId: string) => {
  return `/project/${projectId}/generate_project_scopes`;
};

export const generateProjectScopes = async (
  projectId: string,
  options?: RequestInit,
): Promise<generateProjectScopesResponse> => {
  return customFetch<generateProjectScopesResponse>(
    getGenerateProjectScopesUrl(projectId),
    {
      ...options,
      method: "POST",
    },
  );
};

/**
 * @summary Generate Project Estimates
 */
export type generateProjectEstimatesResponse200 = {
  data: EstimatesResponse;
  status: 200;
};

export type generateProjectEstimatesResponse422 = {
  data: HTTPValidationError;
  status: 422;
};

export type generateProjectEstimatesResponseSuccess =
  generateProjectEstimatesResponse200 & {
    headers: Headers;
  };
export type generateProjectEstimatesResponseError =
  generateProjectEstimatesResponse422 & {
    headers: Headers;
  };

export type generateProjectEstimatesResponse =
  | generateProjectEstimatesResponseSuccess
  | generateProjectEstimatesResponseError;

export const getGenerateProjectEstimatesUrl = (projectId: string) => {
  return `/project/${projectId}/generate_project_estimates`;
};

export const generateProjectEstimates = async (
  projectId: string,
  options?: RequestInit,
): Promise<generateProjectEstimatesResponse> => {
  return customFetch<generateProjectEstimatesResponse>(
    getGenerateProjectEstimatesUrl(projectId),
    {
      ...options,
      method: "POST",
    },
  );
};
