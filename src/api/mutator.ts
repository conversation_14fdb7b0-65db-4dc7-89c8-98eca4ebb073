export function buildApiUrl(path: string): URL {
  return new URL(process.env.NEXT_PUBLIC_API_URL! + path);
}

export async function customFetch<
  T extends { data: unknown; status: number; headers: Headers },
>(url: string, options?: RequestInit): Promise<T> {
  const res = await fetch(buildApiUrl(url), options);
  const body = [204, 205, 304].includes(res.status) ? null : await res.text();
  const data: T["data"] = body ? JSON.parse(body) : {};
  return {
    data,
    status: res.status,
    headers: res.headers,
  } as T;
}
