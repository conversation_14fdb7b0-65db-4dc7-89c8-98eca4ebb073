import { CookieMethodsServer, CookieOptions } from "@supabase/ssr";
import { NextRequest, NextResponse } from "next/server";

export default class SupabaseApiRouteCookieStorage implements CookieMethodsServer {
  constructor(
    private request: NextRequest,
    private response: NextResponse
  ) {}

  getAll = () => {
    return this.request.cookies.getAll();
  };

  setAll = (
    cookiesToSet: {
      name: string;
      value: string;
      options: CookieOptions;
    }[]
  ) => {
    cookiesToSet.forEach(({ name, value, options }) =>
      this.response.cookies.set(name, value, options)
    );
  };
}
