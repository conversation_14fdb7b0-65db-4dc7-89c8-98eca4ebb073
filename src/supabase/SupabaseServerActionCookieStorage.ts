import { CookieMethodsServer, CookieOptions } from "@supabase/ssr";
import type { ReadonlyRequestCookies } from "next/dist/server/web/spec-extension/adapters/request-cookies";

export default class SupabaseServerActionCookieStorage implements CookieMethodsServer {
  constructor(private cookieStore: ReadonlyRequestCookies) {}

  getAll = () => {
    return this.cookieStore.getAll();
  };

  setAll = (
    cookiesToSet: {
      name: string;
      value: string;
      options: CookieOptions;
    }[]
  ) => {
    try {
      cookiesToSet.forEach(({ name, value, options }) =>
        this.cookieStore.set(name, value, options)
      );
    } catch (error) {
      // The `setAll` method was called from a Server Component.
      // This can be ignored if you have middleware refreshing
      // user sessions.
    }
  };
}
