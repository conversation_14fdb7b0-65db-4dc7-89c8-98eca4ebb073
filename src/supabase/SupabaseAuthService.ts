import {
  AuthUser as SupabaseAuthUser,
  Session as SupabaseSession,
} from "@supabase/supabase-js";
import { type SupabaseAuthClient } from "@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js";
import assert from "node:assert";

export default class SupabaseAuthService {
  constructor(private readonly auth: SupabaseAuthClient) {}

  public async getUser(): Promise<AuthUser | null> {
    const { data } = await this.auth.getUser();

    if (data.user == null) {
      return null;
    }

    return parseAuthUser(data.user);
  }

  public async getSession(): Promise<WithErrorResponse<Session | null>> {
    const { data, error } = await this.auth.getSession();

    if (error) {
      return { data: null, error };
    }

    return {
      data: data.session ? parseSession(data.session) : null,
      error: null,
    };
  }

  public async isAuthenticated(): Promise<boolean> {
    const { data, error } = await this.auth.getClaims();

    // If there's an error or no claims, user is not authenticated
    if (error || data?.claims == null) {
      return false;
    }

    // Check if claims have a subject (user ID) - indicates valid authenticated user
    return data.claims.sub.length > 0;
  }

  public async signUp(
    input: SignUpInput
  ): Promise<WithErrorResponse<AuthUser | null>> {
    const { error, data } = await this.auth.signUp({
      email: input.email,
      password: input.password,
      options: {
        emailRedirectTo: input.redirectTo,
      },
    });

    if (error) {
      return { data: null, error };
    }

    return { data: data.user ? parseAuthUser(data.user) : null, error: null };
  }

  public async signIn(
    input: SignInInput
  ): Promise<WithErrorResponse<AuthUser | null>> {
    const { error, data } = await this.auth.signInWithPassword({
      email: input.email,
      password: input.password,
    });

    if (error) {
      return { data: null, error };
    }

    return { data: data.user ? parseAuthUser(data.user) : null, error: null };
  }

  public async sendSignInOtp(
    input: SendSignInOtpInput
  ): Promise<{ error: Error | null }> {
    const { error } = await this.auth.signInWithOtp({
      email: input.email,
      options: {
        shouldCreateUser: false, // Don't create user if doesn't exist
        emailRedirectTo: input.redirectTo,
      },
    });

    return { error };
  }

  public async signOut(): Promise<{ error: Error | null }> {
    return await this.auth.signOut();
  }

  public async resend(input: ResendInput): Promise<{ error: Error | null }> {
    // auth.resend doesn't redirect to /auth-callback with token param.
    // https://github.com/supabase/auth/issues/1872
    // In order to avoid this We use signInWithOtp to resend with PKCE support.
    return await this.sendSignInOtp({
      email: input.email,
      redirectTo: input.redirectTo,
    });
  }

  public async updateUser(
    input: UpdateUserInput
  ): Promise<WithErrorResponse<AuthUser | null>> {
    const { error, data } = await this.auth.updateUser({
      password: input.password,
    });

    if (error) {
      return { data: null, error };
    }

    return { data: data.user ? parseAuthUser(data.user) : null, error: null };
  }

  public async verifyOtp(
    input: VerifyOtpInput
  ): Promise<WithErrorResponse<AuthUser | null>> {
    const { error, data } = await this.auth.verifyOtp(
      input.type === "invite"
        ? {
            token_hash: input.tokenHash,
            type: "invite",
          }
        : {
            email: input.email,
            token: input.token,
            type: input.type,
          }
    );

    if (error) {
      return { data: null, error };
    }

    return { data: data.user ? parseAuthUser(data.user) : null, error: null };
  }

  public async exchangeCodeForSession(
    input: ExchangeCodeForSessionInput
  ): Promise<WithErrorResponse<AuthUser>> {
    const { error, data } = await this.auth.exchangeCodeForSession(input.code);

    if (error) {
      return { data: null, error };
    }

    return { data: parseAuthUser(data.user), error: null };
  }

  public async resetPasswordForEmail(
    input: ResetPasswordForEmailInput
  ): Promise<{ error: Error | null }> {
    const { error } = await this.auth.resetPasswordForEmail(input.email, {
      redirectTo: input.redirectTo,
    });

    return { error };
  }

  public async sendInviteEmail(
    input: SendInviteEmailInput
  ): Promise<WithErrorResponse<AuthUser>> {
    const { error, data } = await this.auth.admin.inviteUserByEmail(
      input.email,
      {
        redirectTo: input.redirectTo,
      }
    );

    if (error) {
      return { data: null, error };
    }

    return { data: parseAuthUser(data.user), error: null };
  }
}

function parseAuthUser(user: SupabaseAuthUser): AuthUser {
  assert(user.email, "User email is required");

  return {
    id: user.id,
    email: user.email,
    emailConfirmedAt: user.email_confirmed_at,
  };
}

function parseSession(session: SupabaseSession): Session {
  return {
    accessToken: session.access_token,
    refreshToken: session.refresh_token,
    expiresIn: session.expires_in,
    expiresAt: session.expires_at,
    tokenType: session.token_type,
    user: parseAuthUser(session.user),
  };
}

type AuthUser = {
  id: string;
  email: string;
  emailConfirmedAt?: string;
};

type Session = {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  expiresAt?: number;
  tokenType: "bearer";
  user: AuthUser;
};

type SignUpInput = {
  email: string;
  password: string;
  redirectTo: string;
};

type SignInInput = {
  email: string;
  password: string;
};

type SendSignInOtpInput = {
  email: string;
  redirectTo: string;
};

type ResendInput = {
  email: string;
  redirectTo: string;
};

type UpdateUserInput = {
  password: string;
};

type VerifyOtpInput =
  | {
      email: string;
      token: string;
      type: "email" | "recovery";
    }
  | {
      tokenHash: string;
      type: "invite";
    };

type ExchangeCodeForSessionInput = {
  code: string;
};

type ResetPasswordForEmailInput = {
  email: string;
  redirectTo: string;
};

type SendInviteEmailInput = {
  email: string;
  redirectTo: string;
  data?: object;
};

type WithErrorResponse<T> =
  | {
      data: T;
      error: null;
    }
  | {
      data: null;
      error: Error;
    };
