import {
  MemberRole as PrismaMemberRole,
  MemberStatus as PrismaMemberStatus,
  OppMerchantComplianceStatus as PrismaOppMerchantComplianceStatus,
  OppMerchantStatus as PrismaOppMerchantStatus,
  OrganizationStatus as PrismaOrganizationStatus,
  Prisma,
  PrismaClient,
} from "@prisma/client";

import {
  Member,
  MemberRole,
  MemberStatus,
  OppMerchantComplianceStatus,
  OppMerchantStatus,
  Organization,
  OrganizationStatus,
} from "@/domain/organization";

export class PrismaOrganizationRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: string): Promise<Organization | null> {
    const org = await this.prisma.organization.findUnique({
      where: { id },
      include: {
        members: true,
      },
    });

    if (!org) {
      return null;
    }

    return {
      id: org.id,
      name: org.name,
      status: prismaOrganizationStatusToDomainMap[org.status],
      oppMerchant:
        org.oppMerchantUid != null &&
        org.oppMerchantStatus != null &&
        org.oppMerchantComplianceStatus != null
          ? {
              uid: org.oppMerchantUid,
              status: prismaOppMerchantStatusToDomainMap[org.oppMerchantStatus],
              complianceStatus:
                prismaOppMerchantComplianceStatusToDomainMap[
                  org.oppMerchantComplianceStatus
                ],
            }
          : null,
      members: org.members.map((m): Member => {
        if (m.status === MemberStatus.INVITED) {
          return {
            userId: m.userId,
            invitedBy: m.invitedBy,
            invitedAt: m.invitedAt,
            role: prismaMemberRoleToDomainMap[m.role],
            status: MemberStatus.INVITED,
          };
        }

        if (m.status === MemberStatus.SUSPENDED) {
          return {
            userId: m.userId,
            invitedBy: m.invitedBy,
            invitedAt: m.invitedAt,
            joinedAt: m.joinedAt!,
            suspendedAt: m.suspendedAt!,
            suspendedBy: m.suspendedBy!,
            role: prismaMemberRoleToDomainMap[m.role],
            status: MemberStatus.SUSPENDED,
          };
        }

        return {
          userId: m.userId,
          invitedBy: m.invitedBy,
          invitedAt: m.invitedAt,
          joinedAt: m.joinedAt!,
          role: prismaMemberRoleToDomainMap[m.role],
          status: MemberStatus.ACTIVE,
        };
      }),
    };
  }

  async add(organization: Organization): Promise<void> {
    await this.prisma.organization.create({
      data: this.toPrismaRow(organization),
    });

    await this.prisma.member.createMany({
      data: organization.members.map((member) =>
        this.memberToPrismaRow(organization, member)
      ),
    });
  }

  async update(
    previousOrganization: Organization,
    organization: Organization
  ): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      const previousOrgRow = this.toPrismaRow(previousOrganization);
      const currentOrgRow = this.toPrismaRow(organization);
      const orgDiff = diff(previousOrgRow, currentOrgRow);

      if (Object.keys(orgDiff).length > 0) {
        await tx.organization.update({
          where: { id: organization.id },
          data: orgDiff,
        });
      }

      const previousMembers = new Map(
        previousOrganization.members.map((m) => [m.userId, m])
      );
      const currentMembers = new Map(
        organization.members.map((m) => [m.userId, m])
      );

      const membersToDelete = previousOrganization.members.filter(
        (m) => !currentMembers.has(m.userId)
      );

      if (membersToDelete.length > 0) {
        await tx.member.deleteMany({
          where: {
            organizationId: organization.id,
            userId: {
              in: membersToDelete.map((m) => m.userId),
            },
          },
        });
      }

      for (const member of organization.members) {
        const previousMember = previousMembers.get(member.userId);

        if (previousMember) {
          const previousMemberRow = this.memberToPrismaRow(
            organization,
            previousMember
          );
          const currentMemberRow = this.memberToPrismaRow(organization, member);
          const memberDiff = diff(previousMemberRow, currentMemberRow);

          if (Object.keys(memberDiff).length > 0) {
            await tx.member.updateMany({
              where: {
                organizationId: organization.id,
                userId: member.userId,
              },
              data: memberDiff,
            });
          }
        } else {
          await tx.member.create({
            data: this.memberToPrismaRow(organization, member),
          });
        }
      }
    });
  }

  private toPrismaRow(
    organization: Organization
  ): Prisma.OrganizationUncheckedCreateInput {
    return {
      id: organization.id,
      name: organization.name,
      status: domainOrganizationStatusToPrismaMap[organization.status],
      oppMerchantUid: organization.oppMerchant?.uid ?? null,
      oppMerchantStatus: organization.oppMerchant
        ? domainOppMerchantStatusToPrismaMap[organization.oppMerchant.status]
        : null,
      oppMerchantComplianceStatus: organization.oppMerchant
        ? domainOppMerchantComplianceStatusToPrismaMap[
            organization.oppMerchant.complianceStatus
          ]
        : null,
    };
  }

  private memberToPrismaRow(
    organization: Organization,
    member: Member
  ): Prisma.MemberUncheckedCreateInput {
    return {
      organizationId: organization.id,
      userId: member.userId,
      invitedBy: member.invitedBy,
      invitedAt: member.invitedAt,
      joinedAt: member.status !== MemberStatus.INVITED ? member.joinedAt : null,
      suspendedAt:
        member.status === MemberStatus.SUSPENDED ? member.suspendedAt : null,
      suspendedBy:
        member.status === MemberStatus.SUSPENDED ? member.suspendedBy : null,
      role: domainMemberRoleToPrismaMap[member.role],
      status: domainMemberStatusToPrismaMap[member.status],
    };
  }
}

const prismaOrganizationStatusToDomainMap: Record<
  PrismaOrganizationStatus,
  OrganizationStatus
> = {
  [PrismaOrganizationStatus.ACTIVE]: OrganizationStatus.ACTIVE,
  [PrismaOrganizationStatus.SUSPENDED]: OrganizationStatus.SUSPENDED,
};

const domainOrganizationStatusToPrismaMap: Record<
  OrganizationStatus,
  PrismaOrganizationStatus
> = {
  [OrganizationStatus.ACTIVE]: PrismaOrganizationStatus.ACTIVE,
  [OrganizationStatus.SUSPENDED]: PrismaOrganizationStatus.SUSPENDED,
};

const domainMemberRoleToPrismaMap: Record<MemberRole, PrismaMemberRole> = {
  [MemberRole.BUILDER_ADMIN]: PrismaMemberRole.BUILDER_ADMIN,
  [MemberRole.BUILDER_USER]: PrismaMemberRole.BUILDER_USER,
};

const prismaMemberRoleToDomainMap: Record<PrismaMemberRole, MemberRole> = {
  [PrismaMemberRole.BUILDER_ADMIN]: MemberRole.BUILDER_ADMIN,
  [PrismaMemberRole.BUILDER_USER]: MemberRole.BUILDER_USER,
};

const domainMemberStatusToPrismaMap: Record<MemberStatus, PrismaMemberStatus> =
  {
    [MemberStatus.ACTIVE]: PrismaMemberStatus.ACTIVE,
    [MemberStatus.INVITED]: PrismaMemberStatus.INVITED,
    [MemberStatus.SUSPENDED]: PrismaMemberStatus.SUSPENDED,
  };

const prismaOppMerchantStatusToDomainMap: Record<
  PrismaOppMerchantStatus,
  OppMerchantStatus
> = {
  [PrismaOppMerchantStatus.NEW]: OppMerchantStatus.NEW,
  [PrismaOppMerchantStatus.PENDING]: OppMerchantStatus.PENDING,
  [PrismaOppMerchantStatus.LIVE]: OppMerchantStatus.LIVE,
  [PrismaOppMerchantStatus.SUSPENDED]: OppMerchantStatus.SUSPENDED,
  [PrismaOppMerchantStatus.TERMINATED]: OppMerchantStatus.TERMINATED,
  [PrismaOppMerchantStatus.BLOCKED]: OppMerchantStatus.BLOCKED,
  [PrismaOppMerchantStatus.DELETED]: OppMerchantStatus.DELETED,
};

const domainOppMerchantStatusToPrismaMap: Record<
  OppMerchantStatus,
  PrismaOppMerchantStatus
> = {
  [OppMerchantStatus.NEW]: PrismaOppMerchantStatus.NEW,
  [OppMerchantStatus.PENDING]: PrismaOppMerchantStatus.PENDING,
  [OppMerchantStatus.LIVE]: PrismaOppMerchantStatus.LIVE,
  [OppMerchantStatus.SUSPENDED]: PrismaOppMerchantStatus.SUSPENDED,
  [OppMerchantStatus.TERMINATED]: PrismaOppMerchantStatus.TERMINATED,
  [OppMerchantStatus.BLOCKED]: PrismaOppMerchantStatus.BLOCKED,
  [OppMerchantStatus.DELETED]: PrismaOppMerchantStatus.DELETED,
};

const prismaOppMerchantComplianceStatusToDomainMap: Record<
  PrismaOppMerchantComplianceStatus,
  OppMerchantComplianceStatus
> = {
  [PrismaOppMerchantComplianceStatus.UNVERIFIED]:
    OppMerchantComplianceStatus.UNVERIFIED,
  [PrismaOppMerchantComplianceStatus.PENDING]:
    OppMerchantComplianceStatus.PENDING,
  [PrismaOppMerchantComplianceStatus.VERIFIED]:
    OppMerchantComplianceStatus.VERIFIED,
};

const domainOppMerchantComplianceStatusToPrismaMap: Record<
  OppMerchantComplianceStatus,
  PrismaOppMerchantComplianceStatus
> = {
  [OppMerchantComplianceStatus.UNVERIFIED]:
    PrismaOppMerchantComplianceStatus.UNVERIFIED,
  [OppMerchantComplianceStatus.PENDING]:
    PrismaOppMerchantComplianceStatus.PENDING,
  [OppMerchantComplianceStatus.VERIFIED]:
    PrismaOppMerchantComplianceStatus.VERIFIED,
};

/* eslint-disable @typescript-eslint/no-explicit-any */
function diff<T extends Record<string, any>>(
  previous: T,
  current: T
): Partial<T> {
  const changes: Partial<T> = {};

  for (const key in current) {
    const prevValue: any = previous[key];
    const currValue: any = current[key];

    // Compare values, handle Date objects specially
    if (prevValue instanceof Date && currValue instanceof Date) {
      if (prevValue.getTime() !== currValue.getTime()) {
        changes[key] = currValue as any;
      }
    } else if (prevValue !== currValue) {
      changes[key] = currValue;
    }
  }

  return changes;
}
/* eslint-enable @typescript-eslint/no-explicit-any */
