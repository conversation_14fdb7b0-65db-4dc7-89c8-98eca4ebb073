import {
  Prisma,
  PrismaClient,
  UserStatus as PrismaUserStatus,
} from "@prisma/client";

import { User, UserStatus } from "@/domain/user";

export class PrismaUserRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: string): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      return null;
    }

    if (user.status === PrismaUserStatus.WAITING_ONBOARDING) {
      return {
        id: user.id,
        email: user.email,
        name: user.name!,
        acceptedTermsAndConditionsAt: user.acceptedTermsAndConditionsAt,
        status: UserStatus.WAITING_ONBOARDING,
      };
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name!,
      onboardedAt: user.onboardedAt!,
      acceptedTermsAndConditionsAt: user.acceptedTermsAndConditionsAt,
      status: UserStatus.ACTIVE,
    };
  }

  async add(user: User): Promise<void> {
    await this.prisma.user.create({
      data: this.toPrismaRow(user),
    });
  }

  async update(previousUser: User, user: User): Promise<void> {
    const previousRow = this.toPrismaRow(previousUser);
    const currentRow = this.toPrismaRow(user);
    const data = diff(previousRow, currentRow);

    if (Object.keys(data).length > 0) {
      await this.prisma.user.update({
        where: { id: user.id },
        data,
      });
    }
  }

  private toPrismaRow(user: User): Prisma.UserUncheckedCreateInput {
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      status: domainUserStatusToPrismaMap[user.status],
      onboardedAt: user.status === UserStatus.ACTIVE ? user.onboardedAt : null,
      acceptedTermsAndConditionsAt: user.acceptedTermsAndConditionsAt,
    };
  }
}

const domainUserStatusToPrismaMap: Record<UserStatus, PrismaUserStatus> = {
  [UserStatus.WAITING_ONBOARDING]: PrismaUserStatus.WAITING_ONBOARDING,
  [UserStatus.ACTIVE]: PrismaUserStatus.ACTIVE,
};

/* eslint-disable @typescript-eslint/no-explicit-any */
function diff<T extends Record<string, any>>(
  previous: T,
  current: T
): Partial<T> {
  const changes: Partial<T> = {};

  for (const key in current) {
    const prevValue: any = previous[key];
    const currValue: any = current[key];

    // Compare values, handle Date objects specially
    if (prevValue instanceof Date && currValue instanceof Date) {
      if (prevValue.getTime() !== currValue.getTime()) {
        changes[key] = currValue as any;
      }
    } else if (prevValue !== currValue) {
      changes[key] = currValue;
    }
  }

  return changes;
}
/* eslint-enable @typescript-eslint/no-explicit-any */
