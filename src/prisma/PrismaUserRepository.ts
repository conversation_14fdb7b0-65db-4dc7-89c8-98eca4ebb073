import type { PrismaClient, User as PrismaUser } from "@prisma/client";

import type { User } from "../domain/user";

export default class PrismaUserRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: string): Promise<User | null> {
    const row = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!row) {
      return null;
    }

    return mapToDomain(row);
  }

  async create(user: User): Promise<void> {
    await this.prisma.user.create({
      data: mapToRow(user),
    });
  }

  async update(prevUser: User, user: User): Promise<void> {
    const prevRow = mapToRow(prevUser);
    const row = mapToRow(user);
    const changes = diff(prevRow, row);

    if (Object.keys(changes).length === 0) {
      return;
    }

    await this.prisma.user.update({
      where: { id: prevUser.id },
      data: changes,
    });
  }
}

type UserRow = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
  createdAt: Date;
  updatedAt: Date;
};

function mapToRow(user: User): UserRow {
  return {
    id: user.id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.phoneNumber,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
}

function mapToDomain(row: PrismaUser): User {
  return {
    id: row.id,
    email: row.email,
    firstName: row.firstName,
    lastName: row.lastName,
    phoneNumber: row.phoneNumber,
    createdAt: row.createdAt,
    updatedAt: row.updatedAt,
  };
}

function diff<T extends Record<string, unknown>>(prev: T, curr: T): Partial<T> {
  const result: Partial<T> = {};
  for (const key of Object.keys(curr) as (keyof T)[]) {
    if (!isEqual(prev[key], curr[key])) {
      result[key] = curr[key];
    }
  }
  return result;
}

function isEqual(a: unknown, b: unknown): boolean {
  if (a === b) return true;
  if (a instanceof Date && b instanceof Date)
    return a.getTime() === b.getTime();
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    return a.every((val, i) => isEqual(val, b[i]));
  }
  if (a && b && typeof a === "object" && typeof b === "object") {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;
    return keysA.every((key) =>
      isEqual(
        (a as Record<string, unknown>)[key],
        (b as Record<string, unknown>)[key]
      )
    );
  }
  return false;
}
