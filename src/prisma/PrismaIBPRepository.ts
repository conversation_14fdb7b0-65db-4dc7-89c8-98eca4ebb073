import type {
  AddOn as PrismaAddOn,
  Appointment as PrismaAppointment,
  AppointmentAddOn as PrismaAppointmentAddOn,
  AppointmentReschedule as PrismaAppointmentReschedule,
  AppointmentStatus as PrismaAppointmentStatus,
  CancellationType as PrismaCancellationType,
  DayAvailability as PrismaDayAvailability,
  DayOfWeek as PrismaDayOfWeek,
  FeeType as PrismaFeeType,
  IBP as PrismaIBP,
  OnboardingStep as PrismaOnboardingStep,
  Prisma,
  PrismaClient,
  Service as PrismaService,
  Specialty as PrismaSpecialty,
  TimeOff as PrismaTimeOff,
  TimeSlot as PrismaTimeSlot,
  VerificationStatus as PrismaVerificationStatus,
} from "@prisma/client";

import type {
  AddOn,
  Appointment,
  AppointmentReschedule,
  CancellationFee,
  DayAvailability,
  IBP,
  SelectedAddOn,
  Service,
  TimeOff,
  WeeklyAvailability,
} from "../domain/ibp";
import {
  AppointmentStatus,
  CancellationType,
  DayOfWeek,
  FeeType,
  OnboardingStep,
  Specialty,
  VerificationStatus,
} from "../domain/ibp";

type PrismaIBPWithRelations = PrismaIBP & {
  services: (PrismaService & { addOns: PrismaAddOn[] })[];
  dayAvailabilities: (PrismaDayAvailability & {
    timeSlots: PrismaTimeSlot[];
  })[];
  timeOffs: PrismaTimeOff[];
  appointments: (PrismaAppointment & {
    selectedAddOns: PrismaAppointmentAddOn[];
    reschedules: PrismaAppointmentReschedule[];
  })[];
};

export default class PrismaIBPRepository {
  constructor(private prisma: PrismaClient) {}

  async findByUserId(userId: string): Promise<IBP | null> {
    const ibp = await this.prisma.iBP.findUnique({
      where: { userId },
      include: {
        services: {
          include: { addOns: true },
        },
        dayAvailabilities: {
          include: { timeSlots: true },
        },
        timeOffs: true,
        appointments: {
          include: {
            selectedAddOns: true,
            reschedules: true,
          },
        },
      },
    });

    if (!ibp) {
      return null;
    }

    return mapToDomain(ibp);
  }

  async create(ibp: IBP): Promise<void> {
    await this.prisma.iBP.create({
      data: {
        userId: ibp.userId,
        businessName: ibp.businessName,
        addressStreet: ibp.address.street,
        addressCity: ibp.address.city,
        addressState: ibp.address.state,
        addressZipCode: ibp.address.zipCode,
        specialties: ibp.specialties.map(mapSpecialtyToPrisma),
        licenseNumber: ibp.licenseNumber,
        verificationStatus: mapVerificationStatusToPrisma(
          ibp.verificationStatus
        ),
        onboardingCompletedSteps: ibp.onboarding.completedSteps.map(
          mapOnboardingStepToPrisma
        ),
        onboardingIsComplete: ibp.onboarding.isComplete,
        freeCancellationWindowHours:
          ibp.cancellationPolicy.freeCancellationWindowHours,
        lateCancellationFeeType: mapFeeTypeToPrisma(
          ibp.cancellationPolicy.lateCancellationFee.type
        ),
        lateCancellationFeeAmount: getCancellationFeeAmount(
          ibp.cancellationPolicy.lateCancellationFee
        ),
        noShowFeeType: mapFeeTypeToPrisma(
          ibp.cancellationPolicy.noShowFee.type
        ),
        noShowFeeAmount: getCancellationFeeAmount(
          ibp.cancellationPolicy.noShowFee
        ),
        createdAt: ibp.createdAt,
        updatedAt: ibp.updatedAt,
        services: {
          create: ibp.services.map((service) => ({
            id: service.id,
            name: service.name,
            description: service.description,
            category: mapSpecialtyToPrisma(service.category),
            durationMinutes: service.durationMinutes,
            priceCents: service.priceCents,
            isActive: service.isActive,
            createdAt: service.createdAt,
            updatedAt: service.updatedAt,
            addOns: {
              create: service.addOns.map((addOn) => ({
                id: addOn.id,
                name: addOn.name,
                description: addOn.description,
                durationMinutes: addOn.durationMinutes,
                priceCents: addOn.priceCents,
                isRequired: addOn.isRequired,
              })),
            },
          })),
        },
        dayAvailabilities: {
          create: ibp.availability.schedule.map((day) => ({
            dayOfWeek: mapDayOfWeekToPrisma(day.dayOfWeek),
            isAvailable: day.isAvailable,
            timeSlots: {
              create: day.timeSlots.map((slot) => ({
                startTime: slot.startTime,
                endTime: slot.endTime,
              })),
            },
          })),
        },
        timeOffs: {
          create: ibp.timeOffs.map((timeOff) => ({
            id: timeOff.id,
            startDate: timeOff.startDate,
            endDate: timeOff.endDate,
            reason: timeOff.reason,
            createdAt: timeOff.createdAt,
          })),
        },
        appointments: {
          create: ibp.appointments.map((appointment) => ({
            id: appointment.id,
            clientId: appointment.clientId,
            serviceId: appointment.serviceId,
            serviceName: appointment.serviceName,
            scheduledDate: appointment.scheduledDate,
            startTime: appointment.startTime,
            endTime: appointment.endTime,
            totalDurationMinutes: appointment.totalDurationMinutes,
            totalPriceCents: appointment.totalPriceCents,
            status: mapAppointmentStatusToPrisma(appointment.status),
            cancelledAt: appointment.cancelledAt,
            cancellationType: appointment.cancellationType
              ? mapCancellationTypeToPrisma(appointment.cancellationType)
              : null,
            createdAt: appointment.createdAt,
            updatedAt: appointment.updatedAt,
            selectedAddOns: {
              create: appointment.selectedAddOns.map((addOn) => ({
                addOnId: addOn.addOnId,
                name: addOn.name,
                priceCents: addOn.priceCents,
                durationMinutes: addOn.durationMinutes,
              })),
            },
            reschedules: {
              create: appointment.reschedules.map((reschedule) => ({
                previousDate: reschedule.previousDate,
                previousStartTime: reschedule.previousStartTime,
                rescheduledDate: reschedule.rescheduledDate,
                rescheduledStartTime: reschedule.rescheduledStartTime,
              })),
            },
          })),
        },
      },
    });
  }

  async update(prevIbp: IBP, ibp: IBP): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      const ibpChanges = diff(mapIBPToRow(prevIbp), mapIBPToRow(ibp));

      if (Object.keys(ibpChanges).length > 0) {
        await tx.iBP.update({
          where: { userId: prevIbp.userId },
          data: ibpChanges,
        });
      }

      const ibpId = ibp.userId;

      // Services
      const services = reconcile((s) => s.id, prevIbp.services, ibp.services);
      for (const s of services.deleted) {
        await tx.service.delete({ where: { id: s.id } });
      }
      for (const s of services.created) {
        await tx.service.create({
          data: {
            id: s.id,
            ibpId,
            name: s.name,
            description: s.description,
            category: mapSpecialtyToPrisma(s.category),
            durationMinutes: s.durationMinutes,
            priceCents: s.priceCents,
            isActive: s.isActive,
            createdAt: s.createdAt,
            updatedAt: s.updatedAt,
            addOns: {
              create: s.addOns.map((a) => ({
                id: a.id,
                name: a.name,
                description: a.description,
                durationMinutes: a.durationMinutes,
                priceCents: a.priceCents,
                isRequired: a.isRequired,
              })),
            },
          },
        });
      }
      for (const { prev, curr } of services.updated) {
        const changes = diff(mapServiceToRow(prev), mapServiceToRow(curr));
        if (Object.keys(changes).length > 0) {
          await tx.service.update({ where: { id: curr.id }, data: changes });
        }
        // AddOns
        const addOns = reconcile((a) => a.id, prev.addOns, curr.addOns);
        for (const a of addOns.deleted) {
          await tx.addOn.delete({ where: { id: a.id } });
        }
        for (const a of addOns.created) {
          await tx.addOn.create({
            data: {
              id: a.id,
              serviceId: curr.id,
              name: a.name,
              description: a.description,
              durationMinutes: a.durationMinutes,
              priceCents: a.priceCents,
              isRequired: a.isRequired,
            },
          });
        }
        for (const { prev: prevA, curr: currA } of addOns.updated) {
          const addOnChanges = diff(mapAddOnToRow(prevA), mapAddOnToRow(currA));
          if (Object.keys(addOnChanges).length > 0) {
            await tx.addOn.update({
              where: { id: currA.id },
              data: addOnChanges,
            });
          }
        }
      }

      // Availability (delete and recreate - simpler for nested structure)
      await tx.dayAvailability.deleteMany({ where: { ibpId } });
      for (const day of ibp.availability.schedule) {
        await tx.dayAvailability.create({
          data: {
            ibpId,
            dayOfWeek: mapDayOfWeekToPrisma(day.dayOfWeek),
            isAvailable: day.isAvailable,
            timeSlots: {
              create: day.timeSlots.map((slot) => ({
                startTime: slot.startTime,
                endTime: slot.endTime,
              })),
            },
          },
        });
      }

      // TimeOffs (immutable, no updates)
      const timeOffs = reconcile((t) => t.id, prevIbp.timeOffs, ibp.timeOffs);
      for (const t of timeOffs.deleted) {
        await tx.timeOff.delete({ where: { id: t.id } });
      }
      for (const t of timeOffs.created) {
        await tx.timeOff.create({
          data: {
            id: t.id,
            ibpId,
            startDate: t.startDate,
            endDate: t.endDate,
            reason: t.reason,
            createdAt: t.createdAt,
          },
        });
      }

      // Appointments
      const appointments = reconcile(
        (a) => a.id,
        prevIbp.appointments,
        ibp.appointments
      );
      for (const a of appointments.deleted) {
        await tx.appointment.delete({ where: { id: a.id } });
      }
      for (const a of appointments.created) {
        await tx.appointment.create({
          data: {
            id: a.id,
            ibpId,
            clientId: a.clientId,
            serviceId: a.serviceId,
            serviceName: a.serviceName,
            scheduledDate: a.scheduledDate,
            startTime: a.startTime,
            endTime: a.endTime,
            totalDurationMinutes: a.totalDurationMinutes,
            totalPriceCents: a.totalPriceCents,
            status: mapAppointmentStatusToPrisma(a.status),
            cancelledAt: a.cancelledAt,
            cancellationType: a.cancellationType
              ? mapCancellationTypeToPrisma(a.cancellationType)
              : null,
            createdAt: a.createdAt,
            updatedAt: a.updatedAt,
            selectedAddOns: {
              create: a.selectedAddOns.map((addOn) => ({
                addOnId: addOn.addOnId,
                name: addOn.name,
                priceCents: addOn.priceCents,
                durationMinutes: addOn.durationMinutes,
              })),
            },
            reschedules: {
              create: a.reschedules.map((r) => ({
                previousDate: r.previousDate,
                previousStartTime: r.previousStartTime,
                rescheduledDate: r.rescheduledDate,
                rescheduledStartTime: r.rescheduledStartTime,
              })),
            },
          },
        });
      }
      for (const { prev, curr } of appointments.updated) {
        const changes = diff(
          mapAppointmentToRow(prev),
          mapAppointmentToRow(curr)
        );
        if (Object.keys(changes).length > 0) {
          await tx.appointment.update({
            where: { id: curr.id },
            data: changes,
          });
        }
        // Reschedules (append only)
        const newReschedules = curr.reschedules.slice(prev.reschedules.length);
        for (const r of newReschedules) {
          await tx.appointmentReschedule.create({
            data: {
              appointmentId: curr.id,
              previousDate: r.previousDate,
              previousStartTime: r.previousStartTime,
              rescheduledDate: r.rescheduledDate,
              rescheduledStartTime: r.rescheduledStartTime,
            },
          });
        }
      }
    });
  }
}

// Diff helpers

function mapIBPToRow(ibp: IBP): Prisma.IBPUncheckedUpdateInput {
  return {
    businessName: ibp.businessName,
    addressStreet: ibp.address.street,
    addressCity: ibp.address.city,
    addressState: ibp.address.state,
    addressZipCode: ibp.address.zipCode,
    specialties: ibp.specialties.map(mapSpecialtyToPrisma),
    licenseNumber: ibp.licenseNumber,
    verificationStatus: mapVerificationStatusToPrisma(ibp.verificationStatus),
    onboardingCompletedSteps: ibp.onboarding.completedSteps.map(
      mapOnboardingStepToPrisma
    ),
    onboardingIsComplete: ibp.onboarding.isComplete,
    freeCancellationWindowHours:
      ibp.cancellationPolicy.freeCancellationWindowHours,
    lateCancellationFeeType: mapFeeTypeToPrisma(
      ibp.cancellationPolicy.lateCancellationFee.type
    ),
    lateCancellationFeeAmount: getCancellationFeeAmount(
      ibp.cancellationPolicy.lateCancellationFee
    ),
    noShowFeeType: mapFeeTypeToPrisma(ibp.cancellationPolicy.noShowFee.type),
    noShowFeeAmount: getCancellationFeeAmount(ibp.cancellationPolicy.noShowFee),
    updatedAt: ibp.updatedAt,
  };
}

function diff<T extends Record<string, unknown>>(prev: T, curr: T): Partial<T> {
  const result: Partial<T> = {};
  for (const key of Object.keys(curr) as (keyof T)[]) {
    if (!isEqual(prev[key], curr[key])) {
      result[key] = curr[key];
    }
  }
  return result;
}

function isEqual(a: unknown, b: unknown): boolean {
  if (a === b) return true;
  if (a instanceof Date && b instanceof Date)
    return a.getTime() === b.getTime();
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    return a.every((val, i) => isEqual(val, b[i]));
  }
  if (a && b && typeof a === "object" && typeof b === "object") {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;
    return keysA.every((key) =>
      isEqual(
        (a as Record<string, unknown>)[key],
        (b as Record<string, unknown>)[key]
      )
    );
  }
  return false;
}

// Helpers

function reconcile<T, K>(
  id: (t: T) => K,
  prev: T[],
  curr: T[]
): { created: T[]; updated: { prev: T; curr: T }[]; deleted: T[] } {
  const prevMap = new Map(prev.map((p) => [id(p), p]));
  const currMap = new Map(curr.map((p) => [id(p), p]));

  return {
    created: curr.filter((value) => !prevMap.has(id(value))),
    updated: curr
      .filter(
        (value) =>
          prevMap.has(id(value)) && !isEqual(value, prevMap.get(id(value))!)
      )
      .map((value) => ({ curr: value, prev: prevMap.get(id(value))! })),
    deleted: prev.filter((value) => !currMap.has(id(value))),
  };
}

function mapServiceToRow(service: Service): Prisma.ServiceUncheckedUpdateInput {
  return {
    name: service.name,
    description: service.description,
    category: mapSpecialtyToPrisma(service.category),
    durationMinutes: service.durationMinutes,
    priceCents: service.priceCents,
    isActive: service.isActive,
    updatedAt: service.updatedAt,
  };
}

function mapAddOnToRow(addOn: AddOn): Prisma.AddOnUncheckedUpdateInput {
  return {
    name: addOn.name,
    description: addOn.description,
    durationMinutes: addOn.durationMinutes,
    priceCents: addOn.priceCents,
    isRequired: addOn.isRequired,
  };
}

function mapAppointmentToRow(
  appointment: Appointment
): Prisma.AppointmentUncheckedUpdateInput {
  return {
    scheduledDate: appointment.scheduledDate,
    startTime: appointment.startTime,
    endTime: appointment.endTime,
    status: mapAppointmentStatusToPrisma(appointment.status),
    cancelledAt: appointment.cancelledAt,
    cancellationType: appointment.cancellationType
      ? mapCancellationTypeToPrisma(appointment.cancellationType)
      : null,
    updatedAt: appointment.updatedAt,
  };
}

// Mapping functions: Domain -> Prisma

function mapVerificationStatusToPrisma(
  status: VerificationStatus
): PrismaVerificationStatus {
  const map: Record<VerificationStatus, PrismaVerificationStatus> = {
    [VerificationStatus.PendingOnboarding]: "PENDING_ONBOARDING",
    [VerificationStatus.PendingVerification]: "PENDING_VERIFICATION",
    [VerificationStatus.Verified]: "VERIFIED",
    [VerificationStatus.Rejected]: "REJECTED",
  };
  return map[status];
}

function mapSpecialtyToPrisma(specialty: Specialty): PrismaSpecialty {
  const map: Record<Specialty, PrismaSpecialty> = {
    [Specialty.Hair]: "HAIR",
    [Specialty.Nails]: "NAILS",
    [Specialty.Braids]: "BRAIDS",
    [Specialty.Barbering]: "BARBERING",
    [Specialty.Skin]: "SKIN",
    [Specialty.Brows]: "BROWS",
    [Specialty.Lashes]: "LASHES",
    [Specialty.Makeup]: "MAKEUP",
  };
  return map[specialty];
}

function mapOnboardingStepToPrisma(step: OnboardingStep): PrismaOnboardingStep {
  const map: Record<OnboardingStep, PrismaOnboardingStep> = {
    [OnboardingStep.Introduction]: "INTRODUCTION",
    [OnboardingStep.PersonalInfo]: "PERSONAL_INFO",
    [OnboardingStep.Specialties]: "SPECIALTIES",
    [OnboardingStep.License]: "LICENSE",
    [OnboardingStep.Review]: "REVIEW",
  };
  return map[step];
}

function mapDayOfWeekToPrisma(day: DayOfWeek): PrismaDayOfWeek {
  const map: Record<DayOfWeek, PrismaDayOfWeek> = {
    [DayOfWeek.Monday]: "MONDAY",
    [DayOfWeek.Tuesday]: "TUESDAY",
    [DayOfWeek.Wednesday]: "WEDNESDAY",
    [DayOfWeek.Thursday]: "THURSDAY",
    [DayOfWeek.Friday]: "FRIDAY",
    [DayOfWeek.Saturday]: "SATURDAY",
    [DayOfWeek.Sunday]: "SUNDAY",
  };
  return map[day];
}

function mapAppointmentStatusToPrisma(
  status: AppointmentStatus
): PrismaAppointmentStatus {
  const map: Record<AppointmentStatus, PrismaAppointmentStatus> = {
    [AppointmentStatus.Pending]: "PENDING",
    [AppointmentStatus.Scheduled]: "SCHEDULED",
    [AppointmentStatus.Completed]: "COMPLETED",
    [AppointmentStatus.Cancelled]: "CANCELLED",
    [AppointmentStatus.NoShow]: "NO_SHOW",
  };
  return map[status];
}

function mapCancellationTypeToPrisma(
  type: CancellationType
): PrismaCancellationType {
  const map: Record<CancellationType, PrismaCancellationType> = {
    [CancellationType.Free]: "FREE",
    [CancellationType.Late]: "LATE",
    [CancellationType.NoShow]: "NO_SHOW",
  };
  return map[type];
}

function mapFeeTypeToPrisma(type: FeeType): PrismaFeeType {
  const map: Record<FeeType, PrismaFeeType> = {
    [FeeType.Flat]: "FLAT",
    [FeeType.Percentage]: "PERCENTAGE",
  };
  return map[type];
}

function getCancellationFeeAmount(fee: CancellationFee): number {
  return fee.type === FeeType.Flat ? fee.amountCents : fee.percentage;
}

// Mapping functions: Prisma -> Domain

function mapToDomain(prisma: PrismaIBPWithRelations): IBP {
  return {
    userId: prisma.userId,
    businessName: prisma.businessName,
    address: {
      street: prisma.addressStreet,
      city: prisma.addressCity,
      state: prisma.addressState,
      zipCode: prisma.addressZipCode,
    },
    specialties: prisma.specialties.map(mapSpecialtyToDomain) as [
      Specialty,
      ...Specialty[],
    ],
    licenseNumber: prisma.licenseNumber,
    verificationStatus: mapVerificationStatusToDomain(
      prisma.verificationStatus
    ),
    onboarding: {
      completedSteps: prisma.onboardingCompletedSteps.map(
        mapOnboardingStepToDomain
      ),
      isComplete: prisma.onboardingIsComplete,
    },
    cancellationPolicy: {
      freeCancellationWindowHours: prisma.freeCancellationWindowHours,
      lateCancellationFee: mapCancellationFeeToDomain(
        prisma.lateCancellationFeeType,
        prisma.lateCancellationFeeAmount
      ),
      noShowFee: mapCancellationFeeToDomain(
        prisma.noShowFeeType,
        prisma.noShowFeeAmount
      ),
    },
    services: prisma.services.map(mapServiceToDomain),
    availability: mapAvailabilityToDomain(prisma.dayAvailabilities),
    timeOffs: prisma.timeOffs.map(mapTimeOffToDomain),
    appointments: prisma.appointments.map(mapAppointmentToDomain),
    createdAt: prisma.createdAt,
    updatedAt: prisma.updatedAt,
  };
}

function mapVerificationStatusToDomain(
  status: PrismaVerificationStatus
): VerificationStatus {
  const map: Record<PrismaVerificationStatus, VerificationStatus> = {
    PENDING_ONBOARDING: VerificationStatus.PendingOnboarding,
    PENDING_VERIFICATION: VerificationStatus.PendingVerification,
    VERIFIED: VerificationStatus.Verified,
    REJECTED: VerificationStatus.Rejected,
  };
  return map[status];
}

function mapSpecialtyToDomain(specialty: PrismaSpecialty): Specialty {
  const map: Record<PrismaSpecialty, Specialty> = {
    HAIR: Specialty.Hair,
    NAILS: Specialty.Nails,
    BRAIDS: Specialty.Braids,
    BARBERING: Specialty.Barbering,
    SKIN: Specialty.Skin,
    BROWS: Specialty.Brows,
    LASHES: Specialty.Lashes,
    MAKEUP: Specialty.Makeup,
  };
  return map[specialty];
}

function mapOnboardingStepToDomain(step: PrismaOnboardingStep): OnboardingStep {
  const map: Record<PrismaOnboardingStep, OnboardingStep> = {
    INTRODUCTION: OnboardingStep.Introduction,
    PERSONAL_INFO: OnboardingStep.PersonalInfo,
    SPECIALTIES: OnboardingStep.Specialties,
    LICENSE: OnboardingStep.License,
    REVIEW: OnboardingStep.Review,
  };
  return map[step];
}

function mapDayOfWeekToDomain(day: PrismaDayOfWeek): DayOfWeek {
  const map: Record<PrismaDayOfWeek, DayOfWeek> = {
    MONDAY: DayOfWeek.Monday,
    TUESDAY: DayOfWeek.Tuesday,
    WEDNESDAY: DayOfWeek.Wednesday,
    THURSDAY: DayOfWeek.Thursday,
    FRIDAY: DayOfWeek.Friday,
    SATURDAY: DayOfWeek.Saturday,
    SUNDAY: DayOfWeek.Sunday,
  };
  return map[day];
}

function mapAppointmentStatusToDomain(
  status: PrismaAppointmentStatus
): AppointmentStatus {
  const map: Record<PrismaAppointmentStatus, AppointmentStatus> = {
    PENDING: AppointmentStatus.Pending,
    SCHEDULED: AppointmentStatus.Scheduled,
    COMPLETED: AppointmentStatus.Completed,
    CANCELLED: AppointmentStatus.Cancelled,
    NO_SHOW: AppointmentStatus.NoShow,
  };
  return map[status];
}

function mapCancellationTypeToDomain(
  type: PrismaCancellationType
): CancellationType {
  const map: Record<PrismaCancellationType, CancellationType> = {
    FREE: CancellationType.Free,
    LATE: CancellationType.Late,
    NO_SHOW: CancellationType.NoShow,
  };
  return map[type];
}

function mapCancellationFeeToDomain(
  type: PrismaFeeType,
  amount: number
): CancellationFee {
  if (type === "FLAT") {
    return { type: FeeType.Flat, amountCents: amount };
  }
  return { type: FeeType.Percentage, percentage: amount };
}

function mapServiceToDomain(
  prisma: PrismaService & { addOns: PrismaAddOn[] }
): Service {
  return {
    id: prisma.id,
    name: prisma.name,
    description: prisma.description,
    category: mapSpecialtyToDomain(prisma.category),
    durationMinutes: prisma.durationMinutes,
    priceCents: prisma.priceCents,
    addOns: prisma.addOns.map(mapAddOnToDomain),
    isActive: prisma.isActive,
    createdAt: prisma.createdAt,
    updatedAt: prisma.updatedAt,
  };
}

function mapAddOnToDomain(prisma: PrismaAddOn): AddOn {
  return {
    id: prisma.id,
    name: prisma.name,
    description: prisma.description,
    durationMinutes: prisma.durationMinutes,
    priceCents: prisma.priceCents,
    isRequired: prisma.isRequired,
  };
}

function mapAvailabilityToDomain(
  prisma: (PrismaDayAvailability & { timeSlots: PrismaTimeSlot[] })[]
): WeeklyAvailability {
  const schedule = prisma.map((day) => ({
    dayOfWeek: mapDayOfWeekToDomain(day.dayOfWeek),
    isAvailable: day.isAvailable,
    timeSlots: day.timeSlots.map((slot) => ({
      startTime: slot.startTime,
      endTime: slot.endTime,
    })),
  }));

  return { schedule: schedule as [DayAvailability, ...DayAvailability[]] };
}

function mapTimeOffToDomain(prisma: PrismaTimeOff): TimeOff {
  return {
    id: prisma.id,
    startDate: prisma.startDate,
    endDate: prisma.endDate,
    reason: prisma.reason,
    createdAt: prisma.createdAt,
  };
}

function mapAppointmentToDomain(
  prisma: PrismaAppointment & {
    selectedAddOns: PrismaAppointmentAddOn[];
    reschedules: PrismaAppointmentReschedule[];
  }
): Appointment {
  return {
    id: prisma.id,
    clientId: prisma.clientId,
    serviceId: prisma.serviceId,
    serviceName: prisma.serviceName,
    selectedAddOns: prisma.selectedAddOns.map(mapSelectedAddOnToDomain),
    scheduledDate: prisma.scheduledDate,
    startTime: prisma.startTime,
    endTime: prisma.endTime,
    totalDurationMinutes: prisma.totalDurationMinutes,
    totalPriceCents: prisma.totalPriceCents,
    status: mapAppointmentStatusToDomain(prisma.status),
    cancelledAt: prisma.cancelledAt,
    cancellationType: prisma.cancellationType
      ? mapCancellationTypeToDomain(prisma.cancellationType)
      : null,
    reschedules: prisma.reschedules.map(mapRescheduleToDomain),
    createdAt: prisma.createdAt,
    updatedAt: prisma.updatedAt,
  };
}

function mapSelectedAddOnToDomain(
  prisma: PrismaAppointmentAddOn
): SelectedAddOn {
  return {
    addOnId: prisma.addOnId,
    name: prisma.name,
    priceCents: prisma.priceCents,
    durationMinutes: prisma.durationMinutes,
  };
}

function mapRescheduleToDomain(
  prisma: PrismaAppointmentReschedule
): AppointmentReschedule {
  return {
    previousDate: prisma.previousDate,
    previousStartTime: prisma.previousStartTime,
    rescheduledDate: prisma.rescheduledDate,
    rescheduledStartTime: prisma.rescheduledStartTime,
  };
}
