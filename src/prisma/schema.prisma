generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
}

model User {
  id                          String     @id
  email                       String     @unique
  name                        String?
  status                      UserStatus
  onboardedAt                 DateTime?
  acceptedTermsAndConditionsAt DateTime?
  createdAt                   DateTime   @default(now())
  updatedAt                   DateTime   @updatedAt

  memberships Member[]
}

enum UserStatus {
  WAITING_ONBOARDING
  ACTIVE
}

model Organization {
  id                          String                          @id
  name                        String
  status                      OrganizationStatus
  oppMerchantUid              String?                         @unique
  oppMerchantStatus           OppMerchantStatus?
  oppMerchantComplianceStatus OppMerchantComplianceStatus?
  createdAt                   DateTime                        @default(now())
  updatedAt                   DateTime                        @updatedAt

  members Member[]
}

model Member {
  userId         String
  organizationId String
  invitedBy      String?
  invitedAt      DateTime
  joinedAt       DateTime?
  suspendedAt    DateTime?
  suspendedBy    String?
  role           MemberRole
  status         MemberStatus
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@id([userId, organizationId])
  @@index([userId])
  @@index([organizationId])
}

enum OrganizationStatus {
  ACTIVE
  SUSPENDED
}

enum MemberRole {
  BUILDER_ADMIN
  BUILDER_USER
}

enum MemberStatus {
  ACTIVE
  INVITED
  SUSPENDED
}

enum OppMerchantStatus {
  NEW
  PENDING
  LIVE
  SUSPENDED
  TERMINATED
  BLOCKED
  DELETED
}

enum OppMerchantComplianceStatus {
  UNVERIFIED
  PENDING
  VERIFIED
}
