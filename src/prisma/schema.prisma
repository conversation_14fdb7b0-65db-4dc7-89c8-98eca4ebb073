generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
}

enum VerificationStatus {
  PENDING_ONBOARDING
  PENDING_VERIFICATION
  VERIFIED
  REJECTED
}

enum Specialty {
  HAIR
  NAILS
  BRAIDS
  BARBERING
  SKIN
  BROWS
  LASHES
  MAKEUP
}

enum OnboardingStep {
  INTRODUCTION
  PERSONAL_INFO
  SPECIALTIES
  LICENSE
  REVIEW
}

enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

enum AppointmentStatus {
  PENDING
  SCHEDULED
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum CancellationType {
  FREE
  LATE
  NO_SHOW
}

enum FeeType {
  FLAT
  PERCENTAGE
}

model User {
  id          String   @id
  email       String   @unique
  firstName   String
  lastName    String
  phoneNumber String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  ibp          IBP?
  appointments Appointment[]
}

model IBP {
  userId                      String             @id
  user                        User               @relation(fields: [userId], references: [id])
  businessName                String?
  addressStreet               String
  addressCity                 String
  addressState                String
  addressZipCode              String
  specialties                 Specialty[]
  licenseNumber               String
  verificationStatus          VerificationStatus @default(PENDING_ONBOARDING)
  onboardingCompletedSteps    OnboardingStep[]
  onboardingIsComplete        Boolean            @default(false)
  freeCancellationWindowHours Int                @default(24)
  lateCancellationFeeType     FeeType            @default(FLAT)
  lateCancellationFeeAmount   Int                @default(0) // cents or percentage based on type
  noShowFeeType               FeeType            @default(FLAT)
  noShowFeeAmount             Int                @default(0) // cents or percentage based on type
  createdAt                   DateTime           @default(now())
  updatedAt                   DateTime           @updatedAt

  services         Service[]
  dayAvailabilities DayAvailability[]
  timeOffs         TimeOff[]
  appointments     Appointment[]
}

model Service {
  id              String    @id @default(uuid())
  ibpId           String
  ibp             IBP       @relation(fields: [ibpId], references: [userId], onDelete: Cascade)
  name            String
  description     String?
  category        Specialty
  durationMinutes Int
  priceCents      Int
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  addOns       AddOn[]
  appointments Appointment[]
}

model AddOn {
  id              String  @id @default(uuid())
  serviceId       String
  service         Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  name            String
  description     String?
  durationMinutes Int
  priceCents      Int
  isRequired      Boolean @default(false)

  appointmentAddOns AppointmentAddOn[]
}

model DayAvailability {
  id          String    @id @default(uuid())
  ibpId       String
  ibp         IBP       @relation(fields: [ibpId], references: [userId], onDelete: Cascade)
  dayOfWeek   DayOfWeek
  isAvailable Boolean   @default(false)

  timeSlots TimeSlot[]

  @@unique([ibpId, dayOfWeek])
}

model TimeSlot {
  id                String          @id @default(uuid())
  dayAvailabilityId String
  dayAvailability   DayAvailability @relation(fields: [dayAvailabilityId], references: [id], onDelete: Cascade)
  startTime         String // HH:mm format
  endTime           String // HH:mm format
}

model TimeOff {
  id        String   @id @default(uuid())
  ibpId     String
  ibp       IBP      @relation(fields: [ibpId], references: [userId], onDelete: Cascade)
  startDate DateTime
  endDate   DateTime
  reason    String?
  createdAt DateTime @default(now())
}

model Appointment {
  id                   String            @id @default(uuid())
  ibpId                String
  ibp                  IBP               @relation(fields: [ibpId], references: [userId])
  clientId             String
  client               User              @relation(fields: [clientId], references: [id])
  serviceId            String
  service              Service           @relation(fields: [serviceId], references: [id])
  serviceName          String // Denormalized for history
  scheduledDate        DateTime
  startTime            String // HH:mm format
  endTime              String // HH:mm format
  totalDurationMinutes Int
  totalPriceCents      Int
  status               AppointmentStatus @default(PENDING)
  cancelledAt          DateTime?
  cancellationType     CancellationType?
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt

  selectedAddOns AppointmentAddOn[]
  reschedules    AppointmentReschedule[]
}

model AppointmentAddOn {
  id              String      @id @default(uuid())
  appointmentId   String
  appointment     Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  addOnId         String
  addOn           AddOn       @relation(fields: [addOnId], references: [id])
  name            String // Denormalized for history
  priceCents      Int // Denormalized for history
  durationMinutes Int // Denormalized for history
}

model AppointmentReschedule {
  id                    String      @id @default(uuid())
  appointmentId         String
  appointment           Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  previousDate          DateTime
  previousStartTime     String
  rescheduledDate       DateTime
  rescheduledStartTime  String
  createdAt             DateTime    @default(now())
}
