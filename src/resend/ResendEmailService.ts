import assert from "node:assert";
import { Resend } from "resend";

export default class ResendEmailService {
  private resend: Resend | null = null;

  constructor(apiKey: string) {
    if (apiKey) {
      this.resend = new Resend(apiKey);
    }
  }

  async sendTeamInviteEmail(options: {
    to: string | string[];
    redirectTo: string;
  }): Promise<void> {
    await this.sendEmail({
      to: options.to,
      subject: `You have been invited`,
      html: `
        <p>You have been invited to join an organization. Please click <a href="${options.redirectTo}">here</a> to accept the invite.</p>
      `,
    });
  }

  private async sendEmail(options: {
    to: string | string[];
    subject: string;
    html: string;
  }): Promise<void> {
    const to = Array.isArray(options.to) ? options.to : [options.to];

    assert(this.resend, "Resend is not configured");

    await this.resend.emails.send({
      from: "<EMAIL>",
      to,
      subject: options.subject,
      html: options.html,
    });
  }
}
