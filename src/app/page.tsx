import assert from "node:assert";

import { createIBP, startOnboarding } from "@/domain/ibp";
import { createUser, updateUser } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import {
  createServerActionSupabaseClient,
  ibpRepository,
  userRepository,
} from "./dependencies";

export default async function Page() {
  const supabase = await createServerActionSupabaseClient();
  const supabaseAuthService = new SupabaseAuthService(supabase.auth);
  const authUser = await supabaseAuthService.getUser();

  assert(authUser, "Unauthorized");

  let user = await userRepository.findById(authUser.id);

  if (!user) {
    user = createUser({
      id: authUser.id,
      email: authUser.email,
      firstName: "Test",
      lastName: "Testov",
      phoneNumber: null,
    });

    await userRepository.create(user);
  }

  if (user) {
    const prevUser = user;

    user = updateUser(user, {
      firstName: "Test",
      lastName: "Testov",
      phoneNumber: "+375 29 111-22-33",
    });

    await userRepository.update(prevUser, user);
  }

  let ibp = await ibpRepository.findByUserId(user.id);

  if (!ibp) {
    ibp = createIBP({ userId: user.id });

    await ibpRepository.create(ibp);
  }

  if (ibp) {
    const prevIbp = ibp;

    ibp = startOnboarding(ibp);

    await ibpRepository.update(prevIbp, ibp);
  }

  return <pre>{JSON.stringify(ibp, null, 2)}</pre>;
}
