"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";

import {
  ConversationTypes,
  createProject,
  postMessageToConversation,
} from "@/api/generated";

export default function Page() {
  const [message, setMessage] = useState("");
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  return (
    <>
      <h1>Define your product with AI</h1>
      <p>
        Describe your idea — AI will prepare your product summary, problem
        statement, and initial estimation.
      </p>
      <form
        onSubmit={(e) => {
          e.preventDefault();

          void (async () => {
            try {
              const userId = window.localStorage.getItem("user_id");
              const projectResponse = await createProject({
                user_id: userId,
              });

              if (projectResponse.status !== 200) {
                throw new Error(projectResponse.data.detail![0].msg);
              }

              const initialConversationId =
                projectResponse.data.conversations.find(
                  (c) => c.type === ConversationTypes.INITIAL
                )!.id;
              const messageResponse = await postMessageToConversation(
                projectResponse.data.project_id,
                initialConversationId,
                { message }
              );

              if (messageResponse.status !== 200) {
                throw new Error(messageResponse.data.detail![0].msg);
              }

              router.push(`/project/?id=${projectResponse.data.project_id}`);
            } catch (error) {
              setError(error instanceof Error ? error.message : String(error));
            }
          })();
        }}
      >
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Describe your product idea or paste a short brief…"
          rows={6}
          cols={50}
        />
        <br />
        <button type="submit">Send</button>
        {error ? (
          <p>{error}</p>
        ) : (
          <p>
            AI will ask only the questions it really needs — everything else
            will be inferred automatically.
          </p>
        )}
      </form>
    </>
  );
}
