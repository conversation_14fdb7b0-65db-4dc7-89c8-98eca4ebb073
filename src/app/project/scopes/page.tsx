/* eslint-disable unicorn/no-nested-ternary */
"use client";

import {
  EventSourceMessage,
  fetchEventSource,
} from "@microsoft/fetch-event-source";
import { notFound, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useReducer, useState } from "react";
import { flushSync } from "react-dom";

import {
  ConversationTypes,
  generateProjectEstimates,
  getOpenConversationSseUrl,
  getOpenProjectScopesSseUrl,
  getProject,
  postMessageToConversation,
  ProjectResponse,
  updateProjectWithUserId,
} from "@/api/generated";
import { buildApiUrl } from "@/api/mutator";

export default function Page() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get("id");

  if (!projectId) {
    return notFound();
  }

  return <ProjectPage projectId={projectId} />;
}

type PageProps = {
  projectId: string;
};

type UserMessageData = {
  id: string;
  sender: "user";
  content: string;
};

type AssistantMessageData = {
  id: string;
  sender: "assistant";
  content: string;
};

type MessageData = UserMessageData | AssistantMessageData;

const ProjectPage: React.FC<PageProps> = ({ projectId }) => {
  const router = useRouter();
  const [project, setProject] = useState<ProjectResponse | null>(null);
  const [message, setMessage] = useState("");
  const [scopes, setScopes] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [connectionAttempt, setConnectionAttempt] = useState(1);
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingEstimates, setIsGeneratingEstimates] = useState(false);
  const [messages, dispatchEvent] = useReducer(
    (state: MessageData[], event: EventSourceMessage): MessageData[] => {
      const lastMessage = state[state.length - 1];

      if (
        state.some((m) => m.id === event.id) &&
        !(lastMessage?.id === event.id && lastMessage.content !== event.data)
      ) {
        return state;
      }

      if (event.event === "message") {
        return [
          ...state,
          { id: event.id, sender: "user", content: event.data },
        ];
      }

      if (event.event === "response") {
        if (lastMessage?.sender === "assistant") {
          const prevMessages = state.slice(0, -1);

          return [
            ...prevMessages,
            {
              id: event.id,
              sender: "assistant",
              content: lastMessage.content + event.data,
            },
          ];
        }

        return [
          ...state,
          {
            id: event.id,
            sender: "assistant",
            content: event.data,
          },
        ];
      }

      return state;
    },
    []
  );
  const [scopesState, dispatchScopes] = useReducer(
    (
      state: { content: string; currentId: string | null },
      event: EventSourceMessage
    ) => {
      if (event.event === "scopes_update") {
        if (state.currentId !== event.id) {
          return { content: event.data, currentId: event.id };
        }

        return { ...state, content: state.content + event.data };
      }
      return state;
    },
    { content: "", currentId: null }
  );

  useEffect(() => {
    void (async () => {
      const response = await getProject(projectId);

      if (response.status !== 200) {
        throw new Error(response.data.detail![0].msg);
      }

      setProject(response.data);
    })();
  }, [projectId]);

  useEffect(() => {
    if (scopesState.content) {
      setScopes(scopesState.content);
    }
  }, [scopesState.content]);

  useEffect(() => {
    if (!project) {
      return;
    }

    const abortController = new AbortController();
    const scopesConversationId = project.conversations.find(
      (c) => c.type === ConversationTypes.SCOPES
    )!.id;

    // Why not native EventSource? Because DigitalOcean sucks:
    // @see https://ideas.digitalocean.com/app-platform/p/http-response-streaming-in-app-platform-for-sse-support
    void fetchEventSource(
      buildApiUrl(
        getOpenConversationSseUrl(project.project_id, scopesConversationId)
      ).toString(),
      {
        method: "POST",
        signal: abortController.signal,
        onmessage: (event) => {
          dispatchEvent(event);
        },
        onerror: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
        onclose: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
      }
    );

    return () => abortController.abort();
  }, [project, connectionAttempt]);

  useEffect(() => {
    if (!project) {
      return;
    }

    const abortController = new AbortController();

    // Why not native EventSource? Because DigitalOcean sucks:
    // @see https://ideas.digitalocean.com/app-platform/p/http-response-streaming-in-app-platform-for-sse-support
    void fetchEventSource(
      buildApiUrl(getOpenProjectScopesSseUrl(project.project_id)).toString(),
      {
        method: "POST",
        signal: abortController.signal,
        onmessage: (event) => {
          dispatchScopes(event);
        },
        onerror: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
        onclose: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
      }
    );

    return () => abortController.abort();
  }, [project, connectionAttempt]);

  const handleScopesUpdate = async () => {
    if (!project) {
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      const response = await updateProjectWithUserId(project.project_id, {
        features_and_scopes: scopes,
      });

      if (response.status !== 200) {
        throw new Error(response.data.detail![0].msg);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : String(error));
    } finally {
      setIsSaving(false);
    }
  };

  const handleNextClick = async () => {
    if (!project) {
      return;
    }

    setIsGeneratingEstimates(true);
    setError(null);

    try {
      await generateProjectEstimates(project.project_id);
      router.push(`/project/estimates/?id=${project.project_id}`);
    } catch (error) {
      setError(error instanceof Error ? error.message : String(error));
      setIsGeneratingEstimates(false);
    }
  };

  return (
    <>
      <div style={{ display: "flex", gap: "2rem" }}>
        <section style={{ flex: 1 }}>
          {messages.map((message) =>
            message.sender === "user" ? (
              <UserMessage key={message.id} message={message} />
            ) : (
              <AssistantMessage key={message.id} message={message} />
            )
          )}
          <form
            onSubmit={(e) => {
              e.preventDefault();

              if (!project) {
                return;
              }

              setMessage("");

              (async () => {
                try {
                  const scopesConversationId = project.conversations.find(
                    (c) => c.type === ConversationTypes.SCOPES
                  )!.id;
                  const response = await postMessageToConversation(
                    project.project_id,
                    scopesConversationId,
                    { message }
                  );

                  if (response.status !== 200) {
                    throw new Error(response.data.detail![0].msg);
                  }
                } catch (error) {
                  setError(
                    error instanceof Error ? error.message : String(error)
                  );
                }
              })();
            }}
          >
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your message…"
              rows={6}
              cols={50}
            />
            <br />
            <button type="submit">Send</button>
          </form>
        </section>

        <section style={{ flex: 1 }}>
          <textarea
            value={scopes}
            onChange={(e) => setScopes(e.target.value)}
            placeholder="Scopes will appear here…"
            rows={20}
            cols={80}
            style={{ width: "100%", fontFamily: "monospace" }}
          />
          <br />
          <button onClick={handleScopesUpdate} disabled={isSaving}>
            {isSaving ? "Saving..." : "Save Scopes"}
          </button>
          {error && <p style={{ color: "red" }}>{error}</p>}
          <div style={{ marginTop: "1rem" }}>
            <button onClick={handleNextClick} disabled={isGeneratingEstimates}>
              {isGeneratingEstimates ? "Generating Estimates..." : "Next"}
            </button>
          </div>
        </section>
      </div>
    </>
  );
};

type UserMessageProps = {
  message: UserMessageData;
};

const UserMessage: React.FC<UserMessageProps> = ({ message }) => {
  return (
    <div>
      <p style={{ whiteSpace: "pre-line", maxWidth: "80ch" }}>
        <b>You: </b>
        {message.content}
      </p>
    </div>
  );
};

type AssistantMessageProps = {
  message: AssistantMessageData;
};

const AssistantMessage: React.FC<AssistantMessageProps> = ({ message }) => {
  const [renderedContentLength, setRenderedContentLength] = useState(
    message.content.length
  );

  useEffect(() => {
    requestAnimationFrame(() => {
      flushSync(() => {
        setRenderedContentLength((prev) =>
          Math.min(prev + 1, message.content.length)
        );
      });
    });
  }, [renderedContentLength, message.content.length]);

  return (
    <div>
      <p style={{ whiteSpace: "pre-line", maxWidth: "80ch" }}>
        <b>Assistant: </b>
        {message.content.slice(0, renderedContentLength)}
      </p>
    </div>
  );
};
