/* eslint-disable unicorn/no-nested-ternary */
"use client";

import {
  EventSourceMessage,
  fetchEventSource,
} from "@microsoft/fetch-event-source";
import { notFound, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useReducer, useState } from "react";
import { flushSync } from "react-dom";

import {
  ConversationTypes,
  generateProjectScopes,
  getOpenConversationSseUrl,
  getOpenProjectOverviewSseUrl,
  getProject,
  postMessageToConversation,
  ProjectResponse,
} from "@/api/generated";
import { buildApiUrl } from "@/api/mutator";

export default function Page() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get("id");

  if (!projectId) {
    return notFound();
  }

  return <ProjectPage projectId={projectId} />;
}

type PageProps = {
  projectId: string;
};

type UserMessageData = {
  id: string;
  sender: "user";
  content: string;
};

type AssistantMessageData = {
  id: string;
  sender: "assistant";
  content: string;
};

type MessageData = UserMessageData | AssistantMessageData;

const ProjectPage: React.FC<PageProps> = ({ projectId }) => {
  const router = useRouter();
  const [project, setProject] = useState<ProjectResponse | null>(null);
  const [message, setMessage] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [connectionAttempt, setConnectionAttempt] = useState(1);
  const [isGeneratingScopes, setIsGeneratingScopes] = useState(false);
  const [messages, dispatchEvent] = useReducer(
    (state: MessageData[], event: EventSourceMessage): MessageData[] => {
      const lastMessage = state[state.length - 1];

      if (
        state.some((m) => m.id === event.id) &&
        !(lastMessage?.id === event.id && lastMessage.content !== event.data)
      ) {
        return state;
      }

      if (event.event === "message") {
        return [
          ...state,
          { id: event.id, sender: "user", content: event.data },
        ];
      }

      if (event.event === "response") {
        if (lastMessage?.sender === "assistant") {
          const prevMessages = state.slice(0, -1);

          return [
            ...prevMessages,
            {
              id: event.id,
              sender: "assistant",
              content: lastMessage.content + event.data,
            },
          ];
        }

        return [
          ...state,
          {
            id: event.id,
            sender: "assistant",
            content: event.data,
          },
        ];
      }

      return state;
    },
    []
  );
  const [overviewState, dispatchOverview] = useReducer(
    (
      state: { content: string; currentId: string | null },
      event: EventSourceMessage
    ) => {
      if (event.event === "overview_update") {
        if (state.currentId !== event.id) {
          return { content: event.data, currentId: event.id };
        }

        return { ...state, content: state.content + event.data };
      }
      return state;
    },
    { content: "", currentId: null }
  );

  useEffect(() => {
    void (async () => {
      const response = await getProject(projectId);

      if (response.status !== 200) {
        throw new Error(response.data.detail![0].msg);
      }

      setProject(response.data);
    })();
  }, [projectId]);

  useEffect(() => {
    if (!project) {
      return;
    }

    const abortController = new AbortController();
    const overviewConversationId = project.conversations.find(
      (c) => c.type === ConversationTypes.OVERVIEW
    )!.id;

    // Why not native EventSource? Because DigitalOcean sucks:
    // @see https://ideas.digitalocean.com/app-platform/p/http-response-streaming-in-app-platform-for-sse-support
    void fetchEventSource(
      buildApiUrl(
        getOpenConversationSseUrl(project.project_id, overviewConversationId)
      ).toString(),
      {
        method: "POST",
        signal: abortController.signal,
        onmessage: (event) => {
          dispatchEvent(event);
        },
        onerror: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
        onclose: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
      }
    );

    return () => abortController.abort();
  }, [project, connectionAttempt]);

  useEffect(() => {
    if (!project) {
      return;
    }

    const abortController = new AbortController();

    // Why not native EventSource? Because DigitalOcean sucks:
    // @see https://ideas.digitalocean.com/app-platform/p/http-response-streaming-in-app-platform-for-sse-support
    void fetchEventSource(
      buildApiUrl(getOpenProjectOverviewSseUrl(project.project_id)).toString(),
      {
        method: "POST",
        signal: abortController.signal,
        onmessage: (event) => {
          dispatchOverview(event);
        },
        onerror: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
        onclose: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
      }
    );

    return () => abortController.abort();
  }, [project, connectionAttempt]);

  const handleNextClick = async () => {
    if (!project) {
      return;
    }

    setIsGeneratingScopes(true);
    setError(null);

    try {
      await generateProjectScopes(project.project_id);
      router.push(`/project/scopes/?id=${project.project_id}`);
    } catch (error) {
      setError(error instanceof Error ? error.message : String(error));
      setIsGeneratingScopes(false);
    }
  };

  return (
    <>
      <output style={{ float: "right", width: "80ch", whiteSpace: "pre-wrap" }}>
        {overviewState.content}
      </output>
      <section>
        {messages.map((message) =>
          message.sender === "user" ? (
            <UserMessage key={message.id} message={message} />
          ) : (
            <AssistantMessage key={message.id} message={message} />
          )
        )}
      </section>
      <form
        onSubmit={(e) => {
          e.preventDefault();

          if (!project) {
            return;
          }

          setMessage("");

          (async () => {
            try {
              const overviewConversationId = project.conversations.find(
                (c) => c.type === ConversationTypes.OVERVIEW
              )!.id;
              const response = await postMessageToConversation(
                project.project_id,
                overviewConversationId,
                { message }
              );

              if (response.status !== 200) {
                throw new Error(response.data.detail![0].msg);
              }
            } catch (error) {
              setError(error instanceof Error ? error.message : String(error));
            }
          })();
        }}
      >
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type your message…"
          rows={6}
          cols={50}
        />
        <br />
        <button type="submit">Send</button>
        {error ? (
          <p style={{ color: "red" }}>{error}</p>
        ) : (
          <p>
            AI will ask only the questions it really needs — everything else
            will be inferred automatically.
          </p>
        )}
      </form>
      <div style={{ marginTop: "2rem" }}>
        <button onClick={handleNextClick} disabled={isGeneratingScopes}>
          {isGeneratingScopes ? "Generating Scopes..." : "Next"}
        </button>
      </div>
    </>
  );
};

type UserMessageProps = {
  message: UserMessageData;
};

const UserMessage: React.FC<UserMessageProps> = ({ message }) => {
  return (
    <div>
      <p style={{ whiteSpace: "pre-line", maxWidth: "80ch" }}>
        <b>You: </b>
        {message.content}
      </p>
    </div>
  );
};

type AssistantMessageProps = {
  message: AssistantMessageData;
};

const AssistantMessage: React.FC<AssistantMessageProps> = ({ message }) => {
  const [renderedContentLength, setRenderedContentLength] = useState(
    message.content.length
  );

  useEffect(() => {
    requestAnimationFrame(() => {
      flushSync(() => {
        setRenderedContentLength((prev) =>
          Math.min(prev + 1, message.content.length)
        );
      });
    });
  }, [renderedContentLength, message.content.length]);

  return (
    <div>
      <p style={{ whiteSpace: "pre-line", maxWidth: "80ch" }}>
        <b>Assistant: </b>
        {message.content.slice(0, renderedContentLength)}
      </p>
    </div>
  );
};
