/* eslint-disable unicorn/no-nested-ternary */
"use client";

import {
  EventSourceMessage,
  fetchEventSource,
} from "@microsoft/fetch-event-source";
import { notFound, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useReducer, useState } from "react";
import { flushSync } from "react-dom";

import {
  ConversationTypes,
  generateProjectOverview,
  getOpenConversationSseUrl,
  getProject,
  initiateSignIn,
  postMessageToConversation,
  ProjectResponse,
  signInWithOtp,
  updateProjectWithUserId,
} from "@/api/generated";
import { buildApiUrl } from "@/api/mutator";

export default function Page() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get("id");

  if (!projectId) {
    return notFound();
  }

  return <ProjectPage projectId={projectId} />;
}

type PageProps = {
  projectId: string;
};

type UserMessageData = {
  id: string;
  sender: "user";
  content: string;
};

type AssistantMessageData = {
  id: string;
  sender: "assistant";
  content: string;
};

type SignInMessageData = {
  id: string;
  sender: "system";
  content: string;
  type: "sign-in-required";
};

type MessageData = UserMessageData | AssistantMessageData | SignInMessageData;

const ProjectPage: React.FC<PageProps> = ({ projectId }) => {
  const router = useRouter();
  const [project, setProject] = useState<ProjectResponse | null>(null);
  const [message, setMessage] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [connectionAttempt, setConnectionAttempt] = useState(1);
  const [messages, dispatchEvent] = useReducer(
    (state: MessageData[], event: EventSourceMessage): MessageData[] => {
      const lastMessage = state[state.length - 1];

      if (
        state.some((m) => m.id === event.id) &&
        !(lastMessage?.id === event.id && lastMessage.content !== event.data)
      ) {
        return state;
      }

      if (event.event === "message") {
        return [
          ...state,
          { id: event.id, sender: "user", content: event.data },
        ];
      }

      if (event.event === "response") {
        if (lastMessage?.sender === "assistant") {
          const prevMessages = state.slice(0, -1);

          return [
            ...prevMessages,
            {
              id: event.id,
              sender: "assistant",
              content: lastMessage.content + event.data,
            },
          ];
        }

        return [
          ...state,
          {
            id: event.id,
            sender: "assistant",
            content: event.data,
          },
        ];
      }

      if (event.event === "sign-in-required") {
        if (window.localStorage.getItem("user_id")) {
          return state;
        }

        return [
          ...state,
          {
            id: event.id,
            sender: "system",
            content:
              "Great — I’ve got everything I need to prepare your product summary and estimation. The generation might take a few minutes.\n\nCould you please share your email, so I can notify you once it’s ready?",
            type: "sign-in-required",
          },
        ];
      }

      if (event.event === "sign-in-completed") {
        return state.slice(0, -1);
      }

      return state;
    },
    []
  );

  const triggerProjectOverviewGeneration = useCallback(() => {
    void (async () => {
      await generateProjectOverview(projectId);
      router.push(`/project/overview/?id=${projectId}`);
    })();
  }, [router, projectId]);

  useEffect(() => {
    void (async () => {
      const response = await getProject(projectId);

      if (response.status !== 200) {
        throw new Error(response.data.detail![0].msg);
      }

      setProject(response.data);
    })();
  }, [projectId]);

  useEffect(() => {
    if (!project) {
      return;
    }

    const abortController = new AbortController();
    const initialConversationId = project.conversations.find(
      (c) => c.type === ConversationTypes.INITIAL
    )!.id;

    void fetchEventSource(
      buildApiUrl(
        getOpenConversationSseUrl(project.project_id, initialConversationId)
      ).toString(),
      {
        method: "POST",
        signal: abortController.signal,
        onmessage: (event) => {
          if (event.data.replace(/\s/g, "") === `{"type":"require-auth"}`) {
            const userId = window.localStorage.getItem("user_id");

            if (userId == null) {
              dispatchEvent({
                event: "sign-in-required",
                id: "",
                data: "",
              });
            } else {
              triggerProjectOverviewGeneration();
            }
          } else {
            dispatchEvent(event);
          }
        },
        onerror: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
        onclose: () => {
          setConnectionAttempt((attempt) => attempt + 1);
        },
      }
    );

    return () => abortController.abort();
  }, [project, triggerProjectOverviewGeneration, connectionAttempt]);

  const onSignIn = (userId: string) => {
    void (async () => {
      await updateProjectWithUserId(projectId, { user_id: userId });
      dispatchEvent({
        event: "sign-in-completed",
        id: "",
        data: "",
      });
      triggerProjectOverviewGeneration();
    })();
  };

  return (
    <>
      <section>
        {messages.map((message) =>
          message.sender === "user" ? (
            <UserMessage key={message.id} message={message} />
          ) : message.sender === "assistant" ? (
            <AssistantMessage key={message.id} message={message} />
          ) : (
            <SignInMessage
              key={message.id}
              message={message}
              onSignIn={onSignIn}
            />
          )
        )}
      </section>
      <form
        onSubmit={(e) => {
          e.preventDefault();

          if (!project) {
            return;
          }

          setMessage("");

          (async () => {
            try {
              const initialConversationId = project.conversations.find(
                (c) => c.type === ConversationTypes.INITIAL
              )!.id;
              const response = await postMessageToConversation(
                project.project_id,
                initialConversationId,
                { message }
              );

              if (response.status !== 200) {
                throw new Error(response.data.detail![0].msg);
              }
            } catch (error) {
              setError(error instanceof Error ? error.message : String(error));
            }
          })();
        }}
      >
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type your message…"
          rows={6}
          cols={50}
        />
        <br />
        <button type="submit">Send</button>
        {error ? (
          <p>{error}</p>
        ) : (
          <p>
            AI will ask only the questions it really needs — everything else
            will be inferred automatically.
          </p>
        )}
        <p>
          Trigger
          {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
          <span
            onClick={() => {
              triggerProjectOverviewGeneration();
            }}
            style={{
              cursor: "pointer",
              textDecoration: "underline",
              textDecorationStyle: "dotted",
            }}
          >
            project overview generation
          </span>{" "}
          right now?
        </p>
      </form>
    </>
  );
};

type UserMessageProps = {
  message: UserMessageData;
};

const UserMessage: React.FC<UserMessageProps> = ({ message }) => {
  return (
    <div>
      <p style={{ whiteSpace: "pre-line", maxWidth: "80ch" }}>
        <b>You: </b>
        {message.content}
      </p>
    </div>
  );
};

type AssistantMessageProps = {
  message: AssistantMessageData;
};

const AssistantMessage: React.FC<AssistantMessageProps> = ({ message }) => {
  const [renderedContentLength, setRenderedContentLength] = useState(
    message.content.length
  );

  useEffect(() => {
    requestAnimationFrame(() => {
      flushSync(() => {
        setRenderedContentLength((prev) =>
          Math.min(prev + 1, message.content.length)
        );
      });
    });
  }, [renderedContentLength, message.content.length]);

  return (
    <div>
      <p style={{ whiteSpace: "pre-line", maxWidth: "80ch" }}>
        <b>Assistant: </b>
        {message.content.slice(0, renderedContentLength)}
      </p>
    </div>
  );
};

type SignInMessageProps = {
  message: SignInMessageData;
  onSignIn: (userId: string) => void;
};

type SignInMessageState =
  | {
      error?: string;
      email: string;
      subscribe: boolean;
      step: "enter_email";
    }
  | {
      error?: string;
      email: string;
      otp: string;
      step: "enter_otp";
    };

const SignInMessage: React.FC<SignInMessageProps> = ({ message, onSignIn }) => {
  const [state, setState] = useState<SignInMessageState>({
    email: "",
    subscribe: true,
    step: "enter_email",
  });

  return (
    <div>
      <p style={{ whiteSpace: "pre-line" }}>
        <b>Assistant: </b>
        {message.content}
      </p>

      {state.step === "enter_email" ? (
        <main>
          <form
            onSubmit={(event) => {
              event.preventDefault();

              initiateSignIn({
                email: state.email,
                subscribe: state.subscribe,
              })
                .then((response) => {
                  if (response.status === 422) {
                    throw new Error(response.data.detail![0].msg);
                  }

                  setState({
                    email: state.email,
                    otp: "",
                    step: "enter_otp",
                  });
                })
                .catch((error) => setState({ ...state, error: String(error) }));
            }}
          >
            <div>
              <label htmlFor="email">Email: </label>
              <input
                type="email"
                name="email"
                id="email"
                required
                value={state.email}
                onChange={(event) => {
                  setState({
                    ...state,
                    email: event.target.value,
                  });
                }}
              />
            </div>
            <br />
            <button type="submit">Sign-in</button>

            {state.error ? (
              <output style={{ color: "red" }}>{state.error}</output>
            ) : null}
          </form>
        </main>
      ) : (
        <main>
          <form
            onSubmit={(event) => {
              event.preventDefault();

              signInWithOtp({
                email: state.email,
                otp: state.otp,
              })
                .then((data) => {
                  if (data.status === 422) {
                    throw new Error(data.data.detail![0].msg);
                  }

                  window.localStorage.setItem("user_id", data.data.user_id);
                  onSignIn(data.data.user_id);
                })
                .catch((error) => setState({ ...state, error: String(error) }));
            }}
          >
            <div>
              <label htmlFor="code">Code: </label>
              <input
                type="text"
                name="code"
                id="code"
                required
                value={state.otp}
                onChange={(event) => {
                  setState({
                    ...state,
                    otp: event.target.value,
                  });
                }}
              />
            </div>
            <br />
            <button type="submit">Enter</button>

            {state.error ? (
              <output style={{ color: "red" }}>{state.error}</output>
            ) : null}
          </form>
        </main>
      )}
    </div>
  );
};
