"use client";

import { notFound, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { getProject, ProjectResponse } from "@/api/generated";

export default function Page() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get("id");

  if (!projectId) {
    return notFound();
  }

  return <EstimatesPage projectId={projectId} />;
}

type PageProps = {
  projectId: string;
};

const EstimatesPage: React.FC<PageProps> = ({ projectId }) => {
  const [project, setProject] = useState<ProjectResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    void (async () => {
      try {
        const response = await getProject(projectId);

        if (response.status !== 200) {
          throw new Error(response.data.detail![0].msg);
        }

        setProject(response.data);
      } catch (error) {
        setError(error instanceof Error ? error.message : String(error));
      }
    })();
  }, [projectId]);

  if (error) {
    return (
      <div>
        <p style={{ color: "red" }}>Error: {error}</p>
      </div>
    );
  }

  if (!project) {
    return <div>Loading...</div>;
  }

  if (!project.coda_doc_page_link) {
    return (
      <div>
        <p style={{ color: "orange" }}>
          No estimates document available yet. Please try again later.
        </p>
      </div>
    );
  }

  return (
    <div style={{ width: "100%", height: "100vh" }}>
      <iframe
        src={`https://coda.io/embed/${project.coda_doc_id}/${project.coda_doc_page_link.split("/").slice(-1)[0]}?viewMode=embedplay&hideSections=true`}
        width={900}
        height={500}
        allow="fullscreen"
        title="Project Estimates"
      />
    </div>
  );
};
