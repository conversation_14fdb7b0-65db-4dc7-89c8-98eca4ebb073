import "./_styles/global.css";

import { ReactNode, Suspense } from "react";

import { LoadingScreen } from "@/components/loading-screen";
import { Toaster } from "@/components/ui/sonner";

type RootLayoutProps = {
  children: ReactNode;
};

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Easy Beauty</title>
      </head>
      <body>
        <div id="root">
          <Suspense fallback={<LoadingScreen />}>{children}</Suspense>
          <Toaster />
        </div>
      </body>
    </html>
  );
}
