import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import assert from "node:assert";

import { But<PERSON> } from "@/components/ui/button";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import {
  createServerActionSupabaseClient,
  oppQueryHandler,
  prismaClient,
} from "../../dependencies";
import DashboardLayout from "../components/DashboardLayout";
import { BankAccountFormData } from "./components/BankAccountForm";
import ComplianceRequirementsList from "./components/ComplianceRequirementsList";
import { type ContactFormData } from "./components/ContactForm";
import {
  ChangeComplianceStatusSandboxButton,
  isSandboxComplianceStatusChangeEnabled,
} from "./components/sandbox";
import { UltimateBeneficialOwnerData } from "./components/UltimateBeneficialOwnerForm";
import { FileSpec } from "./types";

export const dynamic = "force-dynamic";

export type GetMerchantComplianceRequirementsResponse = {
  bankAccount: ComplianceRequirementStatus;
  chamberOfCommerceExtract: ComplianceRequirementStatus;
  contact: ComplianceRequirementStatus;
  organizationStructure: ComplianceRequirementStatus;
  ultimateBenificialOwner: ComplianceRequirementStatus;
  other:
    | {
        status: "pending" | "unverified";
        url: string;
      }
    | {
        status: "verified";
      };
};

export type ComplianceRequirementStatus = "unverified" | "pending" | "verified";

export type CompliancePageData = {
  requirements: GetMerchantComplianceRequirementsResponse;
  bankAccount: BankAccountFormData;
  chamberOfCommerceExtract: {
    fileSpec: FileSpec;
  };
  contact: ContactFormData;
  organizationStructure: {
    fileSpec: FileSpec;
  };
  ultimateBeneficialOwners: UltimateBeneficialOwnerData;
};

export default async function CompliancePage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const member = await prismaClient.member.findFirstOrThrow({
    select: {
      organization: {
        select: {
          oppMerchantUid: true,
        },
      },
    },
    where: {
      userId: authUser.id,
    },
  });

  assert(
    member.organization.oppMerchantUid != null,
    "Organization should be connected to OPP merchant"
  );

  const {
    requirements,
    bankAccount,
    chamberOfCommerceExtract,
    contact,
    organizationStructure,
    ultimateBeneficialOwners,
  } = await oppQueryHandler.getCompliacePageData(
    member.organization.oppMerchantUid
  );

  return (
    <DashboardLayout
      userId={authUser.id}
      heading={
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-semibold">Compliance</h1>
        </div>
      }
    >
      <div className="px-4 lg:px-6">
        <ComplianceRequirementsList
          requirements={requirements}
          bankAccountData={bankAccount}
          chamberOfCommerceData={chamberOfCommerceExtract}
          contactData={contact}
          organizationStructureData={organizationStructure}
          ultimateBeneficialOwnerData={ultimateBeneficialOwners}
        />
      </div>
      {isSandboxComplianceStatusChangeEnabled && (
        <ChangeComplianceStatusSandboxButton />
      )}
    </DashboardLayout>
  );
}
