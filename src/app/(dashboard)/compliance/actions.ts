"use server";

import { revalidatePath } from "next/cache";
import assert from "node:assert";

import { bankAccountSchema } from "@/domain/schemas/compliance";
import {
  CreateContactParams,
  CreateUBOParams,
  UpdateContactParams,
  UpdateUBOParams,
} from "@/opp/OppQueryHandler";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import {
  createServerActionSupabaseClient,
  oppQueryHandler,
  prismaClient,
} from "../../dependencies";
import { BankAccountFormData } from "./components/BankAccountForm";
import { type ContactFormData } from "./components/ContactForm";
import { type UltimateBeneficialOwnerData } from "./components/UltimateBeneficialOwnerForm";

export type CreateBankAccountFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
  data: BankAccountFormData["account"];
};

function revalidateCompliance() {
  revalidatePath("/compliance");
}

const OPP_NO_MERCHANT_ERROR_MSG =
  "Organization should be connected to OPP merchant";

export async function createBankAccount(
  _prevState: CreateBankAccountFormState,
  formData: FormData
): Promise<CreateBankAccountFormState> {
  const accountName = String(formData.get("accountName") ?? "");
  const accountIban = String(formData.get("accountIban") ?? "");
  const accountBic = String(formData.get("accountBic") ?? "");
  const statusRaw = formData.get("status");
  const uidRaw = formData.get("uid");
  const status =
    statusRaw === "new" ||
    statusRaw === "pending" ||
    statusRaw === "approved" ||
    statusRaw === "disapproved"
      ? statusRaw
      : null;
  const uid = typeof uidRaw === "string" && uidRaw.length > 0 ? uidRaw : null;

  const data: BankAccountFormData["account"] = {
    uid,
    accountName,
    accountIban,
    accountBic,
    status,
  };

  const parsed = bankAccountSchema.safeParse({
    accountName,
    accountIban,
    accountBic,
  });

  if (!parsed.success) {
    return {
      fieldErrors: parsed.error.flatten().fieldErrors,
      data,
    };
  }

  const normalizedData: BankAccountFormData["account"] = {
    ...data,
    ...parsed.data,
  };

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    assert(authUser, "Unauthorized");

    const member = await prismaClient.member.findFirstOrThrow({
      select: {
        organization: {
          select: {
            oppMerchantUid: true,
          },
        },
      },
      where: {
        userId: authUser.id,
      },
    });

    assert(member.organization.oppMerchantUid, OPP_NO_MERCHANT_ERROR_MSG);

    const { status, uid } = await oppQueryHandler.createBankAccount({
      merchantUid: member.organization.oppMerchantUid,
      accountName: parsed.data.accountName,
      accountIban: parsed.data.accountIban,
      accountBic: parsed.data.accountBic,
    });

    revalidateCompliance();

    return { data: { ...normalizedData, uid, status } };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "Failed to create bank account",
      data: normalizedData,
    };
  }
}

export type SaveContactInformationFormState = {
  error?: string;
  data: ContactFormData["contacts"][number];
};

export async function saveContactInformation(
  prevState: SaveContactInformationFormState,
  formData: FormData
): Promise<SaveContactInformationFormState> {
  const data = parseContactFormData(formData);

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    assert(authUser, "Unauthorized");

    const member = await prismaClient.member.findFirstOrThrow({
      select: {
        organization: {
          select: {
            oppMerchantUid: true,
          },
        },
      },
      where: {
        userId: authUser.id,
      },
    });

    assert(member.organization.oppMerchantUid, OPP_NO_MERCHANT_ERROR_MSG);

    if (data.status === "verified" || data.status === "pending") {
      return { data, error: "Contact is already verified or pending" };
    }

    if (!data.uid) {
      if (!data.title) {
        return { error: "Title is required", data };
      }
      if (!data.name.initials) {
        return { error: "Name initials are required", data };
      }
      if (!data.name.namesGiven) {
        return { error: "Names given are required", data };
      }
      if (!data.name.first) {
        return { error: "First name is required", data };
      }
      if (!data.name.last) {
        return { error: "Last name is required", data };
      }
      if (!data.dateOfBirth) {
        return { error: "Birth date is required", data };
      }
      if (!data.emailaddress) {
        return { error: "Email address is required", data };
      }
      if (!data.phonenumber) {
        return { error: "Phone number is required", data };
      }

      const params: CreateContactParams = {
        merchantUid: member.organization.oppMerchantUid,
        title: data.title,
        nameInitials: data.name.initials,
        nameNamesGiven: data.name.namesGiven,
        nameFirst: data.name.first,
        nameLast: data.name.last,
        birthdate: data.dateOfBirth,
        emailaddresses: [
          {
            emailaddress: data.emailaddress,
          },
        ],
        phonenumbers: [{ phonenumber: data.phonenumber }],
      };

      const { status, uid } = await oppQueryHandler.createContact(params);
      revalidateCompliance();

      return {
        data: { ...data, status, uid },
      };
    }
    const params: UpdateContactParams = {
      merchantUid: member.organization.oppMerchantUid,
      contactUid: data.uid,
      title: data.title ?? undefined,
      nameInitials: data.name.initials ?? undefined,
      nameNamesGiven: data.name.namesGiven ?? undefined,
      nameFirst: data.name.first ?? undefined,
      nameLast: data.name.last ?? undefined,
      birthdate: data.dateOfBirth ?? undefined,
      emailaddresses: data.emailaddress
        ? [
            {
              emailaddress: data.emailaddress,
            },
          ]
        : undefined,

      phonenumbers: data.phonenumber
        ? [{ phonenumber: data.phonenumber }]
        : undefined,
    };

    const { status, uid } = await oppQueryHandler.updateContact(params);
    revalidateCompliance();

    return {
      data: { ...data, status, uid },
    };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "Failed to save contact information",
      data,
    };
  }
}

export type VerifyContactFormState = {
  error?: string;
};

export type VerifyCoCFormState = {
  error?: string;
};

export type VerifyOrganizationStructureFormState = {
  error?: string;
};

export type VerifyBankAccountFormState = {
  error?: string;
};

export async function verifyContact(
  prevState: VerifyContactFormState,
  formData: FormData
): Promise<VerifyContactFormState> {
  const contactUid = formData.get("contactUid") as string | null;
  const documentType = formData.get("documentType") as
    | "passport"
    | "driver-license"
    | null;
  const passportFile = formData.get("passport") as File | null;
  const driverLicenseFrontFile = formData.get(
    "driverLicenseFront"
  ) as File | null;
  const driverLicenseBackFile = formData.get(
    "driverLicenseBack"
  ) as File | null;

  if (!contactUid) {
    return { error: "Contact UID is required" };
  }

  if (!documentType) {
    return { error: "Document type is required" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    assert(authUser, "Unauthorized");

    const member = await prismaClient.member.findFirstOrThrow({
      select: {
        organization: {
          select: {
            oppMerchantUid: true,
          },
        },
      },
      where: {
        userId: authUser.id,
      },
    });

    assert(member.organization.oppMerchantUid, OPP_NO_MERCHANT_ERROR_MSG);

    const uploadLinks = await oppQueryHandler.getUploadLinksForContactForm(
      member.organization.oppMerchantUid,
      contactUid
    );

    if (documentType === "passport") {
      if (!passportFile || passportFile.size === 0) {
        return { error: "Please select a passport file" };
      }

      const uploadFormData = new FormData();
      uploadFormData.append("file", passportFile);

      const uploadResponse = await fetch(uploadLinks.passportUploadLink.url, {
        method: "POST",
        headers: uploadLinks.passportUploadLink.headers,
        body: uploadFormData,
      });

      if (!uploadResponse.ok) {
        return { error: "Passport file upload failed" };
      }
    } else {
      if (!driverLicenseFrontFile || driverLicenseFrontFile.size === 0) {
        return {
          error: "Please select a driver license front file",
        };
      }

      if (!driverLicenseBackFile || driverLicenseBackFile.size === 0) {
        return {
          error: "Please select a driver license back file",
        };
      }

      // Upload front file
      const frontFormData = new FormData();
      frontFormData.append("file", driverLicenseFrontFile);

      const frontUploadResponse = await fetch(
        uploadLinks.driversLicenseFrontUploadLink.url,
        {
          method: "POST",
          headers: uploadLinks.driversLicenseFrontUploadLink.headers,
          body: frontFormData,
        }
      );

      if (!frontUploadResponse.ok) {
        return {
          error: "Driver license front file upload failed",
        };
      }

      // Upload back file
      const backFormData = new FormData();
      backFormData.append("file", driverLicenseBackFile);

      const backUploadResponse = await fetch(
        uploadLinks.driversLicenseBackUploadLink.url,
        {
          method: "POST",
          headers: uploadLinks.driversLicenseBackUploadLink.headers,
          body: backFormData,
        }
      );

      if (!backUploadResponse.ok) {
        return {
          error: "Driver license back file upload failed",
        };
      }
    }

    revalidateCompliance();

    return {};
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "Failed to upload verification documents",
    };
  }
}

export async function verifyCoC(
  prevState: VerifyCoCFormState,
  formData: FormData
): Promise<VerifyCoCFormState> {
  const cocFile = formData.get("cocExtract") as File | null;

  if (!cocFile || cocFile.size === 0) {
    return { error: "Please select a file" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    assert(authUser, "Unauthorized");

    const member = await prismaClient.member.findFirstOrThrow({
      select: {
        organization: {
          select: {
            oppMerchantUid: true,
          },
        },
      },
      where: {
        userId: authUser.id,
      },
    });

    assert(member.organization.oppMerchantUid, OPP_NO_MERCHANT_ERROR_MSG);

    const uploadLink = await oppQueryHandler.getUploadLinkForCoC(
      member.organization.oppMerchantUid
    );

    const uploadFormData = new FormData();
    uploadFormData.append("file", cocFile);

    const uploadResponse = await fetch(uploadLink.url, {
      method: "POST",
      headers: uploadLink.headers,
      body: uploadFormData,
    });

    if (!uploadResponse.ok) {
      return { error: "File upload failed" };
    }

    revalidateCompliance();

    return {};
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "Failed to upload Chamber of Commerce extract",
    };
  }
}

export async function verifyOrganizationStructure(
  prevState: VerifyOrganizationStructureFormState,
  formData: FormData
): Promise<VerifyOrganizationStructureFormState> {
  const orgStructureFile = formData.get("organizationStructure") as File | null;

  if (!orgStructureFile || orgStructureFile.size === 0) {
    return { error: "Please select a file" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    assert(authUser, "Unauthorized");

    const member = await prismaClient.member.findFirstOrThrow({
      select: {
        organization: {
          select: {
            oppMerchantUid: true,
          },
        },
      },
      where: {
        userId: authUser.id,
      },
    });

    assert(member.organization.oppMerchantUid, OPP_NO_MERCHANT_ERROR_MSG);

    const uploadLink =
      await oppQueryHandler.getUploadLinkForOrganizationStructure(
        member.organization.oppMerchantUid
      );

    const uploadFormData = new FormData();
    uploadFormData.append("file", orgStructureFile);

    const uploadResponse = await fetch(uploadLink.url, {
      method: "POST",
      headers: uploadLink.headers,
      body: uploadFormData,
    });

    if (!uploadResponse.ok) {
      return { error: "File upload failed" };
    }

    revalidateCompliance();

    return {};
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "Failed to upload Organization Structure document",
    };
  }
}

export async function verifyBankAccount(
  prevState: VerifyBankAccountFormState,
  formData: FormData
): Promise<VerifyBankAccountFormState> {
  const bankAccountUid = formData.get("bankAccountUid") as string | null;
  const bankStatementFrontFile = formData.get(
    "bankStatementFront"
  ) as File | null;
  const bankStatementBackFile = formData.get(
    "bankStatementBack"
  ) as File | null;

  if (!bankAccountUid) {
    return { error: "Bank account UID is required" };
  }

  if (!bankStatementFrontFile || bankStatementFrontFile.size === 0) {
    return { error: "Please select a bank statement front file" };
  }

  if (!bankStatementBackFile || bankStatementBackFile.size === 0) {
    return { error: "Please select a bank statement back file" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    assert(authUser, "Unauthorized");

    const member = await prismaClient.member.findFirstOrThrow({
      select: {
        organization: {
          select: {
            oppMerchantUid: true,
          },
        },
      },
      where: {
        userId: authUser.id,
      },
    });

    assert(member.organization.oppMerchantUid, OPP_NO_MERCHANT_ERROR_MSG);

    const uploadLinks = await oppQueryHandler.getUploadLinksForBankAccount(
      member.organization.oppMerchantUid,
      bankAccountUid
    );

    // Upload front file
    const frontFormData = new FormData();
    frontFormData.append("file", bankStatementFrontFile);

    const frontUploadResponse = await fetch(
      uploadLinks.bankStatementFrontUploadLink.url,
      {
        method: "POST",
        headers: uploadLinks.bankStatementFrontUploadLink.headers,
        body: frontFormData,
      }
    );

    if (!frontUploadResponse.ok) {
      return { error: "Bank statement front file upload failed" };
    }

    // Upload back file
    const backFormData = new FormData();
    backFormData.append("file", bankStatementBackFile);

    const backUploadResponse = await fetch(
      uploadLinks.bankStatementBackUploadLink.url,
      {
        method: "POST",
        headers: uploadLinks.bankStatementBackUploadLink.headers,
        body: backFormData,
      }
    );

    if (!backUploadResponse.ok) {
      return { error: "Bank statement back file upload failed" };
    }

    revalidateCompliance();

    return {};
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "Failed to upload bank account verification documents",
    };
  }
}

export type SaveUltimateBeneficialOwnerFormState = {
  error?: string;
  data: UltimateBeneficialOwnerData["ubos"][number];
};

export async function saveUltimateBeneficialOwner(
  prevState: SaveUltimateBeneficialOwnerFormState,
  formData: FormData
): Promise<SaveUltimateBeneficialOwnerFormState> {
  const currentData = parseUBOFormData(formData);

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    assert(authUser, "Unauthorized");

    const member = await prismaClient.member.findFirstOrThrow({
      select: {
        organization: {
          select: {
            oppMerchantUid: true,
          },
        },
      },
      where: {
        userId: authUser.id,
      },
    });

    assert(member.organization.oppMerchantUid, OPP_NO_MERCHANT_ERROR_MSG);

    if (!currentData.uid) {
      if (!currentData.namesFirst) {
        return { error: "First name is required", data: currentData };
      }
      if (!currentData.nameLast) {
        return { error: "Last name is required", data: currentData };
      }
      if (!currentData.dateOfBirth) {
        return { error: "Birth date is required", data: currentData };
      }
      if (!currentData.countryOfResidence) {
        return { error: "Country of residence is required", data: currentData };
      }

      const params: CreateUBOParams = {
        merchantUid: member.organization.oppMerchantUid,
        namesFirst: currentData.namesFirst,
        namePrefix: currentData.namePrefix ?? undefined,
        nameLast: currentData.nameLast,
        dateOfBirth: currentData.dateOfBirth,
        countryOfResidence: currentData.countryOfResidence,
        isDecisionMaker: currentData.isDecisionMaker ?? undefined,
        decisionMakerPercentage:
          currentData.decisionMakerPercentage ?? undefined,
        isShareholder: currentData.isShareholder ?? undefined,
        shareholderPercentage: currentData.shareholderPercentage ?? undefined,
        isPep: currentData.isPep ?? undefined,
      };

      const { status, uid } = await oppQueryHandler.createUBO(params);
      revalidateCompliance();

      return {
        data: { ...currentData, status, uid },
      };
    }
    const params: UpdateUBOParams = {
      merchantUid: member.organization.oppMerchantUid,
      uboUid: currentData.uid,
      namesFirst: currentData.namesFirst ?? undefined,
      namePrefix: currentData.namePrefix ?? undefined,
      nameLast: currentData.nameLast ?? undefined,
      dateOfBirth: currentData.dateOfBirth ?? undefined,
      countryOfResidence: currentData.countryOfResidence ?? undefined,
      isDecisionMaker: currentData.isDecisionMaker ?? undefined,
      decisionMakerPercentage: currentData.decisionMakerPercentage ?? undefined,
      isShareholder: currentData.isShareholder ?? undefined,
      shareholderPercentage: currentData.shareholderPercentage ?? undefined,
      isPep: currentData.isPep ?? undefined,
    };

    const { status } = await oppQueryHandler.updateUBO(params);
    revalidateCompliance();

    return {
      data: { ...currentData, status },
    };
  } catch (error) {
    return {
      data: currentData,
      error:
        error instanceof Error ? error.message : "Failed to save UBO object",
    };
  }
}

function parseUBOFormData(
  formData: FormData
): NonNullable<UltimateBeneficialOwnerData["ubos"][number]> {
  const uid = formData.get("uid") as string | null;
  const namesFirst = formData.get("namesFirst") as string | null;
  const namePrefix = formData.get("namePrefix") as string | null;
  const nameLast = formData.get("nameLast") as string | null;
  const dateOfBirth = formData.get("dateOfBirth") as string | null;
  const decisionMaker = formData.get("decisionMaker") as string | null;
  const decisionMakerPercentage = formData.get("decisionMakerPercentage") as
    | string
    | null;
  const ownership = formData.get("ownership") as string | null;
  const ownershipPercentage = formData.get("ownershipPercentage") as
    | string
    | null;
  const politicalExposed = formData.get("politicalExposed") as string | null;
  const countryOfResidence = formData.get("countryOfResidence") as
    | string
    | null;
  const status = formData.get("status") as
    | "new"
    | "pending"
    | "verified"
    | "unverified"
    | null;

  return {
    uid,
    namesFirst,
    namePrefix,
    nameLast,
    dateOfBirth,
    countryOfResidence,
    isDecisionMaker: decisionMaker ? decisionMaker === "yes" : null,
    decisionMakerPercentage: decisionMakerPercentage
      ? Number.parseFloat(decisionMakerPercentage)
      : null,
    isShareholder: ownership ? ownership === "yes" : null,
    shareholderPercentage: ownershipPercentage
      ? Number.parseFloat(ownershipPercentage)
      : null,
    isPep: politicalExposed ? politicalExposed === "yes" : null,
    status,
  };
}

function parseContactFormData(
  formData: FormData
): NonNullable<ContactFormData["contacts"][number]> {
  const uid = formData.get("uid") as string | null;
  const title = formData.get("title") as "mr" | "mrs" | null;
  const nameInitials = formData.get("nameInitials") as string | null;
  const namesGiven = formData.get("namesGiven") as string | null;
  const firstName = formData.get("nameFirst") as string | null;
  const lastName = formData.get("nameLast") as string | null;
  const dateOfBirth = formData.get("dateOfBirth") as string | null;
  const emailaddress = formData.get("emailaddress") as string | null;
  const phonenumber = formData.get("phonenumber") as string | null;
  const status = formData.get("status") as
    | "unverified"
    | "pending"
    | "verified"
    | null;

  return {
    uid,
    title,
    name: {
      initials: nameInitials,
      namesGiven,
      first: firstName,
      last: lastName,
    },
    dateOfBirth,
    emailaddress,
    phonenumber,
    status,
  };
}
