"use client";

import clsx from "clsx";
import { PlusIcon } from "lucide-react";
import { type ReactNode, useState } from "react";

import { Button } from "@/components/ui/button";

import { useViewModel } from "./hooks";
import { UltimateBeneficialOwnerData } from "./types";
import UltimateBeneficialOwnerFormItem from "./UltimateBeneficialOwnerFormItem";

type Props = {
  data: UltimateBeneficialOwnerData;
};

export default function UltimateBeneficialOwnerForm({
  data: initial,
}: Props): ReactNode {
  const [showEmptyUBOForm, setShowEmptyUBOForm] = useState(
    initial.ubos.length === 0
  );
  const viewModel = useViewModel(initial.ubos);

  return (
    <div className="space-y-4">
      {viewModel.ubos.map((ubo, index) => {
        const isLast = index === viewModel.ubos.length - 1;
        const hideBottomBorder = isLast && !showEmptyUBOForm;

        return (
          <div
            key={ubo.uid}
            className={clsx(
              "border-b border-border pb-4",
              hideBottomBorder && "border-b-0"
            )}
          >
            <UltimateBeneficialOwnerFormItem
              data={ubo}
              onSaveUltimateBeneficialOwner={viewModel.saveUBO}
            />
          </div>
        );
      })}

      {showEmptyUBOForm ? (
        <UltimateBeneficialOwnerFormItem
          onSaveUltimateBeneficialOwner={async (prevState, formData) => {
            const state = await viewModel.saveUBO(prevState, formData);

            if (!state.error) {
              setShowEmptyUBOForm(false);
            }

            return state;
          }}
        />
      ) : (
        <div className="flex justify-center">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowEmptyUBOForm(true)}
          >
            <PlusIcon className="size-4" /> Add UBO
          </Button>
        </div>
      )}
    </div>
  );
}
