"use client";

import { HelpCircle } from "lucide-react";
import { type ReactNode, useActionState, useState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Datepicker } from "@/components/ui/datepicker";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { formatDateToISODay } from "@/lib/utils/formatDate";

import { SaveUltimateBeneficialOwnerFormState } from "../../actions";
import {
  isSandboxComplianceStatusChangeEnabled,
  ToggleUBOStatusSandboxButton,
} from "../sandbox";
import { type UltimateBeneficialOwnerData } from "./index";

export type UltimateBeneficialOwnerFormItemProps = {
  data?: UltimateBeneficialOwnerData["ubos"][number];
  saveUltimateBeneficialOwner: (
    prevState: SaveUltimateBeneficialOwnerFormState,
    formData: FormData
  ) => Promise<SaveUltimateBeneficialOwnerFormState>;
};

const defaultUBO = {
  uid: null,
  namesFirst: null,
  namePrefix: null,
  nameLast: null,
  dateOfBirth: null,
  isDecisionMaker: null,
  decisionMakerPercentage: null,
  isShareholder: null,
  shareholderPercentage: null,
  isPep: null,
  countryOfResidence: null,
  status: "new",
} as const;

function booleanToFormValue(value: boolean | null | undefined): string | null {
  if (value == null) {
    return null;
  }
  return value ? "yes" : "no";
}

export default function UltimateBeneficialOwnerFormItem({
  data = defaultUBO,
  saveUltimateBeneficialOwner,
}: UltimateBeneficialOwnerFormItemProps): ReactNode {
  const [state, formAction, isPending] = useActionState<
    SaveUltimateBeneficialOwnerFormState,
    FormData
  >(
    async (prevState, formData) => {
      const result = await saveUltimateBeneficialOwner(prevState, formData);

      if (result.data && !result.error) {
        toast.success("Persons with Significant Control saved successfully");
      }

      return result;
    },
    { data }
  );

  const [decisionMaker, setDecisionMaker] = useState<string | null>(
    booleanToFormValue(state.data.isDecisionMaker)
  );

  const [ownership, setOwnership] = useState<string | null>(
    booleanToFormValue(state.data.isShareholder)
  );

  const [politicalExposed, setPoliticalExposed] = useState<string | null>(
    booleanToFormValue(state.data.isPep)
  );

  const isReadonly =
    state.data.status === "verified" || state.data.status === "pending";

  const [selectedDateOfBirth, setSelectedDateOfBirth] = useState<
    Date | undefined
  >(data.dateOfBirth ? new Date(data.dateOfBirth) : undefined);

  return (
    <form action={formAction} className="max-w-sm w-full">
      <Input
        type="hidden"
        name="uid"
        defaultValue={state.data.uid ?? undefined}
        readOnly
      />
      <Input
        type="hidden"
        name="status"
        defaultValue={state.data.status ?? undefined}
        readOnly
      />
      <FieldGroup>
        {state.data.status === "verified" && (
          <div className="max-w-sm w-full">
            <div className="text-md rounded-md border border-green-200 bg-green-50 p-3 text-green-800">
              This UBO has been verified.
            </div>
          </div>
        )}
        {state.data.status === "pending" && (
          <div className="max-w-sm w-full">
            <div className="text-md rounded-md border border-yellow-200 bg-yellow-50 p-3 text-yellow-800">
              This UBO is under review and will be verified soon.
            </div>
          </div>
        )}
        <div className="space-y-4">
          <Field>
            <FieldLabel>
              Full name <span className="text-destructive">*</span>
            </FieldLabel>
            <div className="flex gap-2">
              <Input
                type="text"
                id="namesFirst"
                name="namesFirst"
                placeholder="Given names"
                defaultValue={state.data.namesFirst ?? undefined}
                className="flex-1"
                disabled={isReadonly}
              />
              <Input
                type="text"
                id="namePrefix"
                name="namePrefix"
                defaultValue={state.data.namePrefix ?? undefined}
                className="flex-1"
                disabled={isReadonly}
              />
              <Input
                type="text"
                id="nameLast"
                name="nameLast"
                placeholder="Last name"
                defaultValue={state.data.nameLast ?? undefined}
                className="flex-1"
                disabled={isReadonly}
              />
            </div>
          </Field>

          <div className="grid grid-cols-2 gap-4">
            <Field>
              <FieldLabel htmlFor="countryOfResidence">
                Country of residence <span className="text-destructive">*</span>
              </FieldLabel>
              <Select
                name="countryOfResidence"
                defaultValue={state.data.countryOfResidence ?? undefined}
                disabled={isReadonly}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent>
                  {COUNTRIES.map((country) => (
                    <SelectItem key={country} value={getCountryCode(country)}>
                      {country}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </Field>
            <Field>
              <Datepicker
                label={
                  <>
                    Date of birth <span className="text-destructive">*</span>
                  </>
                }
                value={selectedDateOfBirth}
                onChange={setSelectedDateOfBirth}
                disabled={isReadonly}
                disabledFilter={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
              />
              <Input
                type="hidden"
                name="dateOfBirth"
                defaultValue={
                  selectedDateOfBirth && formatDateToISODay(selectedDateOfBirth)
                }
              />
            </Field>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Field>
              <div className="flex items-center gap-2">
                <FieldLabel htmlFor="decisionMaker">
                  Decision maker ≥ 25%
                </FieldLabel>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="size-4 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      Indicate if this person has decision-making authority of
                      25% or more
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="flex items-center gap-4">
                <Input
                  type="hidden"
                  name="decisionMaker"
                  defaultValue={decisionMaker ?? undefined}
                />
                <RadioGroup
                  value={decisionMaker}
                  onValueChange={setDecisionMaker}
                  className="flex flex-row gap-4"
                  disabled={isReadonly}
                >
                  <div className="flex items-center gap-2">
                    <RadioGroupItem value="yes" id="decisionMakerYes" />
                    <Label htmlFor="decisionMakerYes" className="text-sm">
                      Yes
                    </Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <RadioGroupItem value="no" id="decisionMakerNo" />
                    <Label htmlFor="decisionMakerNo" className="text-sm">
                      No
                    </Label>
                  </div>
                </RadioGroup>
                <Input
                  type="number"
                  id="decisionMakerPercentage"
                  name="decisionMakerPercentage"
                  min="0"
                  max="100"
                  defaultValue={state.data.decisionMakerPercentage?.toString()}
                  className="w-20"
                  disabled={isReadonly}
                />
                <span className="text-sm text-muted-foreground">%</span>
              </div>
            </Field>

            <Field>
              <div className="flex items-center gap-2">
                <FieldLabel htmlFor="ownership">Ownership ≥ 25%</FieldLabel>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="size-4 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Indicate if this person has ownership of 25% or more</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="flex items-center gap-4">
                <Input
                  type="hidden"
                  name="ownership"
                  defaultValue={ownership ?? undefined}
                />
                <RadioGroup
                  value={ownership}
                  onValueChange={setOwnership}
                  className="flex flex-row gap-4"
                  disabled={isReadonly}
                >
                  <div className="flex items-center gap-2">
                    <RadioGroupItem value="yes" id="ownershipYes" />
                    <Label htmlFor="ownershipYes" className="text-sm">
                      Yes
                    </Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <RadioGroupItem value="no" id="ownershipNo" />
                    <Label htmlFor="ownershipNo" className="text-sm">
                      No
                    </Label>
                  </div>
                </RadioGroup>
                <Input
                  type="number"
                  id="ownershipPercentage"
                  name="ownershipPercentage"
                  min="0"
                  max="100"
                  defaultValue={state.data.shareholderPercentage?.toString()}
                  className="w-20"
                  disabled={isReadonly}
                />
                <span className="text-sm text-muted-foreground">%</span>
              </div>
            </Field>
          </div>
        </div>

        <div className="space-y-4">
          <Field>
            <div className="flex items-center gap-2">
              <FieldLabel htmlFor="politicalExposed">
                Political Exposed Person
              </FieldLabel>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="size-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Indicate if this person is a politically exposed person
                    (PEP)
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Input
              type="hidden"
              name="politicalExposed"
              defaultValue={politicalExposed ?? undefined}
            />
            <RadioGroup
              value={politicalExposed}
              onValueChange={setPoliticalExposed}
              className="flex flex-row gap-4"
              disabled={isReadonly}
            >
              <div className="flex items-center gap-2">
                <RadioGroupItem value="yes" id="politicalExposedYes" />
                <Label htmlFor="politicalExposedYes" className="text-sm">
                  Yes
                </Label>
              </div>
              <div className="flex items-center gap-2">
                <RadioGroupItem value="no" id="politicalExposedNo" />
                <Label htmlFor="politicalExposedNo" className="text-sm">
                  No
                </Label>
              </div>
            </RadioGroup>
          </Field>
        </div>

        {state.error && <FieldError>{state.error}</FieldError>}

        <div className="flex justify-end items-center gap-2">
          {isSandboxComplianceStatusChangeEnabled && state.data.uid && (
            <ToggleUBOStatusSandboxButton uboUid={state.data.uid} />
          )}
          {!isReadonly && (
            <Button type="submit" disabled={isPending}>
              {isPending ? "Saving..." : "Save"}
            </Button>
          )}
        </div>
      </FieldGroup>
    </form>
  );
}

function getCountryCode(countryName: string): string {
  // Country name to ISO 3166-1 alpha-3 code mapping
  const COUNTRY_CODE_MAP: Record<string, string> = {
    Afghanistan: "AFG",
    Albania: "ALB",
    Algeria: "DZA",
    Argentina: "ARG",
    Australia: "AUS",
    Austria: "AUT",
    Belgium: "BEL",
    Brazil: "BRA",
    Canada: "CAN",
    China: "CHN",
    Denmark: "DNK",
    Finland: "FIN",
    France: "FRA",
    Germany: "DEU",
    India: "IND",
    Italy: "ITA",
    Japan: "JPN",
    Netherlands: "NLD",
    Norway: "NOR",
    Poland: "POL",
    Russia: "RUS",
    Spain: "ESP",
    Sweden: "SWE",
    Switzerland: "CHE",
    "United Kingdom": "GBR",
    "United States": "USA",
  };

  return COUNTRY_CODE_MAP[countryName] || countryName; // Fallback to name if not found
}

// Static country list - can be expanded
const COUNTRIES = [
  "Afghanistan",
  "Albania",
  "Algeria",
  "Argentina",
  "Australia",
  "Austria",
  "Belgium",
  "Brazil",
  "Canada",
  "China",
  "Denmark",
  "Finland",
  "France",
  "Germany",
  "India",
  "Italy",
  "Japan",
  "Netherlands",
  "Norway",
  "Poland",
  "Russia",
  "Spain",
  "Sweden",
  "Switzerland",
  "United Kingdom",
  "United States",
];
