import { <PERSON><PERSON><PERSON><PERSON>2, <PERSON>, CircleEllipsis } from "lucide-react";
import type { ReactNode } from "react";

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import type { ComplianceRequirementStatus } from "../page";

type Props = {
  status: ComplianceRequirementStatus;
};

export default function StepIndicator({ status }: Props): ReactNode {
  const map: Record<ComplianceRequirementStatus, React.ReactNode> = {
    verified: (
      <Tooltip>
        <TooltipTrigger asChild>
          <CheckCircle2
            className="mt-1 size-5 shrink-0 text-green-600"
            aria-hidden="true"
          />
        </TooltipTrigger>
        <TooltipContent>
          <p>Verified</p>
        </TooltipContent>
      </Tooltip>
    ),
    pending: (
      <Tooltip>
        <TooltipTrigger asChild>
          <CircleEllipsis
            className="mt-1 size-5 shrink-0 text-yellow-600"
            aria-hidden="true"
          />
        </TooltipTrigger>
        <TooltipContent>
          <p>Pending</p>
        </TooltipContent>
      </Tooltip>
    ),
    unverified: (
      <Tooltip>
        <TooltipTrigger asChild>
          <Circle
            className="mt-1 size-5 shrink-0 text-red-600"
            strokeWidth={2}
            fill="none"
            aria-hidden="true"
          />
        </TooltipTrigger>
        <TooltipContent>
          <p>Unverified</p>
        </TooltipContent>
      </Tooltip>
    ),
  };

  return map[status];
}
