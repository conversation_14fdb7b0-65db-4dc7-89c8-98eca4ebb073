"use client";

import { useActionState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { verifyCoC, type VerifyCoCFormState } from "../actions";
import { ComplianceRequirementStatus } from "../page";
import { type FileSpec } from "../types";

function mimeTypeToExtension(mimeType: string): string {
  return "." + mimeType.split("/")[1] || "";
}

export type ChamberOfCommerceFormProps = {
  fileSpec: FileSpec;
  status: ComplianceRequirementStatus;
};

export default function ChamberOfCommerceForm({
  fileSpec,
  status,
}: ChamberOfCommerceFormProps) {
  const [state, action, isPending] = useActionState<
    VerifyCoCFormState,
    FormData
  >(async (prevState, formData) => {
    const result = await verifyCoC(prevState, formData);

    if (!result.error) {
      toast.success("Chamber of Commerce document uploaded successfully");
    }

    return result;
  }, {});

  if (status === "pending") {
    return (
      <div className="max-w-sm w-full">
        <div className="text-md rounded-md border border-yellow-200 bg-yellow-50 p-3 text-yellow-800">
          This Chamber of Commerce document is under review and will be verified
          soon.
        </div>
      </div>
    );
  }

  if (status === "verified") {
    return (
      <div className="max-w-sm w-full">
        <div className="text-md rounded-md border border-green-200 bg-green-50 p-3 text-green-800">
          This Chamber of Commerce document has been verified.
        </div>
      </div>
    );
  }

  const { maxFileSizeInBytes: maxFileSize, allowedMimeTypes } = fileSpec;

  return (
    <form action={action} className="max-w-sm w-full">
      <FieldGroup>
        <Field>
          <FieldLabel htmlFor="cocExtract">
            Upload Chamber of Commerce Extract{" "}
            <span className="text-destructive">*</span>
          </FieldLabel>
          <Input
            id="cocExtract"
            name="cocExtract"
            type="file"
            accept={allowedMimeTypes.map(mimeTypeToExtension).join(",")}
            disabled={isPending}
          />
          <p className="text-sm text-muted-foreground">
            Allowed formats:{" "}
            {allowedMimeTypes.map(mimeTypeToExtension).join(", ")}. Max size:{" "}
            {Math.round(maxFileSize / (1024 * 1024))}MB
          </p>
        </Field>

        {state.error && <FieldError>{state.error}</FieldError>}

        <div className="flex justify-end">
          <Button type="submit" disabled={isPending}>
            {isPending ? "Uploading..." : "Upload File"}
          </Button>
        </div>
      </FieldGroup>
    </form>
  );
}
