"use client";

import { useActionState } from "react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import {
  verifyOrganizationStructure,
  type VerifyOrganizationStructureFormState,
} from "../actions";
import { ComplianceRequirementStatus } from "../page";
import { type FileSpec } from "../types";

function mimeTypeToExtension(mimeType: string): string {
  return "." + mimeType.split("/")[1] || "";
}

export type OrganizationStructureFormProps = {
  fileSpec: FileSpec;
  status: ComplianceRequirementStatus;
};

export default function OrganizationStructureForm({
  fileSpec,
  status,
}: OrganizationStructureFormProps) {
  const [state, action, isPending] = useActionState<
    VerifyOrganizationStructureFormState,
    FormData
  >(async (prevState, formData) => {
    const result = await verifyOrganizationStructure(prevState, formData);

    if (!result.error) {
      toast.success("Organization structure document uploaded successfully");
    }

    return result;
  }, {});

  if (status === "pending") {
    return (
      <div className="max-w-sm w-full">
        <div className="text-md rounded-md border border-yellow-200 bg-yellow-50 p-3 text-yellow-800">
          This Organization Structure document is under review and will be
          verified soon.
        </div>
      </div>
    );
  }

  if (status === "verified") {
    return (
      <div className="max-w-sm w-full">
        <div className="text-md rounded-md border border-green-200 bg-green-50 p-3 text-green-800">
          This Organization Structure document has been verified.
        </div>
      </div>
    );
  }

  const { maxFileSizeInBytes: maxFileSize, allowedMimeTypes } = fileSpec;

  return (
    <form action={action} className="max-w-sm w-full">
      <FieldGroup>
        <Field>
          <FieldLabel htmlFor="organizationStructure">
            Upload Organization Structure{" "}
            <span className="text-destructive">*</span>
          </FieldLabel>
          <Input
            id="organizationStructure"
            name="organizationStructure"
            type="file"
            accept={allowedMimeTypes.map(mimeTypeToExtension).join(",")}
            disabled={isPending}
          />
          <p className="text-sm text-muted-foreground">
            Allowed formats:{" "}
            {allowedMimeTypes.map(mimeTypeToExtension).join(", ")}. Max size:{" "}
            {Math.round(maxFileSize / (1024 * 1024))}MB
          </p>
        </Field>

        {state.error && <FieldError>{state.error}</FieldError>}

        <div className="flex justify-end">
          <Button type="submit" disabled={isPending}>
            {isPending ? "Uploading..." : "Upload File"}
          </Button>
        </div>
      </FieldGroup>
    </form>
  );
}
