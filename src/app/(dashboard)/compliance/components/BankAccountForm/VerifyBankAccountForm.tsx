"use client";

import { useActionState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import {
  verifyBankAccount,
  type VerifyBankAccountFormState,
} from "../../actions";
import { type FileSpec } from "../../types";

function mimeTypeToExtension(mimeType: string): string {
  return "." + mimeType.split("/")[1] || "";
}

export type VerifyBankAccountFormProps = {
  bankAccountUid: string;
  bankStatementFrontFileSpec: FileSpec;
  bankStatementBackFileSpec: FileSpec;
};

export default function VerifyBankAccountForm({
  bankAccountUid,
  bankStatementFrontFileSpec,
  bankStatementBackFileSpec,
}: VerifyBankAccountFormProps) {
  const [state, action, isPending] = useActionState<
    VerifyBankAccountFormState,
    FormData
  >(async (prevState, formData) => {
    const result = await verifyBankAccount(prevState, formData);

    if (!result.error) {
      toast.success(
        "Bank account verification documents uploaded successfully"
      );
    }

    return result;
  }, {});

  return (
    <form action={action} className="max-w-sm w-full">
      <FieldGroup>
        <Input type="hidden" name="bankAccountUid" value={bankAccountUid} />
        <Field>
          <FieldLabel htmlFor="bankStatementFront">
            Upload Bank Statement (Front){" "}
            <span className="text-destructive">*</span>
          </FieldLabel>
          <Input
            id="bankStatementFront"
            name="bankStatementFront"
            type="file"
            accept={bankStatementFrontFileSpec.allowedMimeTypes
              .map(mimeTypeToExtension)
              .join(",")}
            disabled={isPending}
          />
          <p className="text-sm text-muted-foreground">
            Allowed formats:{" "}
            {bankStatementFrontFileSpec.allowedMimeTypes
              .map(mimeTypeToExtension)
              .join(", ")}
            . Max size:{" "}
            {Math.round(
              bankStatementFrontFileSpec.maxFileSizeInBytes / (1024 * 1024)
            )}
            MB
          </p>
        </Field>

        <Field>
          <FieldLabel htmlFor="bankStatementBack">
            Upload Bank Statement (Back){" "}
            <span className="text-destructive">*</span>
          </FieldLabel>
          <Input
            id="bankStatementBack"
            name="bankStatementBack"
            type="file"
            accept={bankStatementBackFileSpec.allowedMimeTypes
              .map(mimeTypeToExtension)
              .join(",")}
            disabled={isPending}
          />
          <p className="text-sm text-muted-foreground">
            Allowed formats:{" "}
            {bankStatementBackFileSpec.allowedMimeTypes
              .map(mimeTypeToExtension)
              .join(", ")}
            . Max size:{" "}
            {Math.round(
              bankStatementBackFileSpec.maxFileSizeInBytes / (1024 * 1024)
            )}
            MB
          </p>
        </Field>

        {state.error && <FieldError>{state.error}</FieldError>}

        <div className="flex justify-end">
          <Button type="submit" disabled={isPending}>
            {isPending ? "Uploading..." : "Upload Files"}
          </Button>
        </div>
      </FieldGroup>
    </form>
  );
}
