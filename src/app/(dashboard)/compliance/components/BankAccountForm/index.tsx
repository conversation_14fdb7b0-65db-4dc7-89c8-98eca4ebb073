"use client";

import type { ReactNode } from "react";
import { useActionState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { createBankAccount, CreateBankAccountFormState } from "../../actions";
import { ComplianceRequirementStatus } from "../../page";
import { type FileSpec } from "../../types";
import {
  isSandboxComplianceStatusChangeEnabled,
  ToggleBankAccountStatusSandboxButton,
} from "../sandbox";
import VerifyBankAccountForm from "./VerifyBankAccountForm";

export type BankAccountFormData = {
  account: {
    uid: string | null;
    status: "new" | "pending" | "approved" | "disapproved" | null;
    accountName: string | null;
    accountIban: string | null;
    accountBic: string | null;
  };
  bankStatementFrontFileSpec: FileSpec;
  bankStatementBackFileSpec: FileSpec;
};

type Props = {
  data: BankAccountFormData;
  status: ComplianceRequirementStatus;
};

export default function BankAccountForm({
  data: { account, bankStatementFrontFileSpec, bankStatementBackFileSpec },
  status,
}: Props): ReactNode {
  const [state, action, isPending] = useActionState<
    CreateBankAccountFormState,
    FormData
  >(
    async (prevState, formData) => {
      const result = await createBankAccount(prevState, formData);

      if (result.data && !result.error) {
        toast.success("Bank account saved successfully");
      }

      return result;
    },
    { data: account }
  );
  const accountNameError = state.fieldErrors?.accountName?.[0];
  const accountIbanError = state.fieldErrors?.accountIban?.[0];
  const accountBicError = state.fieldErrors?.accountBic?.[0];

  if (status === "unverified" && account.uid) {
    return (
      <VerifyBankAccountForm
        bankAccountUid={account.uid}
        bankStatementFrontFileSpec={bankStatementFrontFileSpec}
        bankStatementBackFileSpec={bankStatementBackFileSpec}
      />
    );
  }

  const isReadonly = account.uid != null;

  return (
    <form action={action} className="max-w-sm w-full">
      <Input
        type="hidden"
        name="uid"
        defaultValue={state.data.uid ?? undefined}
        readOnly
      />
      <Input
        type="hidden"
        name="status"
        defaultValue={state.data.status ?? undefined}
        readOnly
      />

      <FieldGroup>
        {state.data?.status === "approved" && (
          <div className="max-w-sm w-full">
            <div className="text-md rounded-md border border-green-200 bg-green-50 p-3 text-green-800">
              This bank account has been verified.
            </div>
          </div>
        )}

        {state.data?.status === "disapproved" && (
          <div className="max-w-sm w-full">
            <div className="text-md rounded-md border border-red-200 bg-red-50 p-3 text-red-800">
              This bank account has been disapproved.
            </div>
          </div>
        )}

        <div className="space-y-4">
          <Field data-invalid={Boolean(accountNameError)}>
            <FieldLabel htmlFor="accountName">
              Account Name <span className="text-destructive">*</span>
            </FieldLabel>
            <Input
              type="text"
              id="accountName"
              name="accountName"
              defaultValue={state.data.accountName ?? undefined}
              disabled={isReadonly}
              aria-invalid={Boolean(accountNameError)}
            />
            {accountNameError && <FieldError>{accountNameError}</FieldError>}
          </Field>

          <Field data-invalid={Boolean(accountIbanError)}>
            <FieldLabel htmlFor="accountIban">
              Account IBAN <span className="text-destructive">*</span>
            </FieldLabel>
            <Input
              type="text"
              id="accountIban"
              name="accountIban"
              defaultValue={state.data.accountIban ?? undefined}
              pattern="[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}"
              maxLength={34}
              placeholder="**********************"
              className="uppercase"
              disabled={isReadonly}
              aria-invalid={Boolean(accountIbanError)}
            />
            {accountIbanError && <FieldError>{accountIbanError}</FieldError>}
          </Field>

          <Field data-invalid={Boolean(accountBicError)}>
            <FieldLabel htmlFor="accountBic">
              Account BIC <span className="text-destructive">*</span>
            </FieldLabel>
            <Input
              type="text"
              id="accountBic"
              name="accountBic"
              defaultValue={state.data.accountBic ?? undefined}
              pattern="[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?"
              minLength={8}
              maxLength={11}
              placeholder="NWBKGB2L"
              className="uppercase"
              disabled={isReadonly}
              aria-invalid={Boolean(accountBicError)}
            />
            {accountBicError && <FieldError>{accountBicError}</FieldError>}
          </Field>
        </div>

        {state.error && <FieldError>{state.error}</FieldError>}

        <div className="flex justify-end items-center gap-2">
          {isSandboxComplianceStatusChangeEnabled && account.uid && (
            <ToggleBankAccountStatusSandboxButton
              bankAccountUid={account.uid}
            />
          )}
          {!isReadonly && (
            <Button type="submit" disabled={isPending}>
              {isPending ? "Creating..." : "Create bank account"}
            </Button>
          )}
        </div>
      </FieldGroup>
    </form>
  );
}
