"use client";

import { Settings } from "lucide-react";
import { useActionState, useId, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  changeComplianceStatus,
  type ChangeComplianceStatusFormState,
} from "./actions";

export default function ChangeStatusInSandboxButton() {
  const [type, setType] = useState<"coc_extract" | "organization_structure">(
    "coc_extract"
  );
  const [status, setStatus] = useState<"pending" | "verified" | "unverified">(
    "unverified"
  );
  const [state, action, isPending] = useActionState<
    ChangeComplianceStatusFormState,
    FormData
  >(changeComplianceStatus, {});

  const formId = useId();

  return (
    <>
      <Dialog>
        <DialogTrigger asChild>
          <Button
            type="button"
            size="icon"
            className="fixed bottom-4 right-4 z-50 rounded-full shadow-lg"
          >
            <Settings className="size-5" />
            <span className="sr-only">Change compliance status</span>
          </Button>
        </DialogTrigger>

        <DialogContent className="sm:max-w-sm">
          <DialogHeader>
            <form id={formId} action={action}>
              <DialogTitle>Change Compliance Status</DialogTitle>
              <DialogDescription>
                Update the compliance requirement status for a specific type.
              </DialogDescription>

              <FieldGroup>
                <Field>
                  <FieldLabel htmlFor="type">
                    Type <span className="text-destructive">*</span>
                  </FieldLabel>
                  <Select
                    value={type}
                    onValueChange={(value) =>
                      setType(value as "coc_extract" | "organization_structure")
                    }
                    disabled={isPending}
                    required
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="coc_extract">
                        Chamber of Commerce Extract
                      </SelectItem>
                      <SelectItem value="organization_structure">
                        Organization Structure
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <Input type="hidden" name="type" value={type} />
                </Field>

                <Field>
                  <FieldLabel htmlFor="status">
                    Status <span className="text-destructive">*</span>
                  </FieldLabel>
                  <Select
                    value={status}
                    onValueChange={(value) =>
                      setStatus(value as "pending" | "verified" | "unverified")
                    }
                    disabled={isPending}
                    required
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="verified">Verified</SelectItem>
                      <SelectItem value="unverified">Unverified</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input type="hidden" name="status" value={status} />
                </Field>

                {state.error && <FieldError>{state.error}</FieldError>}
              </FieldGroup>
            </form>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" form={formId} disabled={isPending}>
              {isPending ? "Changing..." : "Change Status"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
