"use server";

import { revalidatePath } from "next/cache";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  prismaClient,
} from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type ChangeComplianceStatusFormState = {
  error?: string;
};

export async function changeComplianceStatus(
  prevState: ChangeComplianceStatusFormState,
  formData: FormData
): Promise<ChangeComplianceStatusFormState> {
  const type = formData.get("type") as
    | "coc_extract"
    | "organization_structure"
    | null;

  const status = formData.get("status") as
    | "pending"
    | "verified"
    | "unverified"
    | null;

  if (!type) {
    return { error: "Type is required" };
  }

  if (!status) {
    return { error: "Status is required" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    assert(authUser, "Unauthorized");

    const member = await prismaClient.member.findFirstOrThrow({
      select: {
        organization: {
          select: {
            oppMerchantUid: true,
          },
        },
      },
      where: {
        userId: authUser.id,
      },
    });

    assert(
      member.organization.oppMerchantUid,
      "Organization should be connected to OPP merchant"
    );

    const apiKey = process.env.OPP_API_KEY;
    assert(apiKey, "OPP_API_KEY is not configured");

    const apiUrl = `https://api-sandbox.onlinebetaalplatform.nl/v1/merchants/${member.organization.oppMerchantUid}/compliance_requirements/change-compliance-status`;
    const body = new URLSearchParams();
    body.append("type", type);
    body.append("status", status);
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
      body,
    });

    if (!response.ok) {
      return {
        error: `Failed to change compliance status: ${response.statusText}`,
      };
    }

    revalidatePath("/compliance");

    return {};
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "Failed to change compliance status",
    };
  }
}
