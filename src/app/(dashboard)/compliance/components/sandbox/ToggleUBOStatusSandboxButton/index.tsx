"use client";

import { Settings } from "lucide-react";
import { useActionState, useId, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { toggleStatus, type ToggleStatusFormState } from "./actions";

type Props = {
  uboUid: string;
};

export default function ToggleUBOStatusSandboxButton({ uboUid }: Props) {
  const [status, setStatus] = useState<"pending" | "verified" | "unverified">(
    "pending"
  );
  const [state, action, isPending] = useActionState<
    ToggleStatusFormState,
    FormData
  >(toggleStatus, {});

  const formId = useId();

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button type="button" variant="outline" size="icon">
          <Settings className="size-4" />
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-sm">
        <DialogHeader>
          <form id={formId} action={action}>
            <DialogTitle>Change UBO Status</DialogTitle>
            <DialogDescription>
              Update the UBO status in sandbox environment.
            </DialogDescription>

            <FieldGroup>
              <Input type="hidden" name="uboUid" value={uboUid} />

              <Field>
                <FieldLabel htmlFor="status">
                  Status <span className="text-destructive">*</span>
                </FieldLabel>
                <Select
                  value={status}
                  onValueChange={(value) =>
                    setStatus(value as "pending" | "verified" | "unverified")
                  }
                  disabled={isPending}
                  required
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                    <SelectItem value="unverified">Unverified</SelectItem>
                  </SelectContent>
                </Select>
                <Input type="hidden" name="status" value={status} />
              </Field>

              {state.error && <FieldError>{state.error}</FieldError>}
            </FieldGroup>
          </form>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending ? "Changing..." : "Change Status"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
