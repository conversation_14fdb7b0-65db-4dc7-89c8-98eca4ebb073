"use client";

import clsx from "clsx";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Card } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

import {
  type ComplianceRequirementStatus,
  type GetMerchantComplianceRequirementsResponse,
} from "../page";
import { type FileSpec } from "../types";
import BankAccountForm, { type BankAccountFormData } from "./BankAccountForm";
import ChamberOfCommerceForm from "./ChamberOfCommerceForm";
import ContactForm from "./ContactForm";
import { type ContactFormData } from "./ContactForm";
import OrganizationStructureForm from "./OrganizationStructureForm";
import StepIndicator from "./StepIndicator";
import UltimateBeneficialOwnerForm from "./UltimateBeneficialOwnerForm";
import { type UltimateBeneficialOwnerData } from "./UltimateBeneficialOwnerForm";

type ComplianceRequirementsListProps = {
  requirements: GetMerchantComplianceRequirementsResponse;
  bankAccountData: BankAccountFormData;
  chamberOfCommerceData: {
    fileSpec: FileSpec;
  };
  contactData: ContactFormData;
  organizationStructureData: {
    fileSpec: FileSpec;
  };
  ultimateBeneficialOwnerData: UltimateBeneficialOwnerData;
};

function CircularProgress({
  completed,
  total,
}: {
  completed: number;
  total: number;
}) {
  const progress = total > 0 ? ((total - completed) / total) * 100 : 0;
  const strokeDashoffset = 100 - progress;

  return (
    <svg
      className="-rotate-90 scale-y-[-1]"
      height="14"
      width="14"
      viewBox="0 0 14 14"
    >
      <circle
        className="stroke-muted"
        cx="7"
        cy="7"
        fill="none"
        r="6"
        strokeWidth="2"
        pathLength="100"
      />
      <circle
        className="stroke-primary"
        cx="7"
        cy="7"
        fill="none"
        r="6"
        strokeWidth="2"
        pathLength="100"
        strokeDasharray="100"
        strokeLinecap="round"
        style={{ strokeDashoffset }}
      />
    </svg>
  );
}

function getRequirementName(
  key: Exclude<keyof GetMerchantComplianceRequirementsResponse, "other">
): string {
  const names: Record<
    Exclude<keyof GetMerchantComplianceRequirementsResponse, "other">,
    string
  > = {
    bankAccount: "Bank Account",
    chamberOfCommerceExtract: "Chamber of Commerce Extract",
    contact: "Contact",
    organizationStructure: "Organization Structure",
    ultimateBenificialOwner: "Persons with Significant Control",
  };
  return names[key];
}

function getRequirementDescription(
  key: Exclude<keyof GetMerchantComplianceRequirementsResponse, "other">
): string {
  const descriptions: Record<
    Exclude<keyof GetMerchantComplianceRequirementsResponse, "other">,
    string
  > = {
    bankAccount: "Provide your bank account details for payment processing.",
    chamberOfCommerceExtract:
      "Upload your Chamber of Commerce extract document for verification.",
    contact: "Update your contact information to ensure we can reach you.",
    organizationStructure: "Provide details about your organization structure.",
    ultimateBenificialOwner:
      "Submit information about the persons with significant control of your organization.",
  };
  return descriptions[key];
}

export default function ComplianceRequirementsList({
  requirements,
  bankAccountData,
  chamberOfCommerceData,
  contactData,
  organizationStructureData,
  ultimateBeneficialOwnerData,
}: ComplianceRequirementsListProps) {
  const { other, ...primaryRequirements } = requirements;

  const requirementsArray = Object.entries(primaryRequirements).map(
    ([key, status]) => ({
      id: key as Exclude<
        keyof GetMerchantComplianceRequirementsResponse,
        "other"
      >,
      status: status as ComplianceRequirementStatus,
      title: getRequirementName(
        key as Exclude<keyof GetMerchantComplianceRequirementsResponse, "other">
      ),
      description: getRequirementDescription(
        key as Exclude<keyof GetMerchantComplianceRequirementsResponse, "other">
      ),
    })
  );

  const completedCount = requirementsArray.filter(
    (r) => r.status === "verified"
  ).length;

  const remainingCount = requirementsArray.length - completedCount;

  const [openStepId, setOpenStepId] = useState<string | null>(() => {
    const firstIncomplete = requirementsArray.find(
      (r) => r.status !== "verified"
    );

    return firstIncomplete?.id ?? requirementsArray[0]?.id ?? null;
  });

  return (
    <div className="w-full">
      <Card className="rounded-lg border bg-card p-4 text-card-foreground shadow-xs">
        <div className="mb-4 mr-2 flex flex-col justify-between sm:flex-row sm:items-center">
          <h3 className="ml-2 font-semibold text-foreground">
            Compliance Requirements
          </h3>
          <div className="mt-2 flex items-center justify-end sm:mt-0">
            <CircularProgress
              completed={remainingCount}
              total={requirementsArray.length}
            />
            <div className="ml-1.5 mr-3 text-sm text-muted-foreground">
              <span className="font-medium text-foreground">
                {remainingCount}
              </span>{" "}
              out of{" "}
              <span className="font-medium text-foreground">
                {requirementsArray.length} requirements
              </span>{" "}
              left
            </div>
          </div>
        </div>

        <div className="space-y-0">
          {requirementsArray.map((requirement, index) => {
            const isOpen = openStepId === requirement.id;
            const isFirst = index === 0;
            const prevStep = requirementsArray[index - 1];
            const isPrevOpen = prevStep && openStepId === prevStep.id;
            const showBorderTop = !isFirst && !isOpen && !isPrevOpen;

            return (
              <Collapsible
                key={requirement.id}
                open={isOpen}
                onOpenChange={(open) => {
                  setOpenStepId(open ? requirement.id : null);
                }}
                className={clsx(showBorderTop && "border-t border-border")}
              >
                <div
                  className={clsx(
                    "relative rounded-lg transition-colors",
                    isOpen && "border border-border"
                  )}
                >
                  <CollapsibleTrigger
                    className={clsx(
                      "w-full cursor-pointer text-left rounded-lg"
                    )}
                  >
                    <div className="relative flex items-center justify-between gap-3 py-3 pl-4 pr-2">
                      <div className="flex w-full gap-3">
                        <div className="shrink-0">
                          <StepIndicator status={requirement.status} />
                        </div>
                        <div className="mt-0.5 grow">
                          <h4 className="font-semibold text-foreground">
                            {requirement.title}
                          </h4>
                        </div>
                      </div>
                      <ChevronRight
                        className={clsx(
                          "h-4 w-4 shrink-0 text-muted-foreground transition-transform",
                          isOpen && "rotate-90"
                        )}
                        aria-hidden="true"
                      />
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <div className="px-4 pb-3">
                      <p className="mt-2 text-sm text-muted-foreground">
                        {requirement.description}
                      </p>

                      <div className="mt-3">
                        {requirement.id === "bankAccount" && (
                          <BankAccountForm
                            data={bankAccountData}
                            status={requirement.status}
                          />
                        )}

                        {requirement.id === "chamberOfCommerceExtract" && (
                          <ChamberOfCommerceForm
                            fileSpec={chamberOfCommerceData.fileSpec}
                            status={requirement.status}
                          />
                        )}

                        {requirement.id === "contact" && (
                          <ContactForm data={contactData} />
                        )}

                        {requirement.id === "organizationStructure" && (
                          <OrganizationStructureForm
                            fileSpec={organizationStructureData.fileSpec}
                            status={requirement.status}
                          />
                        )}

                        {requirement.id === "ultimateBenificialOwner" && (
                          <UltimateBeneficialOwnerForm
                            data={ultimateBeneficialOwnerData}
                          />
                        )}
                      </div>
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            );
          })}

          {(other.status === "pending" || other.status === "unverified") && (
            <div className="group border-t border-border">
              <Collapsible
                open={openStepId === "other"}
                onOpenChange={(open) => {
                  setOpenStepId(open ? "other" : null);
                }}
              >
                <div
                  className={clsx(
                    "relative overflow-hidden rounded-lg transition-colors",
                    openStepId === "other" && "border border-border bg-muted"
                  )}
                >
                  <CollapsibleTrigger className="block w-full cursor-pointer text-left focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                    <div className="relative flex items-center justify-between gap-3 py-3 pl-4 pr-2">
                      <div className="flex w-full gap-3">
                        <div className="shrink-0">
                          <StepIndicator status={other.status} />
                        </div>
                        <div className="mt-0.5 grow">
                          <h4 className="font-semibold text-foreground">
                            Other
                          </h4>
                        </div>
                      </div>
                      <ChevronRight
                        className={clsx(
                          "h-4 w-4 shrink-0 text-muted-foreground transition-transform",
                          openStepId === "other" && "rotate-90"
                        )}
                        aria-hidden="true"
                      />
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <div className="px-4 pb-3">
                      <p className="mt-2 text-sm text-muted-foreground sm:max-w-64 md:max-w-xs">
                        Complete additional compliance requirements as needed.
                      </p>
                      <div className="mt-3">
                        <Link
                          href={other.url}
                          className="text-sm text-primary hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Complete other compliance requirements
                        </Link>
                      </div>
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
