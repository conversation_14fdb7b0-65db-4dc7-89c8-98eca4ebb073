"use client";

import clsx from "clsx";
import { PlusIcon } from "lucide-react";
import { useState } from "react";

import { Button } from "@/components/ui/button";

import ContactFormItem from "./ContactFormItem";
import { useViewModel } from "./hooks";
import { type ContactFormData } from "./types";
import VerifyContactForm from "./VerifyContactForm";

type Props = {
  data: ContactFormData;
};

export default function ContactForm({
  data: {
    contacts,
    passportFileSpec,
    driversLicenseFrontFileSpec,
    driversLicenseBackFileSpec,
  },
}: Props) {
  const [showEmptyContactForm, setShowEmptyContactForm] = useState(
    contacts.length === 0
  );
  const viewModel = useViewModel(contacts);

  return (
    <div className="space-y-4">
      {viewModel.contacts.map((contact, index) => {
        const isLast = index === viewModel.contacts.length - 1;
        const hideBottomBorder = isLast && !showEmptyContactForm;

        return (
          <div
            key={contact.uid}
            className={clsx(
              "border-b border-border pb-4",
              hideBottomBorder && "border-b-0"
            )}
          >
            {contact.status === "unverified" && contact.uid ? (
              <VerifyContactForm
                contactUid={contact.uid}
                onVerifyContact={viewModel.verifyContact}
                passportFileSpec={passportFileSpec}
                driversLicenseFrontFileSpec={driversLicenseFrontFileSpec}
                driversLicenseBackFileSpec={driversLicenseBackFileSpec}
              />
            ) : (
              <ContactFormItem
                data={contact}
                onSaveContact={viewModel.saveContact}
              />
            )}
          </div>
        );
      })}

      {showEmptyContactForm ? (
        <ContactFormItem
          onSaveContact={async (prevState, formData) => {
            const state = await viewModel.saveContact(prevState, formData);

            if (!state.error) {
              setShowEmptyContactForm(false);
            }

            return state;
          }}
        />
      ) : (
        <div className="flex justify-center">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowEmptyContactForm(true)}
          >
            <PlusIcon className="size-4" /> Add Contact
          </Button>
        </div>
      )}
    </div>
  );
}
