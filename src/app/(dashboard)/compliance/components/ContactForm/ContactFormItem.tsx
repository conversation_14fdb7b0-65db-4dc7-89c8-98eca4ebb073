"use client";

import { useActionState, useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Datepicker } from "@/components/ui/datepicker";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDateToISODay } from "@/lib/utils/formatDate";

import { type SaveContactInformationFormState } from "../../actions";
import { type ContactFormData } from "./types";

const TITLES = [
  { value: "mr", label: "Mr" },
  { value: "mrs", label: "Mrs" },
];

export type ContactFormItemProps = {
  data?: ContactFormData["contacts"][number];
  onSaveContact: (
    prevState: SaveContactInformationFormState,
    formData: FormData
  ) => Promise<SaveContactInformationFormState>;
};

const defaultContact: ContactFormData["contacts"][number] = {
  uid: null,
  title: null,
  name: {
    initials: null,
    namesGiven: null,
    first: null,
    last: null,
  },
  dateOfBirth: null,
  emailaddress: null,
  phonenumber: null,
  status: null,
};

export default function ContactFormItem({
  data = defaultContact,
  onSaveContact,
}: ContactFormItemProps) {
  const [state, formAction, isPending] = useActionState<
    SaveContactInformationFormState,
    FormData
  >(onSaveContact, { data });
  const isReadOnly =
    state.data.status === "verified" || state.data.status === "pending";

  const [selectedDateOfBirth, setSelectedDateOfBirth] = useState<
    Date | undefined
  >(data.dateOfBirth ? new Date(data.dateOfBirth) : undefined);

  return (
    <form action={formAction} className="max-w-sm w-full">
      <Input
        type="hidden"
        name="uid"
        value={state.data.uid ?? undefined}
        readOnly
      />

      <Input
        type="hidden"
        name="status"
        value={state.data.status ?? undefined}
        readOnly
      />

      <FieldGroup>
        {state.data.status === "verified" && (
          <div className="max-w-sm w-full">
            <div className="text-md rounded-md border border-green-200 bg-green-50 p-3 text-green-800">
              This contact has been verified.
            </div>
          </div>
        )}

        {state.data.status === "pending" && (
          <div className="max-w-sm w-full">
            <div className="text-md rounded-md border border-yellow-200 bg-yellow-50 p-3 text-yellow-800">
              This contact is under review and will be verified soon.
            </div>
          </div>
        )}

        <div className="space-y-4">
          <Field>
            <FieldLabel>
              Name <span className="text-destructive">*</span>
            </FieldLabel>
            <div className="flex gap-2">
              <Input
                type="text"
                id="nameInitials"
                name="nameInitials"
                placeholder="Initials"
                defaultValue={state.data.name.initials ?? undefined}
                className="flex-1"
                disabled={isReadOnly}
              />
              <Input
                type="text"
                id="namesGiven"
                name="namesGiven"
                placeholder="Names given"
                defaultValue={state.data.name.namesGiven ?? undefined}
                className="flex-1"
                disabled={isReadOnly}
              />
            </div>
            <div className="flex gap-2 mt-2">
              <Input
                type="text"
                id="nameFirst"
                name="nameFirst"
                placeholder="First name"
                defaultValue={state.data.name.first ?? undefined}
                className="flex-1"
                disabled={isReadOnly}
              />
              <Input
                type="text"
                id="nameLast"
                name="nameLast"
                placeholder="Last name"
                defaultValue={state.data.name.last ?? undefined}
                className="flex-1"
                disabled={isReadOnly}
              />
            </div>
          </Field>
          <div className="grid grid-cols-2 gap-4">
            <Field>
              <FieldLabel htmlFor="title">
                Title <span className="text-destructive">*</span>
              </FieldLabel>
              <Select
                name="title"
                defaultValue={state.data.title ?? undefined}
                disabled={isReadOnly}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select title" />
                </SelectTrigger>
                <SelectContent>
                  {TITLES.map((title) => (
                    <SelectItem key={title.value} value={title.value}>
                      {title.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </Field>

            <Field>
              <Datepicker
                label={
                  <>
                    Date of birth <span className="text-destructive">*</span>
                  </>
                }
                value={selectedDateOfBirth}
                onChange={setSelectedDateOfBirth}
                disabled={isReadOnly}
              />
              <Input
                type="hidden"
                name="dateOfBirth"
                defaultValue={
                  selectedDateOfBirth && formatDateToISODay(selectedDateOfBirth)
                }
              />
            </Field>
          </div>
          <Field>
            <FieldLabel htmlFor="emailaddress">
              Email address <span className="text-destructive">*</span>
            </FieldLabel>
            <Input
              type="email"
              id="emailaddress"
              name="emailaddress"
              defaultValue={state.data.emailaddress ?? undefined}
              autoComplete="email"
              disabled={isReadOnly}
            />
          </Field>

          <Field>
            <FieldLabel htmlFor="phonenumber">
              Phone number <span className="text-destructive">*</span>
            </FieldLabel>
            <Input
              type="tel"
              id="phonenumber"
              name="phonenumber"
              defaultValue={state.data.phonenumber ?? undefined}
              autoComplete="tel"
              disabled={isReadOnly}
            />
          </Field>
        </div>

        {state.error && <FieldError>{state.error}</FieldError>}

        {!isReadOnly && (
          <Field className="self-end w-fit">
            <Button type="submit" disabled={isPending}>
              {isPending ? "Saving..." : "Save"}
            </Button>
          </Field>
        )}
      </FieldGroup>
    </form>
  );
}
