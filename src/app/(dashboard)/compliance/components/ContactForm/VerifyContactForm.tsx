"use client";

import { useActionState, useState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Field, FieldGroup, FieldLabel } from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { verifyContact, type VerifyContactFormState } from "../../actions";
import { type FileSpec } from "../../types";

type DocumentType = "passport" | "driver-license";

type Props = {
  passportFileSpec: FileSpec;
  driversLicenseFrontFileSpec: FileSpec;
  driversLicenseBackFileSpec: FileSpec;
  contactUid: string;
};

function mimeTypeToExtension(mimeType: string): string {
  return "." + mimeType.split("/")[1] || "";
}

export default function VerifyContactForm({
  contactUid,
  passportFileSpec,
  driversLicenseFrontFileSpec,
  driversLicenseBackFileSpec,
}: Props) {
  const [documentType, setDocumentType] = useState<DocumentType>("passport");
  const [state, action, isPending] = useActionState<
    VerifyContactFormState,
    FormData
  >(async (prevState, formData) => {
    const result = await verifyContact(prevState, formData);

    if (!result.error) {
      toast.success("Contact verification documents uploaded successfully");
    }

    return result;
  }, {});

  return (
    <form action={action} className="max-w-sm w-full">
      <Input type="hidden" name="contactUid" value={contactUid} />

      <FieldGroup>
        <Field>
          <FieldLabel htmlFor="documentType">Document Type</FieldLabel>
          <Select
            name="documentType"
            value={documentType}
            onValueChange={(value) => setDocumentType(value as DocumentType)}
            disabled={isPending}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select document type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="passport">Passport</SelectItem>
              <SelectItem value="driver-license">Driver License</SelectItem>
            </SelectContent>
          </Select>
        </Field>

        {documentType === "passport" && passportFileSpec && (
          <Field>
            <FieldLabel htmlFor="passport">
              Upload Passport <span className="text-destructive">*</span>
            </FieldLabel>
            <Input
              id="passport"
              name="passport"
              type="file"
              accept={passportFileSpec.allowedMimeTypes
                .map(mimeTypeToExtension)
                .join(",")}
              disabled={isPending}
            />
            <p className="text-sm text-muted-foreground">
              Allowed formats:{" "}
              {passportFileSpec.allowedMimeTypes
                .map(mimeTypeToExtension)
                .join(", ")}
              . Max size:{" "}
              {Math.round(passportFileSpec.maxFileSizeInBytes / (1024 * 1024))}
              MB
            </p>
          </Field>
        )}

        {documentType === "driver-license" &&
          driversLicenseFrontFileSpec &&
          driversLicenseBackFileSpec && (
            <>
              <Field>
                <FieldLabel htmlFor="driverLicenseFront">
                  Upload Driver License (Front){" "}
                  <span className="text-destructive">*</span>
                </FieldLabel>
                <Input
                  id="driverLicenseFront"
                  name="driverLicenseFront"
                  type="file"
                  accept={driversLicenseFrontFileSpec.allowedMimeTypes
                    .map(mimeTypeToExtension)
                    .join(",")}
                  disabled={isPending}
                />
                <p className="text-sm text-muted-foreground">
                  Allowed formats:{" "}
                  {driversLicenseFrontFileSpec.allowedMimeTypes
                    .map(mimeTypeToExtension)
                    .join(", ")}
                  . Max size:{" "}
                  {Math.round(
                    driversLicenseFrontFileSpec.maxFileSizeInBytes /
                      (1024 * 1024)
                  )}
                  MB
                </p>
              </Field>

              <Field>
                <FieldLabel htmlFor="driverLicenseBack">
                  Upload Driver License (Back){" "}
                  <span className="text-destructive">*</span>
                </FieldLabel>
                <Input
                  id="driverLicenseBack"
                  name="driverLicenseBack"
                  type="file"
                  accept={driversLicenseBackFileSpec.allowedMimeTypes
                    .map(mimeTypeToExtension)
                    .join(",")}
                  disabled={isPending}
                />
                <p className="text-sm text-muted-foreground">
                  Allowed formats:{" "}
                  {driversLicenseBackFileSpec.allowedMimeTypes
                    .map(mimeTypeToExtension)
                    .join(", ")}
                  . Max size:{" "}
                  {Math.round(
                    driversLicenseBackFileSpec.maxFileSizeInBytes /
                      (1024 * 1024)
                  )}
                  MB
                </p>
              </Field>
            </>
          )}

        {state.error && (
          <Field>
            <div className="rounded-md border border-destructive bg-destructive/10 p-3 text-sm text-destructive">
              {state.error}
            </div>
          </Field>
        )}

        <div className="flex justify-end">
          <Button type="submit" disabled={isPending}>
            {isPending ? "Uploading..." : "Upload File(s)"}
          </Button>
        </div>
      </FieldGroup>
    </form>
  );
}
