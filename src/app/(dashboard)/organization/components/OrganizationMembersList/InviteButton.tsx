"use client";

import { useActionState, useId, useState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Field, FieldGroup, FieldLabel } from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { inviteUserSchema } from "@/domain/schemas/organization";

import { inviteUser, InviteUserFormState } from "../../actions";

type InviteUserFormValues = {
  email: string | null;
  role: "admin" | "user" | null;
};

type InviteUserFormUiState = InviteUserFormState & {
  values?: InviteUserFormValues;
};

export type InviteButtonProps = {
  organizationId: string;
};

export default function InviteButton({ organizationId }: InviteButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const formId = useId();

  const [state, formAction, isPending] = useActionState<
    InviteUserFormUiState,
    FormData
  >(async (prevState, formData) => {
    const email = formData.get("email") as string | null;
    const role = formData.get("role") as InviteUserFormValues["role"] | null;

    const parsed = inviteUserSchema.safeParse({
      email,
      organizationId,
      role,
    });

    const values: InviteUserFormValues = { email, role };

    if (!parsed.success) {
      return {
        fieldErrors: parsed.error.flatten().fieldErrors,
        values,
      };
    }

    const result = await inviteUser(prevState, formData);

    if (result.error || result.fieldErrors) {
      return { ...result, values };
    }

    setIsDialogOpen(false);
    toast.success("Member invited successfully");

    return {};
  }, {});

  const emailError = state.fieldErrors?.email?.[0];
  const roleError = state.fieldErrors?.role?.[0];

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button>Invite</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-sm">
        <DialogHeader>
          <DialogTitle>Invite Member</DialogTitle>
        </DialogHeader>
        <form id={formId} action={formAction} noValidate>
          {state.error && (
            <div className="mb-4 text-destructive">{state.error}</div>
          )}

          <Input
            type="hidden"
            name="organizationId"
            value={organizationId}
            readOnly
          />

          <FieldGroup>
            <div className="space-y-4">
              <Field data-invalid={Boolean(emailError)}>
                <FieldLabel htmlFor="email">Email Address</FieldLabel>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  disabled={isPending}
                  defaultValue={state.values?.email ?? ""}
                  aria-invalid={Boolean(emailError)}
                />
                {emailError && (
                  <div className="mt-2 text-sm text-destructive">
                    {emailError}
                  </div>
                )}
              </Field>

              <Field data-invalid={Boolean(roleError)}>
                <FieldLabel htmlFor="role">Role</FieldLabel>
                <Select
                  name="role"
                  defaultValue={state.values?.role ?? "user"}
                  disabled={isPending}
                >
                  <SelectTrigger
                    className="w-full"
                    id="role"
                    aria-invalid={Boolean(roleError)}
                  >
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
                {roleError && (
                  <div className="mt-2 text-sm text-destructive">
                    {roleError}
                  </div>
                )}
              </Field>
            </div>
          </FieldGroup>
        </form>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Close
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending ? "Inviting..." : "Invite"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
