"use client";

import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import formatDate from "@/lib/utils/formatDate";

import { Member, MemberRole, MemberStatus } from "../../types";
import ResumeButton from "./ResumeButton";
import SuspendButton from "./SuspendButton";

export type MembersTableProps = {
  currentMember: Member;
  members: Member[];
  organizationId: string;
  onSuspend: (params: { userId: string; suspendedAt: string }) => void;
  onResume: (params: { userId: string; invitedAt: string }) => void;
};

export default function MembersTable({
  currentMember,
  organizationId,
  members,
  onSuspend,
  onResume,
}: MembersTableProps) {
  if (members.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="mb-4 text-muted-foreground">No members yet</p>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow className="hover:bg-transparent border-b">
              <TableHead className="h-12 px-4 font-medium">Email</TableHead>
              <TableHead className="h-12 px-4 font-medium">Role</TableHead>
              <TableHead className="h-12 px-4 font-medium">Status</TableHead>
              <TableHead className="h-12 px-4 font-medium">
                Invited At
              </TableHead>
              <TableHead className="h-12 px-4 font-medium">Joined At</TableHead>
              <TableHead className="h-12 px-4 font-medium">
                Suspended At
              </TableHead>
              {currentMember.role === MemberRole.BuilderAdmin && (
                <TableHead className="h-12 px-4 font-medium w-[100px]">
                  Actions
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((m) => (
              <TableRow key={m.userId} className="hover:bg-muted/50">
                <TableCell className="h-16 px-4 font-medium">
                  {m.email}
                </TableCell>
                <TableCell className="h-16 px-4">
                  <Badge
                    variant={
                      {
                        [MemberRole.BuilderAdmin]: "default" as const,
                        [MemberRole.BuilderUser]: "secondary" as const,
                      }[m.role]
                    }
                  >
                    {m.role === MemberRole.BuilderAdmin ? "Admin" : "User"}
                  </Badge>
                </TableCell>
                <TableCell className="h-16 px-4">
                  <Badge
                    variant={
                      {
                        [MemberStatus.Active]: "default" as const,
                        [MemberStatus.Invited]: "secondary" as const,
                        [MemberStatus.Suspended]: "destructive" as const,
                      }[m.status]
                    }
                  >
                    {m.status}
                  </Badge>
                </TableCell>
                <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                  {formatDate(m.invitedAt)}
                </TableCell>
                <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                  {m.joinedAt ? formatDate(m.joinedAt) : "-"}
                </TableCell>
                <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                  {m.suspendedAt ? formatDate(m.suspendedAt) : "-"}
                </TableCell>

                {currentMember.role === MemberRole.BuilderAdmin && (
                  <TableCell className="h-16 px-4">
                    {m.status === MemberStatus.Active && (
                      <SuspendButton
                        member={m}
                        organizationId={organizationId}
                        onSuspend={onSuspend}
                      />
                    )}
                    {m.status === MemberStatus.Suspended && (
                      <ResumeButton
                        member={m}
                        organizationId={organizationId}
                        onResume={onResume}
                      />
                    )}
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <div className="mt-4 text-sm text-muted-foreground">
        Total members: {members.length}
      </div>
    </>
  );
}
