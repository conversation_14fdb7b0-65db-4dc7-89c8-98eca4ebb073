"use client";

import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import formatDate from "@/lib/utils/formatDate";

import { Organization, OrganizationStatus } from "../../types";

export type OrganizationDetailsProps = {
  organization: Organization;
};

export default function OrganizationDetails({
  organization,
}: OrganizationDetailsProps) {
  return (
    <div className="px-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{organization.name}</CardTitle>
          <CardDescription>
            Organization details and information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Status
              </p>
              <Badge
                variant={
                  organization.status === OrganizationStatus.Active
                    ? ("default" as const)
                    : ("destructive" as const)
                }
                className="mt-1"
              >
                {organization.status}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Created At
              </p>
              <p className="text-sm mt-1">
                {formatDate(organization.createdAt)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
