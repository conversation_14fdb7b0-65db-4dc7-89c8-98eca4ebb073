"use server";

import { revalidatePath } from "next/cache";
import assert from "node:assert";

import {
  ACCEPT_INVITE_REDIRECT_URL,
  createServerActionSupabaseClient,
  emailService,
  organizationRepository,
  prismaClient,
  userRepository,
} from "@/app/dependencies";
import {
  inviteTeamMember,
  MemberRole,
  MemberStatus,
  resumeTeamMember,
  suspendTeamMember,
} from "@/domain/organization";
import {
  inviteUserSchema,
  resumeMemberSchema,
  suspendMemberSchema,
} from "@/domain/schemas/organization";
import { createUser, UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type InviteUserFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function inviteUser(
  prevState: InviteUserFormState,
  formData: FormData
): Promise<InviteUserFormState> {
  const parsed = inviteUserSchema.safeParse({
    email: formData.get("email"),
    organizationId: formData.get("organizationId"),
    role: formData.get("role") ?? "user",
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { email, organizationId: orgId, role } = parsed.data;

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();

    if (!authUser) {
      return { error: "Unauthorized" };
    }

    const org = await organizationRepository.findById(orgId);

    if (!org) {
      return { error: "Organization not found" };
    }

    const inviter = org.members.find((m) => m.userId === authUser.id);

    if (!inviter) {
      return {
        error: "You are not a member of this organization",
      };
    }

    const user = await prismaClient.user.findFirst({
      where: { email },
    });

    if (user) {
      return {
        error: "The user has already been invited to some organization",
      };
    }

    const redirectTo = `${ACCEPT_INVITE_REDIRECT_URL}?organization_id=${org.id}&email=${encodeURIComponent(email)}`;

    const { error, data: invitedAuthUser } = await authService.sendInviteEmail({
      email,
      redirectTo,
    });

    if (error) {
      return { error: error.message };
    }

    const domainUser = createUser({
      id: invitedAuthUser.id,
      email,
      name: email,
      acceptedTermsAndConditionsAt: null,
      status: UserStatus.ACTIVE,
    });

    const newOrg = inviteTeamMember(org, {
      userId: invitedAuthUser.id,
      invitedBy: inviter,
      role:
        role === "admin" ? MemberRole.BUILDER_ADMIN : MemberRole.BUILDER_USER,
    });

    await userRepository.add(domainUser);
    await organizationRepository.update(org, newOrg);

    const member = newOrg.members.find((m) => m.userId === domainUser.id);
    assert(member, "Invited member not found");

    revalidatePath(`/organization`);

    return {};
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to invite member",
    };
  }
}

export type SuspendMemberFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function suspendMember(
  prevState: SuspendMemberFormState,
  formData: FormData
): Promise<SuspendMemberFormState> {
  const parsed = suspendMemberSchema.safeParse({
    userId: formData.get("userId"),
    organizationId: formData.get("organizationId"),
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { userId, organizationId: orgId } = parsed.data;

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();

    if (!authUser) {
      return { error: "Unauthorized" };
    }

    const org = await organizationRepository.findById(orgId);

    if (!org) {
      return { error: "Organization not found" };
    }

    const suspendedBy = org.members.find((m) => m.userId === authUser.id);

    if (!suspendedBy) {
      return { error: "You are not a member of this organization" };
    }

    const newOrg = suspendTeamMember(org, { userId, suspendedBy });
    await organizationRepository.update(org, newOrg);

    const suspendedMember = newOrg.members.find((m) => m.userId === userId);
    assert(suspendedMember, "Suspended member not found");
    assert(
      suspendedMember.status === MemberStatus.SUSPENDED,
      "Member is not suspended"
    );

    revalidatePath(`/organization`);

    return {};
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : "Failed to suspend member",
    };
  }
}

export type ResumeMemberFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function resumeMember(
  prevState: ResumeMemberFormState,
  formData: FormData
): Promise<ResumeMemberFormState> {
  const parsed = resumeMemberSchema.safeParse({
    userId: formData.get("userId"),
    organizationId: formData.get("organizationId"),
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { organizationId: orgId, userId } = parsed.data;

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();

    if (!authUser) {
      return { error: "Unauthorized" };
    }

    const resumingUser = await userRepository.findById(userId);

    if (!resumingUser) {
      return { error: "Resuming user not found" };
    }

    const org = await organizationRepository.findById(orgId);

    if (!org) {
      return { error: "Organization not found" };
    }

    const invitedBy = org.members.find((m) => m.userId === authUser.id);

    if (!invitedBy) {
      return {
        error: "You are not a member of this organization",
      };
    }

    const newOrg = resumeTeamMember(org, {
      userId: resumingUser.id,
      invitedBy,
    });
    await organizationRepository.update(org, newOrg);

    await emailService.sendTeamInviteEmail({
      to: resumingUser.email,
      redirectTo: `${ACCEPT_INVITE_REDIRECT_URL}?organization_id=${org.id}&email=${encodeURIComponent(resumingUser.email)}`,
    });

    const resumedMember = newOrg.members.find(
      (m) => m.userId === resumingUser.id
    );
    assert(resumedMember, "Resumed member not found");

    revalidatePath(`/organization`);

    return {};
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to resume member",
    };
  }
}
