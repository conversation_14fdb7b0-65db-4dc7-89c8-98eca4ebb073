export type Organization = {
  id: string;
  name: string;
};

export enum MemberStatus {
  Active = "Active",
  Invited = "Invited",
  Suspended = "Suspended",
}

export enum MemberRole {
  BuilderAdmin = "BuilderAdmin",
  BuilderUser = "BuilderUser",
}

export type Member = {
  email: string;
  userId: string;
  role: MemberRole;
  status: MemberStatus;
  invitedAt: string;
  joinedAt?: string;
  suspendedAt?: string;
};
