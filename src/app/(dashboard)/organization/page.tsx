import {
  MemberRole as PrismaMemberRole,
  MemberStatus as PrismaMemberStatus,
  Organization as PrismaOrganization,
  OrganizationStatus as PrismaOrganizationStatus,
  Prisma,
} from "@prisma/client";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  prismaClient,
} from "@/app/dependencies";
import { But<PERSON> } from "@/components/ui/button";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import DashboardLayout from "../components/DashboardLayout";
import { Organization as OrganizationType, OrganizationStatus } from "../types";
import OrganizationDetails from "./components/OrganizationDetails";
import OrganizationMembersList from "./components/OrganizationMembersList";
import { Member, MemberRole, MemberStatus } from "./types";

export const dynamic = "force-dynamic";

export default async function OrganizationPage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const adminOrg = await prismaClient.organization.findFirst({
    where: {
      members: {
        some: {
          userId: authUser.id,
          role: PrismaMemberRole.BUILDER_ADMIN,
        },
      },
    },
    include: {
      members: true,
    },
  });

  if (!adminOrg) {
    notFound();
  }

  const prismaMember = adminOrg.members.find((m) => m.userId === authUser.id);
  assert(prismaMember, "You are not a member of this organization");

  const prismaMembers = await prismaClient.member.findMany({
    where: { organizationId: adminOrg.id },
    include: {
      user: true,
    },
    orderBy: {
      user: {
        email: "asc",
      },
    },
  });

  const prismaMembersSorted = [...prismaMembers].sort(
    (a, b) => getStatusSortValue(a.status) - getStatusSortValue(b.status)
  );

  const member: Member = {
    email: authUser.email,
    userId: prismaMember.userId,
    role: prismaMemberRoleToMemberRoleMap[prismaMember.role],
    status: prismaMemberStatusToMemberStatusMap[prismaMember.status],
    invitedAt: prismaMember.invitedAt.toISOString(),
  };

  return (
    <DashboardLayout
      userId={authUser.id}
      heading={
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-semibold">Organization</h1>
        </div>
      }
    >
      <OrganizationDetails organization={parseOrganization(adminOrg)} />
      <OrganizationMembersList
        currentMember={member}
        organization={{ id: adminOrg.id, name: adminOrg.name }}
        members={prismaMembersSorted.map(parsePrismaMemberWithUserToMemberRow)}
      />
    </DashboardLayout>
  );
}

function parseOrganization(organization: PrismaOrganization): OrganizationType {
  return {
    id: organization.id,
    name: organization.name,
    status: prismaOrganizationToOrganizationTypeMap[organization.status],
    createdAt: organization.createdAt.toISOString(),
  };
}

const prismaOrganizationToOrganizationTypeMap: Record<
  PrismaOrganizationStatus,
  OrganizationStatus
> = {
  [PrismaOrganizationStatus.ACTIVE]: OrganizationStatus.Active,
  [PrismaOrganizationStatus.SUSPENDED]: OrganizationStatus.Suspended,
};

const prismaMemberRoleToMemberRoleMap: Record<PrismaMemberRole, MemberRole> = {
  [PrismaMemberRole.BUILDER_ADMIN]: MemberRole.BuilderAdmin,
  [PrismaMemberRole.BUILDER_USER]: MemberRole.BuilderUser,
};

const prismaMemberStatusToMemberStatusMap: Record<
  PrismaMemberStatus,
  MemberStatus
> = {
  [PrismaMemberStatus.ACTIVE]: MemberStatus.Active,
  [PrismaMemberStatus.INVITED]: MemberStatus.Invited,
  [PrismaMemberStatus.SUSPENDED]: MemberStatus.Suspended,
};

function parsePrismaMemberWithUserToMemberRow(
  member: Prisma.MemberGetPayload<{
    include: {
      user: true;
    };
  }>
): Member {
  return {
    email: member.user.email,
    userId: member.userId,
    role: prismaMemberRoleToMemberRoleMap[member.role],
    status: prismaMemberStatusToMemberStatusMap[member.status],
    invitedAt: member.invitedAt.toISOString(),
    joinedAt: member.joinedAt?.toISOString(),
    suspendedAt: member.suspendedAt?.toISOString(),
  };
}

function getStatusSortValue(status: PrismaMemberStatus): number {
  if (status === PrismaMemberStatus.ACTIVE) {
    return 0;
  }

  if (status === PrismaMemberStatus.INVITED) {
    return 1;
  }

  return 2;
}
