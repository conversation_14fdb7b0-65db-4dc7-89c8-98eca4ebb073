import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import assert from "node:assert";

import { createServerActionSupabaseClient } from "@/app/dependencies";
import { Button } from "@/components/ui/button";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import DashboardLayout from "../components/DashboardLayout";
import ConversationsList from "./ConversationsList";
import { Conversation } from "./types";

export const dynamic = "force-dynamic";

// Mock data - replace with actual data fetching later
const mockConversations: Conversation[] = [
  {
    id: "1",
    clientName: "<PERSON>",
    clientEmail: "<EMAIL>",
    projectId: "1",
    projectName: "Kitchen Renovation - 123 Main St",
    milestoneId: "milestone-1",
    milestoneScope: "Kitchen Design Phase",
    lastMessageAt: new Date("2025-01-22T10:30:00").toISOString(),
    unreadCount: 2,
  },
  {
    id: "2",
    clientName: "<PERSON>",
    clientEmail: "<EMAIL>",
    projectId: "2",
    projectName: "Bathroom Remodel - 456 Oak Ave",
    milestoneId: "milestone-2",
    milestoneScope: "Bathroom Installation",
    lastMessageAt: new Date("2025-01-21T14:15:00").toISOString(),
    unreadCount: 0,
  },
  {
    id: "3",
    clientName: "Michael Brown",
    clientEmail: "<EMAIL>",
    projectId: "3",
    projectName: "Full House Renovation - 789 Elm St",
    milestoneId: "milestone-3",
    milestoneScope: "Foundation Work",
    lastMessageAt: new Date("2025-01-20T09:45:00").toISOString(),
    unreadCount: 1,
  },
  {
    id: "4",
    clientName: "Emily Davis",
    clientEmail: "<EMAIL>",
    projectId: "1",
    projectName: "Kitchen Renovation - 123 Main St",
    milestoneId: "milestone-4",
    milestoneScope: "Electrical Installation",
    lastMessageAt: new Date("2025-01-19T16:20:00").toISOString(),
  },
  {
    id: "5",
    clientName: "David Wilson",
    clientEmail: "<EMAIL>",
    projectId: "2",
    projectName: "Bathroom Remodel - 456 Oak Ave",
    milestoneId: "milestone-5",
    milestoneScope: "Plumbing Phase",
    lastMessageAt: new Date("2025-01-18T11:00:00").toISOString(),
    unreadCount: 3,
  },
  {
    id: "6",
    clientName: "Lisa Anderson",
    clientEmail: "<EMAIL>",
    projectId: "3",
    projectName: "Full House Renovation - 789 Elm St",
    milestoneId: "milestone-6",
    milestoneScope: "Roofing Work",
    lastMessageAt: new Date("2025-01-17T13:30:00").toISOString(),
  },
];

export default async function ClientsAndChatsPage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  return (
    <DashboardLayout
      heading={
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-semibold">Clients & Chats</h1>
        </div>
      }
      userId={authUser.id}
    >
      <ConversationsList conversations={mockConversations} />
    </DashboardLayout>
  );
}
