"use client";

import Link from "next/link";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import formatDate from "@/lib/utils/formatDate";

import { Conversation } from "./types";

export type ConversationsListProps = {
  conversations: Conversation[];
};

export default function ConversationsList({
  conversations,
}: ConversationsListProps) {
  if (conversations.length === 0) {
    return (
      <Card className="mx-4">
        <CardHeader>
          <div>
            <CardTitle>Clients & Chats</CardTitle>
            <CardDescription>
              View and manage conversations with clients about milestones
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12">
            <p className="text-muted-foreground">No conversations yet</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="px-4 space-y-4">
      <Card>
        <CardHeader>
          <div>
            <CardTitle>Clients & Chats</CardTitle>
            <CardDescription>
              View and manage conversations with clients about milestones
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent border-b">
                  <TableHead className="h-12 px-4 font-medium">
                    Homeowner
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Project Name
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Milestone Scope
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Last Message
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {conversations.map((conversation) => (
                  <TableRow key={conversation.id} className="hover:bg-muted/50">
                    <TableCell className="h-16 px-4 font-medium">
                      <Link
                        href={`/projects/${conversation.projectId}/milestones/${conversation.milestoneId}`}
                        className="text-primary hover:underline"
                      >
                        {conversation.clientName}
                      </Link>
                    </TableCell>
                    <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                      {conversation.projectName}
                    </TableCell>
                    <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                      {conversation.milestoneScope}
                    </TableCell>
                    <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                      {formatDate(conversation.lastMessageAt)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
