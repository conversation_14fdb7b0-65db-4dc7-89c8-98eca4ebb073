"use server";

import { notFound } from "next/navigation";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  organizationRepository,
} from "@/app/dependencies";
import { MemberStatus } from "@/domain/organization";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import AcceptInviteForm from "./AcceptInviteForm";

export type AcceptInvitePageProps = {
  searchParams: Promise<{
    organization_id: string;
    email: string;
  }>;
};

export default async function AcceptInvitePage({
  searchParams,
}: AcceptInvitePageProps) {
  const { organization_id: organizationId, email } = await searchParams;
  const org = await organizationRepository.findById(organizationId);

  if (!org) {
    notFound();
  }

  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");
  assert(authUser.email === email, "Email mismatch");

  const member = org.members.find((m) => m.userId === authUser.id);

  if (!member) {
    notFound();
  }

  assert(member.status === MemberStatus.INVITED, "Member is not invited");

  return <AcceptInviteForm organization={org} />;
}
