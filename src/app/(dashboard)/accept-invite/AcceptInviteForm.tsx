"use client";

import { ReactNode, useActionState } from "react";

import { Button } from "@/components/ui/button";
import { FieldError, FieldGroup } from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { Organization } from "@/domain/organization";

import { acceptInvite, AcceptInviteFormState } from "./actions";

export type AcceptInviteFormProps = {
  organization: Organization;
};

export default function AcceptInviteForm({
  organization,
}: AcceptInviteFormProps): ReactNode {
  const [state, formAction, isPending] = useActionState<
    AcceptInviteFormState,
    FormData
  >(acceptInvite, {});

  return (
    <div className="flex h-screen items-center justify-center">
      <form action={formAction} className="w-full max-w-md">
        <FieldGroup>
          <Input type="hidden" name="organizationId" value={organization.id} />

          {state.error && <FieldError>{state.error}</FieldError>}

          <div className="flex justify-center">
            <Button type="submit" size="lg" disabled={isPending}>
              {isPending
                ? "Accepting invite..."
                : `Accept Invite to ${organization.name}`}
            </Button>
          </div>
        </FieldGroup>
      </form>
    </div>
  );
}
