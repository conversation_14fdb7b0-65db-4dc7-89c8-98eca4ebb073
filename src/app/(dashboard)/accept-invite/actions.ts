"use server";

import { redirect } from "next/navigation";

import {
  createServerActionSupabaseClient,
  organizationRepository,
} from "@/app/dependencies";
import { acceptTeamInvite, MemberRole } from "@/domain/organization";
import { acceptInviteSchema } from "@/domain/schemas/auth";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type AcceptInviteFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function acceptInvite(
  _prevState: AcceptInviteFormState,
  formData: FormData
): Promise<AcceptInviteFormState> {
  const parsed = acceptInviteSchema.safeParse({
    organizationId: formData.get("organizationId"),
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { organizationId } = parsed.data;
  let redirectTo = "/";

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();
    if (!authUser) {
      return { error: "Unauthorized" };
    }

    const org = await organizationRepository.findById(organizationId);
    if (!org) {
      return { error: "Organization not found" };
    }

    const newOrg = acceptTeamInvite(org, { userId: authUser.id });
    await organizationRepository.update(org, newOrg);

    const member = newOrg.members.find((m) => m.userId === authUser.id);
    if (!member) {
      return { error: "Member not found" };
    }

    redirectTo =
      member.role === MemberRole.BUILDER_ADMIN ? "/organization" : "/";
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }

  redirect(redirectTo);
}
