import { UserStatus } from "@prisma/client";
import { redirect } from "next/navigation";

import { prismaClient } from "@/app/dependencies";
import AppLayout from "@/components/layouts/app-layout";
import { MemberRole } from "@/domain/organization";

export type AppLayoutServerProps = {
  heading: React.ReactNode;
  userId: string;
  children: React.ReactNode;
};

export default async function DashboardLayout({
  heading,
  userId,
  children,
}: AppLayoutServerProps) {
  const user = await prismaClient.user.findFirstOrThrow({
    where: {
      id: userId,
    },
    include: {
      memberships: {
        where: {
          role: MemberRole.BUILDER_ADMIN,
        },
      },
    },
  });

  if (user.status === UserStatus.WAITING_ONBOARDING) {
    redirect("/onboarding");
  }

  return (
    <AppLayout
      heading={heading}
      user={{
        email: user.email,
        name: user.name!,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name!)}&size=128&background=random`,
        canViewOrganization: user.memberships.length > 0,
      }}
    >
      {children}
    </AppLayout>
  );
}
