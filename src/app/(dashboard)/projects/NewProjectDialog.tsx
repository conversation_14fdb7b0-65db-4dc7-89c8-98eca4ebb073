"use client";

import { ReactNode, useActionState, useId, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { newProjectSchema } from "@/domain/schemas/projects";

import { Project } from "./types";

export type NewProjectFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
  data?: {
    name: string | null;
  };
};

export type NewProjectDialogProps = {
  project?: Project;
  onProjectUpdate?: (project: { id: string; name: string }) => void;
  trigger?: ReactNode;
};

export default function NewProjectDialog({
  project,
  onProjectUpdate,
  trigger,
}: NewProjectDialogProps) {
  const isEditing = Boolean(project);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const formId = useId();

  const [state, formAction, isPending] = useActionState<
    NewProjectFormState,
    FormData
  >(
    async (prevState, formData) => {
      const id = formData.get("id") as string | null;
      const name = formData.get("name") as string | null;

      const parsed = newProjectSchema.safeParse({
        id: id ?? undefined,
        name: name ?? undefined,
      });

      if (!parsed.success) {
        return {
          fieldErrors: parsed.error.flatten().fieldErrors,
          data: { id, name },
        };
      }

      const newProject = {
        id: parsed.data.id || Math.random().toString(36).slice(2, 11),
        name: parsed.data.name,
      };

      onProjectUpdate?.(newProject);
      setIsDialogOpen(false);

      return project ? { data: newProject } : {};
    },
    {
      data: {
        name: project?.name ?? null,
      },
    }
  );

  const nameError = state.fieldErrors?.name?.[0];

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Project" : "Create New Project"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update project details"
              : "Enter project information to get started"}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <form id={formId} action={formAction} noValidate>
            <FieldGroup>
              {state.error && <FieldError>{state.error}</FieldError>}

              {project?.id && (
                <input type="hidden" name="id" defaultValue={project.id} />
              )}

              <Field data-invalid={Boolean(nameError)}>
                <FieldLabel htmlFor="name">Project Name</FieldLabel>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  placeholder="e.g., Kitchen Renovation - 123 Main St"
                  defaultValue={state.data?.name ?? undefined}
                  aria-invalid={Boolean(nameError)}
                />
                {nameError && <FieldError>{nameError}</FieldError>}
              </Field>
            </FieldGroup>
          </form>
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending && (isEditing ? "Saving..." : "Creating...")}
            {!isPending && (isEditing ? "Save Project" : "Create Project")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
