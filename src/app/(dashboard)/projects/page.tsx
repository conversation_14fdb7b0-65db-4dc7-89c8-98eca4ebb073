import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import assert from "node:assert";

import { createServerActionSupabaseClient } from "@/app/dependencies";
import { Button } from "@/components/ui/button";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import DashboardLayout from "../components/DashboardLayout";
import ProjectsList from "./ProjectsList";
import { Project } from "./types";

export const dynamic = "force-dynamic";

// Mock data - replace with actual data fetching later
const mockProjects: Project[] = [
  {
    id: "1",
    name: "Kitchen Renovation - 123 Main St",
    createdAt: new Date("2025-01-15").toISOString(),
    updatedAt: new Date("2025-01-20").toISOString(),
    milestones: [],
  },
  {
    id: "2",
    name: "Bathroom Remodel - 456 Oak Ave",
    createdAt: new Date("2025-01-10").toISOString(),
    updatedAt: new Date("2025-01-18").toISOString(),
    milestones: [],
  },
  {
    id: "3",
    name: "Full House Renovation - 789 Elm St",
    createdAt: new Date("2025-01-05").toISOString(),
    updatedAt: new Date("2025-01-22").toISOString(),
    milestones: [],
  },
];

export default async function ProjectsPage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  return (
    <DashboardLayout
      heading={
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-semibold">Project List</h1>
        </div>
      }
      userId={authUser.id}
    >
      <ProjectsList projects={mockProjects} />
    </DashboardLayout>
  );
}
