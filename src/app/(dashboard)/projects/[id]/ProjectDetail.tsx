"use client";

import { Edit, List } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import formatDate from "@/lib/utils/formatDate";

import NewProjectDialog from "../NewProjectDialog";
import { Project } from "../types";
import InviteHomeownerButton from "./InviteHomeownerButton";

export type ProjectDetailProps = {
  project: Project;
};

export default function ProjectDetail({
  project: initialProject,
}: ProjectDetailProps) {
  const [project, setProject] = useState(initialProject);

  const handleProjectUpdate = (updatedProject: {
    id: string;
    name: string;
  }) => {
    setProject((prev) => ({
      ...prev,
      name: updatedProject.name,
      updatedAt: new Date().toISOString(),
    }));
  };

  return (
    <div className="px-4 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{project.name}</CardTitle>
              <CardDescription>Project details and information</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" asChild>
                <Link href={`/projects/${project.id}/milestones`}>
                  <List className="mr-2 h-4 w-4" />
                  Milestones
                </Link>
              </Button>
              <InviteHomeownerButton />

              <NewProjectDialog
                project={project}
                onProjectUpdate={handleProjectUpdate}
                trigger={
                  <Button variant="outline">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                }
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Created At
              </p>
              <p className="text-sm">{formatDate(project.createdAt)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Updated At
              </p>
              <p className="text-sm">{formatDate(project.updatedAt)}</p>
            </div>
          </div>
          <Separator />
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-2">
              Milestones
            </p>
            <p className="text-sm">
              {project.milestones.length} milestone
              {project.milestones.length !== 1 ? "s" : ""} configured
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
