"use client";

import { UserPlus } from "lucide-react";
import {
  ReactNode,
  useActionState,
  useEffect,
  useId,
  useRef,
  useState,
} from "react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Field, FieldLabel } from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { inviteHomeownerSchema } from "@/domain/schemas/projects";

type InviteHomeownerFormValues = {
  email: string;
};

type InviteHomeownerFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
  success?: boolean;
  values?: InviteHomeownerFormValues;
};

export default function InviteHomeownerButton(): ReactNode {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const formId = useId();
  const formRef = useRef<HTMLFormElement | null>(null);
  const initialState: InviteHomeownerFormState = {
    values: {
      email: "",
    },
  };
  const [state, formAction, isPending] = useActionState<
    InviteHomeownerFormState,
    FormData
  >(async (_prevState, formData) => {
    const values: InviteHomeownerFormValues = {
      email: String(formData.get("email") ?? ""),
    };
    const parsed = inviteHomeownerSchema.safeParse(values);

    if (!parsed.success) {
      return { fieldErrors: parsed.error.flatten().fieldErrors, values };
    }

    return { success: true, values: { email: "" } };
  }, initialState);

  useEffect(() => {
    if (!state.success) {
      return;
    }
    formRef.current?.reset();
    setIsDialogOpen(false);
  }, [state.success]);

  const emailError = state.fieldErrors?.email?.[0];

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Invite
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-sm">
        <DialogHeader>
          <DialogTitle>Invite Homeowner</DialogTitle>
        </DialogHeader>
        <form ref={formRef} id={formId} action={formAction} noValidate>
          {state.error && (
            <div className="mb-3 text-sm text-destructive">{state.error}</div>
          )}
          <Field data-invalid={Boolean(emailError)}>
            <FieldLabel
              htmlFor="email"
              style={{
                display: "block",
                marginBottom: "5px",
                fontSize: "14px",
              }}
            >
              Email Address
            </FieldLabel>
            <Input
              id="email"
              type="email"
              name="email"
              placeholder="<EMAIL>"
              disabled={isPending}
              defaultValue={state.values?.email ?? ""}
              aria-invalid={Boolean(emailError)}
            />
            {emailError && (
              <div className="mt-2 text-sm text-destructive">{emailError}</div>
            )}
          </Field>
        </form>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Close
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending ? "Inviting..." : "Invite"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
