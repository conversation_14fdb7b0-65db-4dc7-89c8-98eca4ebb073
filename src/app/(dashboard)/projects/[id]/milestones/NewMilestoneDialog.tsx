"use client";

import {
  ReactNode,
  useActionState,
  useEffect,
  useId,
  useRef,
  useState,
} from "react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { newMilestoneSchema } from "@/domain/schemas/projects";

import { Milestone, MilestoneStatus } from "../../types";

type NewMilestoneFormValues = {
  projectId: string;
  milestoneId: string;
  scope: string;
  startDate: string;
  endDate: string;
  budget: string;
};

export type NewMilestoneFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
  data?: Milestone;
  values?: NewMilestoneFormValues;
};

export type NewMilestoneDialogProps = {
  projectId: string;
  milestone?: Milestone;
  onMilestoneCreate?: (milestone: Milestone) => void;
  onMilestoneUpdate?: (milestone: Milestone) => void;
  trigger: ReactNode;
};

function formatDateForInput(dateString: string): string {
  const date = new Date(dateString);
  return date.toISOString().split("T")[0];
}

export default function NewMilestoneDialog({
  projectId,
  milestone,
  onMilestoneCreate,
  onMilestoneUpdate,
  trigger,
}: NewMilestoneDialogProps): ReactNode {
  const isEditing = Boolean(milestone);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const formId = useId();
  const formRef = useRef<HTMLFormElement | null>(null);
  const initialState: NewMilestoneFormState = {
    values: {
      projectId,
      milestoneId: milestone?.id ?? "",
      scope: milestone?.scope ?? "",
      startDate: milestone ? formatDateForInput(milestone.startDate) : "",
      endDate: milestone ? formatDateForInput(milestone.endDate) : "",
      budget: milestone?.budget ? String(milestone.budget) : "",
    },
  };
  const [state, formAction, isPending] = useActionState<
    NewMilestoneFormState,
    FormData
  >(async (_prevState, formData) => {
    const values: NewMilestoneFormValues = {
      projectId: String(formData.get("projectId") ?? ""),
      milestoneId: String(formData.get("milestoneId") ?? ""),
      scope: String(formData.get("scope") ?? ""),
      startDate: String(formData.get("startDate") ?? ""),
      endDate: String(formData.get("endDate") ?? ""),
      budget: String(formData.get("budget") ?? ""),
    };
    const budgetValue = values.budget;
    const parsed = newMilestoneSchema.safeParse({
      projectId: values.projectId,
      milestoneId: values.milestoneId || undefined,
      scope: values.scope,
      startDate: values.startDate,
      endDate: values.endDate,
      budget: budgetValue !== "" ? Number(budgetValue) : Number.NaN,
    });

    if (!parsed.success) {
      return { fieldErrors: parsed.error.flatten().fieldErrors, values };
    }

    const resetValues: NewMilestoneFormValues = isEditing
      ? {
          projectId,
          milestoneId: milestone?.id ?? "",
          scope: milestone?.scope ?? "",
          startDate: milestone ? formatDateForInput(milestone.startDate) : "",
          endDate: milestone ? formatDateForInput(milestone.endDate) : "",
          budget: milestone?.budget ? String(milestone.budget) : "",
        }
      : {
          projectId,
          milestoneId: "",
          scope: "",
          startDate: "",
          endDate: "",
          budget: "",
        };
    return {
      data: {
        id: parsed.data.milestoneId || Math.random().toString(36).slice(2, 11),
        scope: parsed.data.scope,
        startDate: new Date(parsed.data.startDate).toISOString(),
        endDate: new Date(parsed.data.endDate).toISOString(),
        budget: parsed.data.budget,
        status: milestone?.status || MilestoneStatus.UnderApproval,
      },
      values: resetValues,
    };
  }, initialState);

  useEffect(() => {
    if (isDialogOpen) {
      formRef.current?.reset();
    }
  }, [isDialogOpen, milestone, projectId]);

  useEffect(() => {
    if (!state.data) {
      return;
    }

    if (isEditing && onMilestoneUpdate) {
      onMilestoneUpdate(state.data);
    } else if (!isEditing && onMilestoneCreate) {
      onMilestoneCreate(state.data);
    }
    setIsDialogOpen(false);
  }, [isEditing, onMilestoneCreate, onMilestoneUpdate, state.data]);

  const scopeError = state.fieldErrors?.scope?.[0];
  const startDateError = state.fieldErrors?.startDate?.[0];
  const endDateError = state.fieldErrors?.endDate?.[0];
  const budgetError = state.fieldErrors?.budget?.[0];
  const defaultStartDate = milestone
    ? formatDateForInput(milestone.startDate)
    : "";
  const defaultEndDate = milestone ? formatDateForInput(milestone.endDate) : "";
  const shouldUseStateValues = Boolean(state.fieldErrors || state.error);
  const scopeValue = shouldUseStateValues
    ? (state.values?.scope ?? "")
    : (milestone?.scope ?? "");
  const startDateValue = shouldUseStateValues
    ? (state.values?.startDate ?? "")
    : defaultStartDate;
  const endDateValue = shouldUseStateValues
    ? (state.values?.endDate ?? "")
    : defaultEndDate;
  const budgetValue = shouldUseStateValues
    ? (state.values?.budget ?? "")
    : (milestone?.budget ?? "");

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Milestone" : "Create New Milestone"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update milestone details"
              : "Define the scope, timeline, and budget for this milestone"}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <form ref={formRef} id={formId} action={formAction} noValidate>
            <FieldGroup>
              {state.error && <FieldError>{state.error}</FieldError>}

              <input
                type="hidden"
                name="projectId"
                defaultValue={state.values?.projectId ?? projectId}
              />
              {isEditing && milestone && (
                <input
                  type="hidden"
                  name="milestoneId"
                  defaultValue={state.values?.milestoneId ?? milestone.id}
                />
              )}

              <Field data-invalid={Boolean(scopeError)}>
                <FieldLabel htmlFor="scope">Scope</FieldLabel>
                <Textarea
                  id="scope"
                  name="scope"
                  placeholder="Describe the work to be completed in this milestone..."
                  rows={4}
                  defaultValue={scopeValue}
                  aria-invalid={Boolean(scopeError)}
                />
                {scopeError && <FieldError>{scopeError}</FieldError>}
              </Field>

              <div className="grid grid-cols-2 gap-4">
                <Field data-invalid={Boolean(startDateError)}>
                  <FieldLabel htmlFor="startDate">Start Date</FieldLabel>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    defaultValue={startDateValue}
                    aria-invalid={Boolean(startDateError)}
                  />
                  {startDateError && <FieldError>{startDateError}</FieldError>}
                </Field>

                <Field data-invalid={Boolean(endDateError)}>
                  <FieldLabel htmlFor="endDate">End Date</FieldLabel>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    defaultValue={endDateValue}
                    aria-invalid={Boolean(endDateError)}
                  />
                  {endDateError && <FieldError>{endDateError}</FieldError>}
                </Field>
              </div>

              <Field data-invalid={Boolean(budgetError)}>
                <FieldLabel htmlFor="budget">Budget (GBP)</FieldLabel>
                <Input
                  id="budget"
                  name="budget"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  defaultValue={budgetValue}
                  aria-invalid={Boolean(budgetError)}
                />
                {budgetError && <FieldError>{budgetError}</FieldError>}
              </Field>
            </FieldGroup>
          </form>
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending && (isEditing ? "Saving..." : "Creating...")}
            {!isPending && (isEditing ? "Save Milestone" : "Create Milestone")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
