"use client";

import { Pencil, Plus } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import formatDate from "@/lib/utils/formatDate";

import { Milestone, MilestoneStatus, Project } from "../../types";
import NewMilestoneDialog from "./NewMilestoneDialog";

export type MilestonesListProps = {
  project: Project;
  milestones: Milestone[];
};

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-GB", {
    style: "currency",
    currency: "GBP",
  }).format(amount);
}

export default function MilestonesList({
  project,
  milestones: initialMilestones,
}: MilestonesListProps) {
  const [milestones, setMilestones] = useState(initialMilestones);

  const handleMilestoneCreate = (newMilestone: Milestone) => {
    setMilestones((prev) => [...prev, newMilestone]);
  };

  const handleMilestoneUpdate = (updatedMilestone: Milestone) => {
    setMilestones((prev) =>
      prev.map((m) => (m.id === updatedMilestone.id ? updatedMilestone : m))
    );
  };

  const totalBudget = milestones.reduce((sum, m) => sum + m.budget, 0);

  if (milestones.length === 0) {
    return (
      <Card className="mx-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{project.name}</CardTitle>
              <CardDescription>
                Manage project milestones and payment schedules
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12">
            <p className="mb-4 text-muted-foreground">No milestones yet</p>
            <NewMilestoneDialog
              projectId={project.id}
              onMilestoneCreate={handleMilestoneCreate}
              trigger={
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create your first milestone
                </Button>
              }
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="px-4 space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{project.name}</CardTitle>
              <CardDescription>
                Manage project milestones and payment schedules
              </CardDescription>
            </div>
            <NewMilestoneDialog
              projectId={project.id}
              onMilestoneCreate={handleMilestoneCreate}
              trigger={
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  New Milestone
                </Button>
              }
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent border-b">
                  <TableHead className="h-12 px-4 font-medium">Scope</TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Status
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Start Date
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    End Date
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Budget
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium w-[100px]">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {milestones.map((milestone) => (
                  <TableRow key={milestone.id} className="hover:bg-muted/50">
                    <TableCell className="h-16 px-4 font-medium">
                      <Link
                        href={`/projects/${project.id}/milestones/${milestone.id}`}
                        className="text-primary hover:underline"
                      >
                        {milestone.scope}
                      </Link>
                    </TableCell>
                    <TableCell className="h-16 px-4">
                      <Badge
                        variant={
                          milestone.status === MilestoneStatus.Accepted
                            ? ("default" as const)
                            : ("secondary" as const)
                        }
                      >
                        {milestone.status === MilestoneStatus.Accepted
                          ? "Accepted"
                          : "Under Approval"}
                      </Badge>
                    </TableCell>
                    <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                      {formatDate(milestone.startDate)}
                    </TableCell>
                    <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                      {formatDate(milestone.endDate)}
                    </TableCell>
                    <TableCell className="h-16 px-4 font-medium">
                      {formatCurrency(milestone.budget)}
                    </TableCell>
                    <TableCell className="h-16 px-4">
                      <TooltipProvider>
                        <Tooltip>
                          <NewMilestoneDialog
                            projectId={project.id}
                            milestone={milestone}
                            onMilestoneUpdate={handleMilestoneUpdate}
                            trigger={
                              <TooltipTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  className="h-8 w-8"
                                >
                                  <Pencil className="size-4" />
                                </Button>
                              </TooltipTrigger>
                            }
                          />
                          <TooltipContent>Edit</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className="flex justify-end pt-4 border-t mt-4">
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Total Budget</p>
              <p className="text-2xl font-bold">
                {formatCurrency(totalBudget)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
