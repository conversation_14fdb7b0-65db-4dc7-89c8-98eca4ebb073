"use client";

import {
  ReactNode,
  useActionState,
  useEffect,
  useId,
  useRef,
  useState,
} from "react";

import { Button } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Field, FieldLabel } from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { milestoneSchema } from "@/domain/schemas/projects";

import { Milestone } from "../../../types";

export type EditMilestoneDialogProps = {
  milestone: Milestone;
  trigger: ReactNode;
};

type EditMilestoneFormValues = {
  scope: string;
  startDate: string;
  endDate: string;
  budget: string;
};

type EditMilestoneFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
  success?: boolean;
  values?: EditMilestoneFormValues;
};

function formatDateForInput(dateString: string): string {
  const date = new Date(dateString);
  return date.toISOString().split("T")[0];
}

export default function EditMilestoneDialog({
  milestone,
  trigger,
}: EditMilestoneDialogProps): ReactNode {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const formId = useId();
  const formRef = useRef<HTMLFormElement | null>(null);
  const initialState: EditMilestoneFormState = {
    values: {
      scope: milestone.scope,
      startDate: formatDateForInput(milestone.startDate),
      endDate: formatDateForInput(milestone.endDate),
      budget: String(milestone.budget),
    },
  };
  const [state, formAction, isPending] = useActionState<
    EditMilestoneFormState,
    FormData
  >(async (_prevState, formData) => {
    const values: EditMilestoneFormValues = {
      scope: String(formData.get("scope") ?? ""),
      startDate: String(formData.get("startDate") ?? ""),
      endDate: String(formData.get("endDate") ?? ""),
      budget: String(formData.get("budget") ?? ""),
    };
    const budgetValue = values.budget;
    const parsed = milestoneSchema.safeParse({
      scope: values.scope,
      startDate: values.startDate,
      endDate: values.endDate,
      budget: budgetValue !== "" ? Number(budgetValue) : Number.NaN,
    });

    if (!parsed.success) {
      return { fieldErrors: parsed.error.flatten().fieldErrors, values };
    }

    return {
      success: true,
      values: {
        scope: milestone.scope,
        startDate: formatDateForInput(milestone.startDate),
        endDate: formatDateForInput(milestone.endDate),
        budget: String(milestone.budget),
      },
    };
  }, initialState);

  useEffect(() => {
    if (isDialogOpen) {
      formRef.current?.reset();
    }
  }, [isDialogOpen, milestone]);

  useEffect(() => {
    if (state.success) {
      setIsDialogOpen(false);
    }
  }, [state.success]);

  const scopeError = state.fieldErrors?.scope?.[0];
  const startDateError = state.fieldErrors?.startDate?.[0];
  const endDateError = state.fieldErrors?.endDate?.[0];
  const budgetError = state.fieldErrors?.budget?.[0];
  const shouldUseStateValues = Boolean(state.fieldErrors || state.error);
  const scopeValue = shouldUseStateValues
    ? (state.values?.scope ?? "")
    : milestone.scope;
  const startDateValue = shouldUseStateValues
    ? (state.values?.startDate ?? "")
    : formatDateForInput(milestone.startDate);
  const endDateValue = shouldUseStateValues
    ? (state.values?.endDate ?? "")
    : formatDateForInput(milestone.endDate);
  const budgetValue = shouldUseStateValues
    ? (state.values?.budget ?? "")
    : milestone.budget;

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Milestone</DialogTitle>
        </DialogHeader>
        <form ref={formRef} id={formId} action={formAction} noValidate>
          {state.error && (
            <div className="mb-3 text-sm text-destructive">{state.error}</div>
          )}
          <Field data-invalid={Boolean(scopeError)}>
            <FieldLabel
              htmlFor="scope"
              style={{
                display: "block",
                marginBottom: "5px",
                fontSize: "14px",
              }}
            >
              Scope
            </FieldLabel>
            <Textarea
              id="scope"
              name="scope"
              rows={4}
              placeholder="Describe the work to be completed in this milestone..."
              defaultValue={scopeValue}
              aria-invalid={Boolean(scopeError)}
              disabled={isPending}
            />
            {scopeError && (
              <div className="mt-2 text-sm text-destructive">{scopeError}</div>
            )}
          </Field>

          <div className="grid grid-cols-2 gap-4 mt-4">
            <Field data-invalid={Boolean(startDateError)}>
              <FieldLabel
                htmlFor="startDate"
                style={{
                  display: "block",
                  marginBottom: "5px",
                  fontSize: "14px",
                }}
              >
                Start Date
              </FieldLabel>
              <Input
                id="startDate"
                name="startDate"
                type="date"
                defaultValue={startDateValue}
                aria-invalid={Boolean(startDateError)}
                disabled={isPending}
              />
              {startDateError && (
                <div className="mt-2 text-sm text-destructive">
                  {startDateError}
                </div>
              )}
            </Field>

            <Field data-invalid={Boolean(endDateError)}>
              <FieldLabel
                htmlFor="endDate"
                style={{
                  display: "block",
                  marginBottom: "5px",
                  fontSize: "14px",
                }}
              >
                End Date
              </FieldLabel>
              <Input
                id="endDate"
                name="endDate"
                type="date"
                defaultValue={endDateValue}
                aria-invalid={Boolean(endDateError)}
                disabled={isPending}
              />
              {endDateError && (
                <div className="mt-2 text-sm text-destructive">
                  {endDateError}
                </div>
              )}
            </Field>
          </div>

          <Field className="mt-4" data-invalid={Boolean(budgetError)}>
            <FieldLabel
              htmlFor="budget"
              style={{
                display: "block",
                marginBottom: "5px",
                fontSize: "14px",
              }}
            >
              Budget (GBP)
            </FieldLabel>
            <Input
              id="budget"
              name="budget"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              defaultValue={budgetValue}
              aria-invalid={Boolean(budgetError)}
              disabled={isPending}
            />
            {budgetError && (
              <div className="mt-2 text-sm text-destructive">{budgetError}</div>
            )}
          </Field>
        </form>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Close
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending ? "Saving..." : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
