"use client";

import { Check, MessageCircle, Pencil } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import formatDate from "@/lib/utils/formatDate";

import { Milestone, MilestoneStatus, Project } from "../../../types";
import EditMilestoneDialog from "./EditMilestoneDialog";

export type MilestoneDetailProps = {
  project: Project;
  milestone: Milestone;
};

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-GB", {
    style: "currency",
    currency: "GBP",
  }).format(amount);
}

export default function MilestoneDetail({
  project,
  milestone,
}: MilestoneDetailProps) {
  return (
    <div className="px-4 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{project.name}</CardTitle>
              <CardDescription>
                Milestone details and information
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline">
                <MessageCircle className="mr-2 h-4 w-4" />
                Open Conversation
              </Button>
              <EditMilestoneDialog
                milestone={milestone}
                trigger={
                  <Button variant="outline">
                    <Pencil className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                }
              />
              <Button>
                <Check className="mr-2 h-4 w-4" />
                Accept
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-2">
                Status
              </p>
              <Badge
                variant={
                  milestone.status === MilestoneStatus.Accepted
                    ? ("default" as const)
                    : ("secondary" as const)
                }
              >
                {milestone.status === MilestoneStatus.Accepted
                  ? "Accepted"
                  : "Under Approval"}
              </Badge>
            </div>
          </div>
          <Separator />
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-2">
              Scope
            </p>
            <p className="text-sm">{milestone.scope}</p>
          </div>
          <Separator />
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Start Date
              </p>
              <p className="text-sm">{formatDate(milestone.startDate)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                End Date
              </p>
              <p className="text-sm">{formatDate(milestone.endDate)}</p>
            </div>
          </div>
          <Separator />
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-2">
              Budget
            </p>
            <p className="text-2xl font-bold">
              {formatCurrency(milestone.budget)}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
