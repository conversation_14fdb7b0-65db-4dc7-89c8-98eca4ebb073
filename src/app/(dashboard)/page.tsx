import { OppMerchantComplianceStatus } from "@prisma/client";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  prismaClient,
} from "@/app/dependencies";
import { ChartAreaInteractive } from "@/components/chart-area-interactive";
import K<PERSON><PERSON><PERSON> from "@/components/kyc-card";
import { SectionCards } from "@/components/section-cards";
import { MemberRole } from "@/domain/organization";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import DashboardLayout from "./components/DashboardLayout";

export default async function DashboardPage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const adminOrg = await prismaClient.organization.findFirst({
    where: {
      members: {
        some: {
          userId: authUser.id,
          role: MemberRole.BUILDER_ADMIN,
        },
      },
    },
  });

  const showKYC =
    adminOrg?.oppMerchantComplianceStatus ===
      OppMerchantComplianceStatus.UNVERIFIED ||
    adminOrg?.oppMerchantComplianceStatus ===
      OppMerchantComplianceStatus.PENDING;

  return (
    <DashboardLayout
      userId={authUser.id}
      heading={<h1 className="font-semibold">Dashboard</h1>}
    >
      {showKYC && (
        <div className="px-4 lg:px-6">
          <KYCCard />
        </div>
      )}
      <SectionCards />
      <div className="px-4 lg:px-6">
        <ChartAreaInteractive />
      </div>
    </DashboardLayout>
  );
}
