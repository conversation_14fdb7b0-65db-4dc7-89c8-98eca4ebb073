import { NextRequest, NextResponse } from "next/server";

import { createApiRouteSupabaseClient } from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const type = requestUrl.searchParams.get("type");

  if (code) {
    const redirectPath = type === "recovery" ? "/reset-password" : "/";

    const response = NextResponse.redirect(
      new URL(redirectPath, process.env.SITE_URL)
    );

    try {
      const supabase = await createApiRouteSupabaseClient(request, response);
      const authService = new SupabaseAuthService(supabase.auth);
      const { error } = await authService.exchangeCodeForSession({ code });

      if (error) {
        return NextResponse.redirect(
          new URL("/sign-in?error=verification_failed", requestUrl.origin)
        );
      }

      return response;
    } catch (error) {}
  }

  return NextResponse.redirect(
    new URL("/sign-in?error=verification_failed", requestUrl.origin)
  );
}
