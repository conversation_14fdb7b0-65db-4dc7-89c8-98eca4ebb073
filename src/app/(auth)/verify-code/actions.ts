"use server";

import { redirect } from "next/navigation";

import { createServerActionSupabaseClient } from "@/app/dependencies";
import { verifyOtpSchema } from "@/domain/schemas/auth";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type VerifyOtpFormState = {
  error?: string;
  success?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function verifyOtp(
  prevState: VerifyOtpFormState,
  formData: FormData
): Promise<VerifyOtpFormState> {
  const parsed = verifyOtpSchema.safeParse({
    email: formData.get("email"),
    token: formData.get("token"),
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { email, token } = parsed.data;

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);
    const { error } = await authService.verifyOtp({
      email,
      token,
      type: "email",
    });

    if (error) {
      if (error.message === "Token has expired or is invalid") {
        return { error: "Verification code is incorrect" };
      }
      return { error: error.message };
    }
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }

  redirect("/");
}
