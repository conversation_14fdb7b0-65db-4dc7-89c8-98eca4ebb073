"use server";

import { redirect } from "next/navigation";

import VerifyCodeForm from "./VerifyCodeForm";

export type VerifyCodePageProps = {
  searchParams: Promise<{
    email?: string;
  }>;
};

export default async function VerifyCodePage({
  searchParams,
}: VerifyCodePageProps) {
  const { email } = await searchParams;

  if (!email) {
    redirect("/sign-up");
  }

  return <VerifyCodeForm email={email} />;
}
