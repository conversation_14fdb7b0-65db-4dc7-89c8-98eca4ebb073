"use client";

import { useActionState, useEffect, useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldDescription,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { verifyOtpSchema } from "@/domain/schemas/auth";

import { verifyOtp, type VerifyOtpFormState } from "./actions";

export type VerifyRecoveryCodeFormProps = {
  email: string;
};

export default function VerifyRecoveryCodeForm({
  email,
}: VerifyRecoveryCodeFormProps) {
  const [token, setToken] = useState("");
  const formRef = useRef<HTMLFormElement | null>(null);
  const initialState: VerifyOtpFormState & { values?: { token: string } } = {
    values: {
      token: "",
    },
  };
  const [state, formAction, isPending] = useActionState<
    VerifyOtpFormState & { values?: { token: string } },
    FormData
  >(async (prevState, formData) => {
    const values = {
      token: String(formData.get("token") ?? ""),
    };
    const parsed = verifyOtpSchema.safeParse({
      email: formData.get("email"),
      token: values.token,
    });

    if (!parsed.success) {
      return { fieldErrors: parsed.error.flatten().fieldErrors, values };
    }

    const result = await verifyOtp(prevState, formData);
    if (result.error || result.fieldErrors) {
      return { ...result, values };
    }
    return result;
  }, initialState);

  const tokenError = state.fieldErrors?.token?.[0];

  useEffect(() => {
    if (state.values && state.values.token !== token) {
      setToken(state.values.token);
    }
  }, [state.values, token]);

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-xs">
        <Card>
          <CardHeader>
            <CardTitle>Enter verification code</CardTitle>
            <CardDescription>
              We sent a 6-digit code to your email.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form ref={formRef} action={formAction} noValidate>
              <input type="hidden" name="email" defaultValue={email} />
              <input type="hidden" name="token" value={token} readOnly />
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field data-invalid={Boolean(tokenError)}>
                  <FieldLabel htmlFor="otp">Verification code</FieldLabel>
                  <InputOTP
                    maxLength={6}
                    id="otp"
                    disabled={isPending}
                    value={token}
                    onChange={setToken}
                    aria-invalid={Boolean(tokenError)}
                    onComplete={(value) => {
                      if (typeof value === "string" && value !== token) {
                        setToken(value);
                      }
                      if (!isPending) {
                        formRef.current?.requestSubmit();
                      }
                    }}
                  >
                    <InputOTPGroup className="gap-2.5 *:data-[slot=input-otp-slot]:rounded-md *:data-[slot=input-otp-slot]:border">
                      <InputOTPSlot index={0} />
                      <InputOTPSlot index={1} />
                      <InputOTPSlot index={2} />
                      <InputOTPSlot index={3} />
                      <InputOTPSlot index={4} />
                      <InputOTPSlot index={5} />
                    </InputOTPGroup>
                  </InputOTP>
                  <FieldDescription>
                    Enter the 6-digit code sent to your email.
                  </FieldDescription>
                  {tokenError && <FieldError>{tokenError}</FieldError>}
                </Field>

                <FieldGroup>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Verifying..." : "Verify"}
                  </Button>
                  <FieldDescription className="text-center">
                    Check your email for the 6-digit verification code
                  </FieldDescription>
                </FieldGroup>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
