"use server";

import { redirect } from "next/navigation";

import {
  createServerActionSupabaseClient,
  EMAIL_VERIFICATION_REDIRECT_URL,
  prismaClient,
} from "@/app/dependencies";
import { MemberRole } from "@/domain/organization";
import { signInSchema } from "@/domain/schemas/auth";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type SignInFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function signIn(
  prevState: SignInFormState,
  formData: FormData
): Promise<SignInFormState> {
  const parsed = signInSchema.safeParse({
    email: formData.get("email"),
    password: formData.get("password"),
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { email, password } = parsed.data;

  let redirectTo = "/";

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const { data: authUser, error } = await authService.signIn({
      email,
      password,
    });

    const isNotConfirmed =
      error?.message.includes("Email not confirmed") ||
      (authUser && authUser.emailConfirmedAt == null);

    if (isNotConfirmed) {
      await authService.resend({
        email,
        redirectTo: EMAIL_VERIFICATION_REDIRECT_URL,
      });

      redirectTo = `/verify-email?email=${encodeURIComponent(email)}`;
    } else if (error) {
      return {
        error: error.message.includes("Invalid login credentials")
          ? "Invalid email or password"
          : error.message,
      };
    }

    if (!authUser) {
      return { error: "Invalid email or password" };
    }

    const prismaMember = await prismaClient.member.findFirst({
      select: null,
      where: {
        userId: authUser.id,
        role: MemberRole.BUILDER_ADMIN,
      },
    });

    if (prismaMember) {
      await authService.signOut();
      const { error: otpError } = await authService.sendSignInOtp({
        email,
        redirectTo: EMAIL_VERIFICATION_REDIRECT_URL,
      });

      if (otpError) {
        return { error: otpError.message };
      }

      redirectTo = `/verify-code?email=${encodeURIComponent(email)}`;
    }
  } catch (error) {
    return { error: "An unexpected error occurred" };
  }

  redirect(redirectTo);
}
