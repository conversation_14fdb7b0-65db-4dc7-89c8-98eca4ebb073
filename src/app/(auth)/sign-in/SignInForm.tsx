"use client";

import Link from "next/link";
import { useActionState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldDescription,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { signInSchema } from "@/domain/schemas/auth";

import { signIn, type SignInFormState } from "./actions";

type SignInFormValues = {
  email: string;
};

type SignInFormUiState = SignInFormState & {
  values?: SignInFormValues;
};

export default function SignInForm() {
  const initialState: SignInFormUiState = {
    values: {
      email: "",
    },
  };
  const [state, formAction, isPending] = useActionState<
    SignInFormUiState,
    FormData
  >(async (prevState, formData) => {
    const values: SignInFormValues = {
      email: String(formData.get("email") ?? ""),
    };
    const parsed = signInSchema.safeParse({
      ...values,
      password: String(formData.get("password") ?? ""),
    });

    if (!parsed.success) {
      return { fieldErrors: parsed.error.flatten().fieldErrors, values };
    }

    const result = await signIn(prevState, formData);
    if (result.error || result.fieldErrors) {
      return { ...result, values };
    }
    return result;
  }, initialState);

  const emailError = state.fieldErrors?.email?.[0];
  const passwordError = state.fieldErrors?.password?.[0];

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Sign in to your account</CardTitle>
            <CardDescription>
              Enter your email below to sign in to your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={formAction} noValidate>
              <FieldGroup>
                {state.error && (
                  <FieldError className="mb-2">{state.error}</FieldError>
                )}

                <Field data-invalid={Boolean(emailError)}>
                  <FieldLabel htmlFor="email">Email</FieldLabel>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    disabled={isPending}
                    defaultValue={state.values?.email ?? ""}
                    aria-invalid={Boolean(emailError)}
                  />
                  {emailError && <FieldError>{emailError}</FieldError>}
                </Field>

                <Field data-invalid={Boolean(passwordError)}>
                  <div className="flex items-center">
                    <FieldLabel htmlFor="password">Password</FieldLabel>
                    <Link
                      href="/forgot-password"
                      className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                    >
                      Forgot your password?
                    </Link>
                  </div>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder="••••••••"
                    autoComplete="current-password"
                    disabled={isPending}
                    aria-invalid={Boolean(passwordError)}
                  />
                  {passwordError && <FieldError>{passwordError}</FieldError>}
                </Field>

                <Field>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Signing in..." : "Sign In"}
                  </Button>
                  <FieldDescription className="text-center">
                    Don&apos;t have an account?{" "}
                    <Link
                      href="/sign-up"
                      className="underline-offset-4 hover:underline"
                    >
                      Sign up
                    </Link>
                  </FieldDescription>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
