"use server";

import { redirect } from "next/navigation";

import {
  createServerActionSupabaseClient,
  EMAIL_VERIFICATION_REDIRECT_URL,
} from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import { forgotPasswordSchema } from "./schema";

export type ForgotPasswordFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function sendPasswordResetEmail(
  prevState: ForgotPasswordFormState,
  formData: FormData
): Promise<ForgotPasswordFormState> {
  const parsed = forgotPasswordSchema.safeParse({
    email: formData.get("email"),
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { email } = parsed.data;

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const { error } = await authService.resetPasswordForEmail({
      email,
      redirectTo: `${EMAIL_VERIFICATION_REDIRECT_URL}?type=recovery`,
    });

    if (error) {
      return { error: error.message };
    }
  } catch (error) {
    return { error: "An unexpected error occurred" };
  }

  redirect(`/verify-email?email=${encodeURIComponent(email)}`);
}
