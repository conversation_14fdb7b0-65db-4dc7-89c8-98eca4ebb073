"use client";

import Link from "next/link";
import { useActionState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldDescription,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { forgotPasswordSchema } from "@/domain/schemas/auth";

import {
  type ForgotPasswordFormState,
  sendPasswordResetEmail,
} from "./actions";

type ForgotPasswordFormValues = {
  email: string;
};

type ForgotPasswordFormUiState = ForgotPasswordFormState & {
  values?: ForgotPasswordFormValues;
};

export default function ForgotPasswordForm() {
  const initialState: ForgotPasswordFormUiState = {
    values: {
      email: "",
    },
  };
  const [state, formAction, isPending] = useActionState<
    ForgotPasswordFormUiState,
    FormData
  >(async (prevState, formData) => {
    const values: ForgotPasswordFormValues = {
      email: String(formData.get("email") ?? ""),
    };
    const parsed = forgotPasswordSchema.safeParse(values);

    if (!parsed.success) {
      return { fieldErrors: parsed.error.flatten().fieldErrors, values };
    }

    const result = await sendPasswordResetEmail(prevState, formData);
    if (result.error || result.fieldErrors) {
      return { ...result, values };
    }
    return result;
  }, initialState);

  const emailError = state.fieldErrors?.email?.[0];

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Reset Password</CardTitle>
            <CardDescription>
              Enter your email address and we&apos;ll send you a link to reset
              your password
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={formAction} noValidate>
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field data-invalid={Boolean(emailError)}>
                  <FieldLabel htmlFor="email">Email</FieldLabel>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    disabled={isPending}
                    defaultValue={state.values?.email ?? ""}
                    aria-invalid={Boolean(emailError)}
                  />
                  {emailError && <FieldError>{emailError}</FieldError>}
                </Field>

                <Field>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Sending..." : "Send Reset Link"}
                  </Button>
                  <FieldDescription className="text-center">
                    <Link
                      href="/sign-in"
                      className="underline-offset-4 hover:underline"
                    >
                      Back to Sign In
                    </Link>
                  </FieldDescription>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
