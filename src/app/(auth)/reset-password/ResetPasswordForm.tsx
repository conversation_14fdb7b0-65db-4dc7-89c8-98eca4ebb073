"use client";

import { useSearchParams } from "next/navigation";
import { useActionState } from "react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { resetPassword, type ResetPasswordFormState } from "./actions";
import { resetPasswordSchema } from "./schema";

export default function ResetPasswordForm() {
  const searchParams = useSearchParams();

  const [state, formAction, isPending] = useActionState<
    ResetPasswordFormState,
    FormData
  >(async (prevState, formData) => {
    const parsed = resetPasswordSchema.safeParse({
      password: formData.get("password"),
      confirmPassword: formData.get("confirmPassword"),
    });

    if (!parsed.success) {
      return { fieldErrors: parsed.error.flatten().fieldErrors };
    }

    return resetPassword(
      prevState,
      formData,
      searchParams.get("success_callback_url") ?? undefined
    );
  }, {});

  const passwordError = state.fieldErrors?.password?.[0];
  const confirmPasswordError = state.fieldErrors?.confirmPassword?.[0];

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Set New Password</CardTitle>
            <CardDescription>
              Enter your new password below to reset your password
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={formAction} noValidate>
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field data-invalid={Boolean(passwordError)}>
                  <FieldLabel htmlFor="password">New Password</FieldLabel>
                  <Input
                    type="password"
                    id="password"
                    name="password"
                    placeholder="••••••••"
                    autoComplete="new-password"
                    disabled={isPending}
                    aria-invalid={Boolean(passwordError)}
                  />
                  {passwordError && <FieldError>{passwordError}</FieldError>}
                </Field>

                <Field data-invalid={Boolean(confirmPasswordError)}>
                  <FieldLabel htmlFor="confirmPassword">
                    Confirm New Password
                  </FieldLabel>
                  <Input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    placeholder="••••••••"
                    autoComplete="new-password"
                    disabled={isPending}
                    aria-invalid={Boolean(confirmPasswordError)}
                  />
                  {confirmPasswordError && (
                    <FieldError>{confirmPasswordError}</FieldError>
                  )}
                </Field>

                <Field>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Resetting..." : "Reset Password"}
                  </Button>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
