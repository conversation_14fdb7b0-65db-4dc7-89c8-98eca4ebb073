import assert from "node:assert";

import { createServerActionSupabaseClient } from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import ResetPasswordForm from "./ResetPasswordForm";

export const dynamic = "force-dynamic";

export default async function ResetPasswordPage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  return <ResetPasswordForm />;
}
