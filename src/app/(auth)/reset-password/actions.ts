"use server";

import { redirect } from "next/navigation";

import { createServerActionSupabaseClient } from "@/app/dependencies";
import { resetPasswordSchema } from "@/domain/schemas/auth";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type ResetPasswordFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function resetPassword(
  prevState: ResetPasswordFormState,
  formData: FormData,
  redirectTo = "/"
): Promise<ResetPasswordFormState> {
  const parsed = resetPasswordSchema.safeParse({
    password: formData.get("password"),
    confirmPassword: formData.get("confirmPassword"),
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { password } = parsed.data;

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const { error } = await authService.updateUser({
      password,
    });

    if (error) {
      return { error: error.message };
    }
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }

  redirect(redirectTo);
}
