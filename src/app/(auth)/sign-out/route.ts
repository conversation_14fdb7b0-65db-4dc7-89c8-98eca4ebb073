import { NextRequest, NextResponse } from "next/server";

import { createApiRouteSupabaseClient } from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  const response = NextResponse.redirect(
    new URL("/sign-in", process.env.SITE_URL)
  );

  const supabase = await createApiRouteSupabaseClient(request, response);
  await new SupabaseAuthService(supabase.auth).signOut();

  return response;
}
