"use server";

import Link from "next/link";
import { redirect } from "next/navigation";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

type VerifyEmailPageProps = {
  searchParams: Promise<{ email?: string }>;
};

export default async function VerifyEmailPage({
  searchParams,
}: VerifyEmailPageProps) {
  const { email } = await searchParams;

  if (!email) {
    redirect("/sign-up");
  }

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader className="text-center">
            <div className="text-5xl mb-5">📧</div>
            <CardTitle>Verify your email</CardTitle>
            <CardDescription>
              We&apos;ve sent a verification link to:
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <p className="font-bold text-lg text-primary text-center">
              {email}
            </p>

            <p className="text-muted-foreground text-center leading-relaxed">
              Please check your inbox and click the verification link to
              complete your two-factor authentication.
            </p>

            <div className="bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-300 dark:border-yellow-800 rounded-md p-4">
              <p className="text-sm text-yellow-800 dark:text-yellow-400 m-0">
                <strong>Tip:</strong> If you don&apos;t see the email, check
                your spam folder.
              </p>
            </div>

            <Button asChild variant="outline" className="w-full">
              <Link href="/sign-in">Back to Sign In</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
