"use client";

import { useActionState } from "react";
import PhoneInput from "react-phone-number-input";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { onboardingSchema } from "@/domain/schemas/auth";

import { type OnboardingFormState, submitOnboarding } from "./actions";

type OnboardingFormValues = {
  companyName: string | null;
  chamberOfCommerceNumber: string | null;
  phoneNumber: string | null;
  entityType: string | null;
  city: string | null;
  zipCode: string | null;
  addressLine1: string | null;
  addressLine2: string | null;
};

type OnboardingFormUiState = OnboardingFormState & {
  values?: OnboardingFormValues;
};

export type OnboardingFormProps = {
  legalEntities: {
    code: string;
    name: string;
  }[];
};

export default function OnboardingForm({ legalEntities }: OnboardingFormProps) {
  const [state, formAction, isPending] = useActionState<
    OnboardingFormUiState,
    FormData
  >(
    async (prevState, formData) => {
      const companyName = formData.get("companyName") as string | null;
      const chamberOfCommerceNumber = formData.get(
        "chamberOfCommerceNumber"
      ) as string | null;
      const phoneNumber = formData.get("phoneNumber") as string | null;
      const city = formData.get("city") as string | null;
      const zipCode = formData.get("zipCode") as string | null;
      const addressLine1 = formData.get("addressLine1") as string | null;
      const addressLine2 = formData.get("addressLine2") as string | null;
      const entityType = formData.get("entityType") as string;

      const values: OnboardingFormValues = {
        companyName,
        chamberOfCommerceNumber,
        phoneNumber,
        entityType,
        city,
        zipCode,
        addressLine1,
        addressLine2,
      };

      const parsed = onboardingSchema.safeParse({
        companyName,
        chamberOfCommerceNumber,
        phoneNumber,
        entityType,
        city,
        zipCode,
        addressLine1,
        addressLine2,
      });

      if (!parsed.success) {
        return {
          fieldErrors: parsed.error.flatten().fieldErrors,
          values,
        };
      }

      const result = await submitOnboarding(prevState, formData);

      return { ...result, values };
    },
    {
      values: {
        phoneNumber: "+44",
        companyName: null,
        chamberOfCommerceNumber: null,
        entityType: null,
        city: null,
        zipCode: null,
        addressLine1: null,
        addressLine2: null,
      },
    }
  );

  const companyNameError = state.fieldErrors?.companyName?.[0];
  const chamberError = state.fieldErrors?.chamberOfCommerceNumber?.[0];
  const phoneError = state.fieldErrors?.phoneNumber?.[0];
  const cityError = state.fieldErrors?.city?.[0];
  const zipCodeError = state.fieldErrors?.zipCode?.[0];
  const addressLine1Error = state.fieldErrors?.addressLine1?.[0];
  const entityTypeError = state.fieldErrors?.entityType?.[0];

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Complete Your Profile</CardTitle>
            <CardDescription>
              Please provide your company information to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={formAction} noValidate>
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field data-invalid={Boolean(companyNameError)}>
                  <FieldLabel htmlFor="companyName">Company Name</FieldLabel>
                  <Input
                    type="text"
                    id="companyName"
                    name="companyName"
                    disabled={isPending}
                    autoComplete="organization"
                    defaultValue={state.values?.companyName ?? ""}
                    aria-invalid={Boolean(companyNameError)}
                  />
                  {companyNameError && (
                    <FieldError>{companyNameError}</FieldError>
                  )}
                </Field>

                <Field data-invalid={Boolean(chamberError)}>
                  <FieldLabel htmlFor="chamberOfCommerceNumber">
                    Company Number
                  </FieldLabel>
                  <Input
                    type="text"
                    id="chamberOfCommerceNumber"
                    name="chamberOfCommerceNumber"
                    disabled={isPending}
                    defaultValue={state.values?.chamberOfCommerceNumber ?? ""}
                    aria-invalid={Boolean(chamberError)}
                  />
                  {chamberError && <FieldError>{chamberError}</FieldError>}
                </Field>

                <Field data-invalid={Boolean(chamberError)}>
                  <FieldLabel>Entity Type</FieldLabel>
                  <Select
                    name="entityType"
                    defaultValue={state.values?.entityType ?? undefined}
                    disabled={isPending}
                  >
                    <SelectTrigger aria-invalid={Boolean(entityTypeError)}>
                      <SelectValue placeholder="Select Entity Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {legalEntities.map((legalEntity) => (
                        <SelectItem
                          key={legalEntity.code}
                          value={legalEntity.code}
                        >
                          {legalEntity.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {entityTypeError && (
                    <FieldError>{entityTypeError}</FieldError>
                  )}
                </Field>

                <div className="flex gap-3">
                  <Field data-invalid={Boolean(cityError)}>
                    <FieldLabel htmlFor="city">City</FieldLabel>
                    <Input
                      type="text"
                      id="city"
                      name="city"
                      disabled={isPending}
                      autoComplete="address-level2"
                      defaultValue={state.values?.city ?? undefined}
                      aria-invalid={Boolean(cityError)}
                    />
                    {cityError && <FieldError>{cityError}</FieldError>}
                  </Field>
                  <Field data-invalid={Boolean(zipCodeError)}>
                    <FieldLabel htmlFor="zipCode">Zip Code</FieldLabel>
                    <Input
                      type="text"
                      id="zipCode"
                      name="zipCode"
                      disabled={isPending}
                      autoComplete="postal-code"
                      defaultValue={state.values?.zipCode ?? undefined}
                      aria-invalid={Boolean(zipCodeError)}
                    />
                    {zipCodeError && <FieldError>{zipCodeError}</FieldError>}
                  </Field>
                </div>

                <div className="flex gap-3">
                  <Field
                    data-invalid={Boolean(addressLine1Error)}
                    className="flex-1"
                  >
                    <FieldLabel htmlFor="addressLine1">
                      Address Line 1
                    </FieldLabel>
                    <Input
                      type="text"
                      id="addressLine1"
                      name="addressLine1"
                      disabled={isPending}
                      autoComplete="address-line1"
                      defaultValue={state.values?.addressLine1 ?? undefined}
                      aria-invalid={Boolean(addressLine1Error)}
                    />

                    {addressLine1Error && (
                      <FieldError>{addressLine1Error}</FieldError>
                    )}
                  </Field>

                  <Field className="flex-1">
                    <FieldLabel htmlFor="addressLine2">
                      Address Line 2
                    </FieldLabel>
                    <Input
                      type="text"
                      id="addressLine2"
                      name="addressLine2"
                      disabled={isPending}
                      autoComplete="address-line2"
                      defaultValue={state.values?.addressLine2 ?? undefined}
                    />
                  </Field>
                </div>

                <Field data-invalid={Boolean(phoneError)}>
                  <FieldLabel htmlFor="phoneNumber">Phone Number</FieldLabel>
                  <PhoneInput
                    id="phoneNumber"
                    name="phoneNumber"
                    disabled={isPending}
                    inputComponent={Input}
                    autoComplete="tel"
                    defaultCountry="GB"
                    international
                    withCountryCallingCode
                    onChange={() => {}}
                    aria-invalid={Boolean(phoneError)}
                    defaultValue={state.values?.phoneNumber ?? undefined}
                    required
                  />
                  {phoneError && <FieldError>{phoneError}</FieldError>}
                </Field>

                <Field>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Completing..." : "Complete Onboarding"}
                  </Button>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
