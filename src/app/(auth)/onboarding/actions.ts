"use server";

import { redirect } from "next/navigation";
import { randomUUID } from "node:crypto";

import {
  createServerActionSupabaseClient,
  oppQueryHandler,
  organizationRepository,
  userRepository,
} from "@/app/dependencies";
import {
  associateOrganizationWithOppMerchant,
  createOrganization,
} from "@/domain/organization";
import { onboardingSchema } from "@/domain/schemas/auth";
import { finishOnboarding, UserStatus } from "@/domain/user";
import { OppApiError } from "@/opp/OppClient";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type OnboardingFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function submitOnboarding(
  prevState: OnboardingFormState,
  formData: FormData
): Promise<OnboardingFormState> {
  const parsed = onboardingSchema.safeParse({
    companyName: formData.get("companyName"),
    chamberOfCommerceNumber: formData.get("chamberOfCommerceNumber"),
    phoneNumber: formData.get("phoneNumber"),
    entityType: formData.get("entityType"),
    city: formData.get("city"),
    zipCode: formData.get("zipCode"),
    addressLine1: formData.get("addressLine1"),
    addressLine2: formData.get("addressLine2"),
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const {
    companyName,
    chamberOfCommerceNumber,
    phoneNumber,
    entityType,
    city,
    zipCode,
    addressLine1,
    addressLine2,
  } = parsed.data;

  const supabase = await createServerActionSupabaseClient();
  const authUser = await new SupabaseAuthService(supabase.auth).getUser();

  if (!authUser) {
    return { error: "Unauthorized" };
  }

  let user = await userRepository.findById(authUser.id);

  if (!user) {
    return { error: "User not found" };
  }

  const previousUser = user;

  if (user.status !== UserStatus.WAITING_ONBOARDING) {
    redirect("/");
  }

  user = finishOnboarding(user);

  let organization = createOrganization({
    id: randomUUID(),
    name: companyName.trim(),
    adminUserId: user.id,
  });

  try {
    const oppMerchant = await oppQueryHandler.createMerchant({
      chamberOfCommerceNumber,
      email: user.email,
      phoneNumber,
      entityType,
      city,
      zipCode,
      addressLine1,
      addressLine2,
    });

    organization = associateOrganizationWithOppMerchant(organization, {
      merchantId: oppMerchant.merchantUid,
      status: oppMerchant.merchantStatus,
      complianceStatus: oppMerchant.merchantComplianceStatus,
    });
  } catch (error) {
    if (error instanceof OppApiError) {
      return { error: `${error.statusCode} ${error.message}` };
    }

    return { error: String(error) };
  }

  await userRepository.update(previousUser, user);
  await organizationRepository.add(organization);

  redirect("/");
}
