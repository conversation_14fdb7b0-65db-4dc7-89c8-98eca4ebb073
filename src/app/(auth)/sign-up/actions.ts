"use server";

import { redirect } from "next/navigation";

import {
  createServerActionSupabaseClient,
  EMAIL_VERIFICATION_REDIRECT_URL,
  userRepository,
} from "@/app/dependencies";
import { signUpSchema } from "@/domain/schemas/auth";
import { createUser, UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type SignUpFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function signUp(
  prevState: SignUpFormState,
  formData: FormData
): Promise<SignUpFormState> {
  const parsed = signUpSchema.safeParse({
    email: formData.get("email"),
    firstName: formData.get("firstName"),
    lastName: formData.get("lastName"),
    password: formData.get("password"),
    acceptedTerms: formData.get("acceptedTerms") === "on",
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { email, firstName, lastName, password } = parsed.data;
  const name = `${firstName.trim()} ${lastName.trim()}`;

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const { data: authUser, error } = await authService.signUp({
      email,
      password,
      redirectTo: EMAIL_VERIFICATION_REDIRECT_URL,
    });

    if (error) {
      return { error: error.message };
    }

    if (!authUser) {
      return { error: "Failed to create user" };
    }

    let user = await userRepository.findById(authUser.id);

    if (user) {
      return { error: "User already exists" };
    }

    user = createUser({
      id: authUser.id,
      email: authUser.email,
      name,
      acceptedTermsAndConditionsAt: new Date(),
      status: UserStatus.WAITING_ONBOARDING,
    });

    await userRepository.add(user);
  } catch (error) {
    return { error: "An unexpected error occurred" };
  }

  redirect(`/verify-email?email=${encodeURIComponent(email)}`);
}
