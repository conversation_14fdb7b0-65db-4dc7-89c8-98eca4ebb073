"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useActionState, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Field,
  FieldDescription,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { signUp, type SignUpFormState } from "./actions";
import { signUpSchema } from "./schema";

type SignUpFormValues = {
  email: string | null;
  acceptedTerms: boolean | null;
};

type SignUpFormUiState = SignUpFormState & {
  values?: SignUpFormValues;
};

export default function SignUpForm() {
  const searchParams = useSearchParams();
  const verificationError = searchParams.get("error") === "verification_failed";
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  const [state, formAction, isPending] = useActionState<
    SignUpFormUiState,
    FormData
  >(async (prevState, formData) => {
    const email = formData.get("email") as string | null;
    const acceptedTerms = formData.get("acceptedTerms") === "on";
    const password = formData.get("password") as string | null;
    const values: SignUpFormValues = { email, acceptedTerms };
    const parsed = signUpSchema.safeParse({
      email,
      acceptedTerms,
      password,
    });

    if (!parsed.success) {
      return { fieldErrors: parsed.error.flatten().fieldErrors, values };
    }

    const result = await signUp(prevState, formData);

    if (result.error || result.fieldErrors) {
      return { ...result, values };
    }

    return result;
  }, {});

  const emailError = state.fieldErrors?.email?.[0];
  const passwordError = state.fieldErrors?.password?.[0];
  const acceptedTermsError = state.fieldErrors?.acceptedTerms?.[0];

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Create an account</CardTitle>
            <CardDescription>
              Enter your email below to create your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={formAction} noValidate>
              <FieldGroup>
                {verificationError && (
                  <FieldError>
                    Email verification failed. Please try again.
                  </FieldError>
                )}

                {state.error && <FieldError>{state.error}</FieldError>}

                <Field data-invalid={Boolean(emailError)}>
                  <FieldLabel htmlFor="email">Email</FieldLabel>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    disabled={isPending}
                    defaultValue={state.values?.email ?? undefined}
                    aria-invalid={Boolean(emailError)}
                  />
                  {emailError && <FieldError>{emailError}</FieldError>}
                </Field>

                <Field data-invalid={Boolean(passwordError)}>
                  <FieldLabel htmlFor="password">Password</FieldLabel>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder="••••••••"
                    autoComplete="new-password"
                    disabled={isPending}
                    aria-invalid={Boolean(passwordError)}
                  />
                  {passwordError && <FieldError>{passwordError}</FieldError>}
                </Field>

                <Field
                  orientation="horizontal"
                  data-invalid={Boolean(acceptedTermsError)}
                >
                  <Input
                    type="hidden"
                    name="acceptedTerms"
                    value={acceptedTerms ? "on" : "off"}
                    readOnly
                  />
                  <Checkbox
                    id="acceptedTerms"
                    checked={acceptedTerms}
                    onCheckedChange={(value) =>
                      setAcceptedTerms(value === true)
                    }
                    disabled={isPending}
                    aria-invalid={Boolean(acceptedTermsError)}
                  />
                  <FieldLabel htmlFor="acceptedTerms">
                    I accept the Terms and Conditions
                  </FieldLabel>
                </Field>
                {acceptedTermsError && (
                  <FieldError>{acceptedTermsError}</FieldError>
                )}
                <Field>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Signing up..." : "Sign Up"}
                  </Button>
                  <FieldDescription className="text-center">
                    Already have an account?{" "}
                    <Link
                      href="/sign-in"
                      className="underline-offset-4 hover:underline"
                    >
                      Sign in
                    </Link>
                  </FieldDescription>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
