import { NextRequest, NextResponse } from "next/server";
import assert from "node:assert";

import {
  oppQ<PERSON><PERSON><PERSON><PERSON><PERSON>,
  oppWebhookHandler,
  organizationRepository,
  prismaClient,
} from "@/app/dependencies";
import {
  changeOppMerchantComplianceStatus,
  changeOppMerchantStatus,
} from "@/domain/organization";

export async function POST(request: NextRequest) {
  try {
    const payload = await oppWebhookHandler.readEvent(request);

    console.log("OPP Webhook Received:", payload);

    if (
      payload.type === "merchant.compliance_status.changed" ||
      payload.type === "merchant.status.changed"
    ) {
      const oppMerchantUid = payload.object_uid;
      const { id: organizationId } =
        await prismaClient.organization.findUniqueOrThrow({
          select: {
            id: true,
          },
          where: {
            oppMerchantUid,
          },
        });
      let organization = await organizationRepository.findById(organizationId);

      assert(organization, `Couldn't find organization: ${organizationId}`);

      const prevOrganization = organization;

      if (payload.type === "merchant.compliance_status.changed") {
        organization = changeOppMerchantComplianceStatus(
          organization,
          await oppQueryHandler.getMerchantComplianceStatus(oppMerchantUid)
        );
      } else if (payload.type === "merchant.status.changed") {
        organization = changeOppMerchantStatus(
          organization,
          await oppQueryHandler.getMerchantStatus(oppMerchantUid)
        );
      }

      await organizationRepository.update(prevOrganization, organization);
    }

    return NextResponse.json({ ok: true }, { status: 200 });
  } catch (error) {
    console.error("Error processing OPP webhook:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
