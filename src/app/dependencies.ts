import { PrismaPg } from "@prisma/adapter-pg";
import { PrismaClient } from "@prisma/client";
import { CookieMethodsServer, createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import assert from "node:assert";

import OppClient from "@/opp/OppClient";
import Opp<PERSON><PERSON>yHandler from "@/opp/OppQueryHandler";
import OppWebhookHandler from "@/opp/OppWebhookHandler";
import { PrismaOrganizationRepository } from "@/prisma/PrismaOrganizationRepository";
import { PrismaUserRepository } from "@/prisma/PrismaUserRepository";
import ResendEmailService from "@/resend/ResendEmailService";
import SupabaseApiRouteCookieStorage from "@/supabase/SupabaseApiRouteCookieStorage";
import SupabaseServerActionCookieStorage from "@/supabase/SupabaseServerActionCookieStorage";

const adapter = new PrismaPg({ connectionString: process.env.DATABASE_URL });
export const prismaClient = new PrismaClient({ adapter });

export const organizationRepository = new PrismaOrganizationRepository(
  prismaClient
);
export const userRepository = new PrismaUserRepository(prismaClient);

const oppClient = new OppClient({
  apiKey: process.env.OPP_API_KEY!,
  apiUrl: process.env.OPP_API_URL!,
  filesApiUrl: process.env.OPP_FILES_API_URL!,
});

export const oppQueryHandler = new OppQueryHandler({
  oppClient,
  notifyUrl: `${process.env.LOCALTUNNEL_SITE_URL ?? process.env.SITE_URL}/api/opp/webhook`,
  returnUrl: `${process.env.LOCALTUNNEL_SITE_URL ?? process.env.SITE_URL}/compliance`,
});

export const oppWebhookHandler = new OppWebhookHandler({
  secret: process.env.OPP_WEBHOOK_SECRET!,
});

export async function createApiRouteSupabaseClient(
  request: NextRequest,
  response: NextResponse
) {
  return createSupabaseClient(
    new SupabaseApiRouteCookieStorage(request, response)
  );
}

export async function createServerActionSupabaseClient() {
  return createSupabaseClient(
    new SupabaseServerActionCookieStorage(await cookies())
  );
}

export function createSupabaseClient(cookieStorage: CookieMethodsServer) {
  assert(process.env.SUPABASE_URL, "Expected SUPABASE_URL to be defined");
  assert(
    process.env.SUPABASE_SECRET_KEY,
    "Expected SUPABASE_SECRET_KEY to be defined"
  );

  return createServerClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SECRET_KEY,
    {
      cookies: cookieStorage,
    }
  );
}

export const emailService = new ResendEmailService(process.env.RESEND_API_KEY!);

export const ACCEPT_INVITE_REDIRECT_URL = `${process.env.SITE_URL}/accept-invite`;

export const EMAIL_VERIFICATION_REDIRECT_URL = `${process.env.SITE_URL}/auth-callback`;
