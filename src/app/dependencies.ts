import { PrismaPg } from "@prisma/adapter-pg";
import { PrismaClient } from "@prisma/client";
import { CookieMethodsServer, createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import assert from "node:assert";

import PrismaIBPRepository from "@/prisma/PrismaIBPRepository";
import PrismaUserRepository from "@/prisma/PrismaUserRepository";
import SupabaseApiRouteCookieStorage from "@/supabase/SupabaseApiRouteCookieStorage";
import SupabaseServerActionCookieStorage from "@/supabase/SupabaseServerActionCookieStorage";

const adapter = new PrismaPg({ connectionString: process.env.DATABASE_URL });
export const prismaClient = new PrismaClient({ adapter });
export const userRepository = new PrismaUserRepository(prismaClient);
export const ibpRepository = new PrismaIBPRepository(prismaClient);

export async function createApiRouteSupabaseClient(
  request: NextRequest,
  response: NextResponse
) {
  return createSupabaseClient(
    new SupabaseApiRouteCookieStorage(request, response)
  );
}

export async function createServerActionSupabaseClient() {
  return createSupabaseClient(
    new SupabaseServerActionCookieStorage(await cookies())
  );
}

export function createSupabaseClient(cookieStorage: CookieMethodsServer) {
  assert(process.env.SUPABASE_URL, "Expected SUPABASE_URL to be defined");
  assert(
    process.env.SUPABASE_SECRET_KEY,
    "Expected SUPABASE_SECRET_KEY to be defined"
  );

  return createServerClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SECRET_KEY,
    {
      cookies: cookieStorage,
    }
  );
}

export const EMAIL_VERIFICATION_REDIRECT_URL = `${process.env.SITE_URL}/auth-callback`;
