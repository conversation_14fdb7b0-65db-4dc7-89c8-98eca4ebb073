export type UserId = string;

export type User = {
  id: UserId;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
  createdAt: Date;
  updatedAt: Date;
};

type CreateUserParams = {
  id: UserId;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
};

export function createUser(params: CreateUserParams): User {
  const now = new Date();
  return {
    id: params.id,
    email: params.email,
    firstName: params.firstName,
    lastName: params.lastName,
    phoneNumber: params.phoneNumber,
    createdAt: now,
    updatedAt: now,
  };
}

type UpdateUserParams = {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string | null;
};

export function updateUser(user: User, params: UpdateUserParams): User {
  return {
    ...user,
    firstName: params.firstName ?? user.firstName,
    lastName: params.lastName ?? user.lastName,
    phoneNumber:
      params.phoneNumber !== undefined ? params.phoneNumber : user.phoneNumber,
    updatedAt: new Date(),
  };
}
