import { z } from "zod";

export const emailSchema = z.string().trim().email("Enter a valid email");

export const passwordSchema = z
  .string()
  .min(6, "Password must be at least 6 characters");

export const organizationIdSchema = z
  .string()
  .trim()
  .min(1, "Organization ID is required")
  .uuid("Organization ID is invalid");

export const userIdSchema = z
  .string()
  .trim()
  .min(1, "User ID is required")
  .uuid("User ID is invalid");

export const dateStringSchema = z
  .string()
  .trim()
  .refine((value) => !Number.isNaN(Date.parse(value)), {
    message: "Invalid date",
  });
