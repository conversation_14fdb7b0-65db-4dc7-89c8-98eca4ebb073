import { z } from "zod";

const ibanSchema = z
  .string()
  .trim()
  .min(1, "Account IBAN is required")
  .transform((value) => value.replace(/\s+/g, "").toUpperCase())
  .refine((value) => /^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/.test(value), {
    message: "Account IBAN is invalid",
  });

const bicSchema = z
  .string()
  .trim()
  .min(1, "Account BIC is required")
  .transform((value) => value.replace(/\s+/g, "").toUpperCase())
  .refine(
    (value) => /^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$/.test(value),
    {
      message: "Account BIC is invalid",
    }
  );

export const bankAccountSchema = z.object({
  accountName: z.string().trim().min(1, "Account name is required"),
  accountIban: ibanSchema,
  accountBic: bicSchema,
});

export type BankAccountInput = z.infer<typeof bankAccountSchema>;

type ChamberOfCommerceUploadSchemaParams = {
  allowedMimeTypes: string[];
  maxFileSizeInBytes: number;
};

const isFile = (value: unknown): value is File =>
  typeof File !== "undefined" && value instanceof File;

export const createChamberOfCommerceUploadSchema = (
  params: ChamberOfCommerceUploadSchemaParams
) => {
  const maxSizeMB = Math.round(params.maxFileSizeInBytes / (1024 * 1024));

  return z.object({
    file: z
      .custom<File>(isFile, { message: "Please select a file" })
      .refine((file) => file.size <= params.maxFileSizeInBytes, {
        message: `File size must be less than ${maxSizeMB}MB`,
      })
      .refine((file) => params.allowedMimeTypes.includes(file.type), {
        message: `File type must be one of: ${params.allowedMimeTypes.join(", ")}`,
      }),
  });
};

export type ChamberOfCommerceUploadInput = z.infer<
  ReturnType<typeof createChamberOfCommerceUploadSchema>
>;
