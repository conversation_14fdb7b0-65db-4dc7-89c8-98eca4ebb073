import { z } from "zod";

import { dateStringSchema, emailSchema } from "./shared";

const milestoneBaseSchema = z.object({
  scope: z.string().trim().min(1, "Scope is required"),
  startDate: dateStringSchema,
  endDate: dateStringSchema,
  budget: z.number().min(0, "Budget must be 0 or greater"),
});

export const milestoneSchema = milestoneBaseSchema.refine(
  (data) => new Date(data.endDate) >= new Date(data.startDate),
  {
    message: "End date must be on or after start date",
    path: ["endDate"],
  }
);

export type MilestoneInput = z.infer<typeof milestoneSchema>;

export const newMilestoneSchema = milestoneBaseSchema
  .extend({
    projectId: z.string().trim().min(1, "Project ID is required"),
    milestoneId: z.string().trim().optional(),
  })
  .refine((data) => new Date(data.endDate) >= new Date(data.startDate), {
    message: "End date must be on or after start date",
    path: ["endDate"],
  });

export type NewMilestoneInput = z.infer<typeof newMilestoneSchema>;

export const newProjectSchema = z.object({
  id: z.string().trim().optional(),
  name: z.string().trim().min(1, "Project name is required"),
});

export type NewProjectInput = z.infer<typeof newProjectSchema>;

export const inviteHomeownerSchema = z.object({
  email: emailSchema,
});

export type InviteHomeownerInput = z.infer<typeof inviteHomeownerSchema>;
