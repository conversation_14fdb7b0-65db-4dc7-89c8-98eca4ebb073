import { z } from "zod";

import { emailSchema, organizationIdSchema, userIdSchema } from "./shared";

export const inviteUserSchema = z.object({
  email: emailSchema,
  organizationId: organizationIdSchema,
  role: z.enum(["admin", "user"]),
});

export type InviteUserInput = z.infer<typeof inviteUserSchema>;

export const suspendMemberSchema = z.object({
  userId: userIdSchema,
  organizationId: organizationIdSchema,
});

export type SuspendMemberInput = z.infer<typeof suspendMemberSchema>;

export const resumeMemberSchema = z.object({
  userId: userIdSchema,
  organizationId: organizationIdSchema,
});

export type ResumeMemberInput = z.infer<typeof resumeMemberSchema>;
