import { z } from "zod";

import { emailSchema, organizationIdSchema, passwordSchema } from "./shared";

export const signInSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
});

export type SignInInput = z.infer<typeof signInSchema>;

const nameSchema = z
  .string()
  .trim()
  .min(2, "Must be at least 2 characters")
  .regex(
    /^[A-Za-z\s'-]+$/,
    "Only letters, spaces, hyphens and apostrophes are allowed"
  );

export const signUpSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  firstName: nameSchema,
  lastName: nameSchema,
  acceptedTerms: z.boolean().refine((value) => value, {
    message: "You must accept the Terms and Conditions",
  }),
});

export type SignUpInput = z.infer<typeof signUpSchema>;

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;

export const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: passwordSchema,
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;

export const verifyOtpSchema = z.object({
  email: emailSchema,
  token: z
    .string()
    .trim()
    .regex(/^\d{6}$/, "Verification code must be 6 digits"),
});

export type VerifyOtpInput = z.infer<typeof verifyOtpSchema>;

export const acceptInviteSchema = z.object({
  organizationId: organizationIdSchema,
});

export type AcceptInviteInput = z.infer<typeof acceptInviteSchema>;

export const onboardingSchema = z.object({
  companyName: z.string().trim().min(1, "Company name is required"),
  chamberOfCommerceNumber: z
    .string()
    .trim()
    .min(1, "Company Number is required")
    .regex(/^[A-Za-z0-9-]+$/, "Company Number is invalid"),
  phoneNumber: z
    .string()
    .trim()
    .min(7, "Phone Number is required")
    .regex(/^[0-9+()\-\s]+$/, "Phone Number is invalid"),
  entityType: z.string().trim().min(1, "Entity Type is required"),
  city: z.string().trim().min(1, "City is required"),
  zipCode: z.string().trim().min(1, "Zip Code is required"),
  addressLine1: z.string().trim().min(1, "Address Line 1 is required"),
  addressLine2: z.string().trim().optional(),
});

export type OnboardingInput = z.infer<typeof onboardingSchema>;
