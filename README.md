## Init Development

To install all packages

```bash
# ./
npm ci
```

> **Don't forget to fill the `.env` file**

To generate supabase access token

```bash
# ./astrala-site
npx supabase login
```

To run application

```bash
# ./
npm run start
```

## Useful information for Development

To generate types from DB

```bash
# ./astrala-site
npm run gen:types
```

> If you have `sh: 1: source: not found` error then do this
>
> ```bash
> ls -l `which sh`
> #/bin/sh -> dash
>
> sudo dpkg-reconfigure dash
> #Select "no" when you're asked
>
> ls -l `which sh`
> #/bin/sh -> bash
> ```
