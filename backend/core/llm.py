import json
import uuid

from openai import AsyncOpenAI, _types
from pydantic import BaseModel

from config import settings
from .prompts import (
    INITIAL_CHAT_PROMPT_TEMPLATE,
    OVERVIEW_GENERATION_PROMPT_TEMPLATE,
    OVERVIEW_CHAT_PROMPT_TEMPLATE,
    SCOPES_GENERATION_PROMPT_TEMPLATE,
    SCOPES_CHAT_PROMPT_TEMPLATE,
    ESTIMATES_GENERATION_PROMPT_TEMPLATE,
)


# TODO: move to settings?
LLM_MODEL_NAME = "gpt-5-mini"
# LLM_MODEL_NAME = "gpt-4.1-mini"


USER_REQUEST_TYPE = "user_request"
AI_RESPONSE_TYPE = "your_response"
USER_MODIFICATION_REQUEST_TYPE = "user_modification_request"
AI_MODIFICATION_RESPONSE_TYPE = "modification_summary"
GENERATION_TRIGGER_MESSAGE = "}require-auth{" # to return json.dumps({"type": "require-auth"})
OVERVIEW_SUMMARY_KEY = "modification_summary"
OVERVIEW_MODIFIED_KEY = "modified_overview"
SCOPES_SUMMARY_KEY = "modification_summary"
SCOPES_MODIFIED_KEY = "modified_scopes"


class SummaryAndModifiedOverviewLLMResponse(BaseModel):
    modification_summary: str
    modified_overview: str


assert (
    {OVERVIEW_SUMMARY_KEY, OVERVIEW_MODIFIED_KEY}
    == set(SummaryAndModifiedOverviewLLMResponse.model_fields.keys())
)


async def _get_streaming_response(messages, response_format=None):
    client = AsyncOpenAI(
        api_key=settings.OPENAI_API_KEY,
        timeout=600,
        max_retries=2,
    )
    if response_format is None:
        response_format = _types.omit

    async with client.beta.chat.completions.stream(
        model=LLM_MODEL_NAME,
        messages=messages,
        response_format=response_format,
    ) as stream:
        async for event in stream:
            if event.type == "content.delta":
                yield event.delta


async def post_chat_message_initial(previous_history: list[dict]):
    messages = [
        {
            "role": "system",
            "content": INITIAL_CHAT_PROMPT_TEMPLATE.format(
                user_request=USER_REQUEST_TYPE,
                your_response=AI_RESPONSE_TYPE,
                trigger=GENERATION_TRIGGER_MESSAGE,
            ),
        },
        {
            "role": "user",
            "content": json.dumps(previous_history),
        },
    ]
    async for chunk in _get_streaming_response(messages):
        yield chunk


async def post_overview_generation(previous_history: list[dict]):
    messages = [
        {
            "role": "system",
            "content": OVERVIEW_GENERATION_PROMPT_TEMPLATE.format(
                user_request=USER_REQUEST_TYPE,
                your_response=AI_RESPONSE_TYPE,
            ),
        },
        {
            "role": "user",
            "content": json.dumps(previous_history),
        },
    ]
    async for chunk in _get_streaming_response(messages):
        yield chunk


async def post_chat_message_overview(
    previous_history_initial: list[dict],
    previous_history_overview: list[dict],
    modification_request: str,
    overview: str,
):
    messages = [
        {
            "role": "system",
            "content": OVERVIEW_CHAT_PROMPT_TEMPLATE.format(
                user_request=USER_REQUEST_TYPE,
                your_response=AI_RESPONSE_TYPE,
                user_modification_request=USER_MODIFICATION_REQUEST_TYPE,
                ai_modification_response=AI_MODIFICATION_RESPONSE_TYPE,
                modified_key=OVERVIEW_MODIFIED_KEY,
                summary_key=OVERVIEW_SUMMARY_KEY,
            ),
        },
        {
            "role": "user",
            "content": overview,
        },
        {
            "role": "user",
            "content": json.dumps(previous_history_initial),
        },
        {
            "role": "user",
            "content": json.dumps(previous_history_overview),
        },
        {
            "role": "user",
            "content": modification_request,
        }
    ]
    kwargs = dict(
        messages=messages,
        response_format=SummaryAndModifiedOverviewLLMResponse
    )
    async for chunk in _get_streaming_response(**kwargs):
        yield chunk


# # coda format
#
# TABLE_TYPE = "table"
# ROW_TYPE = "row"
# COLUMN_TYPE = "column"
# CELL_TYPE = "cell"
#
# TABLE_ID_FORMAT = f"{TABLE_TYPE}_{{}}"
# ROW_ID_FORMAT = f"{ROW_TYPE}_{{}}"
# COLUMN_ID_FORMAT = f"{COLUMN_TYPE}_{{}}"
# CELL_ID_FORMAT = f"{CELL_TYPE}_{{}}"
#
# COLUMN_NAME_GROUP = "Group"
# COLUMN_NAME_FEATURE = "Feature"
# COLUMN_NAME_MILESTONE = "Milestone"
#
# COLUMN_ID_GROUP = COLUMN_ID_FORMAT.format(COLUMN_NAME_GROUP)
# COLUMN_ID_FEATURE = COLUMN_ID_FORMAT.format(COLUMN_NAME_FEATURE)
# COLUMN_ID_MILESTONE = COLUMN_ID_FORMAT.format(COLUMN_NAME_MILESTONE)
#
# def _get_table_fixture() -> dict:
#     # TODO: think of format. DO something similar to coda, where
#     # TODO: table, row and cell entities are present, and each has it's own unique id
#
#     row_1_id = ROW_ID_FORMAT.format(uuid.uuid4())
#     row_2_id = ROW_ID_FORMAT.format(uuid.uuid4())
#     row_3_id = ROW_ID_FORMAT.format(uuid.uuid4())
#     row_4_id = ROW_ID_FORMAT.format(uuid.uuid4())
#
#     scopes = {
#         "id": TABLE_ID_FORMAT.format(uuid.uuid4()),
#         "type": TABLE_TYPE,
#         "columns": [
#             {
#                 "id": COLUMN_ID_GROUP,
#                 "type": COLUMN_TYPE,
#                 "name": COLUMN_NAME_GROUP,  # whatever verbose name here
#             },
#             {
#                 "id": COLUMN_ID_FEATURE,
#                 "type": COLUMN_TYPE,
#                 "name": COLUMN_NAME_FEATURE,  # whatever verbose name here
#             },
#             {
#                 "id": COLUMN_ID_MILESTONE,
#                 "type": COLUMN_TYPE,
#                 "name": COLUMN_NAME_MILESTONE,  # whatever verbose name here
#             },
#         ],
#         "rows": [
#             {
#                 "id": row_1_id,
#                 "type": ROW_TYPE,
#                 # "cells": {
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_GROUP): "buyer?",
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_FEATURE): "row: 1, column: feature",
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_MILESTONE): "milestone 1",
#                 # },
#                 "cells": [
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_1_id,
#                         "column_id": COLUMN_ID_GROUP,
#                         "value": "buyer (group example)",
#                     },
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_1_id,
#                         "column_id": COLUMN_ID_FEATURE,
#                         "value": "row: 1, column: feature (feature example)",
#                     },
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_1_id,
#                         "column_id": COLUMN_ID_MILESTONE,
#                         "value": "milestone 1 (milestone example)",
#                     },
#                 ],
#             },
#             {
#                 "id": row_2_id,
#                 "type": ROW_TYPE,
#                 # "cells": {
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_GROUP): "buyer?",
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_FEATURE): "row: 2, column: feature",
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_MILESTONE): "milestone 2",
#                 # },
#                 "cells": [
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_2_id,
#                         "column_id": COLUMN_ID_GROUP,
#                         "value": "buyer (group example)",
#                     },
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_2_id,
#                         "column_id": COLUMN_ID_FEATURE,
#                         "value": "row: 2, column: feature (feature example)",
#                     },
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_2_id,
#                         "column_id": COLUMN_ID_MILESTONE,
#                         "value": "milestone 2 (milestone example)",
#                     },
#                 ],
#             },
#             {
#                 "id": row_3_id,
#                 "type": ROW_TYPE,
#                 # "cells": {
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_GROUP): "seller?",
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_FEATURE): "row: 3, column: feature",
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_MILESTONE): "milestone 1",
#                 # },
#                 "cells": [
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_3_id,
#                         "column_id": COLUMN_ID_GROUP,
#                         "value": "seller (group example)",
#                     },
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_3_id,
#                         "column_id": COLUMN_ID_FEATURE,
#                         "value": "row: 3, column: feature (feature example)",
#                     },
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_3_id,
#                         "column_id": COLUMN_ID_MILESTONE,
#                         "value": "milestone 1 (milestone example)",
#                     },
#                 ],
#             },
#             {
#                 "id": row_4_id,
#                 "type": ROW_TYPE,
#                 # "cells": {
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_GROUP): "seller?",
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_FEATURE): "row: 4, column: feature",
#                 #     COLUMN_ID_FORMAT.format(COLUMN_NAME_MILESTONE): "milestone 2",
#                 # },
#                 "cells": [
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_4_id,
#                         "column_id": COLUMN_ID_GROUP,
#                         "value": "seller (group example)",
#                     },
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_4_id,
#                         "column_id": COLUMN_ID_FEATURE,
#                         "value": "row: 4, column: feature (feature example)",
#                     },
#                     {
#                         "id": CELL_ID_FORMAT.format(uuid.uuid4()),
#                         "type": CELL_TYPE,
#                         "row_id": row_4_id,
#                         "column_id": COLUMN_ID_MILESTONE,
#                         "value": "milestone 2 (milestone example)",
#                     },
#                 ],
#             },
#         ],
#     }
#     return scopes
#
#
# class _ColumnDef(BaseModel):
#     id: str
#     type: str
#     name: str
#
#
# class _CellDef(BaseModel):
#     id: str
#     type: str
#     column_id: str
#     row_id: str
#     value: str
#
#
# class _RowDef(BaseModel):
#     id: str
#     type: str
#     cells: list[_CellDef]
#
#
# class _TableDef(BaseModel):
#     id: str
#     type: str
#     columns: list[_ColumnDef]
#     rows: list[_RowDef]


STORY_KEY = "story"
GROUP_KEY = "group"
MILESTONE_KEY = "milestone"
STORIES_KEY = "stories"

def _get_table_fixture() -> dict:
    row_1_id = str(uuid.uuid4())
    row_2_id = str(uuid.uuid4())
    row_3_id = str(uuid.uuid4())
    row_4_id = str(uuid.uuid4())

    scopes = {
        "stories": [
            {
                "id": row_1_id,
                STORY_KEY: "row: 1, column: feature (feature example)",
                GROUP_KEY: "buyer (group example)",
                MILESTONE_KEY: "milestone 1 (milestone example)",
            },
            {
                "id": row_2_id,
                STORY_KEY: "row: 2, column: feature (feature example)",
                GROUP_KEY: "buyer (group example)",
                MILESTONE_KEY: "milestone 2 (milestone example)",
            },
            {
                "id": row_3_id,
                STORY_KEY: "row: 3, column: feature (feature example)",
                GROUP_KEY: "seller (group example)",
                MILESTONE_KEY: "milestone 1 (milestone example)",
            },
            {
                "id": row_4_id,
                STORY_KEY: "row: 4, column: feature (feature example)",
                GROUP_KEY: "seller (group example)",
                MILESTONE_KEY: "milestone 2 (milestone example)",
            },
        ],
    }
    return scopes


class _StoryDef(BaseModel):
    id: str
    story: str
    group: str
    milestone: str


assert (
    {"id", STORY_KEY, GROUP_KEY, MILESTONE_KEY}
    == set(_StoryDef.model_fields.keys())
)


class _TableDef(BaseModel):
    stories: list[_StoryDef]


class ScopesLLMResponse(_TableDef):
    pass


# for coda format
# async def post_scopes_generation(overview: str):
#     messages = [
#         {
#             "role": "system",
#             "content": SCOPES_GENERATION_PROMPT_TEMPLATE.format(
#                 table_type=TABLE_TYPE,
#                 column_type=COLUMN_TYPE,
#                 row_type=ROW_TYPE,
#                 cell_type=CELL_TYPE,
#             ),
#         },
#         {
#             "role": "user",
#             "content": overview,
#         },
#         {
#             "role": "user",
#             "content": str(_get_table_fixture()),
#         },
#     ]
#     kwargs = dict(
#         messages=messages,
#         response_format=ScopesLLMResponse,
#     )
#     async for chunk in _get_streaming_response(**kwargs):
#         yield chunk



async def post_scopes_generation(overview: str):
    messages = [
        {
            "role": "system",
            "content": SCOPES_GENERATION_PROMPT_TEMPLATE.format(
                id="id",
                story=STORY_KEY,
                group=GROUP_KEY,
                milestone=MILESTONE_KEY,
                stories=STORIES_KEY,
            ),
        },
        {
            "role": "user",
            "content": overview,
        },
        {
            "role": "user",
            "content": json.dumps(_get_table_fixture()),
        },
    ]
    kwargs = dict(
        messages=messages,
        response_format=ScopesLLMResponse,
    )
    async for chunk in _get_streaming_response(**kwargs):
        yield chunk


class SummaryAndModifiedScopesLLMResponse(BaseModel):
    modification_summary: str
    modified_scopes: ScopesLLMResponse


assert (
    {SCOPES_SUMMARY_KEY, SCOPES_MODIFIED_KEY}
    == set(SummaryAndModifiedScopesLLMResponse.model_fields.keys())
)


async def post_chat_message_scopes(
    previous_history_scopes: list[dict],
    modification_request: str,
    scopes: str,
):
    messages = [
        {
            "role": "system",
            "content": SCOPES_CHAT_PROMPT_TEMPLATE.format(
                id="id",
                story=STORY_KEY,
                group=GROUP_KEY,
                milestone=MILESTONE_KEY,
                stories=STORIES_KEY,
                user_modification_request=USER_MODIFICATION_REQUEST_TYPE,
                ai_modification_response=AI_MODIFICATION_RESPONSE_TYPE,
                modified_key=SCOPES_MODIFIED_KEY,
                summary_key=SCOPES_SUMMARY_KEY,
            ),
        },
        {
            "role": "user",
            "content": scopes,
        },
        {
            "role": "user",
            "content": json.dumps(previous_history_scopes),
        },
        {
            "role": "user",
            "content": modification_request,
        }
    ]
    kwargs = dict(
        messages=messages,
        response_format=SummaryAndModifiedScopesLLMResponse
    )
    async for chunk in _get_streaming_response(**kwargs):
        yield chunk


class _StoryEstimateDef(BaseModel):
    id: str
    story: str
    group: str
    milestone: str
    frontend_optimistic: float
    frontend_most_likely: float
    frontend_pessimistic: float
    backend_optimistic: float
    backend_most_likely: float
    backend_pessimistic: float


FRONT_OPTIMISTIC = "frontend_optimistic"
FRONT_MOST_LIKELY = "frontend_most_likely"
FRONT_PESSIMISTIC = "frontend_pessimistic"
BACK_OPTIMISTIC = "backend_optimistic"
BACK_MOST_LIKELY = "backend_most_likely"
BACK_PESSIMISTIC = "backend_pessimistic"


assert (
    {
        "id", STORY_KEY, GROUP_KEY, MILESTONE_KEY, FRONT_OPTIMISTIC,
        FRONT_MOST_LIKELY, FRONT_PESSIMISTIC, BACK_OPTIMISTIC, BACK_MOST_LIKELY,
        BACK_PESSIMISTIC,
    }
    == set(_StoryEstimateDef.model_fields.keys())
)


class _TableEstimateDef(BaseModel):
    stories: list[_StoryEstimateDef]


class EstimatesLLMResponse(_TableEstimateDef):
    pass


async def post_estimates_generation(scopes: str):
    messages = [
        {
            "role": "system",
            "content": ESTIMATES_GENERATION_PROMPT_TEMPLATE.format(
                id="id",
                story=STORY_KEY,
                group=GROUP_KEY,
                milestone=MILESTONE_KEY,
                stories=STORIES_KEY,
                front_optimistic=FRONT_OPTIMISTIC,
                front_most_likely=FRONT_MOST_LIKELY,
                front_pessimistic=FRONT_PESSIMISTIC,
                back_optimistic=BACK_OPTIMISTIC,
                back_most_likely=BACK_MOST_LIKELY,
                back_pessimistic=BACK_PESSIMISTIC,
            ),
        },
        {
            "role": "user",
            "content": scopes,
        },
    ]
    kwargs = dict(
        messages=messages,
        response_format=EstimatesLLMResponse,
    )
    async for chunk in _get_streaming_response(**kwargs):
        yield chunk
