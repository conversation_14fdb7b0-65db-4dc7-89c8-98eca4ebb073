import time
import httpx
import asyncio
from pydantic import BaseModel

from .llm import (
    STORY_KEY,
    GROUP_KEY,
    MILESTONE_KEY,
    FRONT_OPTIMISTIC,
    FRONT_MOST_LIKELY,
    FRONT_PESSIMISTIC,
    BACK_OPTIMISTIC,
    BACK_MOST_LIKELY,
    BACK_PESSIMISTIC,
)

from config import settings, log


CODA_URI = "https://coda.io/apis/v1/docs"

FOLDER_ID = "fl-T56sc1iNYd"
TEMPLATE_ID = "soFVvbnuln"
TASKS_TABLE_ID = "grid-T3DtF7SXeZ"
ESTIMATES_TABLE_ID = "table-fwO0sRhMp3"
ESTIMATES_PAGE_ID = "canvas-zY8zmdF34K"

COMMENT_KEY = "comment"

# ids must remain the same for same table
COLUMN_IDS_MAP = {
    STORY_KEY: "c-_lU2hA6uGX",
    G<PERSON><PERSON>_KEY: "c-113lfJH<PERSON><PERSON>",
    MILESTONE_KEY: "c-t_a_qVNJZP",
    FRONT_OPTIMISTIC: "c-rTttR3N4zj",
    FRONT_MOST_LIKELY: "c-fWp6lqla0j",
    FRONT_PESSIMISTIC: "c-rdAMa7wcZF",
    BACK_OPTIMISTIC: "c-6j26TWyudi",
    BACK_MOST_LIKELY: "c-zZl_pCyjLn",
    BACK_PESSIMISTIC: "c-x9_MGwYu7f",
    COMMENT_KEY: "c-AZUAgtcJYM",
}


class DocProcessResponse(BaseModel):
    coda_doc_id: str
    coda_doc_link: str
    coda_doc_page_id: str
    coda_doc_page_link: str


class CodaManager:
    def __init__(self, coda_api_key=settings.CODAIO_API_KEY, coda_uri=CODA_URI):
        self._coda_api_key = coda_api_key
        self._coda_api_base_uri = coda_uri
        self._base_headers = {
            'Authorization': f'Bearer {self._coda_api_key}'
        }
        self._headers_w_content_type = self._base_headers.copy()
        self._headers_w_content_type.update({'Content-Type': 'application/json'})

        self._client = httpx.AsyncClient(timeout=10)

    async def aclose(self):
        await self._client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.aclose()

    async def gen_doc_with_estimates(
        self, name: str|None, estimates_data: list[dict],
    ) -> DocProcessResponse:
        # returns link to doc and link to doc's specific page
        # TODO: what to do if name is empty?
        # TODO: temp: generate string like "from_app_{timestamp}"
        name = name or f"from_app_{time.time()}"

        # transform estimates_data into format for tasks
        tasks = []
        for task_est in estimates_data:
            task = {
                "cells": [
                    {
                        "column": COLUMN_IDS_MAP[STORY_KEY],
                        "value": task_est[STORY_KEY],
                    },
                    {
                        "column": COLUMN_IDS_MAP[GROUP_KEY],
                        "value": task_est[GROUP_KEY],
                    },
                    {
                        "column": COLUMN_IDS_MAP[MILESTONE_KEY],
                        "value": task_est[MILESTONE_KEY],
                    },
                    {
                        "column": COLUMN_IDS_MAP[FRONT_OPTIMISTIC],
                        "value": task_est[FRONT_OPTIMISTIC],
                    },
                    {
                        "column": COLUMN_IDS_MAP[FRONT_MOST_LIKELY],
                        "value": task_est[FRONT_MOST_LIKELY],
                    },
                    {
                        "column": COLUMN_IDS_MAP[FRONT_PESSIMISTIC],
                        "value": task_est[FRONT_PESSIMISTIC],
                    },
                    {
                        "column": COLUMN_IDS_MAP[BACK_OPTIMISTIC],
                        "value": task_est[BACK_OPTIMISTIC],
                    },
                    {
                        "column": COLUMN_IDS_MAP[BACK_MOST_LIKELY],
                        "value": task_est[BACK_MOST_LIKELY],
                    },
                    {
                        "column": COLUMN_IDS_MAP[BACK_PESSIMISTIC],
                        "value": task_est[BACK_PESSIMISTIC],
                    },
                    # # Put id to comments to check if order of rows after upsert is maintained
                    # # and to reference
                    # {
                    #     "column": COLUMN_IDS_MAP[COMMENT_KEY],
                    #     "value": task_est["id"],
                    # },
                ]
            }
            tasks.append(task)

        # generate doc from template
        doc_data = await self.create_doc(name=name)
        doc_id = doc_data["id"]
        doc_link = doc_data["browserLink"]

        # remove all rows from table
        # FIXME: potentially necessary to add sleep (it return 422 if performed right after doc creation)
        # FIXME: httpx.HTTPStatusError: Client error '422 Unprocessable Entity' for url
        # FIXME: 'https://coda.io/apis/v1/docs/{doc_id}/tables/{table_id}/rows?limit=50'
        await asyncio.sleep(1)
        await self.remove_all_rows(doc_id=doc_id, table_id=TASKS_TABLE_ID)

        # insert new rows
        new_rows_resp = await self.upsert_rows(
            doc_id=doc_id,
            table_id=TASKS_TABLE_ID,
            rows_data={"rows": tasks},
        )
        # resp is
        # {
        #     "requestId": "abc-123-def-456",
        #     "addedRowIds": [
        #         "i-bCdeFgh",
        #         "i-CdEfgHi"
        #     ]
        # }

        # get page link
        page = await self.get_page(doc_id=doc_id, page_id=ESTIMATES_PAGE_ID)
        resp = DocProcessResponse(
            coda_doc_id=doc_id,
            coda_doc_link=doc_link,
            coda_doc_page_id=page["id"],
            coda_doc_page_link=page["browserLink"],
        )
        return resp

    async def create_doc(self, name):
        body = {
            "title": name,
            "sourceDoc": TEMPLATE_ID,
            "folderId": FOLDER_ID,
        }
        resp = await self._client.post(
            url=self._coda_api_base_uri,
            json=body,
            headers=self._headers_w_content_type,
        )
        log.info(f"==create_doc==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()

        return data

    async def get_doc(self, doc_id):
        uri = f"{self._coda_api_base_uri}/{doc_id}"
        resp = await self._client.get(
            url=uri,
            headers=self._base_headers,
        )
        log.info(f"==get_doc==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()

        return data

    async def get_pages(self, doc_id):
        uri = f"{self._coda_api_base_uri}/{doc_id}/pages"
        resp = await self._client.get(
            url=uri,
            headers=self._base_headers,
        )
        log.info(f"==get_pages==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()

        return data

    async def get_page(self, doc_id, page_id):
        uri = f"{self._coda_api_base_uri}/{doc_id}/pages/{page_id}"
        resp = await self._client.get(
            url=uri,
            headers=self._base_headers,
        )
        log.info(f"==get_page==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()

        return data

    async def get_tables(self, doc_id):
        uri = f"{self._coda_api_base_uri}/{doc_id}/tables"
        resp = await self._client.get(
            url=uri,
            headers=self._base_headers,
        )
        log.info(f"==get_tables==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()

        return data

    async def get_columns(self, doc_id, table_id):
        uri = f"{self._coda_api_base_uri}/{doc_id}/tables/{table_id}/columns"
        resp = await self._client.get(
            url=uri,
            headers=self._base_headers,
        )
        log.info(f"==get_columns==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()

        return data

    async def get_rows(self, doc_id, table_id, limit=50):
        uri = f"{self._coda_api_base_uri}/{doc_id}/tables/{table_id}/rows"
        params = {"limit": limit}
        resp = await self._client.get(
            url=uri,
            params=params,
            headers=self._base_headers,
        )
        log.info(f"==get_rows==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()

        return data

    async def upsert_rows(self, doc_id, table_id, rows_data):
        uri = f"{self._coda_api_base_uri}/{doc_id}/tables/{table_id}/rows"
        resp = await self._client.post(
            url=uri,
            json=rows_data,
            headers=self._headers_w_content_type,
        )
        log.info(f"==upsert_rows==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()

        return data

    async def remove_all_rows(self, doc_id, table_id):
        existing_rows = await self.get_rows(doc_id, table_id)
        row_ids = [r["id"] for r in existing_rows["items"]]
        uri = f"{self._coda_api_base_uri}/{doc_id}/tables/{table_id}/rows"
        payload = {"rowIds": row_ids}
        log.info(f"---- Removing existing rows: {payload}")
        resp = await self._client.request(
            method="DELETE",
            url=uri,
            json=payload,
            headers=self._headers_w_content_type,
        )
        log.info(f"==remove_all_rows==:\n{resp}:\n{resp.text}")
        resp.raise_for_status()
        data = resp.json()
        return data
