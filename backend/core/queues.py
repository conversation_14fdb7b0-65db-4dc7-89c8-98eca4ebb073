import sqlalchemy as sa
import asyncio
import json
import uuid
import time
from asyncio.queues import Queue, QueueEmpty
from collections import defaultdict
from typing import Any
from fastapi import Request, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sse_starlette import ServerSentEvent
from partial_json_parser import (
    MalformedJSON,
    loads as partial_json_loads
)

import models as m
from models.conversation import ConversationTypes
from config import log
from core.llm import (
    OVERVIEW_SUMMARY_KEY,
    OVERVIEW_MODIFIED_KEY,
    SCOPES_SUMMARY_KEY,
    SCOPES_MODIFIED_KEY,
    STORIES_KEY,
    post_chat_message_initial,
    post_chat_message_overview,
    post_chat_message_scopes,
    post_overview_generation,
    post_scopes_generation,
    post_estimates_generation,
)
from core.utils import (
    get_dict_diff,
    get_conversation_history,
)
from core.coda_manager import CodaManager, DocProcessResponse
from db import persistent_engine


class ListenerQueues:
    def __init__(self):
        self._listeners = defaultdict(list)

    def add_listener(self, topic: str) -> Queue:
        q = Queue()
        self._listeners[topic].append(q)
        return q

    def remove_listener(self, topic: str, q: Queue) -> None:
        try:
            self._listeners[topic].remove(q)
        except Exception as e:
            # in case somehow we ended in situation when q is absent from listeners to log?
            log.exception("error in remove_listener")
            pass

    async def publish(self, topic: str, event: Any) -> None:
        log.info(f"Queue publishing:\ntopic: {topic}\nevent {str(event)}")
        if not self._listeners[topic]:
            # no listeners for project id are waiting, meaning
            # no sse connections for this topic are open
            # TODO: maybe raise an exception here to indicate that?
            return

        for listener in self._listeners[topic]:
            await listener.put(event)


LISTENER_QUEUES = ListenerQueues()

EVENT_CHECK_DELAY = 1
EVENT_MESSAGE_TYPE = "message"
EVENT_RESPONSE_TYPE = "response"
EVENT_OVERVIEW_TYPE = "overview_update"
EVENT_SCOPES_TYPE = "scopes_update"

TOPIC_CONVERSATION_FORMAT = "{project_id}.{conversation_id}"
TOPIC_OVERVIEW_FORMAT = "{project_id}.overview"
TOPIC_SCOPES_FORMAT = "{project_id}.scopes"
# TODO: perhaps add different event types for sse connections where we push updates for pages?


async def response_sending_loop(
    topic: str,
    request: Request,
    previous_history: list[dict]|None,
):
    log.info(f"Opened sse connection for topic {topic}")
    # create listener
    global LISTENER_QUEUES
    listener_queue = LISTENER_QUEUES.add_listener(topic=topic)

    # when sse (chat) is opened, preload previous history of events
    # (messages for chat, one overview for overview value).
    if previous_history:
        for previous_event in previous_history:
            if await request.is_disconnected():
                # remove listener
                LISTENER_QUEUES.remove_listener(topic=topic, q=listener_queue)
                del listener_queue
                return

            log.info(
                f"SSE LOOP, sending++++++++++++:"
                f"\ntopic: {topic}"
                f"\nevent: {str(previous_event)}"
            )
            event = ServerSentEvent(**previous_event)
            yield event
            log.info(
                f"SSE LOOP, sent!==============:"
                f"\ntopic: {topic}"
                f"\nevent: {str(previous_event)}"
            )

    # get items from queue
    while True:
        if await request.is_disconnected():
            # remove listener
            LISTENER_QUEUES.remove_listener(topic=topic, q=listener_queue)
            del listener_queue
            return

        try:
            queued_event = listener_queue.get_nowait()
        except QueueEmpty:
            await asyncio.sleep(EVENT_CHECK_DELAY)
            continue

        log.info(
            f"SSE LOOP, sending++++++++++++:"
            f"\ntopic: {topic}"
            f"\nevent: {str(queued_event)}"
        )
        event = ServerSentEvent(**queued_event)
        yield event
        listener_queue.task_done()
        log.info(
            f"SSE LOOP, sent===============:"
            f"\ntopic: {topic}"
            f"\nevent: {str(queued_event)}"
        )


async def _handle_message_initial(
    message_id: int,
    project_id: str,
    conversation_id: str,
):
    global LISTENER_QUEUES

    async with AsyncSession(persistent_engine) as session:
        previous_history = await get_conversation_history(
            session=session,
            project_id=project_id,
            conversation_id=conversation_id,
        )

    conv_topic = TOPIC_CONVERSATION_FORMAT.format(
        project_id=project_id,
        conversation_id=conversation_id,
    )

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.INITIAL.value});"
        f"\nGot previous messages from db"
    )

    total_response = ""
    ready_flag = False
    async for response_chunk in post_chat_message_initial(previous_history):
        total_response += response_chunk
        if ready_flag:
            # do not send chunk and only collect to total_response
            continue

        if total_response.startswith("}"):
            # check if ai thinks that currently there is enough info and
            # project overview generation can be started

            # this is trigger case, which returns GENERATION_TRIGGER_MESSAGE,
            # which is '}require-auth{', so it starts with "{"
            # in such case, collect this message and send it whole.
            # Also do not save it as a response to db.
            ready_flag = True
            continue

        # TODO: add message_id to event to some key, so that we know which massage
        # TODO: chunks to collect together?
        response_event = {
            "data": response_chunk,
            "event": EVENT_RESPONSE_TYPE,
            "id": str(message_id) + "A",
        }
        await LISTENER_QUEUES.publish(topic=conv_topic, event=response_event)

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.INITIAL.value});"
        f"\nPosted message to openai and fully streamed response"
    )

    # update message's response in db after openai response is fully streamed?
    # so do not update db record for each chunk, to not bother db

    if ready_flag:
        # assert total_response == GENERATION_TRIGGER_MESSAGE
        # TODO: send different event type for this response?
        ready_event = {
            "data": json.dumps({"type": "require-auth"}),
            "event": EVENT_RESPONSE_TYPE,
            "id": str(message_id) + "A",
        }
        await LISTENER_QUEUES.publish(topic=conv_topic, event=ready_event)
        # also do not save it as a response to db
        return

    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.update(m.ConversationMessage)
            .where(m.ConversationMessage.id == message_id)
            .values(response=total_response)
        )
        await session.execute(q)
        await session.commit()

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.INITIAL.value});"
        f"\nSaved openai response to db"
    )


async def _handle_message_overview(
    message_id: int,
    message: str,
    project_id: str,
    conversation_id: str,
):
    global LISTENER_QUEUES

    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.select(m.Conversation.id)
            .where(
                sa.and_(
                    m.Conversation.project_id == project_id,
                    m.Conversation.conv_type == ConversationTypes.INITIAL.value,
                )
            )
        )
        initial_conv_id = (await session.execute(q)).scalar()
        previous_history_initial = await get_conversation_history(
            session=session,
            project_id=project_id,
            conversation_id=initial_conv_id,
        )
        previous_history_overview = await get_conversation_history(
            session=session,
            project_id=project_id,
            conversation_id=conversation_id,
            exclude=message_id,
            is_chat=False,
        )
        q = (
            sa.select(m.Project.project_overview)
            .where(m.Project.id == project_id)
        )
        overview = (await session.execute(q)).scalar()

    conv_topic = TOPIC_CONVERSATION_FORMAT.format(
        project_id=project_id,
        conversation_id=conversation_id,
    )
    overview_topic = TOPIC_OVERVIEW_FORMAT.format(project_id=project_id)

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.OVERVIEW.value});"
        f"\nGot previous messages from db"
    )

    kwargs = dict(
        previous_history_initial=previous_history_initial,
        previous_history_overview=previous_history_overview,
        modification_request=message,
        overview=overview,
    )

    # Here we are expecting openai to return json with fields:
    # OVERVIEW_SUMMARY_KEY and OVERVIEW_MODIFIED_KEY.
    # OVERVIEW_SUMMARY_KEY is to be streamed to chat sse,
    # while OVERVIEW_MODIFIED_KEY - to overview sse.
    topic_map = {
        OVERVIEW_SUMMARY_KEY: conv_topic,
        OVERVIEW_MODIFIED_KEY: overview_topic,
    }
    event_type_map = {
        OVERVIEW_SUMMARY_KEY: EVENT_RESPONSE_TYPE,
        OVERVIEW_MODIFIED_KEY: EVENT_OVERVIEW_TYPE,
    }

    value_map = {
        OVERVIEW_SUMMARY_KEY: "",
        OVERVIEW_MODIFIED_KEY: "",
    }

    parsed_prev_state = {}
    total_response = ""

    async for response_chunk in post_chat_message_overview(**kwargs):
        total_response += response_chunk

        # attempt to parse returned json
        try:
            parsed = partial_json_loads(total_response)
        except MalformedJSON:
            parsed = {}

        if not parsed:
            # could not parse yet, continue to receive chunks
            continue

        # could parse at least one key and a bit of value
        diff = get_dict_diff(parsed, parsed_prev_state)
        parsed_prev_state = parsed

        for key, value_diff in diff:
            value_map[key] += value_diff
            response_event = {
                "data": value_diff,
                "event": event_type_map[key],
                # FIXME: keep id for EVENT_OVERVIEW_TYPE ?
                "id": str(message_id) + "A",
            }
            await LISTENER_QUEUES.publish(topic=topic_map[key], event=response_event)

    # In the end, parsed_prev_state dict should be equal to value_map dict
    assert parsed_prev_state == value_map

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.OVERVIEW.value});"
        f"\nPosted message to openai and fully streamed responses to chat and overview"
    )

    async with AsyncSession(persistent_engine) as session:
        # save summary to message response
        # and modified overview to project_overview
        q = (
            sa.update(m.ConversationMessage)
            .where(m.ConversationMessage.id == message_id)
            .values(response=value_map[OVERVIEW_SUMMARY_KEY])
        )
        await session.execute(q)
        q = (
            sa.update(m.Project)
            .where(m.Project.id == project_id)
            .values(project_overview=value_map[OVERVIEW_MODIFIED_KEY])
        )
        await session.execute(q)
        await session.commit()

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.OVERVIEW.value});"
        f"\nSaved openai response to db"
    )


async def _handle_message_scopes(
    message_id: int,
    message: str,
    project_id: str,
    conversation_id: str,
):
    global LISTENER_QUEUES

    async with AsyncSession(persistent_engine) as session:
        # q = (
        #     sa.select(m.Conversation.id)
        #     .where(
        #         sa.and_(
        #             m.Conversation.project_id == project_id,
        #             m.Conversation.conv_type == ConversationTypes.INITIAL.value,
        #         )
        #     )
        # )
        # initial_conv_id = (await session.execute(q)).scalar()
        # previous_history_initial = await get_conversation_history(
        #     session=session,
        #     project_id=project_id,
        #     conversation_id=initial_conv_id,
        # )
        previous_history_scopes = await get_conversation_history(
            session=session,
            project_id=project_id,
            conversation_id=conversation_id,
            exclude=message_id,
            is_chat=False,
        )
        q = (
            sa.select(m.Project.features_and_scopes)
            .where(m.Project.id == project_id)
        )
        scopes = (await session.execute(q)).scalar()

    conv_topic = TOPIC_CONVERSATION_FORMAT.format(
        project_id=project_id,
        conversation_id=conversation_id,
    )
    scopes_topic = TOPIC_SCOPES_FORMAT.format(project_id=project_id)

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.SCOPES.value});"
        f"\nGot previous messages from db"
    )

    kwargs = dict(
        # previous_history_initial=previous_history_initial,
        previous_history_scopes=previous_history_scopes,
        modification_request=message,
        scopes=scopes,
    )

    # no streaming for now
    total_response = ""
    async for response_chunk in post_chat_message_scopes(**kwargs):
        total_response += response_chunk

    # should contain keys: modified_scopes and modification_summary
    total_response = json.loads(total_response)
    new_scopes = json.dumps(total_response[SCOPES_MODIFIED_KEY])
    summary = total_response[SCOPES_SUMMARY_KEY]

    scopes_event = {
        "data": new_scopes,
        "event": EVENT_SCOPES_TYPE,
        # "id": str(message_id) + "A",
    }
    await LISTENER_QUEUES.publish(topic=scopes_topic, event=scopes_event)

    summary_event = {
        "data": summary,
        "event": EVENT_RESPONSE_TYPE,
        # "id": str(message_id) + "A",
    }
    await LISTENER_QUEUES.publish(topic=conv_topic, event=summary_event)

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.SCOPES.value});"
        f"\nPosted message to openai and fully streamed responses to chat and overview"
    )

    async with AsyncSession(persistent_engine) as session:
        # save summary to message response
        # and modified overview to project_overview
        q = (
            sa.update(m.ConversationMessage)
            .where(m.ConversationMessage.id == message_id)
            .values(response=summary)
        )
        await session.execute(q)
        q = (
            sa.update(m.Project)
            .where(m.Project.id == project_id)
            .values(features_and_scopes=new_scopes)
        )
        await session.execute(q)
        await session.commit()

    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id}"
        f" (type - {ConversationTypes.SCOPES.value});"
        f"\nSaved openai response to db"
    )


async def post_message_and_stream_response(
    message_id: int,
    message: str,
    project_id: str,
    conversation_id: str,
):
    log.info(
        f"Task for publishing posted message {message_id} to"
        f" conversation {conversation_id} for project {project_id};"
        f"\nStarting..."
    )

    global LISTENER_QUEUES

    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.select(m.Conversation.conv_type)
            .where(m.Conversation.id == conversation_id)
        )
        conversation_type = (await session.execute(q)).scalar()

    if conversation_type == ConversationTypes.INITIAL.value:
        await _handle_message_initial(
            message_id=message_id,
            project_id=project_id,
            conversation_id=conversation_id,
        )
    elif conversation_type == ConversationTypes.OVERVIEW.value:
        await _handle_message_overview(
            message_id=message_id,
            message=message,
            project_id=project_id,
            conversation_id=conversation_id,
        )
    elif conversation_type == ConversationTypes.SCOPES.value:
        await _handle_message_scopes(
            message_id=message_id,
            message=message,
            project_id=project_id,
            conversation_id=conversation_id,
        )


async def generate_overview_and_stream_response(
    project_id: str,
    topic: str,
    stream: bool = False,
):
    global LISTENER_QUEUES

    async with AsyncSession(persistent_engine) as session:
        # get initial message history to generate from
        q = (
            sa.select(m.Conversation.id)
            .select_from(m.Conversation)
            .where(
                sa.and_(
                    m.Conversation.project_id == project_id,
                    m.Conversation.conv_type == ConversationTypes.INITIAL,
                )
            )
        )
        conversation_id = (await session.execute(q)).scalar_one()
        previous_history = await get_conversation_history(
            session=session,
            project_id=project_id,
            conversation_id=conversation_id,
        )

    project_overview = ""
    async for response_chunk in post_overview_generation(previous_history):
        project_overview += response_chunk

        if stream:
            response_event = {
                "data": response_chunk,
                "event": EVENT_OVERVIEW_TYPE,
            }
            await LISTENER_QUEUES.publish(topic=topic, event=response_event)

    if not stream:
        response_event = {
            "data": project_overview,
            "event": EVENT_OVERVIEW_TYPE,
        }
        await LISTENER_QUEUES.publish(topic=topic, event=response_event)

    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.update(m.Project)
            .where(m.Project.id == project_id)
            .values(project_overview=project_overview)
        )
        await session.execute(q)
        await session.commit()


async def generate_scopes_and_stream_response(
    project_id: str,
    topic: str,
    stream: bool = False,
):
    # FIXME: since response should be a json, is there any point in streaming it
    # FIXME: to front?
    global LISTENER_QUEUES

    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.select(m.Project.project_overview)
            .select_from(m.Project)
            .where(
                m.Project.id == project_id,
            )
        )
        overview = (await session.execute(q)).scalar_one()

    scopes = ""
    ts = str(time.time())
    async for response_chunk in post_scopes_generation(overview):
        scopes += response_chunk

        if stream:
            response_event = {
                "data": response_chunk,
                "event": EVENT_SCOPES_TYPE,
                "id": ts,
            }
            await LISTENER_QUEUES.publish(topic=topic, event=response_event)

    if not stream:
        response_event = {
            "data": scopes,
            "event": EVENT_SCOPES_TYPE,
        }
        await LISTENER_QUEUES.publish(topic=topic, event=response_event)

    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.update(m.Project)
            .where(m.Project.id == project_id)
            .values(features_and_scopes=scopes)
        )
        await session.execute(q)
        await session.commit()


async def update_project_and_send_queues(
    project_id: str,
    project_upd_request,
    session: AsyncSession,
):
    global LISTENER_QUEUES
    overview_topic = TOPIC_OVERVIEW_FORMAT.format(project_id=project_id)
    scopes_topic = TOPIC_SCOPES_FORMAT.format(project_id=project_id)

    values = dict()
    overview_event = None
    scopes_event = None
    if project_upd_request.user_id:
        # update project with user id, but only if project did not have user id set already
        q = sa.select(m.Project.user_id).where(m.Project.id == project_id)
        existing_user_id = (await session.execute(q)).scalar_one_or_none()
        if existing_user_id:
            raise HTTPException(
                status_code=400,
                detail="User is already set on this project"
            )

        values[m.Project.user_id.key] = project_upd_request.user_id

    if project_upd_request.project_overview:
        values[
            m.Project.project_overview.key] = project_upd_request.project_overview
        # post to LISTENER_QUEUES
        overview_event = {
            "data": project_upd_request.project_overview,
            "event": EVENT_OVERVIEW_TYPE,
        }

    if project_upd_request.features_and_scopes:
        # TODO: are we expecting json as string, or parsed json into dict?
        # TODO: do we use text field for db or hstore (for dict)?
        values[
            m.Project.features_and_scopes] = project_upd_request.features_and_scopes
        # post to LISTENER_QUEUES
        scopes_event = {
            "data": project_upd_request.features_and_scopes,
            "event": EVENT_SCOPES_TYPE,
        }

    if values:
        q = (
            sa.update(m.Project)
            .where(m.Project.id == project_id)
            .values(**values)
        )
        await session.execute(q)
        await session.commit()

    if overview_event:
        await LISTENER_QUEUES.publish(
            topic=overview_topic,
            event=overview_event
        )

    if scopes_event:
        await LISTENER_QUEUES.publish(topic=scopes_topic, event=scopes_event)


async def process_message_and_send_queues(
    project_id: str,
    conversation_id: str,
    message: str,
):
    log.info(f"Posted message in conversation {conversation_id} for project {project_id}")

    global LISTENER_QUEUES

    # create message in database and update it with response after open ai returns full response
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.insert(m.ConversationMessage)
            .values(
                message=message,
                project_id=project_id,
                conversation_id=conversation_id,
            )
            .returning(m.ConversationMessage.id)
        )
        message_id = (await session.execute(q)).scalar()
        await session.commit()

    log.info(
        f"Posted message in conversation {conversation_id} for project {project_id};"
        f"\nCommitted message to db, message_id: {message_id}"
    )
    topic = TOPIC_CONVERSATION_FORMAT.format(
        project_id=project_id,
        conversation_id=conversation_id,
    )
    message_event = {
        "data": message,
        "event": EVENT_MESSAGE_TYPE,
        "id": str(message_id) + "Q",
    }
    await LISTENER_QUEUES.publish(topic=topic, event=message_event)

    # schedule task to work at the background, while we immediately return http response
    task_coro = post_message_and_stream_response(
        message_id=message_id,
        message=message,
        project_id=project_id,
        conversation_id=conversation_id,
        # topic=topic,
    )
    task = asyncio.create_task(task_coro)


async def generate_estimates(
    project_id: str,
    # stream: bool = False,
) -> DocProcessResponse:
    log.info(f"Generating estimates for project {project_id}: starting...")
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.select(
                m.Project.features_and_scopes,
                m.Project.name,
            )
            .select_from(m.Project)
            .where(
                m.Project.id == project_id,
            )
        )
        proj = (await session.execute(q)).one()
        scopes = proj.features_and_scopes
        name = proj.name

    log.info(f"Generating estimates for project {project_id}: got scopes from db.")
    estimates = ""
    async for response_chunk in post_estimates_generation(scopes):
        estimates += response_chunk

    log.info(f"Generating estimates for project {project_id}: got full response from openai.")
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.update(m.Project)
            .where(m.Project.id == project_id)
            .values(project_estimate=estimates)
        )
        await session.execute(q)
        await session.commit()

    # async with AsyncSession(persistent_engine) as session:
    #     q = (
    #         sa.select(
    #             m.Project.project_estimate,
    #             m.Project.name,
    #         )
    #         .select_from(m.Project)
    #         .where(
    #             m.Project.id == project_id,
    #         )
    #     )
    #     proj = (await session.execute(q)).one()
    #     estimates = proj.project_estimate
    #     name = proj.name

    log.info(f"Generating estimates for project {project_id}: saved openai response to db.")
    parsed_estimates = json.loads(estimates)

    async with CodaManager() as manager:
        result = await manager.gen_doc_with_estimates(
            name=name,
            estimates_data=parsed_estimates[STORIES_KEY],
        )

    log.info(f"Generating estimates for project {project_id}: generated doc in coda.")
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.update(m.Project)
            .where(m.Project.id == project_id)
            .values(
                coda_doc_id=result.coda_doc_id,
                coda_doc_link=result.coda_doc_link,
                coda_doc_page_id=result.coda_doc_page_id,
                coda_doc_page_link=result.coda_doc_page_link,
            )
        )
        await session.execute(q)
        await session.commit()

    log.info(f"Generating estimates for project {project_id}: saved coda links to db.")
    return result
