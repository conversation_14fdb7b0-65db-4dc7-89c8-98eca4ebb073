import sqlalchemy as sa
from sqlalchemy.ext.asyncio import AsyncSession

import models as m
from models.conversation import ConversationTypes
from config import log
from core.llm import (
    USER_REQUEST_TYPE,
    AI_RESPONSE_TYPE,
    USER_MODIFICATION_REQUEST_TYPE,
    AI_MODIFICATION_RESPONSE_TYPE,
)


def get_dict_diff(dict_new: dict, dict_old: dict) -> list[tuple]:
    # diff = {}
    diff = []
    for key, new_value in dict_new.items():
        old_value = dict_old.get(key) or ""
        if old_value:
            assert new_value.startswith(old_value)

        value_diff = new_value[len(old_value):]
        if value_diff:
            # diff[key] = value_diff
            diff.append((key, value_diff))

    return diff


async def get_conversation_history(
    session: AsyncSession,
    project_id: str,
    conversation_id: str,
    is_chat: bool = True,
    exclude: int|None = None,
) -> list[dict]:
    if exclude:
        cond = sa.and_(
            m.ConversationMessage.project_id == project_id,
            m.ConversationMessage.conversation_id == conversation_id,
            m.ConversationMessage.id != exclude,
        )
    else:
        cond = sa.and_(
            m.ConversationMessage.project_id == project_id,
            m.ConversationMessage.conversation_id == conversation_id,
        )

    q = (
        sa.select(
            m.ConversationMessage.message,
            m.ConversationMessage.response,
        )
        .select_from(m.ConversationMessage)
        .where(cond)
        .order_by(sa.asc(m.ConversationMessage.record_created))
    )
    previous_messages = (await session.execute(q)).fetchall()

    if is_chat:
        req_type = USER_REQUEST_TYPE
        resp_type = AI_RESPONSE_TYPE
    else:
        # modification case
        req_type = USER_MODIFICATION_REQUEST_TYPE
        resp_type = AI_MODIFICATION_RESPONSE_TYPE

    previous_history = []
    for prev_msg in previous_messages:
        # check core.prompts.INITIAL_CHAT_PROMPT_TEMPLATE for example of previous_history input
        previous_history.append(
            {
                "message": prev_msg.message,
                "type": req_type,
            }
        )
        if prev_msg.response:
            previous_history.append(
                {
                    "message": prev_msg.response,
                    "type": resp_type,
                }
            )

    return previous_history
