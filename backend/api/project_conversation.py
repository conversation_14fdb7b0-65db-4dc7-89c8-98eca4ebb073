import sqlalchemy as sa
import asyncio

from itertools import groupby
from fastapi import APIRouter, status, Request
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import JSONResponse
from sse_starlette import EventSourceResponse

import models as m
from models.conversation import ConversationTypes
from config import log

from core.queues import (
    EVENT_MESSAGE_TYPE,
    EVENT_OVERVIEW_TYPE,
    EVENT_SCOPES_TYPE,
    EVENT_RESPONSE_TYPE,
    TOPIC_CONVERSATION_FORMAT,
    TOPIC_OVERVIEW_FORMAT,
    TOPIC_SCOPES_FORMAT,
    update_project_and_send_queues,
    process_message_and_send_queues,
    generate_overview_and_stream_response,
    generate_scopes_and_stream_response,
    response_sending_loop,
    generate_estimates,
)
from db import get_session_dep, persistent_engine

# ###############################


project_conversation_router = APIRouter(prefix="/project")


class ConversationResponse(BaseModel):
    id: str
    type: ConversationTypes


class ProjectResponse(BaseModel):
    project_id: str
    coda_doc_id: str|None = None
    coda_doc_link: str|None = None
    coda_doc_page_id: str|None = None
    coda_doc_page_link: str|None = None
    conversations: list[ConversationResponse]


class EstimatesResponse(BaseModel):
    project_id: str
    coda_doc_id: str
    coda_doc_link: str
    coda_doc_page_id: str
    coda_doc_page_link: str


class ProjectRequest(BaseModel):
    user_id: str|None = None


@project_conversation_router.post(
    "/",
    status_code=status.HTTP_200_OK,
    response_model=ProjectResponse,
    operation_id="create_project",
)
async def create_project(
    project_request: ProjectRequest | None = None,
    session: AsyncSession = get_session_dep
):
    # create project and conversation of conv_type ConversationTypes.INITIAL
    # in database here and return it's id
    q = sa.insert(m.Project).returning(m.Project.id)
    if project_request and project_request.user_id:
        q = q.values(user_id=project_request.user_id)

    project_id = str((await session.execute(q)).scalar())
    q = (
        sa.insert(m.Conversation)
        .values(project_id=project_id, conv_type=ConversationTypes.INITIAL.value)
        .returning(m.Conversation.id)
    )
    conversation_id = str((await session.execute(q)).scalar())
    resp = ProjectResponse(
        project_id=project_id,
        conversations=[
            ConversationResponse(
                id=conversation_id,
                type=ConversationTypes.INITIAL
            ),
        ],
    )
    return resp


@project_conversation_router.get(
    "/{project_id}",
    status_code=status.HTTP_200_OK,
    response_model=ProjectResponse,
    operation_id="get_project",
)
async def get_project(project_id: str, session: AsyncSession = get_session_dep):
    # get conversation id for project (of conv_type ConversationTypes.INITIAL)
    q = (
        sa.select(m.Conversation.id, m.Conversation.conv_type)
        .where(
            m.Conversation.project_id == project_id,
        )
    )
    conversations = (await session.execute(q)).fetchall()
    q = (
        sa.select(
            m.Project.coda_doc_id,
            m.Project.coda_doc_link,
            m.Project.coda_doc_page_id,
            m.Project.coda_doc_page_link,
        )
        .where(
            m.Project.id == project_id,
        )
    )
    proj_data = (await session.execute(q)).one()
    resp = ProjectResponse(
        project_id=project_id,
        coda_doc_id=proj_data.coda_doc_id,
        coda_doc_link=proj_data.coda_doc_link,
        coda_doc_page_id=proj_data.coda_doc_page_id,
        coda_doc_page_link=proj_data.coda_doc_page_link,
        conversations=[
            ConversationResponse(
                id=str(r.id),
                type=ConversationTypes(r.conv_type),
            )
            for r in conversations
        ],
    )
    return resp


# TOOD: add api for manual update of project overview


class ProjectUpdateRequest(BaseModel):
    user_id: str|None = None
    project_overview: str|None = None
    features_and_scopes: str|None = None


@project_conversation_router.patch(
    "/{project_id}",
    status_code=status.HTTP_200_OK,
    operation_id="update_project_with_user_id",
)
async def update_project(
    project_id: str,
    project_upd_request: ProjectUpdateRequest,
    session: AsyncSession = get_session_dep
):
    await update_project_and_send_queues(
        project_id=project_id,
        project_upd_request=project_upd_request,
        session=session,
    )
    return JSONResponse(status_code=status.HTTP_200_OK, content={"status": "ok"})


class ManyProjectsResponse(BaseModel):
    projects: list[ProjectResponse]


@project_conversation_router.get(
    "/",
    status_code=status.HTTP_200_OK,
    response_model=ManyProjectsResponse,
    operation_id="get_many_projects_for_user",
)
async def get_many_projects(
    user_id: str,
    request: Request,
    session: AsyncSession = get_session_dep,
):
    # TODO: get user_id from request after authentication.
    # TODO: although it will require different logic after we implement invites

    # get conversation ids and project ids (of conv_type ConversationTypes.INITIAL)
    q = (
        sa.select(
            m.Project.id,
            m.Project.coda_doc_id,
            m.Project.coda_doc_link,
            m.Project.coda_doc_page_id,
            m.Project.coda_doc_page_link,
            m.Conversation.id.label("conv_id"),
            m.Conversation.conv_type,
        )
        .select_from(
            sa.join(
                m.Project,
                m.Conversation,
                m.Conversation.project_id == m.Project.id,
            )
        )
        .where(
            m.Project.user_id == user_id
        )
        .order_by(
            sa.asc(m.Project.id),
            sa.asc(m.Conversation.id),
        )
    )
    result = (await session.execute(q)).fetchall()
    resp = []
    for proj_id, group_ in groupby(result, lambda r: r.id):
        proj_convs = []
        for conv in group_:
            proj_convs.append(
                ConversationResponse(
                    id=str(conv.conv_id),
                    type=ConversationTypes(conv.conv_type),
                )
            )
        resp.append(
            ProjectResponse(
                project_id=str(proj_id),
                coda_doc_id=conv.coda_doc_id,
                coda_doc_link=conv.coda_doc_link,
                coda_doc_page_id=conv.coda_doc_page_id,
                coda_doc_page_link=conv.coda_doc_page_link,
                conversatons=proj_convs,
            )
        )

    return ManyProjectsResponse(projects=resp)


@project_conversation_router.post(
    "/{project_id}/conversation/{conversation_id}",
    operation_id="open_conversation_sse",
)
async def open_conversation_sse(
    project_id: str,
    conversation_id: str,
    request: Request,
    # session: AsyncSession = get_session_dep,
):
    log.info(f"Opening conversation {conversation_id} for project {project_id}")
    # get previous messages for project_id to preload them into chat
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.select(m.Conversation.conv_type)
            .where(m.Conversation.id == conversation_id)
        )
        conv_type = (await session.execute(q)).scalar()
        if conv_type == ConversationTypes.OVERVIEW:
            # also get history for initial conversation
            q = (
                sa.select(m.Conversation.id)
                .where(
                    sa.and_(
                        m.Conversation.project_id == project_id,
                        m.Conversation.conv_type == ConversationTypes.INITIAL.value,
                    )
                )
            )
            initial_conv_id = (await session.execute(q)).scalar()
            q = (
                sa.select(
                    m.ConversationMessage.id,
                    m.ConversationMessage.message,
                    m.ConversationMessage.response,
                )
                .select_from(m.ConversationMessage)
                .where(
                    sa.and_(
                        m.ConversationMessage.project_id == project_id,
                        m.ConversationMessage.conversation_id.in_(
                            [conversation_id, initial_conv_id]
                        ),
                    )
                )
                .order_by(sa.asc(m.ConversationMessage.record_created))
            )
        else:
            q = (
                sa.select(
                    m.ConversationMessage.id,
                    m.ConversationMessage.message,
                    m.ConversationMessage.response,
                )
                .select_from(m.ConversationMessage)
                .where(
                    sa.and_(
                        m.ConversationMessage.project_id == project_id,
                        m.ConversationMessage.conversation_id == conversation_id,
                    )
                )
                .order_by(sa.asc(m.ConversationMessage.record_created))
            )

        previous_messages = (await session.execute(q)).fetchall()

    log.info(
        f"Opening conversation {conversation_id} for project {project_id};"
        f"\nGot previous_messages from database."
    )
    previous_history = []
    for prev_msg in previous_messages:
        previous_history.append(
            {
                "data": prev_msg.message,
                "event": EVENT_MESSAGE_TYPE,
                "id": str(prev_msg.id) + "Q",
            }
        )
        if prev_msg.response:
            previous_history.append(
                {
                    "data": prev_msg.response,
                    "event": EVENT_RESPONSE_TYPE,
                    "id": str(prev_msg.id) + "A",
                }
            )

    topic = TOPIC_CONVERSATION_FORMAT.format(
        project_id=project_id,
        conversation_id=conversation_id,
    )
    return EventSourceResponse(
        response_sending_loop(
            topic=topic,
            request=request,
            previous_history=previous_history,
        )
    )


class MessageRequest(BaseModel):
    message: str


@project_conversation_router.post(
    "/{project_id}/conversation/{conversation_id}/message",
    status_code=status.HTTP_200_OK,
    operation_id="post_message_to_conversation",
)
async def post_message(
    project_id: str,
    conversation_id: str,
    message_request: MessageRequest,
    # session: AsyncSession = get_session_dep,
):
    await process_message_and_send_queues(
        project_id=project_id,
        conversation_id=conversation_id,
        message=message_request.message,
    )
    return JSONResponse(status_code=status.HTTP_200_OK, content={"status": "ok"})


@project_conversation_router.post(
    "/{project_id}/overview_sse",
    operation_id="open_project_overview_sse",
)
async def open_project_overview_sse(
    project_id: str,
    request: Request,
    # session: AsyncSession = get_session_dep,
):
    # get project overview for project_id to preload it
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.select(m.Project.project_overview)
            .select_from(m.Project)
            .where(
                m.Project.id == project_id
            )
        )
        proj_overview = (await session.execute(q)).scalar()

    previous_history = []
    if proj_overview:
        previous_history = [
            {
                "data": proj_overview,
                "event": EVENT_OVERVIEW_TYPE,
            },
        ]

    topic = TOPIC_OVERVIEW_FORMAT.format(project_id=project_id)
    return EventSourceResponse(
        response_sending_loop(
            topic=topic,
            request=request,
            previous_history=previous_history,
        )
    )


@project_conversation_router.post(
    "/{project_id}/generate_project_overview",
    status_code=status.HTTP_200_OK,
    response_model=ProjectResponse,
    operation_id="generate_project_overview",
)
async def generate_project_overview(project_id: str):
    topic = TOPIC_OVERVIEW_FORMAT.format(project_id=project_id)

    # TODO: resolve cases when this can called multiple time?
    # TODO: recreate conversation with wiping history, or do create_or_update to keep history?
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.insert(m.Conversation)
            .values(
                project_id=project_id,
                conv_type=ConversationTypes.OVERVIEW.value,
            )
            .returning(m.Conversation.id)
        )
        conversation_id = str((await session.execute(q)).scalar())
        await session.commit()

    # schedule task to work at the background, while we immediately return http response
    task_coro = generate_overview_and_stream_response(
        project_id=project_id,
        topic=topic,
        stream=True,
    )
    task = asyncio.create_task(task_coro)

    resp = ProjectResponse(
        project_id=project_id,
        conversations=[
            ConversationResponse(
                id=conversation_id,
                type=ConversationTypes.OVERVIEW
            ),
        ],
    )
    return resp


@project_conversation_router.post(
    "/{project_id}/scopes_sse",
    operation_id="open_project_scopes_sse",
)
async def open_project_scopes_sse(
    project_id: str,
    request: Request,
    # session: AsyncSession = get_session_dep,
):
    # get project overview for project_id to preload it
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.select(m.Project.features_and_scopes)
            .select_from(m.Project)
            .where(
                m.Project.id == project_id
            )
        )
        proj_scopes = (await session.execute(q)).scalar()

    previous_history = []
    if proj_scopes:
        previous_history = [
            {
                "data": proj_scopes,
                "event": EVENT_SCOPES_TYPE,
            },
        ]

    topic = TOPIC_SCOPES_FORMAT.format(project_id=project_id)
    return EventSourceResponse(
        response_sending_loop(
            topic=topic,
            request=request,
            previous_history=previous_history,
        )
    )


@project_conversation_router.post(
    "/{project_id}/generate_project_scopes",
    status_code=status.HTTP_200_OK,
    response_model=ProjectResponse,
    operation_id="generate_project_scopes",
)
async def generate_project_scopes(project_id: str):
    topic = TOPIC_SCOPES_FORMAT.format(project_id=project_id)

    # TODO: resolve cases when this can called multiple time?
    # TODO: recreate conversation with wiping history, or do create_or_update to keep history?
    async with AsyncSession(persistent_engine) as session:
        q = (
            sa.insert(m.Conversation)
            .values(
                project_id=project_id,
                conv_type=ConversationTypes.SCOPES.value,
            )
            .returning(m.Conversation.id)
        )
        conversation_id = str((await session.execute(q)).scalar())
        await session.commit()

    # schedule task to work at the background, while we immediately return http response
    task_coro = generate_scopes_and_stream_response(
        project_id=project_id,
        topic=topic,
        stream=False,
    )
    task = asyncio.create_task(task_coro)

    resp = ProjectResponse(
        project_id=project_id,
        conversations=[
            ConversationResponse(
                id=conversation_id,
                type=ConversationTypes.SCOPES
            ),
        ],
    )
    return resp


@project_conversation_router.post(
    "/{project_id}/generate_project_estimates",
    status_code=status.HTTP_200_OK,
    response_model=EstimatesResponse,
    operation_id="generate_project_estimates",
)
async def generate_project_estimates(project_id: str):
    result = await generate_estimates(project_id=project_id)

    resp = EstimatesResponse(
        project_id=project_id,
        coda_doc_id=result.coda_doc_id,
        coda_doc_link=result.coda_doc_link,
        coda_doc_page_id=result.coda_doc_page_id,
        coda_doc_page_link=result.coda_doc_page_link,
    )
    return resp
