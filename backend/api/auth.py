import sqlalchemy as sa
from fastapi import APIRouter, status, Response
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import JSONResponse, RedirectResponse

import models as m
from config import log, settings
from db import get_session_dep
from utils.sb_auth import (
    sb_initiate_sign_in,
    sb_sign_in_with_otp,
    sb_refresh_session,
    SignInResponse,
    RefreshResponse,
)


auth_router = APIRouter(prefix="/auth")


class SignInInitRequest(BaseModel):
    email: str
    subscribe: bool


class SignInRequest(BaseModel):
    email: str
    otp: str


class SignInApiResponse(BaseModel):
    user_id: str


class RefreshTokenRequest(BaseModel):
    refresh_token: str


@auth_router.post("/initiate-sign-in", operation_id="initiate_sign_in")
async def initiate_sign_in(
    sign_in_init_request: SignInInitRequest,
    session: AsyncSession = get_session_dep,
):
    await sb_initiate_sign_in(sign_in_init_request.email)
    # TODO: add create or update on UserProfile model with subscription flag
    # TODO: subscription flag logic: if user has it "on" and sent "off" - keep "on"
    # TODO: move this to sign_in_with_otp, since here we do not have user_id yet
    return JSONResponse(status_code=status.HTTP_200_OK, content={"status": "ok"})


@auth_router.post(
    "/sign-in-with-otp",
    status_code=status.HTTP_200_OK,
    response_model=SignInApiResponse,
    operation_id="sign_in_with_otp",
)
async def sign_in_with_otp(sign_in_request: SignInRequest, response: Response):
    result: SignInResponse = await sb_sign_in_with_otp(email=sign_in_request.email, otp=sign_in_request.otp)
    # TODO: set refresh token here too?
    response.set_cookie(key=settings.AUTH_COOKIE_NAME, value=result.access_token)
    response.set_cookie(key=settings.AUTH_REFRESH_COOKIE_NAME, value=result.refresh_token)
    return SignInApiResponse(user_id=result.user_id)


@auth_router.post("/refresh-token", operation_id="refresh_token")
async def refresh_token(refresh_request: RefreshTokenRequest, response: Response):
    result: RefreshResponse = await sb_refresh_session(refresh_request.refresh_token)
    response.set_cookie(key=settings.AUTH_COOKIE_NAME, value=result.access_token)
    response.set_cookie(key=settings.AUTH_REFRESH_COOKIE_NAME, value=result.refresh_token)
    return JSONResponse(status_code=status.HTTP_200_OK, content={"status": "ok"})
