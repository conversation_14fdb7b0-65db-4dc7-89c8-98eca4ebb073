[default]
    OPENAI_API_KEY = ""

    DATABASE_PORT = '54322'
    DATABASE_USER = 'postgres'
    DATABASE_DB = 'postgres'
    DATABASE_PASSWORD = 'postgres'
    DATABASE_HOST = '127.0.0.1'

    LOG_TO_SENTRY = false
    SENTRY_ENVIRONMENT = "development"

    FRONTEND_URL = ""

[development]
    OPENAI_API_KEY = ""
    CODAIO_API_KEY = ""

    DATABASE_PORT = '54322'
    DATABASE_USER = 'postgres'
    DATABASE_DB = 'postgres'
    DATABASE_PASSWORD = 'postgres'
    DATABASE_HOST = '127.0.0.1'

    LOG_TO_SENTRY = false
    SENTRY_ENVIRONMENT = "development"

    FRONTEND_URL = ""

[test]
    OPENAI_API_KEY = ""
    CODAIO_API_KEY = ""

    DATABASE_PORT = '54322'
    DATABASE_USER = 'postgres'
    DATABASE_DB = 'postgres'
    DATABASE_PASSWORD = 'postgres'
    DATABASE_HOST = '127.0.0.1'

    LOG_TO_SENTRY = false
    SENTRY_ENVIRONMENT = "test"

    FRONTEND_URL = ""

[global]
    PROJECT_NAME = "scopezilla"
    SENTRY_URL = ""
    AUTH_COOKIE_NAME = "sz_access_token"
    AUTH_REFRESH_COOKIE_NAME = "sz_refresh_token"
