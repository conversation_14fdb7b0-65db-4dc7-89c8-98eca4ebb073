"""coda_fields

Revision ID: ab2b2aec21df
Revises: 23593a561098
Create Date: 2025-12-04 12:30:15.885960

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ab2b2aec21df'
down_revision: Union[str, Sequence[str], None] = '23593a561098'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('projects', sa.Column('coda_doc_id', sa.Text(), nullable=True))
    op.add_column('projects', sa.Column('coda_doc_link', sa.Text(), nullable=True))
    op.add_column('projects', sa.Column('coda_doc_page_id', sa.Text(), nullable=True))
    op.add_column('projects', sa.Column('coda_doc_page_link', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('projects', 'coda_doc_page_link')
    op.drop_column('projects', 'coda_doc_page_id')
    op.drop_column('projects', 'coda_doc_link')
    op.drop_column('projects', 'coda_doc_id')
    # ### end Alembic commands ###
