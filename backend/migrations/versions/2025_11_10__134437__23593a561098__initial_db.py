"""initial_db

Revision ID: 23593a561098
Revises: 
Create Date: 2025-11-10 13:44:37.210323

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '23593a561098'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('projects',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
    sa.Column('name', sa.Text(), nullable=True),
    sa.Column('project_overview', sa.Text(), nullable=True),
    sa.Column('features_and_scopes', sa.Text(), nullable=True),
    sa.Column('project_estimate', sa.Text(), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('record_created', sa.DateTime(), server_default=sa.text('statement_timestamp()'), nullable=False),
    sa.Column('record_modified', sa.DateTime(), server_default=sa.text('statement_timestamp()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_projects'))
    )
    op.create_index(op.f('ix_projects_record_modified'), 'projects', ['record_modified'], unique=False)
    op.create_index(op.f('ix_projects_user_id'), 'projects', ['user_id'], unique=False)
    op.create_table('user_profiles',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('is_subscribed', sa.Boolean(), server_default=sa.text('FALSE'), nullable=False),
    sa.Column('record_created', sa.DateTime(), server_default=sa.text('statement_timestamp()'), nullable=False),
    sa.Column('record_modified', sa.DateTime(), server_default=sa.text('statement_timestamp()'), nullable=False),
    sa.PrimaryKeyConstraint('user_id', name=op.f('pk_user_profiles'))
    )
    op.create_index(op.f('ix_user_profiles_record_modified'), 'user_profiles', ['record_modified'], unique=False)
    op.create_table('conversations',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
    sa.Column('conv_type', sa.Text(), nullable=False),
    sa.Column('project_id', sa.UUID(), nullable=False),
    sa.Column('record_created', sa.DateTime(), server_default=sa.text('statement_timestamp()'), nullable=False),
    sa.Column('record_modified', sa.DateTime(), server_default=sa.text('statement_timestamp()'), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_conversations_project_id_projects'), onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_conversations')),
    sa.UniqueConstraint('project_id', 'conv_type', name=op.f('uq_conversations_project_id_conv_type'))
    )
    op.create_index(op.f('ix_conversations_project_id'), 'conversations', ['project_id'], unique=False)
    op.create_index(op.f('ix_conversations_record_modified'), 'conversations', ['record_modified'], unique=False)
    op.create_table('conversation_messages',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('response', sa.Text(), nullable=True),
    sa.Column('project_id', sa.UUID(), nullable=False),
    sa.Column('conversation_id', sa.UUID(), nullable=False),
    sa.Column('record_created', sa.DateTime(), server_default=sa.text('statement_timestamp()'), nullable=False),
    sa.Column('record_modified', sa.DateTime(), server_default=sa.text('statement_timestamp()'), nullable=False),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], name=op.f('fk_conversation_messages_conversation_id_conversations'), onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_conversation_messages_project_id_projects'), onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_conversation_messages'))
    )
    op.create_index(op.f('ix_conversation_messages_conversation_id'), 'conversation_messages', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_conversation_messages_project_id'), 'conversation_messages', ['project_id'], unique=False)
    op.create_index(op.f('ix_conversation_messages_record_modified'), 'conversation_messages', ['record_modified'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_conversation_messages_record_modified'), table_name='conversation_messages')
    op.drop_index(op.f('ix_conversation_messages_project_id'), table_name='conversation_messages')
    op.drop_index(op.f('ix_conversation_messages_conversation_id'), table_name='conversation_messages')
    op.drop_table('conversation_messages')
    op.drop_index(op.f('ix_conversations_record_modified'), table_name='conversations')
    op.drop_index(op.f('ix_conversations_project_id'), table_name='conversations')
    op.drop_table('conversations')
    op.drop_index(op.f('ix_user_profiles_record_modified'), table_name='user_profiles')
    op.drop_table('user_profiles')
    op.drop_index(op.f('ix_projects_user_id'), table_name='projects')
    op.drop_index(op.f('ix_projects_record_modified'), table_name='projects')
    op.drop_table('projects')
    # ### end Alembic commands ###
