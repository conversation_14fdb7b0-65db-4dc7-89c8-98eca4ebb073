#!/bin/bash
set -e

echo "=== scopezilla Backend Startup ==="
echo "Environment: $ENV_FOR_DYNACONF"

# Optional fast path for CI or schema-only runs
if [ "${SKIP_DB_BOOTSTRAP}" = "true" ]; then
  echo "SKIP_DB_BOOTSTRAP=true -> skipping DB wait and migrations"
  echo "Starting FastAPI application without DB bootstrap..."
  exec python main.py
fi

# Wait for database to be ready
echo "Waiting for database connection..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if python -c "
import asyncio
import asyncpg
import os
import sys
from config import settings

async def check_db():
    try:
        conn = await asyncpg.connect(
            host=settings.DATABASE_HOST,
            port=settings.DATABASE_PORT,
            user=settings.DATABASE_USER,
            password=settings.DATABASE_PASSWORD,
            database=settings.DATABASE_DB
        )
        await conn.close()
        return True
    except Exception as e:
        print(f'Database connection failed: {e}')
        return False

if not asyncio.run(check_db()):
    sys.exit(1)
"; then
        echo "Database connection successful!"
        break
    else
        echo "Database connection failed. Attempt $attempt/$max_attempts"
        if [ $attempt -eq $max_attempts ]; then
            echo "Failed to connect to database after $max_attempts attempts"
            exit 1
        fi
        sleep 2
        attempt=$((attempt + 1))
    fi
done

# Apply database migrations
echo "Applying database migrations..."
alembic upgrade head

echo "Starting FastAPI application..."
exec python main.py
