[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
dynaconf = "==3.2.12"
langchain = "==1.0.3"
openai = "==2.6.1"
sentry-sdk = "==2.43.0"
certifi = "==2025.10.5"
alembic = "==1.17.1"
sqlalchemy = "==2.0.44"
langchain-community = "==0.4.1"
langchain-openai = "==1.0.1"
fastapi = "==0.120.2"
aiocache = "==0.12.3"
asyncpg = "==0.30.0"
uvicorn = "==0.38.0"
httpx = {extras = ["http2"], version = "==0.28.1"}
sse-starlette = "==3.0.3"
partial-json-parser = "==0.2.1.1.post7"
supabase = "==2.24.0"
codaio = "==0.6.12"

[dev-packages]
black = "*"
pytest-asyncio = "==1.2.0"
pytest = "==8.4.2"

[requires]
python_version = "3.12"
