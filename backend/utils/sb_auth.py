import asyncio
from pydantic import BaseModel

from supabase import Async<PERSON>lient, create_async_client
from supabase_auth.types import UserResponse as SbUserResponse, AuthResponse as SbAuthResponse
from supabase_auth.errors import AuthApiError
from fastapi import HTT<PERSON>Exception
from starlette.middleware.base import BaseHTTPMiddleware

from config import settings, log

_supabase_client: AsyncClient | None = None
_client_lock = asyncio.Lock()


async def get_client() -> AsyncClient:
    global _supabase_client
    async with _client_lock:
        if _supabase_client is not None:
            return _supabase_client

        supabase_client: AsyncClient = await create_async_client(
            settings.SUPABASE_URL, settings.SUPABASE_SECRET_KEY
        )
        _supabase_client = supabase_client
        return supabase_client


class AuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request, call_next):
        # handle CORS preflight requests
        if request.method == "OPTIONS":
            response = await call_next(request)
            return response

        # path = request.scope["path"]
        path = request.url.path

        # do not validate user for stripe webhooks and healthchecks
        # TODO: do we need to add /api prefix here?
        condition = (
            path.startswith("/docs")
            or path.startswith("/redoc")
            or path.startswith("/openapi.json")
            or path.startswith("/healthcheck")
            or path.startswith("/auth")
        )
        if condition:
            response = await call_next(request)
            return response

        # get user from token and save it to request
        supabase_client = await get_client()

        # TODO: fix to use cookies. Save cookie name somewhere
        # TODO: is dict. Can set access token and refresh token?
        auth_token = request.cookies.get(settings.AUTH_COOKIE_NAME) # dict
        if not auth_token:
            raise HTTPException(status_code=400, detail="Invalid auth token")

        # auth_header = request.headers.get("Authorization")
        # if not auth_header:
        #     raise HTTPException(status_code=400, detail="Invalid auth token")
        #
        # type_, auth_token = auth_header.split(" ")
        # if type_ != "Bearer":
        #     raise HTTPException(status_code=400, detail="Invalid auth token")

        # TODO: figure out refresh later!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        try:
            user_info: SbUserResponse | None = await supabase_client.auth.get_user(
                auth_token
            )
        except Exception as e:
            log.exception("Error requesting user from supabase")
            raise HTTPException(status_code=400, detail="Invalid auth token")

        if user_info is None:
            raise HTTPException(status_code=400, detail="Invalid auth token")

        # set user to request, to use it's id for validating access to entities
        # currently validation happens in api resources.
        request.state.sb_user_info = user_info.user
        request.state.sb_user_id = (
            user_info.user.id
        )  # User object has 'id', not 'user_id'
        request.state.sb_user_is_anon = user_info.user.is_anonymous

        response = await call_next(request)
        return response


async def sb_initiate_sign_in(email: str) -> None:
    supabase_client = await get_client()

    try:
        resp = await supabase_client.auth.sign_in_with_otp(
            {"email": email, "options": {"should_create_user": True}}
        )
    except Exception as e:
        log.exception("Error initiating sign in with otp through supabase")
        raise HTTPException(status_code=500, detail="Error initiaing sign in with otp")


class SignInResponse(BaseModel):
    access_token: str
    refresh_token: str
    user_id: str


async def sb_sign_in_with_otp(email: str, otp: str) -> SignInResponse:
    supabase_client = await get_client()

    try:
        resp: SbAuthResponse = await supabase_client.auth.verify_otp(
            {"email": email, "token": otp, "type": "email"}
        )
    except AuthApiError as e:
        log.exception("Invalid OTP login attempt")
        raise HTTPException(status_code=400, detail="Invalid token")
    except Exception as e:
        log.exception("Error verifying otp and signing in through supabase")
        raise HTTPException(status_code=500, detail="Error initiaing sign in with otp")

    result = SignInResponse(
        access_token=resp.session.access_token,
        refresh_token=resp.session.refresh_token,
        user_id=resp.user.id,
    )
    return result


class RefreshResponse(BaseModel):
    access_token: str
    refresh_token: str


async def sb_refresh_session(refresh_token: str) -> RefreshResponse:
    supabase_client = await get_client()

    try:
        resp: SbAuthResponse = await supabase_client.auth.refresh_session(refresh_token)
    except AuthApiError as e:
        log.exception("Invalid token refresh attempt")
        raise HTTPException(status_code=400, detail="Invalid refresh token")
    except Exception as e:
        log.exception("Error verifying otp and signing in through supabase")
        raise HTTPException(status_code=500, detail="Error refreshing token through supabase")

    result = RefreshResponse(
        access_token=resp.session.access_token,
        refresh_token=resp.session.refresh_token,
    )
    return result
