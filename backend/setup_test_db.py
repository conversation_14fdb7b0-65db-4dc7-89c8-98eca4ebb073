#!/usr/bin/env python3
import asyncio
import asyncpg

from config import settings as s
from db import psql_url


def check_test_db():
    if s.DATABASE_HOST not in ("localhost", "127.0.0.1", "postgres"):
        print(s.DATABASE_HOST)
        raise Exception("Use local database only!")


async def setup_db_for_tests():
    check_test_db()
    conn = await asyncpg.connect(psql_url)

    await conn.execute("commit")
    await conn.execute(f'DROP DATABASE IF EXISTS "{s.DATABASE_DB}"')
    await conn.execute("commit")
    await conn.execute(f'CREATE DATABASE "{s.DATABASE_DB}"')
    await conn.execute("commit")
    await conn.close()

    test_db_url = psql_url + f"/{s.DATABASE_DB}"
    conn = await asyncpg.connect(test_db_url)

    await conn.execute("CREATE SCHEMA IF NOT EXISTS auth")
    await conn.execute(
        """
        CREATE TABLE IF NOT EXISTS auth.users (
            id uuid PRIMARY KEY
        )
        """
    )

    await conn.close()


if __name__ == "__main__":
    asyncio.run(setup_db_for_tests())
