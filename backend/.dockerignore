# Secrets and sensitive files
.secrets.toml
.env
.env.*
*.pem
*.key

# Development files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
*.egg-info/
dist/
build/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Git
.git/
.gitignore

# Documentation
*.md
docs/

# Docker
Dockerfile*
docker-compose*.yaml
.dockerignore

# Logs
*.log

# Database
*.db
*.sqlite

# Temporary files
tmp/
temp/
*.tmp

