import sqlalchemy as sa

from models.base import Model, RecordTimestampFields


class ConversationMessage(Model, RecordTimestampFields):
    """
    Utilize RecordTimestampFields.record_created as ordering field to figure out
    order of messages in chat.
    """
    __tablename__ = "conversation_messages"

    id = sa.Column(sa.BigInteger, primary_key=True, autoincrement=True)

    message = sa.Column(sa.Text, nullable=False)
    # TODO: can response be empty?
    # TODO: Can we reuse this model for conversations requesting modifications for pages?
    # TODO: In such a case, does reponse even happen?
    # TODO: It should as we can put explanation by openai into chat box as a response.
    # TODO: So the question is: do we save message before we get whole
    # TODO: response from openai (so response is empty at that moment)?
    response = sa.Column(sa.Text, nullable=True)

    project_id = sa.Column(
        sa.UUID,
        sa.ForeignKey("projects.id", onupdate="CASCADE", ondelete="CASCADE"),
        index=True,
        nullable=False,
    )

    conversation_id = sa.Column(
        sa.UUID,
        sa.ForeignKey("conversations.id", onupdate="CASCADE", ondelete="CASCADE"),
        index=True,
        nullable=False,
    )


