import uuid
import enum
import sqlalchemy as sa

from models.base import Model, RecordTimestampFields


class ConversationTypes(enum.StrEnum):
    INITIAL = "INITIAL"
    OVERVIEW = "OVERVIEW"
    SCOPES = "SCOPES"


class Conversation(Model, RecordTimestampFields):
    __tablename__ = "conversations"
    # only one conversation for each type (page)
    __table_args__ = (sa.UniqueConstraint("project_id", "conv_type"),)

    id = sa.Column(
        sa.UUID, #(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        server_default=sa.text("gen_random_uuid()"), # "uuid_generate_v4()"
    )
    # type field to differentiate between conversations for different pages
    conv_type = sa.Column(sa.Text, nullable=False)

    project_id = sa.Column(
        sa.UUID,
        sa.ForeignKey("projects.id", onupdate="CASCADE", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
