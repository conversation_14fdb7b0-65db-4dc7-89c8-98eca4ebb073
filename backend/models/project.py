import uuid
import sqlalchemy as sa

from models.base import Model, RecordTimestampFields


class Project(Model, RecordTimestampFields):
    __tablename__ = "projects"

    id = sa.Column(
        sa.UUID, #(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        server_default=sa.text("gen_random_uuid()"), # "uuid_generate_v4()"
    )

    name = sa.Column(sa.Text, nullable=True)
    # TODO: keep relevant data here? like project overview, features and scope, project estimate?
    # TODO: some fields may be markdown formatted?
    project_overview = sa.Column(sa.Text, nullable=True)
    features_and_scopes = sa.Column(sa.Text, nullable=True)
    project_estimate = sa.Column(sa.Text, nullable=True)

    # TODO: foreign key to user, it will be empty before email confirmation
    # TODO: probably will need to clean up projects with empty user_ids
    user_id = sa.Column(
        sa.UUID,
        # sa.ForeignKey("auth.users.id", onupdate="CASCADE", ondelete="CASCADE"),
        nullable=True,
        index=True,
    )

    coda_doc_id = sa.Column(sa.Text, nullable=True)
    coda_doc_link = sa.Column(sa.Text, nullable=True)
    coda_doc_page_id = sa.Column(sa.Text, nullable=True)
    coda_doc_page_link = sa.Column(sa.Text, nullable=True)
