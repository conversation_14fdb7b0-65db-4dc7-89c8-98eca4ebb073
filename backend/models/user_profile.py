import sqlalchemy as sa

from models.base import Model, RecordTimestampFields


class UserProfile(Model, RecordTimestampFields):
    """
    User profile table to store additional user data that extends Supabase auth.users.
    This table has a 1:1 relationship with auth.users.
    """

    __tablename__ = "user_profiles"

    # Primary key is the user_id from Supabase auth
    user_id = sa.Column(
        sa.UUID,
        # sa.ForeignKey("auth.users.id", onupdate="CASCADE", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    )

    is_subscribed = sa.Column(sa.Boolean, nullable=False, default=False, server_default=sa.text("FALSE"))
