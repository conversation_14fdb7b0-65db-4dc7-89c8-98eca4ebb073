FROM python:3.12

WORKDIR /app

ENV PYTHONUNBUFFERED=1

# Install dependencies first (better layer caching)
RUN pip install pipenv
COPY Pipfile Pipfile.lock /app/
RUN pipenv install --system --deploy --clear

# Copy only required application files (secrets excluded via .dockerignore)
COPY alembic.ini /app/
COPY config.py db.py main.py /app/
COPY api/ /app/api/
COPY core/ /app/core/
COPY migrations/ /app/migrations/
COPY models/ /app/models/
COPY schemas/ /app/schemas/
COPY utils/ /app/utils/
COPY settings.toml /app/
COPY entrypoint.sh /usr/local/bin/entrypoint.sh

# Create non-root user
RUN groupadd -r backend && useradd -r -g backend -u 1000 -s /sbin/nologin backend

# Set ownership and permissions
RUN chown -R backend:backend /app /usr/local/bin/entrypoint.sh && \
    chmod +x /usr/local/bin/entrypoint.sh

ENV ENV_FOR_DYNACONF="production"

EXPOSE 8000

# Switch to non-root user
USER backend

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
