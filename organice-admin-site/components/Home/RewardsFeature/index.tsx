import { FC, useCallback } from "react";

import { useMixpanelContext } from "../../../context/MixpanelContext";
import {
  RewardActivityType,
  useOpenRewardsInSlackAppMutation,
} from "../../../graphql/client.generated";
import CoinSVG from "../../../public/svg/rewards/coin.svg";
import Button from "../../Button";
import { CoinsValue } from "../../Rewards/CoinsValue";
import { Activities } from "../../Settings/Rewards";

import TopCoinsCard, { TopCoinEarner } from "./TopCoinsCard";

export interface RewardsFeatureProps {
  slackHomeTabUrl: string;
  balance: number;
  activities: {
    type: RewardActivityType;
    points: number;
  }[];
  topEarner?: TopCoinEarner;
}

const RewardsFeature: FC<RewardsFeatureProps> = ({
  balance,
  activities,
  topEarner,
  slackHomeTabUrl,
}) => {
  const { track } = useMixpanelContext();
  const [openRewards] = useOpenRewardsInSlackAppMutation();

  const handleRequestReward = useCallback(async () => {
    await openRewards();

    window.open(slackHomeTabUrl, "_blank");
  }, [slackHomeTabUrl, openRewards]);

  return (
    <div className="flex h-fit w-[440px] flex-col rounded-xl border border-main-300 bg-white">
      <div className="flex items-center justify-between border-b border-b-main-300 px-4 py-3 font-semibold">
        <span>Rewards</span>

        <div className="flex items-center gap-1 text-2xl">
          <CoinSVG />
          <span className="font-semibold">{balance}</span>
        </div>
      </div>

      <div className="flex-grow p-4">
        <div className="mb-4 font-semibold">Earn coins:</div>

        <div className="space-y-3">
          {activities.map((activity) => {
            const activityData = Activities[activity.type];

            return (
              <div
                key={activity.type}
                className="flex items-center justify-between rounded-lg bg-gray-50 p-3 text-sm"
              >
                <CoinsValue coins={activity.points} />
                <div>{activityData.postfix}</div>
              </div>
            );
          })}
        </div>

        {balance ? (
          <div className="mt-4">
            <Button
              onClick={() => {
                track("Homepage - 'Request Reward' clicked");
                void handleRequestReward();
              }}
              size="xs"
              className="text-sm"
              variant="outline"
            >
              Claim rewards
            </Button>
          </div>
        ) : null}
      </div>

      {topEarner ? (
        <div className="p-4">
          <div className="mb-4 font-semibold">Top earner</div>
          <TopCoinsCard topCoinEarner={topEarner} />
        </div>
      ) : null}
    </div>
  );
};

export default RewardsFeature;
