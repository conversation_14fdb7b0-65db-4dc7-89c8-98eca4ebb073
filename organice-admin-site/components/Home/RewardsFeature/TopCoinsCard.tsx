import { FC } from "react";

import Avatar from "../../Avatar";
import { CoinsValue } from "../../Rewards/CoinsValue";

export interface TopCoinEarner {
  avatarUrl: string | null;
  title?: string | null;
  realName: string;
  points: number;
}

interface Props {
  topCoinEarner: TopCoinEarner;
}

const TopCoinsCard: FC<Props> = ({
  topCoinEarner: { title, avatarUrl, realName, points },
}) => {
  return (
    <div className="relative flex items-start justify-between rounded-lg bg-violet-200 p-4">
      <div className="flex items-center gap-3 text-sm">
        <Avatar photoUrl={avatarUrl} />
        <div>
          <div className="font-semibold">{realName}</div>
          {title ? <div className="text-sm">{title}</div> : null}
        </div>
      </div>

      <div>
        <div className="flex items-center gap-1 rounded-md bg-violet-600 px-2 py-1 text-sm text-white">
          +
          <CoinsValue coins={points} />
        </div>
      </div>
    </div>
  );
};

export default TopCoinsCard;
