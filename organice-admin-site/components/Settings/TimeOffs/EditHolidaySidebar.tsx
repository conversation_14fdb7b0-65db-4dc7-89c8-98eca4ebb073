import { format } from "date-fns";
import React, { FC, useState } from "react";

import { HolidayInput } from "../../../graphql/client.generated";
import Button from "../../Button";
import InfoTooltip from "../../InfoTooltip";
import Input from "../../Input";
import Sidebar from "../../Sidebar/Sidebar";
import Textarea from "../../Textarea";
import Toggle from "../../Toggle";

interface EditHolidaySidebarProps {
  onClose: () => void;
  holiday?: HolidayInput;
  year: number;
  onChange: (state: HolidayInput) => void;
}

const EditHolidaySidebar: FC<EditHolidaySidebarProps> = ({
  onClose,
  holiday,
  year,
  onChange,
}) => {
  const [formState, setFormState] = useState<HolidayInput>(() => ({
    id: holiday?.id,
    description: holiday?.description ?? "",
    name: holiday?.name ?? "",
    endDate: holiday ? holiday.endDate : new Date(year, 0, 1),
    startDate: holiday ? holiday.startDate : new Date(year, 0, 1),
    isOfficial: holiday?.isOfficial ?? true,
  }));

  const updateHolidayData = <K extends keyof HolidayInput>(
    filed: K,
    value: HolidayInput[K]
  ): void => setFormState((prevState) => ({ ...prevState, [filed]: value }));

  const isNotValid = !formState.name;
  const startOfYear = new Date(year, 0, 1, 0, 0);
  const endOfYear = new Date(year, 11, 31, 23, 59, 59);

  return (
    <Sidebar
      closeSidebar={onClose}
      title={
        <span className="font-bold leading-8">
          {holiday ? "Edit holiday" : "Add holiday"}
        </span>
      }
      headerClassName="!py-3"
      menu={[]}
      size="medium"
      footerClassName="flex gap-x-3 !py-3 justify-end"
      footer={
        <>
          <Button
            className="!block w-full"
            variant="outline"
            color="gray"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            className="!block w-full"
            onClick={() => {
              onChange(formState);
              onClose();
            }}
            disabled={isNotValid}
          >
            {holiday ? "Save holiday" : "Add holiday"}
          </Button>
        </>
      }
    >
      <div className="flex h-full flex-col p-6">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center gap-x-2">
            <span className="text-sm font-semibold leading-4">
              Official time off
            </span>
            <InfoTooltip tooltipMessage="Enabling this setting will exclude holidays or event dates from any time off that intersects with them" />
          </div>
          <Toggle
            checked={formState.isOfficial}
            onChange={() =>
              updateHolidayData("isOfficial", !formState.isOfficial)
            }
          />
        </div>
        <Input
          label="Name"
          value={formState.name}
          placeholder="Holiday or event name"
          onChange={(value) => updateHolidayData("name", value)}
        />
        <div className="mt-4 mb-6 flex items-center gap-5">
          <Input
            label="From"
            type="date"
            wrapperClassname="w-full"
            value={format(formState.startDate, "yyyy-MM-dd")}
            min={format(startOfYear, "yyyy-MM-dd")}
            max={format(endOfYear, "yyyy-MM-dd")}
            onChange={(value) => {
              if (value !== "") {
                updateHolidayData("startDate", new Date(`${value}T00:00:00`));
                updateHolidayData("endDate", new Date(`${value}T23:59:59`));
              }
            }}
          />
          <Input
            label="To"
            type="date"
            wrapperClassname="w-full"
            value={format(formState.endDate, "yyyy-MM-dd")}
            min={format(formState.startDate, "yyyy-MM-dd")}
            max={format(endOfYear, "yyyy-MM-dd")}
            onChange={(value) => {
              if (value !== "") {
                updateHolidayData("endDate", new Date(`${value}T23:59:59`));
              }
            }}
          />
        </div>
        <Textarea
          label="Description"
          rows={6}
          value={formState.description}
          onChange={(event) =>
            updateHolidayData("description", event.target.value)
          }
          placeholder="Holiday or event description"
        />
      </div>
    </Sidebar>
  );
};

export default EditHolidaySidebar;
