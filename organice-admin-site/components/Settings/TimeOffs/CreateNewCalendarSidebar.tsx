import React, { FC, useState } from "react";

import { useCreateNewCalendarMutation } from "../../../graphql/client.generated";
import { countryList } from "../../../helpers/flattenMember";
import { useToast } from "../../../hooks/useToast";
import Button from "../../Button";
import Checkbox from "../../Checkbox";
import Input from "../../Input";
import Select, { Option } from "../../Select";
import Sidebar from "../../Sidebar/Sidebar";

interface Props {
  onClose: () => void;
}

type FormState =
  | { country: string | null; title: string; type: "country" }
  | { title: string; type: "custom" };

const CreateNewCalendarSidebar: FC<Props> = ({ onClose }) => {
  const toast = useToast();
  const [formState, setFormState] = useState<FormState>({
    country: null,
    title: "",
    type: "country",
  });
  const countryOptions = countryList.map(
    ({ id, name, emoji }): Option => ({
      value: id,
      title: name,
      prefix: emoji,
    })
  );

  const [createNewCalendarMutation, { loading }] = useCreateNewCalendarMutation(
    {
      variables: {
        holidayCalendar: {
          country: formState.type === "country" ? formState.country : null,
          title: formState.title,
        },
      },
      refetchQueries: ["TimeOffsTypesPoliciesSettingsPage"],
    }
  );

  const submitHandler = (): void => {
    void createNewCalendarMutation()
      .then(() => {
        toast("success", `Holiday calendar has been created`);

        onClose();
      })
      .catch(() => toast("error", "Could not create new calendar"));
  };

  const isInvalid =
    formState.type === "country"
      ? formState.country === null || formState.title.trim().length === 0
      : formState.title.trim().length === 0;

  return (
    <Sidebar
      closeSidebar={onClose}
      title={<span className="font-bold leading-8">Create new calendar</span>}
      headerClassName="!py-3"
      menu={[]}
      size="medium"
      footerClassName="flex gap-x-3 justify-end !py-3"
      footer={
        <>
          <Button
            className="!block w-full"
            variant="outline"
            color="gray"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            className="!block w-full"
            onClick={submitHandler}
            disabled={isInvalid || loading}
          >
            Create new calendar
          </Button>
        </>
      }
    >
      <div className="py-5 px-6 text-sm">
        <p className="pb-4">What type of calendar do you want to create?</p>
        <Checkbox
          name="country"
          checked={formState.type === "country"}
          rounded
          onChange={() => {
            if (formState.type === "country") {
              return;
            }

            setFormState({
              country: null,
              title: "",
              type: "country",
            });
          }}
        >
          Country calendar
        </Checkbox>
        <p className="pt-2 pb-3 text-slate-500">
          Pre-filled with public holidays of a selected country. Holidays will
          be updated automatically every year.
        </p>

        <Checkbox
          name="custom"
          checked={formState.type === "custom"}
          rounded
          onChange={() => {
            if (formState.type === "custom") {
              return;
            }

            setFormState({
              title: "",
              type: "custom",
            });
          }}
        >
          Custom calendar
        </Checkbox>
        <p className="pt-2 pb-6 text-slate-500">
          Not linked to a country. Add your own company or local holidays (no
          automatic updates).
        </p>

        {formState.type === "country" ? (
          <Select<Option, false>
            placeholder="Select country"
            allowClear={false}
            value={
              countryOptions.find(
                (option) => option.value === formState.country
              ) ?? null
            }
            options={countryOptions}
            label="Country"
            onChange={(option) => {
              setFormState((currentFormState) =>
                currentFormState.type === "country"
                  ? {
                      ...currentFormState,
                      country: option?.value ?? null,
                      title: option?.value
                        ? `${option.value} Holidays`
                        : currentFormState.title,
                    }
                  : currentFormState
              );
            }}
          />
        ) : null}

        <Input
          label="Calendar title"
          placeholder="Enter title"
          value={formState.title}
          wrapperClassname="pt-4"
          onChange={(value) =>
            setFormState((currentFormState) => ({
              ...currentFormState,
              title: value,
            }))
          }
        />
      </div>
    </Sidebar>
  );
};

export default CreateNewCalendarSidebar;
