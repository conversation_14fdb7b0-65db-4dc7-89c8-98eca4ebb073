import { compareAsc, format, isSameDay } from "date-fns";
import { AnimatePresence } from "framer-motion";
import { isEqual } from "lodash";
import React, { FC, useState } from "react";
import { Pen2 } from "solar-icon-set";

import { useConfirm } from "../../../context/ConfirmContext";
import {
  EditHolidayCalendarSidebarStateFragment,
  EditHolidayCalendarInput,
  HolidayCalendarYearInput,
  useEditHolidayCalendarMutation,
  useReAddMissingHolidaysLazyQuery,
} from "../../../graphql/client.generated";
import { useToast } from "../../../hooks/useToast";
import Button from "../../Button";
import DropdownMenu from "../../DropdownMenu";
import Input from "../../Input";
import Select, { Option } from "../../Select";
import Sidebar from "../../Sidebar/Sidebar";
import TreeDotsButton from "../../TreeDotsButton";

import EditHolidaySidebar from "./EditHolidaySidebar";

interface Props {
  initialHolidayCalendarState: EditHolidayCalendarSidebarStateFragment;
  onClose: () => void;
}

type FormState = EditHolidayCalendarInput;

const EditNewCalendarSidebar: FC<Props> = ({
  initialHolidayCalendarState,
  onClose,
}) => {
  const toast = useToast();
  const initialFormState: FormState = {
    id: initialHolidayCalendarState.id,
    title: initialHolidayCalendarState.title,
    years: initialHolidayCalendarState.years.map((year) => ({
      year: year.year,
      holidays: year.holidays.map((holiday) => ({
        id: holiday.id,
        description: holiday.description,
        endDate: holiday.endDate,
        isOfficial: holiday.isOfficial,
        name: holiday.name,
        startDate: holiday.startDate,
      })),
    })),
  };
  const [formState, setFormState] = useState<FormState>(initialFormState);
  const { confirm } = useConfirm();

  const safeClose = (): void => {
    if (isEqual(formState, initialFormState)) {
      onClose();
    } else {
      void confirm({
        message: `You have unsaved changes`,
        buttonConfirm: "Discard changes",
        buttonCancel: "Back to editing",
      }).then(() => onClose());
    }
  };

  const [editingTitle, setEditingTitle] = useState(false);
  const [newHolidaySidebarOpen, setNewHolidaySidebarOpen] = useState(false);
  const [editingHolidayIndex, setEditingHolidayIndex] = useState<number | null>(
    null
  );
  const yearOptions = formState.years.map(
    (y): Option => ({
      title: `Year ${y.year}`,
      value: String(y.year),
    })
  );
  const [year, setYear] = useState(new Date().getFullYear());
  const formStateSelectedYear = formState.years.find(
    (holidayCalendarYear) => holidayCalendarYear.year === Number(year)
  )!;

  const [reAddMissingHolidaysLazyQuery, { loading: reAddingMissingHolidays }] =
    useReAddMissingHolidaysLazyQuery({
      fetchPolicy: "no-cache",
    });
  const [editHolidayCalendar, { loading: saving }] =
    useEditHolidayCalendarMutation({
      variables: {
        holidayCalendar: formState,
      },
      refetchQueries: ["TimeOffsTypesPoliciesSettingsPage"],
    });
  const setFormStateSelectedYear = (
    holidayCalendarYear: HolidayCalendarYearInput
  ): void => {
    setFormState((currentFormState) => ({
      ...currentFormState,
      years: currentFormState.years.map((y) =>
        y.year === holidayCalendarYear.year ? holidayCalendarYear : y
      ),
    }));
  };

  const submitHandler = (): void => {
    void editHolidayCalendar()
      .then(() => {
        toast("success", `Holiday calendar has been updated`);

        onClose();
      })
      .catch(() => toast("error", "Could not update new calendar"));
  };

  const reAddMissingHolidays = (): void => {
    void reAddMissingHolidaysLazyQuery({
      variables: {
        holidayCalendarId: initialHolidayCalendarState.id,
        holidayCalendarYear: formStateSelectedYear,
      },
    })
      .then(({ data }) => {
        if (!data) {
          return;
        }

        const { reAddMissingHolidays: restoredHolidayCalendarYear } = data;

        setFormState((currentFormState) => ({
          ...currentFormState,
          years: currentFormState.years.map((holidayCalendarYear) =>
            holidayCalendarYear.year === restoredHolidayCalendarYear.year
              ? {
                  year: restoredHolidayCalendarYear.year,
                  holidays: restoredHolidayCalendarYear.holidays.map(
                    (holiday) => ({
                      id: holiday.id,
                      description: holiday.description,
                      endDate: holiday.endDate,
                      isOfficial: holiday.isOfficial,
                      name: holiday.name,
                      startDate: holiday.startDate,
                    })
                  ),
                }
              : holidayCalendarYear
          ),
        }));
      })
      .catch(() => toast("error", "Failed to restore holidays"));
  };

  const hasNoTitle = formState.title.trim().length === 0;

  return (
    <Sidebar
      closeSidebar={safeClose}
      title={
        editingTitle ? (
          <form
            onSubmit={(event) => {
              event.preventDefault();

              if (!hasNoTitle) {
                setEditingTitle(false);
              }
            }}
          >
            <Input
              value={formState.title}
              onChange={(value) =>
                setFormState((currentFormState) => ({
                  ...currentFormState,
                  title: value,
                }))
              }
              wrapperClassname="mt-[-4px]"
              autoFocus
              required
            />
          </form>
        ) : (
          <span className="flex max-w-[800px] flex-shrink font-bold leading-8">
            <span className="truncate">
              {initialHolidayCalendarState.emoji} {formState.title}
            </span>
            <button
              className="hover:text-purple-600"
              type="button"
              onClick={() => setEditingTitle(true)}
            >
              <Pen2 className="ml-2 translate-y-[2px] text-current" />
            </button>
          </span>
        )
      }
      headerClassName="!py-6 !pl-6"
      menu={[]}
      size="large"
      footerClassName="flex justify-end gap-x-3 !py-6 !pr-6"
      footer={
        <>
          <Button
            className="!block"
            variant="outline"
            color="gray"
            onClick={safeClose}
          >
            Cancel
          </Button>
          <Button
            className="!block"
            onClick={submitHandler}
            disabled={saving || reAddingMissingHolidays || hasNoTitle}
          >
            Save calendar
          </Button>
        </>
      }
    >
      <div className="px-6 py-5">
        <div className="flex justify-between">
          <div className="w-[140px]">
            <Select<Option, false>
              allowClear={false}
              value={
                yearOptions.find((option) => option.value === String(year)) ??
                null
              }
              className="!mt-0"
              options={yearOptions}
              onChange={(option) => {
                setYear(Number(option!.value));
              }}
            />
          </div>

          <div className="flex space-x-2">
            {initialHolidayCalendarState.country ? (
              <Button
                disabled={reAddingMissingHolidays}
                variant="outline"
                onClick={reAddMissingHolidays}
              >
                Re-add Missing Holidays
              </Button>
            ) : null}

            <Button
              disabled={reAddingMissingHolidays}
              onClick={() => setNewHolidaySidebarOpen(true)}
            >
              Add Holiday
            </Button>
          </div>
        </div>

        <table className="mt-6 w-full table-fixed text-xs">
          <tr className="font-normal text-slate-500">
            <td className="w-[150px] px-3 py-2">Date</td>
            <td className="w-auto px-3 py-2">Holiday name</td>
            <td className="w-[155px] px-3 py-2">Official holiday</td>
            <td className="w-[48px] px-3 py-2" />
          </tr>
          {formStateSelectedYear.holidays.map((holiday, index) => (
            <tr key={holiday.id ?? index}>
              <td className="border-y px-3 py-4 align-top">
                {isSameDay(holiday.startDate, holiday.endDate) ? (
                  <span>{format(holiday.startDate, "d MMM yyyy")}</span>
                ) : (
                  <>
                    <span>
                      {format(holiday.startDate, "d MMM yyyy")} &mdash;{" "}
                    </span>
                    <span>{format(holiday.endDate, "d MMM yyyy")}</span>
                  </>
                )}
              </td>
              <td className="border-y px-3 py-4 align-top">
                <strong className="break-words">{holiday.name}</strong>
                <p className="break-words text-slate-500">
                  {holiday.description}
                </p>
              </td>
              <td className="border-y px-3 py-4 align-top">
                {holiday.isOfficial ? (
                  <span className="rounded bg-green-50 px-2 py-0.5 text-green-500">
                    Official
                  </span>
                ) : (
                  <span className="rounded bg-main-100 px-2 py-0.5 text-main-400">
                    Non-official
                  </span>
                )}
              </td>
              <td className="border-y px-3 py-2 align-top">
                <DropdownMenu
                  menu={[
                    {
                      label: "Edit",
                      id: "edit_type",
                      onClick: () => {
                        setEditingHolidayIndex(index);
                      },
                    },
                    {
                      label: "Delete",
                      id: "delete_type",
                      type: "danger",
                      onClick: () => {
                        setFormStateSelectedYear({
                          ...formStateSelectedYear,
                          holidays: formStateSelectedYear.holidays.filter(
                            (_, i) => i !== index
                          ),
                        });
                      },
                    },
                  ]}
                >
                  <TreeDotsButton itemClassName="bg-black" />
                </DropdownMenu>
              </td>
            </tr>
          ))}
        </table>
      </div>

      <AnimatePresence>
        {newHolidaySidebarOpen || editingHolidayIndex != null ? (
          <EditHolidaySidebar
            holiday={
              editingHolidayIndex != null
                ? formStateSelectedYear.holidays[editingHolidayIndex]
                : undefined
            }
            onChange={(holiday) => {
              const holidays = [...formStateSelectedYear.holidays];

              holidays[editingHolidayIndex ?? holidays.length] = holiday;

              setFormStateSelectedYear({
                ...formStateSelectedYear,
                holidays: holidays.sort((a, b) =>
                  compareAsc(a.startDate, b.startDate)
                ),
              });
            }}
            onClose={() => {
              setNewHolidaySidebarOpen(false);
              setEditingHolidayIndex(null);
            }}
            year={year}
          />
        ) : null}
      </AnimatePresence>
    </Sidebar>
  );
};

export default EditNewCalendarSidebar;
