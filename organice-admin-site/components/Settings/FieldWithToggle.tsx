import cn from "classnames";
import { FC, JSX } from "react";

import Toggle, { Props as ToggleProps } from "../Toggle";

interface Props extends ToggleProps {
  className?: string;
  label?: string | JSX.Element;
  description?: string | JSX.Element;
}

const FieldWithToggle: FC<Props> = ({
  className,
  label,
  description,
  onChange,
  checked,
  alwaysActive,
  disabled,
}) => {
  return (
    <div className={cn("flex gap-4", className)}>
      <div className="grow">
        {label ? (
          <div className="max-w-[550px] font-bold leading-4 text-slate-600">
            {label}
          </div>
        ) : null}
        {description ? (
          <div className="mt-2 max-w-[550px] text-sm text-slate-500">
            {description}
          </div>
        ) : null}
      </div>
      <Toggle
        checked={checked}
        onChange={onChange}
        alwaysActive={alwaysActive}
        disabled={disabled}
      />
    </div>
  );
};

export default FieldWithToggle;
