import cn from "classnames";
import { FC, PropsWithChildren, JSX } from "react";

interface Props extends PropsWithChildren {
  className?: string;
  label?: string | JSX.Element;
  description?: string | JSX.Element;
  dataTest?: string;
}

const Field: FC<Props> = ({
  children,
  className,
  label,
  description,
  dataTest,
}) => {
  return (
    <div
      className={cn("mt-8 max-w-[550px] pb-8", className)}
      data-test={dataTest}
    >
      {label ? (
        <div className="font-bold leading-4 text-slate-600">{label}</div>
      ) : null}
      {description ? (
        <div className="mt-2 text-sm text-slate-500">{description}</div>
      ) : null}
      {children}
    </div>
  );
};

export default Field;
