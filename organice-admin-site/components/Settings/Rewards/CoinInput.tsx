import { FC, useCallback } from "react";

import CoinSVG from "../../../public/svg/rewards/coin.svg";
import Input, { InputProps } from "../../Input";

interface CoinInputProps
  extends Omit<InputProps, "value" | "onChange" | "onBlur"> {
  value: number;
  onChange?: (value: number) => void;
  onBlur?: (value: number) => void;
}

const CoinInput: FC<CoinInputProps> = ({
  value,
  className = "w-[160px]",
  min = "0",
  onChange,
  onBlur,
  ...props
}) => {
  const CoinIconPrefix = useCallback(() => <CoinSVG />, []);

  return (
    <div className="relative">
      <Input
        type="number"
        defaultValue={value.toString()}
        max="1000000"
        onChange={
          onChange
            ? (newVal) => {
                const numValue = parseInt(newVal, 10);

                onChange(isNaN(numValue) ? 0 : Math.max(0, numValue));
              }
            : undefined
        }
        onBlur={
          onBlur
            ? (e) => {
                const numValue = parseInt(e.target.value, 10);

                onBlur(isNaN(numValue) ? 0 : Math.max(0, numValue));
              }
            : undefined
        }
        className={className}
        min={min}
        getBeforeIconBlock={CoinIconPrefix}
        {...props}
      />
    </div>
  );
};

export default CoinInput;
