import { FC, FormEventHandler, useCallback, useState } from "react";

import { useConfirmDeactivateReward } from "../../../hooks/useConfirmDeactivateReward";
import Button from "../../Button";
import ImagesUpload from "../../ImagesUpload";
import Input from "../../Input";
import Sidebar from "../../Sidebar/Sidebar";
import Textarea from "../../Textarea";
import Toggle from "../../Toggle";

import CoinInput from "./CoinInput";

export interface RewardFormData {
  id?: string;
  name: string;
  description?: string;
  imageUrl?: string;
  points: number;
  isActive: boolean;
}

export interface ManageRewardSidebarProps {
  data?: RewardFormData;
  onSubmit: (input: RewardFormData) => Promise<void>;
  onClose: VoidFunction;
}

const ManageRewardSidebar: FC<ManageRewardSidebarProps> = ({
  data,
  onSubmit,
  onClose,
}) => {
  const [name, setName] = useState<string>(data?.name ?? "");
  const [points, setPoints] = useState<number>(data?.points ?? 1);
  const [image, setImage] = useState<string | undefined>(data?.imageUrl);
  const [isActive, setIsActive] = useState<boolean>(data?.isActive ?? true);
  const [isLoading, setIsLoading] = useState(false);
  const [isImageUploading, setIsImageUploading] = useState(false);

  const nameTrimmed = name.trim();
  const isFormValid = nameTrimmed.length > 0 && points > 0;

  const rewardId = data?.id;
  const confirmDeactivateReward = useConfirmDeactivateReward();

  const handleSubmit: FormEventHandler<HTMLFormElement> = useCallback(
    (e) => {
      e.preventDefault();
      const fd = new FormData(e.currentTarget);

      void (async () => {
        if (isFormValid) {
          const description = fd.get("description") as string;

          const reward: RewardFormData = {
            id: rewardId,
            name: nameTrimmed,
            description: description.trim(),
            imageUrl: image,
            points,
            isActive,
          };

          if (rewardId && data.isActive && !isActive) {
            const shouldProceed = await confirmDeactivateReward(rewardId);

            if (!shouldProceed) {
              return;
            }
          }

          setIsLoading(true);
          await onSubmit(reward).finally(() => {
            setIsLoading(false);
          });
        }
      })();
    },
    [
      onSubmit,
      rewardId,
      nameTrimmed,
      image,
      points,
      isFormValid,
      isActive,
      data?.isActive,
      confirmDeactivateReward,
    ]
  );

  return (
    <Sidebar
      menu={[]}
      headerClassName="border-b border-b-gray-300"
      title={
        <div className="flex items-center font-semibold">
          {rewardId != null ? "Edit Reward" : "Add Reward"}
        </div>
      }
      footer={
        <div className="flex justify-end gap-3">
          <Button color="secondary" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            submit
            form="AddRewardForm"
            disabled={!isFormValid || isImageUploading}
            loading={isLoading}
          >
            {rewardId != null ? "Save Reward" : "Add Reward"}
          </Button>
        </div>
      }
      size="medium"
      closeSidebar={onClose}
    >
      <form
        id="AddRewardForm"
        onSubmit={handleSubmit}
        className="relative flex flex-col gap-6 py-4 px-6"
        data-test="reward-form"
      >
        {rewardId != null ? (
          <div className="flex gap-2 rounded-md bg-slate-100 px-4 py-3">
            <div className="flex-1 text-sm font-medium">Active</div>
            <Toggle
              checked={isActive}
              onChange={() => setIsActive((v) => !v)}
            />
          </div>
        ) : null}
        <Input
          type="text"
          label="Reward name"
          name="name"
          placeholder="Enter reward name"
          value={name}
          onChange={setName}
        />

        <Textarea
          name="description"
          label={
            <>
              Description{" "}
              <span className="font-normal text-slate-500">(optional)</span>
            </>
          }
          textareaClasses="min-h-[100px]"
          labelClasses="normal-case"
          placeholder="Enter reward description"
          defaultValue={data?.description}
        />

        <div>
          <div className="mb-2 text-sm font-semibold leading-4">Image</div>
          <ImagesUpload
            onChange={([url]) => {
              setImage(url);
            }}
            value={image ? [image] : []}
            onUploadStatusChange={setIsImageUploading}
          />
        </div>

        <CoinInput
          label="Price in coins"
          name="points"
          placeholder="Enter coin price"
          value={points}
          onChange={setPoints}
          className="w-full"
        />
      </form>
    </Sidebar>
  );
};

export default ManageRewardSidebar;
