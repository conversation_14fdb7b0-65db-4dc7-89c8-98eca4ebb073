import { ActionsRewardsData } from "@organice/core/domain";
import cn from "classnames";
import { FC, useCallback, useMemo, useState, useEffect, useRef } from "react";
import {
  ClipboardList as SurveysIcon,
  CupStar as KudosIcon,
  Gift,
} from "solar-icon-set";

import { useConfirm } from "../../../context/ConfirmContext";
import {
  useRewardsSettingsPageQuery,
  useDeleteRewardMutation,
  useAddOrUpdateRewardMutation,
  useUpdateRewardsSettingsMutation,
  RewardsSettingsPageQuery,
  RewardActivityType,
  RewardRequestStatus,
  useGetPendingRewardRequestsLazyQuery,
} from "../../../graphql/client.generated";
import { useConfirmDeactivateReward } from "../../../hooks/useConfirmDeactivateReward";
import { useToast } from "../../../hooks/useToast";
import TreeSVG from "../../../public/svg/navigation/vertical_tree.svg";
import PlusSVG from "../../../public/svg/rawPlus.svg";
import CoinSVG from "../../../public/svg/rewards/coin.svg";
import EmptyRewards from "../../../public/svg/rewards/emptyRewards.svg";
import Button from "../../Button";
import DropdownMenu, { Menu } from "../../DropdownMenu";
import EmptyPageState from "../../EmptyPageState";
import Skeleton from "../../Loading/Skeleton";
import { Tab } from "../../Tab";
import TableSkeleton from "../../TableSkeleton";
import TreeDotsButton from "../../TreeDotsButton";
import Field from "../Field";
import FieldGroupSkeletonBlockContent from "../FieldGroupSkeletonBlockContent";
import FieldWithToggle from "../FieldWithToggle";

import CoinInput from "./CoinInput";
import ManageRewardSidebar, { RewardFormData } from "./ManageRewardSidebar";

enum RewardsImagesEnum {
  ProductivityBook = "productivity-book-image",
  CoffeeTeaSet = "coffee-tea-set-image",
  DeskUpgradeKit = "desk-upgrade-kit-image",
  WellnessGoodie = "wellness-goodie-image",
  ExperienceVoucher = "experience-voucher-image",
}

const RewardsImages = {
  [RewardsImagesEnum.ProductivityBook]:
    "/png/settings/rewards/productivity-book.png",
  [RewardsImagesEnum.CoffeeTeaSet]: "/png/settings/rewards/coffee-tea-set.png",
  [RewardsImagesEnum.DeskUpgradeKit]:
    "/png/settings/rewards/desk-upgrade-kit.png",
  [RewardsImagesEnum.WellnessGoodie]:
    "/png/settings/rewards/wellness-goodie.png",
  [RewardsImagesEnum.ExperienceVoucher]:
    "/png/settings/rewards/experience-voucher.png",
};

export const Activities = {
  [RewardActivityType.Kudos]: {
    ...ActionsRewardsData[RewardActivityType.Kudos],
    icon: <KudosIcon size={24} />,
  },
  [RewardActivityType.Profile]: {
    ...ActionsRewardsData[RewardActivityType.Profile],
    icon: <TreeSVG />,
  },
  [RewardActivityType.Survey]: {
    ...ActionsRewardsData[RewardActivityType.Survey],
    icon: <SurveysIcon size={24} />,
  },
};

interface RewardCardProps {
  reward: RewardsSettingsPageQuery["rewardsSettings"]["rewards"][0];
  onEdit: () => void;
  onDelete: () => void;
  onToggleActive: () => void;
}

const RewardCard: FC<RewardCardProps> = ({
  reward,
  onEdit,
  onDelete,
  onToggleActive,
}) => {
  const menu: Menu[] = useMemo(
    () => [
      {
        label: "Edit",
        id: "edit_type",
        onClick: () => onEdit(),
      },
      {
        label: reward.isActive ? "Deactivate" : "Activate",
        id: "toggle_active_type",
        onClick: () => onToggleActive(),
      },
      {
        label: "Delete",
        id: "delete_type",
        type: "danger",
        onClick: () => onDelete(),
      },
    ],
    [onEdit, onDelete, onToggleActive, reward.isActive]
  );

  return (
    <div
      className={cn(
        "flex flex-col overflow-hidden rounded-lg border border-gray-200",
        {
          "grayscale filter": !reward.isActive,
        }
      )}
      title={!reward.isActive ? "This reward is inactive" : undefined}
      data-test={`reward-card-${reward.name}`}
    >
      <div className="relative m-2 h-32 overflow-hidden rounded-lg bg-main-100">
        {reward.imageUrl ? (
          <img
            src={reward.imageUrl}
            alt={reward.name}
            className="h-full w-full object-cover"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center text-main-500">
            <Gift size={24} />
          </div>
        )}
      </div>
      <div className="flex grow flex-col px-2 pt-1 pb-3">
        <div className="mb-1 flex items-center gap-2">
          <div
            title={reward.name}
            className="grow truncate text-sm font-medium text-gray-900"
          >
            {reward.name}
          </div>
          <DropdownMenu menu={menu}>
            <TreeDotsButton itemClassName="bg-black" />
          </DropdownMenu>
        </div>

        <p
          title={reward.description ?? ""}
          className="mb-3 grow break-words text-xs text-gray-500 line-clamp-2"
        >
          {reward.description}
        </p>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <CoinSVG />

            <span className="text-sm font-semibold ">{reward.points}</span>
            <span className="text-xs">Coins</span>
          </div>
        </div>
      </div>
    </div>
  );
};

interface TemplateCardProps {
  template: RewardsSettingsPageQuery["rewardTemplates"][0];
  onUse: () => void;
}

const TemplateCard: FC<TemplateCardProps> = ({ template, onUse }) => {
  return (
    <div
      data-test={`template-card-${template.name}`}
      className="hover:shadow-sm flex flex-col overflow-hidden rounded-lg border border-dashed border-gray-200"
    >
      <div className="relative m-2 h-32 overflow-hidden rounded-lg bg-main-100">
        <img
          src={RewardsImages[template.imageUrl as RewardsImagesEnum]}
          alt={template.name}
          className="h-full w-full object-cover"
        />
      </div>
      <div className="flex grow flex-col px-2 pt-1 pb-3">
        <div className="mb-1 flex items-center gap-2">
          <div
            title={template.name}
            className="grow truncate text-sm font-medium text-gray-900"
          >
            {template.name}
          </div>
        </div>

        <p
          title={template.description ?? ""}
          className="mb-3 grow break-words text-xs text-gray-500 line-clamp-2"
        >
          {template.description}
        </p>
        <div className="mb-2 flex items-center justify-between">
          <div className="flex items-center gap-1">
            <CoinSVG />

            <span className="text-sm font-semibold ">{template.points}</span>
            <span className="text-xs">Coins</span>
          </div>
        </div>
        <Button
          variant="outline"
          color="gray"
          onClick={onUse}
          size="s"
          className="!justify-center"
        >
          Use
        </Button>
      </div>
    </div>
  );
};

const RewardsSettings: FC = () => {
  const { data, loading } = useRewardsSettingsPageQuery();
  const [deleteReward] = useDeleteRewardMutation();
  const [updateRewardsSettings] = useUpdateRewardsSettingsMutation();
  const [addOrUpdateReward] = useAddOrUpdateRewardMutation();

  const [editingRewardValue, setEditingRewardValue] = useState<
    RewardFormData | undefined
  >();
  const [getPendingRewardRequests] = useGetPendingRewardRequestsLazyQuery();
  const [activeTab, setActiveTab] = useState("Rewards");
  const toast = useToast();
  const { confirm } = useConfirm();
  const confirmDeactivateReward = useConfirmDeactivateReward();
  const presetActiveTab = useRef<boolean>(false);
  const updateActivityPoints = useCallback(
    (activityType: RewardActivityType, points: number) => {
      const updatedActivity = data?.rewardsSettings.activities.find(
        (activity) => activity.type === activityType
      );

      if (updatedActivity?.points === points) {
        return;
      }

      const activities =
        data?.rewardsSettings.activities.map((activity) => {
          return {
            type: activity.type,
            points: activity.type === activityType ? points : activity.points,
          };
        }) ?? [];

      updateRewardsSettings({
        variables: {
          sendPointsNotifications:
            data?.rewardsSettings.sendPointsNotifications ?? false,
          activities,
        },
      })
        .then(() => {
          toast("success", "Activity points updated successfully");
        })
        .catch(() => {
          toast("error", "Failed to update activity points");
        });
    },
    [toast, updateRewardsSettings, data]
  );

  useEffect(() => {
    if (presetActiveTab.current) {
      return;
    }

    if (data?.rewardsSettings) {
      presetActiveTab.current = true;

      if (!data.rewardsSettings.rewards.length) {
        setActiveTab("Templates");
      }
    }
  }, [data?.rewardsSettings, setActiveTab]);

  const updatePointsNotifications = useCallback(
    (enabled: boolean) => {
      updateRewardsSettings({
        variables: {
          sendPointsNotifications: enabled,
          activities:
            data?.rewardsSettings.activities.map((activity) => ({
              type: activity.type,
              points: activity.points,
            })) ?? [],
        },
      })
        .then(() => {
          toast("success", "Points notifications updated successfully");
        })
        .catch(() => {
          toast("error", "Failed to update points notifications");
        });
    },
    [toast, updateRewardsSettings, data]
  );

  const handleEditReward = useCallback(
    (rewardId: string): void => {
      const reward = data?.rewardsSettings.rewards.find(
        (r) => r.id === rewardId
      );

      if (reward) {
        setEditingRewardValue({
          id: reward.id,
          name: reward.name,
          description: reward.description ?? undefined,
          points: reward.points,
          imageUrl: reward.imageUrl ?? undefined,
          isActive: reward.isActive,
        });
      }
    },
    [data]
  );

  const handleUseTemplate = useCallback(
    (template: RewardsSettingsPageQuery["rewardTemplates"][0]): void => {
      /**
       * NOTE: Reward image URLs must be stored as absolute paths
       * in the database.
       * when creating a reward from a template, the relative image
       * path is converted to an absolute URL.
       * if a new template image is added, it must be deployed
       * to production to avoid 404 errors.
       */
      const adminSiteUrl = "https://dashboard.organice.app";

      setEditingRewardValue({
        name: template.name,
        description: template.description ?? undefined,
        points: template.points,
        imageUrl: `${adminSiteUrl}${
          RewardsImages[template.imageUrl as RewardsImagesEnum]
        }`,
        isActive: true,
      });
    },
    []
  );

  const handleDeleteReward = useCallback(
    async (rewardId: string): Promise<void> => {
      const { data: requestsData } = await getPendingRewardRequests({
        fetchPolicy: "network-only",
      });

      const pendingRequestsCount =
        requestsData?.rewardRequests.filter(
          (req) =>
            req.reward.id === rewardId &&
            req.status === RewardRequestStatus.Pending
        ).length ?? 0;

      await confirm({
        message: "Are you sure you want to delete this reward?",
        description:
          pendingRequestsCount > 0
            ? `There are ${pendingRequestsCount} pending requests for this reward. Deleting this reward will automatically reject them. All requesters will be notified.`
            : "Deleting this reward cannot be undone.",
        buttonConfirm: "Delete",
        buttonCancel: "Cancel",
      });

      try {
        await deleteReward({
          variables: { id: rewardId },
          refetchQueries: ["RewardsSettingsPage"],
        });

        toast("success", "Reward deleted successfully");
      } catch {
        toast("error", "Failed to delete reward");
      }
    },
    [toast, deleteReward, confirm, getPendingRewardRequests]
  );

  const handleAddReward = useCallback((): void => {
    setEditingRewardValue({
      name: "",
      description: "",
      points: 1,
      isActive: true,
    });
  }, []);

  const handleToggleRewardActive = useCallback(
    async (rewardId: string): Promise<void> => {
      const reward = data?.rewardsSettings.rewards.find(
        (r) => r.id === rewardId
      );

      if (!reward) {
        return;
      }

      if (reward.isActive) {
        const shouldProceed = await confirmDeactivateReward(rewardId);

        if (!shouldProceed) {
          return;
        }
      }

      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { __typename, ...rewardData } = reward;

      await addOrUpdateReward({
        variables: {
          data: {
            ...rewardData,
            isActive: !reward.isActive,
          },
        },
      })
        .then(() => {
          toast(
            "success",
            !reward.isActive
              ? "Reward activated successfully"
              : "Reward deactivated successfully"
          );
        })
        .catch(() => {
          toast(
            "error",
            !reward.isActive
              ? "Failed to activate reward"
              : "Failed to deactivate reward"
          );
        });
    },
    [toast, addOrUpdateReward, data, confirmDeactivateReward]
  );

  const handleSubmitReward = useCallback(
    async (rewardData: RewardFormData): Promise<void> => {
      await addOrUpdateReward({
        variables: {
          data: {
            id: rewardData.id,
            name: rewardData.name,
            description: rewardData.description,
            imageUrl: rewardData.imageUrl,
            points: rewardData.points,
            isActive: rewardData.isActive,
          },
        },
      })
        .then(() => {
          toast(
            "success",
            rewardData.id
              ? "Reward updated successfully"
              : "Reward created successfully"
          );
          setEditingRewardValue(undefined);
          setActiveTab("Rewards");
        })
        .catch(() => {
          toast(
            "error",
            rewardData.id
              ? "Failed to update reward"
              : "Failed to create reward"
          );
        });
    },
    [toast, addOrUpdateReward]
  );

  const handleCloseSidebar = useCallback((): void => {
    setEditingRewardValue(undefined);
  }, []);

  if (loading) {
    return (
      <Skeleton>
        <FieldGroupSkeletonBlockContent className="mb-8" />
        <FieldGroupSkeletonBlockContent className="mb-8" />
        <FieldGroupSkeletonBlockContent />
        <hr className="my-8 divide-y divide-slate-300" />
        <FieldGroupSkeletonBlockContent className="mb-8" />
        <TableSkeleton />
      </Skeleton>
    );
  }

  return (
    <>
      <div className="text-lg font-bold">Rewarded Actions</div>
      <div className="mb-8 space-y-4">
        {data?.rewardsSettings.activities.map((activity) => {
          const activityData = Activities[activity.type];

          return (
            <Field
              dataTest={`activity-field-${activity.type}`}
              key={activity.type}
              label={
                <div className="flex items-center gap-2">
                  {activityData.icon}
                  <div>{activityData.title}</div>
                </div>
              }
              description={activityData.description}
            >
              <div className="mt-2.5 flex items-center gap-4">
                <CoinInput
                  value={activity.points}
                  onBlur={(value) => {
                    updateActivityPoints(activity.type, value);
                  }}
                />

                <span className="whitespace-nowrap text-sm ">
                  {activityData.postfix}
                </span>
              </div>
            </Field>
          );
        })}
      </div>
      <FieldWithToggle
        label="Enable notifications"
        description="Automatically send Slack notifications when users receive coins."
        checked={data?.rewardsSettings.sendPointsNotifications ?? false}
        onChange={() => {
          const enable = !data?.rewardsSettings.sendPointsNotifications;

          updatePointsNotifications(enable);
        }}
      />
      <hr className="my-8 border-gray-200" />
      {/* Reward Management Section */}
      <div>
        <div className="mb-6 flex items-start justify-between">
          <div>
            <p className="mb-3 text-sm font-bold text-slate-600">
              Reward manage
            </p>
            <p className="text-sm text-gray-500">
              Manage the rewards your team can redeem with coins.
            </p>
          </div>
          <Button
            variant="outline"
            dataTest="add-reward"
            onClick={handleAddReward}
          >
            Add Reward
          </Button>
        </div>

        <div className="flex gap-1">
          {["Rewards", "Templates"].map((tab) => (
            <Tab
              dataTest={`tab-${tab}`}
              key={tab}
              isActive={() => activeTab === tab}
              onClick={() => setActiveTab(tab)}
            >
              {tab}
            </Tab>
          ))}
        </div>

        <div className="mt-6">
          {activeTab === "Rewards" &&
            (data?.rewardsSettings.rewards.length ? (
              <div className="grid grid-cols-3 gap-4">
                {data.rewardsSettings.rewards.map((reward) => (
                  <RewardCard
                    key={reward.id}
                    reward={reward}
                    onEdit={() => handleEditReward(reward.id)}
                    onDelete={() => {
                      void handleDeleteReward(reward.id);
                    }}
                    onToggleActive={() => {
                      void handleToggleRewardActive(reward.id);
                    }}
                  />
                ))}
              </div>
            ) : (
              <EmptyPageState
                className="rounded-md border border-dashed py-8"
                emptyStateIcon={<EmptyRewards />}
              >
                <div className="flex flex-col gap-2">
                  <div>
                    You don&apos;t have any rewards created yet. <br /> Use our
                    templates or create your own.
                  </div>
                  <div>
                    <Button
                      dataTest="add-rewards"
                      onClick={() => handleAddReward()}
                    >
                      Add Reward
                    </Button>
                  </div>
                </div>
              </EmptyPageState>
            ))}

          {activeTab === "Templates" && (
            <div className="grid grid-cols-3 gap-4">
              {data?.rewardTemplates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  onUse={() => handleUseTemplate(template)}
                />
              ))}
              <button
                type="button"
                className="rounded-lg border border-dashed border-gray-200 bg-main-100 p-4 text-sm font-medium text-main-500 hover:border-violet-600"
                onClick={handleAddReward}
              >
                <div className="flex h-full flex-col items-center justify-center gap-2">
                  <PlusSVG />
                  Create a personalized reward for your team
                </div>
              </button>
            </div>
          )}
        </div>
      </div>{" "}
      {editingRewardValue && (
        <ManageRewardSidebar
          data={editingRewardValue}
          onSubmit={handleSubmitReward}
          onClose={handleCloseSidebar}
        />
      )}
    </>
  );
};

export default RewardsSettings;
