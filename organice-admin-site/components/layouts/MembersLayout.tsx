import { FC, useCallback, useMemo, useState, KeyboardEvent } from "react";
import { Magnifer, CloseCircle } from "solar-icon-set";

import { useSessionContext } from "../../context/SessionContext";
import useDebouncedCallback from "../../hooks/useDebouncedCallback";
import { useReplaceRouterQuery } from "../../hooks/useReplaceRouterQuery";
import Input from "../Input";
import CompletionProgressBar from "../Users/<USER>";

import { HeaderBase } from "./Layout/Header";

const MembersHeader: FC = () => {
  const { router, replaceQuery } = useReplaceRouterQuery();
  const { session } = useSessionContext();
  const searchTerm = useMemo(
    () => (router.query.q ?? "") as string,
    [router.query.q]
  );

  const [visibleSearch, setVisibleSearch] = useState(searchTerm);
  const MagniferIconPrefix = useCallback(() => <Magnifer />, []);

  const syncSearchQuery = useDebouncedCallback((term: string) => {
    replaceQuery({
      q: term,
    });
  }, 500);

  const search = useCallback(
    (term: string) => {
      setVisibleSearch(term);
      syncSearchQuery(term);
    },
    [syncSearchQuery]
  );

  const CloseCircleIconSuffix = useCallback(
    () =>
      visibleSearch ? (
        <CloseCircle
          iconStyle="Bold"
          className="cursor-pointer !text-slate-400"
          onClick={() => search("")}
        />
      ) : null,
    [visibleSearch, search]
  );

  const onKeyDown = (e: KeyboardEvent<HTMLInputElement>): void => {
    if (e.code === "Escape") {
      search("");
    }
  };

  return (
    <HeaderBase title="Employees">
      <>
        {session ? (
          <div className="flex items-center gap-2">
            <span className="text-xs leading-4 text-gray-500">
              Data completion
            </span>
            <CompletionProgressBar
              totalMembers={session.stats.totalMembers}
              totalPositions={session.stats.totalPositions}
              totalPositionsOnboarded={session.stats.totalPositionsOnboarded}
              totalPositionsInProgress={session.stats.totalPositionsInProgress}
            />
          </div>
        ) : null}
        <div className="ml-4 flex-1">
          <Input
            data-test="search"
            getBeforeIconBlock={MagniferIconPrefix}
            getAfterIconBlock={CloseCircleIconSuffix}
            value={visibleSearch}
            onChange={search}
            placeholder="Search for people"
            wrapperClassname="w-[350px] cursor-pointer float-right"
            onKeyDown={onKeyDown}
          />
        </div>
        <div id="users-header-actions" />
      </>
    </HeaderBase>
  );
};

export function getHeader(): JSX.Element {
  return <MembersHeader />;
}
