import {
  autoUpdate,
  flip,
  FloatingArrow,
  FloatingPortal,
  offset,
  safePolygon,
  shift,
  useFloating,
  useHover,
  useInteractions,
  useRole,
  arrow,
} from "@floating-ui/react";
import cn from "classnames";
import { FC, useRef, useState } from "react";

import { Me } from "../../../graphql/client.generated";
import { useToast } from "../../../hooks/useToast";
import Avatar from "../../Avatar";

interface Props {
  member: Omit<Me, "coinsBalance">;
  isCollapsed?: boolean;
}

export const UserNavigationDropdownItem: FC<{
  id: string;
  children: JSX.Element | string;
  onClick: () => void;
}> = ({ id, children, ...props }) => {
  return (
    <div
      data-test={`dropdown-item-${id}`}
      {...props}
      className={cn(
        "w-full max-w-xs truncate rounded-md px-3 py-2 hover:text-violet-600"
      )}
    >
      {children}
    </div>
  );
};

const NavigationProfile: FC<Props> = (props) => {
  const toast = useToast();
  const [visible, setVisible] = useState(false);
  const arrowRef = useRef(null);

  const { refs, floatingStyles, context } = useFloating({
    open: visible,
    onOpenChange: setVisible,
    strategy: "fixed",
    placement: "right-end",
    middleware: [
      offset(12),
      flip({
        padding: 9,
      }),
      shift({ padding: 9 }),
      arrow({
        element: arrowRef,
        padding: {
          top: 18,
        },
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const hover = useHover(context, {
    handleClose: safePolygon(),
  });
  const role = useRole(context, { role: "menu" });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    hover,
    role,
  ]);

  const selectMe = (): void => {
    if (!props.member.positionId) {
      toast("error", "You are not on the Org Chart");

      return;
    }

    const url = `${location.origin}/org-chart/departments?nodeId=${props.member.positionId}`;

    location.href = url;
  };

  return (
    <>
      <div className="w-full" ref={refs.setReference} {...getReferenceProps()}>
        <div
          className={cn(
            "flex h-[40px] cursor-pointer items-center justify-start overflow-hidden rounded-lg px-1 py-2 transition-all duration-200 hover:bg-violet-50 hover:text-violet-600"
          )}
        >
          <div className="flex w-full items-center gap-3">
            <Avatar
              className="shrink-0 bg-slate-300 bg-cover"
              size="s"
              rounded="full"
              photoUrl={props.member.photo72Url ?? props.member.photoUrl}
            />
            <div className="flex min-w-0 flex-col overflow-hidden whitespace-nowrap">
              <span className="truncate text-sm font-medium text-slate-900">
                {props.member.realName}
              </span>
              <span className="truncate text-xs text-slate-400">
                {props.member.email}
              </span>
            </div>
          </div>
        </div>
      </div>
      {visible ? (
        <FloatingPortal>
          <div
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
            className={cn(
              `z-[99999] min-w-[150px] rounded-md border border-gray-300 bg-white py-2 text-sm shadow-primary`
            )}
            data-test="dropdown-menu"
          >
            <FloatingArrow
              ref={arrowRef}
              context={context}
              width={15}
              height={10}
              fill="white"
              stroke="#CBD5E1"
              strokeWidth={1}
            />
            <div className="cursor-pointer pl-1 pr-1 first:pt-1 last:pb-1">
              <UserNavigationDropdownItem
                id="show_on_org_chart"
                onClick={selectMe}
              >
                Show me on the Org Chart
              </UserNavigationDropdownItem>
              <UserNavigationDropdownItem
                id="give_feedback"
                onClick={() => {
                  window.open(
                    "https://organice.canny.io/feature-requests",
                    "_blank"
                  );
                }}
              >
                Give feedback
              </UserNavigationDropdownItem>
              <UserNavigationDropdownItem
                id="logout"
                onClick={() => {
                  window.location.pathname = "/sign-out";
                }}
              >
                Logout
              </UserNavigationDropdownItem>
            </div>
          </div>
        </FloatingPortal>
      ) : null}
    </>
  );
};

export default NavigationProfile;
