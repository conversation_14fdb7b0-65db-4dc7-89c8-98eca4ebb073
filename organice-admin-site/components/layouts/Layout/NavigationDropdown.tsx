import {
  useFloating,
  useInteractions,
  useRole,
  offset,
  flip,
  shift,
  autoUpdate,
  FloatingPortal,
  FloatingArrow,
  arrow,
  useHover,
  safePolygon,
} from "@floating-ui/react";
import cn from "classnames";
import { FC, useRef, useState, ReactNode } from "react";

interface NavigationDropdownProps {
  children: ReactNode;
  trigger: ReactNode;
}

const NavigationDropdown: FC<NavigationDropdownProps> = ({
  children,
  trigger,
}) => {
  const [visible, setVisible] = useState(false);
  const arrowRef = useRef(null);

  const { refs, floatingStyles, context } = useFloating({
    open: visible,
    onOpenChange: setVisible,
    strategy: "fixed",
    placement: "right-start",
    middleware: [
      offset(12),
      flip({
        padding: 9,
      }),
      shift({ padding: 9 }),
      arrow({
        element: arrowRef,
        padding: {
          top: 18,
        },
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const hover = useHover(context, {
    handleClose: safePolygon(),
  });
  const role = useRole(context, { role: "menu" });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    hover,
    role,
  ]);

  return (
    <>
      <div
        className="relative w-full"
        ref={refs.setReference}
        {...getReferenceProps()}
      >
        <div className="cursor-pointer">{trigger}</div>
      </div>
      {visible ? (
        <FloatingPortal>
          <div
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
            className={cn(
              `z-[99999] min-w-[150px] rounded-md border border-gray-300 bg-white py-2 text-sm shadow-primary`
            )}
            data-test="dropdown-menu"
          >
            <FloatingArrow
              ref={arrowRef}
              context={context}
              width={15}
              height={10}
              fill="white"
              stroke="#CBD5E1"
              strokeWidth={1}
            />
            <div className="group cursor-pointer pl-1 pr-1 first:pt-1 last:pb-1">
              {children}
            </div>
          </div>
        </FloatingPortal>
      ) : null}
    </>
  );
};

export default NavigationDropdown;
