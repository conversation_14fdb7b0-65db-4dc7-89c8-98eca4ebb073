import cn from "classnames";
import { FC, useCallback, useMemo, useState, KeyboardEvent } from "react";
import { Magni<PERSON>, CloseCircle, AltArrowDown } from "solar-icon-set";

import { usePageContext } from "../../context/PageContext";
import { useSessionContext } from "../../context/SessionContext";
import useDebouncedCallback from "../../hooks/useDebouncedCallback";
import { useReplaceRouterQuery } from "../../hooks/useReplaceRouterQuery";
import { DropdownLinks } from "../DropdownLinks";
import Input from "../Input";
import CoinsProgressBar from "../Rewards/CoinsProgressBar";

import { HeaderBase } from "./Layout/Header";

export interface PageData {
  totalPointsEarned: number;
  totalPointsSpent: number;
  totalPointsAvailable: number;
}

const RewardsHeader: FC = () => {
  const { data: pageContextData } = usePageContext<PageData>();
  const { session } = useSessionContext();
  const { totalPointsEarned, totalPointsSpent, totalPointsAvailable } =
    pageContextData ?? {
      totalPointsEarned: 0,
      totalPointsSpent: 0,
      totalPointsAvailable: 0,
    };

  const { router, replaceQuery } = useReplaceRouterQuery();
  const searchTerm = useMemo(
    () => (router.query.q ?? "") as string,
    [router.query.q]
  );

  const [visibleSearch, setVisibleSearch] = useState(searchTerm);
  const MagniferIconPrefix = useCallback(() => <Magnifer />, []);

  const syncSearchQuery = useDebouncedCallback((term: string) => {
    replaceQuery({
      q: term,
    });
  }, 500);

  const search = useCallback(
    (term: string) => {
      setVisibleSearch(term);
      syncSearchQuery(term);
    },
    [syncSearchQuery]
  );

  const CloseCircleIconSuffix = useCallback(
    () =>
      visibleSearch ? (
        <CloseCircle
          iconStyle="Bold"
          className="cursor-pointer !text-slate-400"
          onClick={() => search("")}
        />
      ) : null,
    [visibleSearch, search]
  );

  const onKeyDown = (e: KeyboardEvent<HTMLInputElement>): void => {
    if (e.code === "Escape") {
      search("");
    }
  };

  const pagesLinks = useMemo(() => {
    const links = [
      {
        id: "leaderboard",
        label: "Leaderboard",
        href: "/rewards/leaderboard",
        active: router.pathname === "/rewards/leaderboard",
      },
    ];

    if (session?.me.isAdmin) {
      links.push({
        id: "coins",
        label: "Manage Coins",
        href: "/rewards/coins",
        active: router.pathname === "/rewards/coins",
      });

      links.push({
        id: "requests",
        label: "Rewards Requests",
        href: "/rewards/requests",
        active: router.pathname === "/rewards/requests",
      });
    }

    return links;
  }, [router, session?.me.isAdmin]);

  const activePageLink = useMemo<(typeof pagesLinks)[0]>(() => {
    return pagesLinks.find((link) => link.active) ?? pagesLinks[0];
  }, [pagesLinks]);

  return (
    <HeaderBase
      title={
        session?.me.isAdmin ? (
          <DropdownLinks
            links={pagesLinks}
            renderTrigger={({ isMenuOpen }) => (
              <div className="flex items-center gap-2">
                {activePageLink.label}
                <AltArrowDown
                  className={cn(
                    "mt-0.5 transform text-main-500 transition-all",
                    isMenuOpen && "rotate-180"
                  )}
                />
              </div>
            )}
          />
        ) : (
          <div className="flex items-center gap-2">{activePageLink.label}</div>
        )
      }
    >
      <>
        {activePageLink.id === "coins" ? (
          <>
            <div className="flex items-center gap-2">
              <span className="text-xs leading-4 text-gray-500">
                Rewards statistics
              </span>
              <CoinsProgressBar
                totalCoinsEarned={totalPointsEarned}
                totalCoinsSpent={totalPointsSpent}
                totalCoinsAvailable={totalPointsAvailable}
              />
            </div>
            <div className="ml-4 flex-1">
              <Input
                data-test="search"
                getBeforeIconBlock={MagniferIconPrefix}
                getAfterIconBlock={CloseCircleIconSuffix}
                value={visibleSearch}
                onChange={search}
                placeholder="Search for people"
                wrapperClassname="max-w-[350px] w-full cursor-pointer float-right"
                onKeyDown={onKeyDown}
              />
            </div>
          </>
        ) : (
          <div className="flex-1" />
        )}

        <div id="rewards-header-actions" />
      </>
    </HeaderBase>
  );
};

export function getHeader(): JSX.Element {
  return <RewardsHeader />;
}
