import { FC } from "react";

import { RewardRequestChangedType } from "../../../../../graphql/client.generated";
import { getMemberName, Member } from "../../../CalendarSidebar/utils";

import ActivityLogItem from "./ActivityLogItem";

interface Props {
  createdAt: Date;
  type: RewardRequestChangedType;
  request: {
    reward: {
      name: string;
      points: number;
    };
    status: string;
  };
  member: Member;
  initiator?: Member;
  isLast?: boolean;
}

const RewardRequestLog: FC<Props> = ({
  createdAt,
  type,
  request,
  member,
  initiator,
  isLast,
}) => {
  const content = {
    [RewardRequestChangedType.Create]: (
      <div>
        <b>{getMemberName(member)}</b> requested a reward{" "}
        <b>{request.reward.name}</b> for <b>{request.reward.points} coins</b>
      </div>
    ),
    [RewardRequestChangedType.Approve]: (
      <div>
        <b>{getMemberName(initiator ?? member)}</b> approved a reward{" "}
        <b>{request.reward.name}</b> for <b>{getMemberName(member)}</b>
      </div>
    ),
    [RewardRequestChangedType.Reject]: (
      <div>
        <b>{getMemberName(initiator ?? member)}</b> rejected a reward{" "}
        <b>{request.reward.name}</b> for <b>{getMemberName(member)}</b>
      </div>
    ),
    [RewardRequestChangedType.Delete]: (
      <div>
        <b>{getMemberName(initiator ?? member)}</b> deleted a reward{" "}
        <b>{request.reward.name}</b> for <b>{getMemberName(member)}</b>
      </div>
    ),
  };

  return (
    <ActivityLogItem
      type="reward"
      createdAt={createdAt}
      content={content[type]}
      isLast={isLast}
    />
  );
};

export default RewardRequestLog;
