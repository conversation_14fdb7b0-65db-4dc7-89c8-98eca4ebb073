import { CoinsBalanceChangedType } from "@organice/core/domain/activityLog";
import { FC } from "react";

import {
  CoinsLogType,
  RewardActivityType,
} from "../../../../../graphql/client.generated";
import { getMemberName, Member } from "../../../CalendarSidebar/utils";

import ActivityLogItem from "./ActivityLogItem";

interface Props {
  createdAt: Date;
  type: CoinsLogType;
  increase?: number;
  absolute?: number;
  activity?: {
    type: RewardActivityType;
    points: number;
  };
  member: Member;
  initiator?: Member;
  isLast?: boolean;
}

function getActivityTypeLabel(activityType: RewardActivityType): string {
  if (activityType === RewardActivityType.Kudos) {
    return "receiving kudos";
  }

  if (activityType === RewardActivityType.Profile) {
    return "fully completing their profile";
  }

  return "fully completing a survey";
}

const CoinsLog: FC<Props> = ({
  createdAt,
  type,
  increase,
  absolute,
  activity,
  member,
  initiator,
  isLast,
}) => {
  const content = {
    [CoinsBalanceChangedType.manual]: initiator ? (
      <div>
        <b>{getMemberName(initiator)}</b> manually updated{" "}
        <b>{getMemberName(member)}</b> coins balance to <b>{absolute}</b>
      </div>
    ) : null,
    [CoinsBalanceChangedType.activityCompleted]: activity ? (
      <div>
        <b>{getMemberName(member)}</b> earned <b>{increase} coins</b> for{" "}
        {getActivityTypeLabel(activity.type)}
      </div>
    ) : null,
    [CoinsBalanceChangedType.rewardApproved]: (
      <div>
        <b>{getMemberName(initiator ?? member)}</b> spent{" "}
        <b>{increase} coins</b> on a reward
      </div>
    ),
    [CoinsBalanceChangedType.rewardDeleted]: (
      <div>
        <b>{getMemberName(initiator ?? member)}</b> had <b>{increase} coins</b>{" "}
        refunded from a deleted reward
      </div>
    ),
  };

  return (
    <ActivityLogItem
      type="coins"
      createdAt={createdAt}
      content={content[type]}
      isLast={isLast}
    />
  );
};

export default CoinsLog;
