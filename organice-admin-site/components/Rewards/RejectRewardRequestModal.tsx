import React, { useState } from "react";

import Button from "../Button";
import Modal from "../Modal";
import Textarea from "../Textarea";

interface Props {
  loading?: boolean;
  onClose: () => void;
  onSubmit: (reason: string | null) => void;
}

const RejectRewardRequestModal: React.FC<Props> = ({
  loading,
  onClose,
  onSubmit,
}) => {
  const [reason, setReason] = useState("");

  const handleSubmit = (): void => {
    onSubmit(reason.trim() === "" ? null : reason.trim());
  };

  return (
    <Modal
      show
      title="Reject Reward Request"
      onClose={onClose}
      onSubmit={handleSubmit}
      wrapperClassName="pt-36"
      data-test="reject-request-modal"
    >
      <form
        data-test="reject-reward-request-modal"
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <Textarea
          name="rejection-reason"
          label="Reason (optional)"
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          placeholder="Provide a reason for rejecting this request"
          textareaClasses="h-24"
        />
        <div
          className="flex justify-end gap-x-5 pt-6"
          data-prevent-sidebar-close
        >
          <Button variant="outline" color="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button loading={loading} variant="primary" color="danger" submit>
            Reject
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default RejectRewardRequestModal;
