import {
  useFloating,
  offset,
  flip,
  shift,
  autoUpdate,
  FloatingPortal,
} from "@floating-ui/react";
import pluralize from "pluralize";
import { useState, useEffect, ForwardedRef, forwardRef } from "react";
import { CellProps, CellComponent, intColumn } from "react-datasheet-grid";

import { RewardActivity } from "../../graphql/client.generated";
import { Activities } from "../Settings/Rewards";

type Activity = Pick<RewardActivity, "type" | "points">;

export interface BalanceData {
  balance: number;
}

export interface BalanceOptions {
  activities: Activity[];
  placeholder?: string;
}

const defaultIntColumn = intColumn;

interface Props {
  activities: Activity[];
  style: React.CSSProperties;
}

const ActivityInfo = forwardRef(
  ({ activities, style }: Props, ref: ForwardedRef<HTMLDivElement>) => {
    return (
      <div
        ref={ref}
        style={style}
        className="rounded-md border border-gray-300 bg-white p-2 text-xs  shadow-primary"
      >
        {activities.map((activity) => {
          const activityData = Activities[activity.type];

          return (
            <p key={activity.type}>
              • receives <strong>{activity.points}</strong>{" "}
              {pluralize("coin", activity.points)} for {activityData.postfix}
            </p>
          );
        })}
      </div>
    );
  }
);

const BalanceColumn = ({
  rowData,
  columnData,
  active,
  disabled,
  ...args
}: CellProps<number, BalanceOptions>): JSX.Element => {
  const IntComponent = defaultIntColumn.component as CellComponent;
  const [delayedActive, setDelayedActive] = useState(false);

  /**
   * Set Active with a delay so TextComponent can handle data change before
   * it will be destroyed and so a user can trigger a link before it will be
   * destroyed
   */
  useEffect(() => {
    setDelayedActive(active);
  }, [active]);

  const { refs, floatingStyles } = useFloating({
    open: true,
    strategy: "fixed",
    placement: "bottom",
    middleware: [
      offset(12),
      flip({
        padding: 9,
      }),
      shift({ padding: 9 }),
    ],
    whileElementsMounted: autoUpdate,
  });

  return delayedActive && !disabled ? (
    <>
      <div ref={refs.setReference} className="flex w-full">
        <IntComponent
          rowData={rowData}
          columnData={{
            ...defaultIntColumn.columnData,
            placeholder: "Start typing a number",
            continuousUpdates: false,
          }}
          disabled={disabled}
          active
          {...args}
        />
      </div>
      <FloatingPortal>
        <ActivityInfo
          ref={refs.setFloating}
          style={floatingStyles}
          activities={columnData.activities}
        />
      </FloatingPortal>
    </>
  ) : (
    <div className="w-full px-2 text-right text-sm">
      <span className="text-xs text-gray-500">Available coins</span> {rowData}
    </div>
  );
};

export default BalanceColumn;
