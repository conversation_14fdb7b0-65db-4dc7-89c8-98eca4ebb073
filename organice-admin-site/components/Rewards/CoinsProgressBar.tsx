import { FC } from "react";
import { InfoCircle } from "solar-icon-set";

import ProgressBar from "../ProgressBar";
import Tooltip from "../Tooltip";

interface CoinsProgressBarProps {
  totalCoinsEarned: number;
  totalCoinsSpent: number;
  totalCoinsAvailable: number;
}

const CoinsProgressBar: FC<CoinsProgressBarProps> = ({
  totalCoinsEarned,
  totalCoinsSpent,
  totalCoinsAvailable,
}) => {
  return (
    <div className="flex items-center gap-1 text-sm">
      <ProgressBar
        total={totalCoinsEarned}
        value={totalCoinsSpent}
        style={{ width: 190 }}
      />
      <Tooltip
        className="h-4"
        message={
          <div className="px-1 text-left leading-normal text-white">
            <div>
              <span className="mr-2 inline-block h-[0.75rem] w-[0.75rem] rounded-full border border-white bg-indigo-500" />
              <span>
                Total coins spent: <b>{totalCoinsSpent}</b>
              </span>
            </div>
            <div>
              <span className="mr-2 inline-block h-[0.75rem] w-[0.75rem] rounded-full border border-white bg-indigo-100" />
              <span>
                Total coins available: <b>{totalCoinsAvailable}</b>
              </span>
            </div>
            <div className="mt-2">
              Total coins earned: <b>{totalCoinsEarned}</b>
            </div>
          </div>
        }
        tooltipClassName="top-10 max-w-[22rem]"
      >
        <InfoCircle size={16} className="!text-violet-600" />
      </Tooltip>
    </div>
  );
};

export default CoinsProgressBar;
