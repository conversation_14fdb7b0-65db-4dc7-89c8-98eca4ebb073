import { CellProps } from "react-datasheet-grid";

const medals = ["🥇", "🥈", "🥉"];

const PlaceColumn = ({ rowData }: CellProps<number>): JSX.Element => {
  if (!rowData) {
    return <div className="w-full px-2 text-center">N/A</div>;
  }

  const medal = rowData > 0 && rowData <= 3 ? medals[rowData - 1] : null;

  if (medal) {
    return (
      <div className="flex h-full w-full items-center justify-between px-2">
        <div>{rowData}</div>
        <div className="text-2xl">{medal}</div>
      </div>
    );
  }

  return <div className="w-full px-2">{rowData}</div>;
};

export default PlaceColumn;
