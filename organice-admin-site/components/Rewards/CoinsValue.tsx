import pluralize from "pluralize";

import CoinSVG from "../../public/svg/rewards/coin.svg";

interface CoinsValueProps {
  coins: number;
}

export const CoinsValue: React.FC<CoinsValueProps> = ({ coins }) => {
  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-1">
        <CoinSVG />
        <strong>{coins}</strong>
      </div>
      {pluralize("Coin", coins)}
    </div>
  );
};
