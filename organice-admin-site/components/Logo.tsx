import Image from "next/image";
import NextLink from "next/link";
import React from "react";

interface LogoProps {
  withLabel?: boolean;
  size?: number;
}

const Logo: React.FC<LogoProps> = ({ withLabel, size = 22 }) => {
  const scale = size / 22;
  const totalWidth = 102 * scale;
  const iconWidth = 22 * scale;

  return (
    <NextLink
      className="relative block overflow-hidden transition-all duration-200"
      style={{
        height: size,
        width: withLabel ? totalWidth : iconWidth,
      }}
      href="/"
    >
      <Image
        className="max-w-none"
        alt="logo"
        src="/svg/logo.svg"
        width={totalWidth}
        height={size}
        priority
      />
    </NextLink>
  );
};

export default Logo;
