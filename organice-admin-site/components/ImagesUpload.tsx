import cn from "classnames";
import pluralize from "pluralize";
import { FC, useCallback, useState, useEffect, useRef } from "react";
import type { DropzoneOptions, FileRejection } from "react-dropzone";
import { useDropzone } from "react-dropzone";
import { CloudUpload, CheckCircle, TrashBin2 } from "solar-icon-set";

import { useGeneratePresignedUploadUrlMutation } from "../graphql/client.generated";

const renderBytes = (bytes: number): string => {
  const units = ["B", "KB", "MB", "GB", "TB", "PB"];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex += 1;
  }

  return `${size.toFixed()}${units[unitIndex]}`;
};

function getFilenameFromUrl(url: string): string {
  const segments = url.split("/");
  const filename = segments[segments.length - 1];

  const match = filename.match(/^\d+-(.+)$/);

  return match ? match[1] : filename;
}

async function getFileFromUrl(url: string): Promise<File> {
  const response = await fetch(url);
  const blob = await response.blob();
  const filename = getFilenameFromUrl(url);

  return new File([blob], filename, { type: blob.type });
}

interface UploadedFile {
  id: string;
  url: string;
  name: string;
  size: number;
  preview?: string;
  error?: string;
}

interface UploadingFile {
  id: string;
  file: File;
  progress: number;
  loaded: number;
  error?: string;
  preview?: string;
  size: number;
  name: string;
}

interface ImagesUploadProps {
  value: string[];
  onChange: (urls: string[]) => void;
  maxFiles?: number;
  maxSize?: number;
  disabled?: boolean;
  accept?: DropzoneOptions["accept"];
  className?: string;
  onUploadStatusChange?: (isUploading: boolean) => void;
}

interface ImageItemProps {
  file: UploadedFile | UploadingFile;
  onRemove: (id: string) => void;
}

const ImageItem: FC<ImageItemProps> = ({ file, onRemove }) => {
  const isUploading = "progress" in file;
  const [preview, setPreview] = useState<string | undefined>(file.preview);

  useEffect(() => {
    if ("file" in file && !preview) {
      const reader = new FileReader();

      reader.onload = (e) => {
        if (e.target?.result && typeof e.target.result === "string") {
          setPreview(e.target.result);
        }
      };
      reader.readAsDataURL(file.file);
    } else if ("url" in file && !preview) {
      setPreview(file.url);
    }
  }, [file, preview]);

  return (
    <div className="relative flex items-center gap-4 rounded-lg border border-gray-200 bg-main-100 p-4">
      <button
        type="button"
        onClick={() => onRemove(file.id)}
        className="absolute right-2 top-2 flex h-6 w-6 cursor-pointer items-center justify-center rounded-full bg-main-100 hover:bg-gray-200"
      >
        <TrashBin2 size={14} />
      </button>
      <div className="h-[42px] w-[42px] overflow-hidden rounded bg-gray-100">
        {preview ? (
          <img
            src={preview}
            alt={file.name}
            className="h-full w-full object-cover"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center">
            <CloudUpload size={20} className="text-gray-400" />
          </div>
        )}
      </div>

      <div className="min-w-0 flex-1">
        <p className="mb-1 text-sm font-medium">{file.name}</p>

        <div className="flex items-center gap-2 text-xs text-main-500">
          <span>
            {isUploading && "loaded" in file
              ? `${renderBytes(file.loaded)} of ${renderBytes(file.size)}`
              : `${renderBytes(file.size)} of ${renderBytes(file.size)}`}
          </span>
          {!isUploading && (
            <>
              <span>&bull;</span>
              <span className="flex items-center gap-1">
                <CheckCircle iconStyle="Bold" size={16} color="#10B981" />
                Completed
              </span>
            </>
          )}
        </div>

        {file.error ? (
          <p className="mt-1 text-xs text-red-500">{file.error}</p>
        ) : isUploading && "progress" in file ? (
          <div className="mt-2">
            <div className="h-1.5 w-full overflow-hidden rounded-full bg-gray-200">
              <div
                className="h-full bg-violet-600 transition-all duration-300"
                style={{ width: `${file.progress}%` }}
              />
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

interface DropzoneEmptyStateProps {
  maxFiles?: number;
  accept?: DropzoneOptions["accept"];
  maxSize?: number;
  rejectedFiles: FileRejection[];
}

const DropzoneEmptyState: FC<DropzoneEmptyStateProps> = ({
  maxFiles = 1,
  accept,
  maxSize,
  rejectedFiles,
}) => (
  <div className="flex cursor-pointer flex-col gap-4 rounded-lg border border-gray-200 bg-main-100 p-4 hover:border-violet-600">
    <div className="flex items-center gap-4">
      <div className="flex h-[42px] w-[42px] items-center justify-center ">
        <CloudUpload size={24} className=" text-gray-400" />
      </div>
      <div>
        <p className="mb-1 text-sm font-medium">
          Upload {pluralize("image", maxFiles, true)}
        </p>
        <div className="text-xs text-main-500">
          <p>Drag and drop or click to browse</p>
          <p>
            {accept ? Object.keys(accept).join(", ") : "PNG, JPG, JPEG"} up to{" "}
            {renderBytes(maxSize ?? 10 * 1024 * 1024)}
          </p>
        </div>
      </div>
    </div>
    {rejectedFiles.length > 0 && (
      <div className="space-y-2 text-xs text-red-500">
        {rejectedFiles.map(({ file, errors }) => (
          <div key={file.name}>
            {file.name}:{" "}
            {errors
              .map((e) => {
                if (e.code === "file-too-large" && maxSize) {
                  return `File is larger than ${renderBytes(maxSize)}`;
                }

                return e.message;
              })
              .join(", ")}
          </div>
        ))}
      </div>
    )}
  </div>
);

const ImagesUpload: FC<ImagesUploadProps> = ({
  value = [],
  onChange,
  maxFiles = 1,
  disabled = false,
  accept = { "image/*": [".png", ".jpg", ".jpeg"] },
  maxSize = 4 * 1024 * 1024,
  className,
  onUploadStatusChange,
}) => {
  const xhrRefs = useRef<Record<string, XMLHttpRequest | undefined>>({});
  const [generatePresignedUploadUrl] = useGeneratePresignedUploadUrlMutation();
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>([]);

  useEffect(() => {
    if (onUploadStatusChange) {
      const isUploading = uploadingFiles.some((f) => !f.error);

      onUploadStatusChange(isUploading);
    }
  }, [uploadingFiles, onUploadStatusChange]);

  useEffect(() => {
    const syncUploadedFiles = async (): Promise<void> => {
      const fetchedFiles = uploadedFiles.filter((file) =>
        value.includes(file.url)
      );

      const urls = uploadedFiles.map((file) => file.url);

      const urlsToFetch = value.filter((url) => !urls.includes(url));

      if (urlsToFetch.length > 0) {
        const newFiles = await Promise.all(
          urlsToFetch.map(async (url) => {
            try {
              const file = await getFileFromUrl(url);

              return {
                id: `uploaded-${url}`,
                url,
                name: file.name,
                size: file.size,
                preview: url,
              };
            } catch (error) {
              return {
                id: `uploaded-${url}`,
                url,
                name: getFilenameFromUrl(url),
                size: 0,
                error: "Failed to load image",
              };
            }
          })
        );

        setUploadedFiles([...fetchedFiles, ...newFiles]);
      } else {
        setUploadedFiles(fetchedFiles);
      }
    };

    void syncUploadedFiles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const totalFiles = uploadedFiles.length + uploadingFiles.length;
  const canUploadMore = totalFiles < maxFiles;

  const handleUpload = useCallback(
    async (files: File[]): Promise<void> => {
      const filesToUpload = files.slice(0, maxFiles - totalFiles);

      if (filesToUpload.length === 0) return;

      const newUploadingFiles: UploadingFile[] = filesToUpload.map((file) => ({
        id: `uploading-${Date.now()}-${file.name}`,
        file,
        progress: 0,
        loaded: 0,
        size: file.size,
        name: file.name,
      }));

      setUploadingFiles((prev) => [...prev, ...newUploadingFiles]);

      for (const uploadingFile of newUploadingFiles) {
        try {
          setUploadingFiles((prev) =>
            prev.map((f) =>
              f.id === uploadingFile.id ? { ...f, progress: 5 } : f
            )
          );

          const { data } = await generatePresignedUploadUrl({
            variables: {
              type: "rewards",
              filename: uploadingFile.file.name,
              contentType: uploadingFile.file.type,
            },
          });

          if (!data?.generatePresignedUploadUrl) {
            throw new Error("Failed to get upload URL");
          }

          const uploadData = data.generatePresignedUploadUrl;
          const { url, fields } = uploadData as {
            url: string;
            fields: Record<string, string>;
          };

          const xhr = new XMLHttpRequest();

          xhrRefs.current[uploadingFile.id] = xhr;

          xhr.open("POST", url);

          const formData = new FormData();

          for (const [key, val] of Object.entries(fields)) {
            formData.append(key, val);
          }

          formData.append("file", uploadingFile.file);

          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
              const percent = Math.round((event.loaded / event.total) * 100);

              setUploadingFiles((prev) =>
                prev.map((f) =>
                  f.id === uploadingFile.id
                    ? { ...f, progress: percent, loaded: event.loaded }
                    : f
                )
              );
            }
          };

          xhr.onload = () => {
            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
            delete xhrRefs.current[uploadingFile.id];

            if (xhr.status === 204 || xhr.status === 201) {
              const publicUrl = `${url}${fields.key}`;

              setUploadingFiles((prev) =>
                prev.filter((f) => f.id !== uploadingFile.id)
              );

              const newUrls = [...value, publicUrl];

              setUploadedFiles((prev) => [
                ...prev,
                {
                  id: `uploaded-${publicUrl}`,
                  url: publicUrl,
                  name: uploadingFile.name,
                  size: uploadingFile.size,
                  preview: publicUrl,
                },
              ]);

              onChange(newUrls);
            } else {
              setUploadingFiles((prev) =>
                prev.map((f) =>
                  f.id === uploadingFile.id
                    ? { ...f, error: `Upload failed with status ${xhr.status}` }
                    : f
                )
              );
            }
          };

          xhr.onerror = () => {
            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
            delete xhrRefs.current[uploadingFile.id];
            setUploadingFiles((prev) =>
              prev.map((f) =>
                f.id === uploadingFile.id
                  ? { ...f, error: "Network error during upload" }
                  : f
              )
            );
          };

          xhr.open("POST", url);
          xhr.send(formData);
        } catch (error) {
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          delete xhrRefs.current[uploadingFile.id];
          setUploadingFiles((prev) =>
            prev.map((f) =>
              f.id === uploadingFile.id
                ? {
                    ...f,
                    error:
                      error instanceof Error ? error.message : "Upload failed",
                  }
                : f
            )
          );
        }
      }
    },
    [generatePresignedUploadUrl, onChange, value, maxFiles, totalFiles]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept,
    maxFiles: maxFiles - totalFiles,
    maxSize,
    disabled: disabled || !canUploadMore,
    onDrop: (acceptedFiles, fileRejections) => {
      setRejectedFiles(fileRejections);

      if (acceptedFiles.length > 0) {
        void handleUpload(acceptedFiles);
      }
    },
  });

  const handleRemoveFile = useCallback(
    (fileId: string) => {
      const uploadingFile = uploadingFiles.find((f) => f.id === fileId);

      if (uploadingFile) {
        const xhr = xhrRefs.current[fileId];

        if (xhr) {
          xhr.abort();
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          delete xhrRefs.current[fileId];
        }

        setUploadingFiles((prev) => prev.filter((f) => f.id !== fileId));

        return;
      }

      const uploadedFile = uploadedFiles.find((f) => f.id === fileId);

      if (uploadedFile) {
        const newUrls = value.filter((url) => url !== uploadedFile.url);

        onChange(newUrls);
      }
    },
    [onChange, uploadingFiles, uploadedFiles, value]
  );

  return (
    <div className={cn("space-y-4", className)}>
      {uploadedFiles.length > 0 && (
        <div className="sm:grid-cols-2 lg:grid-cols-3 grid grid-cols-1 gap-3">
          {uploadedFiles.map((file) => (
            <ImageItem key={file.id} file={file} onRemove={handleRemoveFile} />
          ))}
        </div>
      )}

      {uploadingFiles.length > 0 && (
        <div className="sm:grid-cols-2 lg:grid-cols-3 grid grid-cols-1 gap-3">
          {uploadingFiles.map((file) => (
            <ImageItem key={file.id} file={file} onRemove={handleRemoveFile} />
          ))}
        </div>
      )}

      {canUploadMore && (
        <div
          {...getRootProps()}
          className={cn(
            "cursor-pointer transition-colors",
            isDragActive && "ring-2 ring-blue-500 ring-offset-2",
            disabled && "cursor-not-allowed opacity-50"
          )}
        >
          <input {...getInputProps()} />
          <DropzoneEmptyState
            maxFiles={maxFiles}
            maxSize={maxSize}
            accept={accept}
            rejectedFiles={rejectedFiles}
          />
        </div>
      )}

      {maxFiles > 1 && (
        <p className="text-xs text-gray-500">
          {totalFiles} of {maxFiles} files uploaded
        </p>
      )}
    </div>
  );
};

export default ImagesUpload;
