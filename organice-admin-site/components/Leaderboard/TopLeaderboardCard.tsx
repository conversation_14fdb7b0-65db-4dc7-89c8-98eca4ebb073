import cn from "classnames";
import { FC, ReactNode } from "react";

import Avatar from "../Avatar";

import LeaderboardNumberIcon from "./LeaderboardNumberIcon";

export interface TopCardActor {
  id: string;
  realName: string;
  avatarUrl?: string | null;
}

interface Props {
  order: number;
  member: TopCardActor;
  renderPoints: () => ReactNode;
}

const TopLeaderboardCard: FC<Props> = ({
  order,
  member: { avatarUrl, realName },
  renderPoints,
}) => {
  return (
    <div
      className={cn("relative rounded-lg p-3", {
        "bg-yellow-100": order === 1,
        "bg-main-100": order === 2,
        "bg-orange-50": order === 3,
      })}
    >
      <div className="flex justify-between gap-4">
        <div className="flex items-center gap-2">
          <Avatar photoUrl={avatarUrl} size="s" rounded="full" />
          <span className="text-sm font-semibold">{realName}</span>
        </div>

        <LeaderboardNumberIcon value={order} />
      </div>

      <div className="mt-2">{renderPoints()}</div>
    </div>
  );
};

export default TopLeaderboardCard;
