import cn from "classnames";
import React from "react";

import EmptyLeaderboard from "../../public/svg/kudos/emptyLeaderboard.svg";
import EmptyPageState from "../EmptyPageState";
import Skeleton from "../Loading/Skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../Table/Table";

export interface LeaderboardMember {
  id: string;
  realName: string;
  avatarUrl?: string | null;
  quantity: number;
}

export interface Column {
  key: string;
  displayLabel: string;
  className?: string;
  render: (member: LeaderdboardRow, index: number) => React.ReactNode;
}

interface LeaderboardProps {
  data: LeaderboardMember[];
  isLoading?: boolean;
  emptyComponent: React.ReactNode;
  leaderComponent: React.ComponentType<{
    member: LeaderboardMember;
    order: number;
  }>;
  columns: Column[];
  dataTest?: string;
}

interface LeaderdboardRow extends LeaderboardMember {
  rank: number;
}

const LeaderboardSkeleton: React.FC = () => {
  return (
    <>
      <div className="grid grid-cols-3 gap-3">
        {Array(3)
          .fill(null)
          .map((_, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <Skeleton key={index} className="h-[120px] w-full !bg-main-200" />
          ))}
      </div>
      <div className="mt-4 flex grow flex-col">
        {new Array(10).fill(null).map((_el, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <div key={index} className="h-10 py-2">
            <Skeleton className="h-full w-full !bg-main-200" />
          </div>
        ))}
      </div>
    </>
  );
};

const Leaderboard: React.FC<LeaderboardProps> = ({
  data,
  isLoading,
  emptyComponent,
  leaderComponent: LeaderComponent,
  columns,
  dataTest,
}) => {
  if (isLoading) {
    return (
      <div className="p-4">
        <LeaderboardSkeleton />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <EmptyPageState emptyStateIcon={<EmptyLeaderboard />}>
        {emptyComponent}
      </EmptyPageState>
    );
  }

  const leaders = data.slice(0, 3);
  const rest = data.slice(3);

  return (
    <div className="p-4" data-test={dataTest}>
      <div className="grid grid-cols-3 gap-3">
        {leaders.map((member, idx) => (
          <LeaderComponent key={member.id} order={idx + 1} member={member} />
        ))}
      </div>
      {rest.length > 0 ? (
        <Table className="mt-4">
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead
                  key={column.key}
                  className={cn("py-2", column.className)}
                >
                  {column.displayLabel}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {rest.map((member, idx) => (
              <TableRow
                key={member.id}
                className={cn({
                  "bg-main-100": idx % 2,
                })}
              >
                {columns.map((column) => (
                  <TableCell
                    key={column.key}
                    className={cn("py-2", column.className)}
                  >
                    {column.render({ ...member, rank: idx + 4 }, idx)}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : null}
    </div>
  );
};

export default Leaderboard;
