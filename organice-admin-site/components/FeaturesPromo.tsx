import Image from "next/image";
import { useRouter } from "next/router";
import { FC, useMemo } from "react";

import { useMixpanelContext } from "../context/MixpanelContext";
import { useSessionContext } from "../context/SessionContext";
import { SupportedFeature } from "../graphql/client.generated";
import { useLocalStorage } from "../hooks/useLocalStorage";

import Button from "./Button";
import CloseButton from "./Button/CloseButton";

const FeaturesPromo: FC = () => {
  const { session } = useSessionContext();
  const router = useRouter();
  const { track } = useMixpanelContext();

  const [rewardsPromoHidden, setRewardsPromoHidden] = useLocalStorage(
    "hidden-promos:rewards",
    false
  );

  const showBanner = useMemo(() => {
    if (rewardsPromoHidden || !session) {
      return false;
    }

    const { onboarding, me } = session;
    const { bookmarkedFeatures } = onboarding;

    const isRewardsEnabled = bookmarkedFeatures.includes(
      SupportedFeature.Rewards
    );

    if (isRewardsEnabled || !me.isAdmin) {
      return false;
    }

    const hasRequiredFeatures = [
      SupportedFeature.Kudos,
      SupportedFeature.Surveys,
      SupportedFeature.OrgChart,
    ].some((feature) => bookmarkedFeatures.includes(feature));

    if (!hasRequiredFeatures) {
      return false;
    }

    const allowedPaths = [
      "/users",
      "/calendar",
      "/kudos",
      "/surveys",
      "/org-chart",
    ];
    const isAllowedPath =
      allowedPaths.some((path) => router.pathname.startsWith(path)) ||
      router.pathname === "/";

    const isAllowedSettingsPath =
      router.pathname.startsWith("/settings") &&
      !router.pathname.startsWith("/settings/rewards") &&
      !router.pathname.startsWith("/settings/pricing");

    return isAllowedPath || isAllowedSettingsPath;
  }, [router.pathname, session, rewardsPromoHidden]);

  return showBanner ? (
    <div
      className="shadow-lg absolute bottom-4 right-4 z-10 h-[365px] w-[365px] cursor-pointer overflow-hidden rounded-lg border border-violet-600 bg-violet-100 p-5"
      data-test="features-promo"
    >
      <CloseButton
        className="absolute top-5 right-5 z-20"
        onClick={() => setRewardsPromoHidden(true)}
      />
      <div className="relative z-10 flex h-full flex-col justify-between">
        <div>
          <div className="mb-2 text-lg font-bold">New! Rewards are here 🎁</div>
          <p className="text-main-500">
            Recognize achievements and motivate your team with rewards.
          </p>
        </div>
        <div>
          <Button
            onClick={() => {
              track("clicked try rewards from promo");
              void router.push("/settings/rewards");
            }}
          >
            Try rewards
          </Button>
        </div>
      </div>

      <Image
        src="/png/rewards/Rewards Promo.png"
        alt="Try rewards"
        priority
        width={365}
        height={365}
        className="absolute bottom-0 right-0 z-0"
      />
    </div>
  ) : null;
};

export default FeaturesPromo;
