enum RewardActivityType {
  KUDOS
  PROFILE
  SURVEY
}

type RewardActivity {
  type: RewardActivityType!
  points: Int!
}

type Reward {
  id: ID!
  name: String!
  description: String
  imageUrl: String
  points: Int!
  isActive: Boolean!
}

type RewardTemplate {
  id: ID!
  name: String!
  description: String
  imageUrl: String
  points: Int!
}

type RewardsSettings {
  sendPointsNotifications: Boolean!
  activities: [RewardActivity!]!
  rewards: [Reward!]!
}

type MemberCoinsEntry {
  member: Member!
  place: Int!
  lastActivity: Date
}

type CoinsLeaderboardEntry {
  member: Member!
  coins: Int!
}

extend type Query {
  rewardsSettings: RewardsSettings!
  rewardTemplates: [RewardTemplate!]!
  membersCoins: [MemberCoinsEntry!]!
  coinEarners(first: Int!, from: Date, to: Date): [CoinsLeaderboardEntry!]!
  coinSpenders(first: Int!, from: Date, to: Date): [CoinsLeaderboardEntry!]!
  rewardRequests(from: Date, to: Date): [RewardRequest!]!
}

input RewardInput {
  id: ID
  name: String!
  description: String
  imageUrl: String
  points: Int!
  isActive: Boolean!
}

input ActivityInput {
  type: RewardActivityType!
  points: Int!
}

enum RewardRequestStatus {
  PENDING
  APPROVED
  REJECTED
}

type RewardRequest {
  id: ID!
  member: Member!
  reward: Reward!
  status: RewardRequestStatus!
  comment: String
  createdAt: Date!
  updatedAt: Date!
  rejectReason: String
}

input updateCoinsBalanceInput {
  memberId: ID!
  balance: Int!
}

extend type Mutation {
  updateRewardsSettings(
    sendPointsNotifications: Boolean!
    activities: [ActivityInput!]!
  ): RewardsSettings!
  addOrUpdateReward(data: RewardInput!): RewardsSettings!
  deleteReward(id: ID!): Void
  approveRewardRequest(id: ID!): [RewardRequest!]!
  rejectRewardRequest(id: ID!, reason: String): RewardRequest!
  deleteRewardRequest(id: ID!): Void
  updateCoinsBalance(balance: updateCoinsBalanceInput!): [MemberCoinsEntry!]!
  openRewardsInSlackApp: Void
}
