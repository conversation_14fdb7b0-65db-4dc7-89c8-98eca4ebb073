import assert from "assert";

import {
  ReadonlyDeep,
  Workspace,
  RewardActivityType as DomainRewardActivityType,
  RewardActivity as DomainRewardActivity,
  RewardRequest as DomainRewardRequest,
  HomePages,
} from "@organice/core/domain";
import {
  ActivityEvent,
  ActivityEventType,
  isCoinsBalanceChangedData,
  isRewardRequestChangeData,
  CoinsBalanceChangedType,
  RewardRequestChangedType,
} from "@organice/core/domain/activityLog";
import {
  addOrUpdateReward,
  deleteReward,
  updateRewardsSettings,
  getMembersCoins,
  updateCoinsBalance,
  getMemberCoins,
  MemberCoinsEntry as DomainMemberCoinsEntry,
  approveRewardRequest,
  rejectRewardRequest,
  deleteRewardRequest,
} from "@organice/core/domain/rewards";

import { getSessionWorkspace, Session } from "../../../domain";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import {
  QueryResolvers,
  MutationResolvers,
  RewardsSettings,
  RewardActivityType,
  RewardTemplate,
  MemberCoinsEntry,
  RewardActivity,
  RewardRequest,
  CoinsLeaderboardEntry,
  RewardRequestStatus,
} from "../../server.generated";

import { formatMember, Context } from "./_common";

enum RewardsImages {
  ProductivityBook = "productivity-book-image",
  CoffeeTeaSet = "coffee-tea-set-image",
  DeskUpgradeKit = "desk-upgrade-kit-image",
  WellnessGoodie = "wellness-goodie-image",
  ExperienceVoucher = "experience-voucher-image",
}

async function getTopMembersByActivity(
  { repository, session, activityLog }: Context,
  args: { first: number; from?: string | null; to?: string | null },
  eventTypes: ActivityEventType[],
  processEvents: (
    events: {
      type: string;
      data: ReadonlyDeep<ActivityEvent["data"]>;
    }[]
  ) => Record<string, number>
): Promise<ReadonlyDeep<CoinsLeaderboardEntry>[]> {
  const workspace = await getSessionWorkspace(repository, session);
  const { first, from, to } = args;
  const lastYear = new Date();

  lastYear.setFullYear(lastYear.getFullYear() - 1);

  const activityEvents = await activityLog.getRawActivityEvents({
    from: from ?? lastYear.toISOString(),
    to: to ?? new Date().toISOString(),
    types: eventTypes,
  });

  const memberValues = processEvents(
    activityEvents.map((log) => ({
      type: log.type,
      data: log.data as unknown,
    })) as ReadonlyDeep<ActivityEvent>[]
  );

  return Object.entries(memberValues)
    .map(([id, value]) => ({ id, value }))
    .sort((a, b) => b.value - a.value)
    .slice(0, first)
    .map((item) => {
      const member = workspace.members.find((m) => m.id === item.id);

      assert(member, "Member not found");

      return {
        member: formatMember({
          workspace,
          member,
          session,
        }),
        coins: item.value,
      };
    });
}

export const Query = {
  async rewardsSettings(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatRewardsSettings(workspace);
  },
  async rewardTemplates() {
    return Promise.resolve(formatRewardTemplates());
  },

  async membersCoins(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const membersCoins = getMembersCoins(workspace);

    return membersCoins.map((item) =>
      formatMemberCoinsEntry(workspace, session, item)
    );
  },

  async coinEarners(_parent, args, context) {
    return getTopMembersByActivity(
      context,
      args,
      [ActivityEventType.coinsBalanceChanged],
      (events) =>
        events
          .filter(isCoinsBalanceChangedData)
          .reduce<Record<string, number>>((acc, curr) => {
            let earned = 0;

            if (curr.data.updateType === CoinsBalanceChangedType.manual) {
              if (
                curr.data.absolute &&
                curr.data.absolute > curr.data.prevBalance
              ) {
                earned = curr.data.absolute - curr.data.prevBalance;
              }
            } else if (curr.data.increase && curr.data.increase > 0) {
              earned = curr.data.increase;
            }

            if (earned > 0) {
              acc[curr.data.memberId] = (acc[curr.data.memberId] ?? 0) + earned;
            }

            return acc;
          }, {})
    );
  },

  async coinSpenders(_parent, args, context) {
    const workspace = await getSessionWorkspace(
      context.repository,
      context.session
    );

    return getTopMembersByActivity(
      context,
      args,
      [ActivityEventType.rewardRequestChanged],
      (events) =>
        events
          .filter(isRewardRequestChangeData)
          .filter((e) => e.data.updateType === RewardRequestChangedType.approve)
          .reduce<Record<string, number>>((acc, curr) => {
            const rewardRequest = workspace.rewardsRequests.find(
              (rr) => rr.id === curr.data.requestId
            );

            if (!rewardRequest) return acc;

            const reward = workspace.rewardsSettings.rewards.find(
              (r) => r.id === rewardRequest.rewardId
            );

            if (!reward) return acc;

            acc[curr.data.memberId] =
              (acc[curr.data.memberId] ?? 0) + reward.points;

            return acc;
          }, {})
    );
  },

  async rewardRequests(_parent, args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const { from, to } = args;
    const statusOrder = {
      [RewardRequestStatus.Pending]: 0,
      [RewardRequestStatus.Approved]: 1,
      [RewardRequestStatus.Rejected]: 2,
    };

    const requests = workspace.rewardsRequests
      .filter((rr) => {
        const createdAt = new Date(rr.createdAt as Date);

        if (from && createdAt < new Date(from)) {
          return false;
        }

        if (to && createdAt > new Date(to)) {
          return false;
        }

        return true;
      })
      .sort((a, b) => {
        const statusA = statusOrder[a.status];
        const statusB = statusOrder[b.status];

        if (statusA !== statusB) {
          return statusA - statusB;
        }

        return b.createdAt.getTime() - a.createdAt.getTime();
      });

    return requests.map((request) =>
      formatRewardRequest(workspace, session, request)
    );
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async openRewardsInSlackApp(_parent, _args, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    logger = logger.withContext({ workspace });

    const member = workspace.members.find((m) => {
      return m.id === session.memberId;
    });

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    logger.info(`Member opened rewards in slack app`);
    assert(member, `Expected member with id "${session.memberId}" to exist`);

    await slackAdapter.renderActiveTab(workspace, member, null, (pages) =>
      pages[HomePages.Rewards](member.isAdmin)
    );
  },

  async updateRewardsSettings(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const activities = args.activities.map((activity) => ({
      type: activity.type as DomainRewardActivityType,
      points: activity.points,
    }));

    workspace = updateRewardsSettings(workspace, {
      sendPointsNotifications: args.sendPointsNotifications,
      activities,
    });

    await repository.setWorkspace(workspace);

    return formatRewardsSettings(workspace);
  },

  async addOrUpdateReward(
    _parent,
    args,
    { repository, session, activityLog, logger }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    workspace = await addOrUpdateReward({
      workspace,
      input: {
        id: args.data.id ?? undefined,
        name: args.data.name,
        description: args.data.description ?? undefined,
        imageUrl: args.data.imageUrl ?? undefined,
        points: args.data.points,
        isActive: args.data.isActive,
      },
      slackAdapter,
      activityLog,
      logger,
    });

    await repository.setWorkspace(workspace);

    return formatRewardsSettings(workspace);
  },

  async deleteReward(
    _parent,
    args,
    { repository, session, activityLog, logger }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    workspace = await deleteReward({
      workspace,
      rewardId: args.id,
      slackAdapter,
      activityLog,
      logger,
    });

    await repository.setWorkspace(workspace);

    return null;
  },

  async updateCoinsBalance(
    _parent,
    args,
    { repository, session, activityLog, logger }
  ) {
    const workspace = await getSessionWorkspace(repository, session);
    const member = workspace.members.find(
      (m) => m.id === args.balance.memberId
    );

    if (!member) {
      throw new Error("Editor not found");
    }

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    const [, updatedWorkspace] = updateCoinsBalance({
      workspace,
      member,
      input: {
        initiatorId: session.memberId,
        absolute: args.balance.balance,
        type: CoinsBalanceChangedType.manual,
      },
      slackAdapter,
      activityLog,
      logger,
    });

    await repository.setWorkspace(updatedWorkspace);
    const leaderboardEntry = getMemberCoins(
      updatedWorkspace,
      args.balance.memberId
    );

    assert(leaderboardEntry, "Leaderboard entry not found");

    const leaderboard = getMembersCoins(updatedWorkspace);

    return leaderboard.map((item) =>
      formatMemberCoinsEntry(updatedWorkspace, session, item)
    );
  },

  async approveRewardRequest(
    _parent,
    args,
    { repository, session, activityLog, logger }
  ) {
    const workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    const [updatedWorkspace, requests] = await approveRewardRequest({
      workspace,
      input: {
        requestId: args.id,
        initiatorId: session.memberId,
      },
      activityLog,
      slackAdapter,
      logger,
    });

    await repository.setWorkspace(updatedWorkspace);

    return requests.map((request) =>
      formatRewardRequest(updatedWorkspace, session, request)
    );
  },

  async rejectRewardRequest(
    _parent,
    args,
    { repository, session, activityLog, logger }
  ) {
    const workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    const [updatedWorkspace, rewardRequest] = await rejectRewardRequest({
      workspace,
      input: {
        rewardRequestId: args.id,
        rejectReason: args.reason ?? undefined,
        initiatorId: session.memberId,
      },
      activityLog,
      slackAdapter,
      logger,
    });

    await repository.setWorkspace(updatedWorkspace);

    return formatRewardRequest(updatedWorkspace, session, rewardRequest);
  },

  async deleteRewardRequest(
    _parent,
    args,
    { repository, session, activityLog, logger }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    workspace = await deleteRewardRequest({
      workspace,
      input: {
        rewardRequestId: args.id,
        initiatorId: session.memberId,
      },
      activityLog,
      slackAdapter,
      logger,
    });

    await repository.setWorkspace(workspace);

    return null;
  },
} satisfies MutationResolvers<Context>;

function formatRewardsSettings(
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<RewardsSettings> {
  return {
    sendPointsNotifications: workspace.rewardsSettings.sendPointsNotifications,
    activities: workspace.rewardsSettings.activities.map((activity) => ({
      type: activity.type as unknown as RewardActivityType,
      points: activity.points,
    })),
    rewards: workspace.rewardsSettings.rewards,
  };
}

export function formatRewardsActivity(
  activity: DomainRewardActivity
): ReadonlyDeep<RewardActivity> {
  return {
    type: activity.type as unknown as RewardActivityType,
    points: activity.points,
  };
}

function formatRewardTemplates(): ReadonlyDeep<RewardTemplate[]> {
  return [
    {
      id: RewardsImages.ProductivityBook,
      name: "Productivity Book",
      description:
        "A bestselling productivity or self-improvement book, packed with insights to help you sharpen your focus and achieve more with less effort",
      imageUrl: RewardsImages.ProductivityBook,
      points: 10,
    },
    {
      id: RewardsImages.CoffeeTeaSet,
      name: "Premium Coffee / Tea Set",
      description:
        "A curated selection of specialty coffee or fine teas to energize your mornings and make your daily routine more enjoyable",
      imageUrl: RewardsImages.CoffeeTeaSet,
      points: 25,
    },
    {
      id: RewardsImages.DeskUpgradeKit,
      name: "Desk Upgrade Kit",
      description:
        "A set of stylish and practical desk items, like a notebook, quality pens, or even a small plant to refresh your workspace and boost creativity",
      imageUrl: RewardsImages.DeskUpgradeKit,
      points: 15,
    },
    {
      id: RewardsImages.WellnessGoodie,
      name: "Wellness Goodie",
      description:
        "A thoughtful wellness item, such as a yoga accessory, foam roller, or high-quality water bottle, to support balance and wellbeing",
      imageUrl: RewardsImages.WellnessGoodie,
      points: 30,
    },
    {
      id: RewardsImages.ExperienceVoucher,
      name: "Experience Voucher",
      description:
        "A ticket or voucher for a fun experience, like the cinema, a local activity, or an online course, because memories and learning last longer than things",
      imageUrl: RewardsImages.ExperienceVoucher,
      points: 20,
    },
  ];
}

function formatMemberCoinsEntry(
  workspace: ReadonlyDeep<Workspace>,
  session: Session,
  entry: DomainMemberCoinsEntry
): ReadonlyDeep<MemberCoinsEntry> {
  return {
    member: formatMember({
      workspace,
      member: entry.member,
      session,
    }),
    place: entry.place,
    lastActivity: entry.lastActivity ? entry.lastActivity.toDateString() : null,
  };
}

export function formatRewardRequest(
  workspace: ReadonlyDeep<Workspace>,
  session: Session,
  request: ReadonlyDeep<DomainRewardRequest>
): ReadonlyDeep<RewardRequest> {
  const member = workspace.members.find((m) => m.id === request.memberId);
  const reward = workspace.rewardsSettings.rewards.find(
    (r) => r.id === request.rewardId
  );

  assert(member, "Member not found");
  assert(reward, "Reward not found");

  return {
    id: request.id,
    member: formatMember({
      workspace,
      member,
      session,
    }),
    reward,
    status: request.status as unknown as RewardRequest["status"],
    comment: request.comment ?? undefined,
    createdAt: request.createdAt.toISOString(),
    updatedAt: request.updatedAt.toISOString(),
    rejectReason: request.rejectReason ?? undefined,
  };
}
