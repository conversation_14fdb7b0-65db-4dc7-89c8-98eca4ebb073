fragment RewardsSettingsFragment on RewardsSettings {
  sendPointsNotifications
  activities {
    type
    points
  }
  rewards {
    id
    name
    description
    imageUrl
    points
    isActive
  }
}

query RewardsSettingsPage {
  rewardsSettings {
    ...RewardsSettingsFragment
  }
  rewardTemplates {
    id
    name
    description
    imageUrl
    points
  }
}

query GetPendingRewardRequests {
  rewardRequests {
    id
    status
    reward {
      id
    }
  }
}

mutation UpdateRewardsSettings(
  $sendPointsNotifications: Boolean!
  $activities: [ActivityInput!]!
) {
  updateRewardsSettings(
    sendPointsNotifications: $sendPointsNotifications
    activities: $activities
  ) {
    ...RewardsSettingsFragment
  }
}

mutation AddOrUpdateReward($data: RewardInput!) {
  addOrUpdateReward(data: $data) {
    ...RewardsSettingsFragment
  }
}

mutation DeleteReward($id: ID!) {
  deleteReward(id: $id)
}

mutation GeneratePresignedUploadUrl(
  $type: String!
  $filename: String!
  $contentType: String!
) {
  generatePresignedUploadUrl(
    type: $type
    filename: $filename
    contentType: $contentType
  ) {
    url
    fields
  }
}
