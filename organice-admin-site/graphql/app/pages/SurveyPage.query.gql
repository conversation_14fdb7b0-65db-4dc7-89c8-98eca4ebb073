query SurveyPage($survey: SurveyInput!) {
  surveys(survey: $survey) {
    id
    isAnonymous
    instances {
      ...SurveyInstanceFields
    }
    ...SurveyCardFields
  }
}

query SurveyStatsPage($survey: SurveyInput!, $surveyId: String!) {
  surveys(survey: $survey) {
    isAnonymous
    instances {
      ...SurveyInstanceFields
    }
  }
  answersStats(surveyId: $surveyId) {
    questionId
    surveyInstanceId
    questionTitle
    totalResponsesNumber
    singleOptions {
      id
      value
      responsesNumber
    }
    multipleOptions {
      id
      value
      responsesNumber
    }
    scaleOptions {
      id
      value
      responsesNumber
      label
    }
    rawTextAnswers {
      text
      responder {
        id
        realName
        photo72Url
      }
    }
  }
}

query SurveyAnswersPage($survey: SurveyInput!) {
  surveys(survey: $survey) {
    id
    isAnonymous
    instances {
      responders {
        id
        realName
        title
        photo72Url
      }
    }
  }
}

query SurveyResponderAnswers($survey: SurveyInput) {
  surveys(survey: $survey) {
    id
    instances {
      ...SurveyInstanceFields
      answers {
        ...SurveyAnswerFields
      }
    }
  }
}

fragment SurveyAnswerFields on SurveyAnswer {
  id
  responder {
    id
  }
  question {
    ...SurveyQuestionFields
  }
  rawTextValue
  scaleValue {
    id
    value
  }
  singleValue {
    id
    value
  }
  multipleValue {
    id
    value
  }
}

fragment SurveyQuestionFields on SurveyQuestion {
  id
  title
  required
  type
  singleOptions {
    id
    value
  }
  multipleOptions {
    id
    value
  }
  scaleOptions {
    id
    value
    label
  }
}
