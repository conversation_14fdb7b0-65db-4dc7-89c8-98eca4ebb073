query RewardsRequestsPage($from: Date, $to: Date) {
  rewardRequests(from: $from, to: $to) {
    id
    member {
      id
      realName
      photo72Url
      title
      coinsBalance {
        balance
      }
    }
    reward {
      id
      name
      points
    }
    status
    comment
    rejectReason
    createdAt
    updatedAt
  }
}

mutation approveRewardRequest($id: ID!) {
  approveRewardRequest(id: $id) {
    id
    status
    updatedAt
  }
}

mutation rejectRewardRequest($id: ID!, $reason: String) {
  rejectRewardRequest(id: $id, reason: $reason) {
    id
    status
    rejectReason
    updatedAt
  }
}

mutation deleteRewardRequest($id: ID!) {
  deleteRewardRequest(id: $id)
}
