query RewardsCoinsPage {
  rewardsSettings {
    activities {
      type
      points
    }
  }
  membersCoins {
    member {
      id
      ...UpdatableMemberFields
      fields {
        id
        key
        ... on PhotoField {
          url72
        }
      }
      coinsBalance {
        coinsEarned
        coinsSpent
        balance
      }
    }
    place
    lastActivity
  }
}

mutation UpdateMemberPoints($balance: updateCoinsBalanceInput!) {
  updateCoinsBalance(balance: $balance) {
    member {
      id
      coinsBalance {
        coinsEarned
        coinsSpent
        balance
      }
    }
    place
    lastActivity
  }
}
