import {
  NoOpProductOwnerNotifier,
  ProductOwnerNotifier,
} from "@organice/core/domain";
import SlackWebhookProductOwnerNotifier from "@organice/core/slack/SlackWebhookProductOwnerNotifier";
import { NextApiRequest, NextApiResponse } from "next";

function getProductOwnerNotifier(): ProductOwnerNotifier {
  return process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
    ? new SlackWebhookProductOwnerNotifier(
        process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
      )
    : new NoOpProductOwnerNotifier();
}

export default async function collectEmail(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });

    return;
  }

  const { fullName, email, companyName } = req.body as {
    fullName?: string;
    email?: string;
    companyName?: string;
  };

  if (!fullName || !email || !companyName) {
    res.status(400).json({ error: "Missing required fields" });

    return;
  }

  const productOwnerNotifier = getProductOwnerNotifier();

  await productOwnerNotifier.handleEmailCollection({
    fullName,
    email,
    companyName,
  });

  res.status(200).json({ success: true });
}
