import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";

import RewardsSettings from "../../components/Settings/Rewards";
import { NextPageWithLayout } from "../../components/layouts/Layout";
import {
  SettingsPageLayout,
  getHeader,
} from "../../components/layouts/SettingsLayout";
import { redirectIfNotAdmin } from "../../helpers/redirectIfNotAdmin";
import { redirectIfOnboardingIsUnfinished } from "../../helpers/redirectIfOnboardingIsUnfinished";
import { redirectIfUnauthenticated } from "../../helpers/redirectIfUnauthenticated";
import { redirectIfWorkspaceIsSuspended } from "../../helpers/redirectIfWorkspaceIsSuspended";

const RewardsSettingsPage: NextPageWithLayout = () => {
  return <RewardsSettings />;
};

RewardsSettingsPage.getLayoutContent = SettingsPageLayout;
RewardsSettingsPage.getHeader = getHeader;
RewardsSettingsPage.getNavigation = () => null;

export default RewardsSettingsPage;

export async function getServerSideProps(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<unknown>> {
  const redirect =
    (await redirectIfUnauthenticated(ctx)) ??
    (await redirectIfOnboardingIsUnfinished(ctx)) ??
    (await redirectIfNotAdmin(ctx)) ??
    (await redirectIfWorkspaceIsSuspended(ctx));

  if (redirect) {
    return redirect;
  }

  return { props: {} };
}
