import cn from "classnames";
import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";
import { useCallback, useState, useMemo, useEffect } from "react";
import {
  DynamicDataSheetGrid,
  keyColumn,
  Column,
  intColumn,
} from "react-datasheet-grid";
import { Operation } from "react-datasheet-grid/dist/types";

import Button from "../../components/Button";
import ClientOnlyPortal from "../../components/ClientOnlyPortal";
import DateColumn from "../../components/Columns/DateColumn";
import { copyDate } from "../../components/Columns/utils";
import CountDisplay from "../../components/CountDisplay";
import MultipleFiltersDropdown from "../../components/MultipleFiltersDropdown";
import BalanceColumn from "../../components/Rewards/BalanceColumn";
import PlaceColumn from "../../components/Rewards/PlaceColumn";
import SingleSection from "../../components/SingleSection";
import TableSkeleton from "../../components/TableSkeleton";
import MemberColumn from "../../components/Users/<USER>";
import TitleCell from "../../components/Users/<USER>";
import { NextPageWithLayout } from "../../components/layouts/Layout";
import { getHeader, PageData } from "../../components/layouts/RewardsLayout";
import { useActiveSidebarContext } from "../../context/ActiveSidebarContext";
import { useConfirm } from "../../context/ConfirmContext";
import { usePageContext } from "../../context/PageContext";
import { useSessionContext } from "../../context/SessionContext";
import {
  useRewardsCoinsPageQuery,
  useUpdateMemberPointsMutation,
} from "../../graphql/client.generated";
import downloadCsv from "../../helpers/downloadCsv";
import {
  filterMembers,
  sortMembers,
} from "../../helpers/filterEditableFlatMembers";
import {
  flattenEditableMember,
  EditableFlatMember,
} from "../../helpers/flattenMember";
import { redirectIfNotAdmin } from "../../helpers/redirectIfNotAdmin";
import { redirectIfOnboardingIsUnfinished } from "../../helpers/redirectIfOnboardingIsUnfinished";
import { redirectIfUnauthenticated } from "../../helpers/redirectIfUnauthenticated";
import { redirectIfWorkspaceIsSuspended } from "../../helpers/redirectIfWorkspaceIsSuspended";
import useDebouncedCallback from "../../hooks/useDebouncedCallback";
import useGetEditableFlatMemberFilters from "../../hooks/useGetEditableFlatMemberFilters";
import useGetMultipleFilterFromRoute from "../../hooks/useGetMultipleFilterFromRoute";
import { useReplaceRouterQuery } from "../../hooks/useReplaceRouterQuery";
import { useToast } from "../../hooks/useToast";

import "react-datasheet-grid/dist/style.css";

type MemberWithPoints = EditableFlatMember & {
  place: number;
  coinsEarned: number;
  coinsSpent: number;
  balance: number;
  lastActivity: Date | null;
};

const LeaderboardPage: NextPageWithLayout = () => {
  const { replaceQuery, router } = useReplaceRouterQuery();
  const { session } = useSessionContext();
  const { setData } = usePageContext<PageData>();
  const { confirm } = useConfirm();
  const toast = useToast();
  const notifyAboutUpdates = useDebouncedCallback(() => {
    toast("success", "All changes were saved successfully");
  }, 1000);

  const selectedTeams = useGetMultipleFilterFromRoute("t");
  const selectedDepartments = useGetMultipleFilterFromRoute("d");
  const selectedCountries = useGetMultipleFilterFromRoute("c");
  const selectedManagers = useGetMultipleFilterFromRoute("m");
  const [updateCoinsBalance] = useUpdateMemberPointsMutation();

  const searchTerm = useMemo(
    () => (router.query.q ?? "") as string,
    [router.query.q]
  );

  const [sort, setSort] = useState<string>(
    router.query.sortBy ? (router.query.sortBy as string) : "coinsEarned:desc"
  );

  const onSortChange = useCallback(
    (newSort: string) => {
      replaceQuery({ sortBy: newSort });
      setSort(newSort);
    },
    [replaceQuery]
  );

  const onResetFilters = useCallback(() => {
    replaceQuery({
      t: undefined,
      d: undefined,
      m: undefined,
      c: undefined,
    });
  }, [replaceQuery]);

  const syncUpdates = async (
    oldRows: MemberWithPoints[],
    newRows: MemberWithPoints[]
  ): Promise<void> => {
    const updates: Promise<unknown>[] = [];

    for (const newRow of newRows) {
      const oldRow = oldRows[newRows.indexOf(newRow)];

      if (newRow.balance !== oldRow.balance) {
        const newBalance = newRow.balance;
        const oldCoinsEarned = oldRow.coinsEarned;
        const oldCoinsSpent = oldRow.coinsSpent;

        updates.push(
          updateCoinsBalance({
            variables: {
              balance: {
                memberId: newRow.id,
                balance: newBalance,
              },
            },
            optimisticResponse: {
              updateCoinsBalance: [
                {
                  __typename: "MemberCoinsEntry",
                  member: {
                    __typename: "Member",
                    id: newRow.id,
                    coinsBalance: {
                      coinsEarned: oldCoinsEarned + newBalance,
                      coinsSpent: oldCoinsSpent,
                      balance: newBalance,
                    },
                  },
                  // NOTE: the place will be recalculated on the server
                  place: oldRow.place,
                  lastActivity: oldRow.lastActivity,
                },
              ],
            },
          })
        );
      }
    }

    try {
      await Promise.all(updates);

      if (updates.length) {
        notifyAboutUpdates();
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast("error", `Operation cancelled, ${err.message}`);
      }
    }
  };

  const handleDataUpdate = (
    newRows: MemberWithPoints[],
    operations: Operation[]
  ): void => {
    const [operation] = operations;
    const { fromRowIndex, toRowIndex } = operation;
    const numberOfAffectedRows = toRowIndex - fromRowIndex;

    (numberOfAffectedRows > 1
      ? confirm({
          message: "Update multiple rows?",
          buttonConfirm: "Update",
          buttonCancel: "Don't update",
        })
      : Promise.resolve()
    )
      .then(() => {
        syncUpdates(
          rows.slice(fromRowIndex, toRowIndex),
          newRows.slice(fromRowIndex, toRowIndex)
        ).catch(() => {});
      })
      .catch(() => {});
  };

  const { data, loading, previousData } = useRewardsCoinsPageQuery({
    fetchPolicy: "cache-and-network",
  });

  const fetchedData = data ?? previousData;

  const members = useMemo(() => {
    return (fetchedData?.membersCoins ?? []).map((item) => {
      const flatMember = flattenEditableMember(item.member);
      const coinsBalance = item.member.coinsBalance;

      return {
        ...flatMember,
        coinsEarned: coinsBalance.coinsEarned,
        coinsSpent: coinsBalance.coinsSpent,
        balance: coinsBalance.balance,
        lastActivity: item.lastActivity,
        place: item.place,
      } as MemberWithPoints;
    });
  }, [fetchedData?.membersCoins]);

  useEffect(() => {
    const { totalPointsEarned, totalPointsSpent, totalPointsAvailable } =
      members.reduce(
        (acc, m) => {
          return {
            totalPointsEarned: acc.totalPointsEarned + m.coinsEarned,
            totalPointsSpent: acc.totalPointsSpent + m.coinsSpent,
            totalPointsAvailable: acc.totalPointsAvailable + m.balance,
          };
        },
        { totalPointsEarned: 0, totalPointsSpent: 0, totalPointsAvailable: 0 }
      );

    setData({
      totalPointsEarned,
      totalPointsSpent,
      totalPointsAvailable,
    });
  }, [members, setData]);

  const totalMembers = session?.stats.totalMembers ?? 0;

  const { activeSidebar, openSidebar } = useActiveSidebarContext();

  const columns: Column<MemberWithPoints>[] = useMemo(() => {
    const cols: Column<MemberWithPoints>[] = [
      {
        id: "member",
        component: MemberColumn,
        columnData: {
          sidebarTooltip: "Click to view member profile",
          /**
           * NOTE: this page is available only for admins,
           * so we can skip the check here
           */
          isActive: (row: MemberWithPoints & { positionId: string }) => {
            return (
              activeSidebar?.type === "member" && row.id === activeSidebar.id
            );
          },

          hasSidebar: () => true,
          openSidebar: (row: MemberWithPoints) => {
            openSidebar({
              type: "member",
              id: row.id,
              tabs: [
                {
                  id: "profile",
                },
                {
                  id: "timeOffs",
                },
                {
                  id: "activityLog",
                  isActive: true,
                },
              ],
              mode: "edit",
              showGoToOrgChartBtn: true,
            });
          },
        },
        title: (
          <TitleCell
            title="Employee"
            field="name"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        copyValue: ({ rowData }: { rowData: MemberWithPoints }) => {
          return rowData.name ?? "";
        },
        disabled: true,
        basis: 250,
        grow: 0,
        keepFocus: false,
      },
      {
        ...keyColumn<MemberWithPoints, "coinsEarned">(
          "coinsEarned",
          intColumn as Partial<Column<number>>
        ),
        disabled: true,
        title: (
          <TitleCell
            title="Coins Earned"
            field="coinsEarned"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        minWidth: 150,
      },
      {
        ...keyColumn<MemberWithPoints, "coinsSpent">(
          "coinsSpent",
          intColumn as Partial<Column<number>>
        ),
        title: (
          <TitleCell
            title="Coins Spent"
            field="coinsSpent"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        disabled: true,
        minWidth: 150,
      },
      {
        ...keyColumn<MemberWithPoints, "balance">("balance", {
          component: BalanceColumn,
          columnData: {
            activities: fetchedData?.rewardsSettings.activities ?? [],
          },
          cellClassName: `dsg-cell--balance`,
        }),
        title: (
          <TitleCell
            title="Current Balance"
            field="balance"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        copyValue: ({ rowData }: { rowData: MemberWithPoints }) => {
          return rowData.balance;
        },
        minWidth: 150,
      },
      {
        ...keyColumn<MemberWithPoints, "lastActivity">("lastActivity", {
          component: DateColumn,
          copyValue: copyDate,
          columnData: {},
        }),
        disabled: true,
        title: "Last Activity",
        minWidth: 150,
      },
    ];

    return cols;
  }, [
    onSortChange,
    sort,
    activeSidebar,
    openSidebar,
    fetchedData?.rewardsSettings.activities,
  ]);

  const filterFields = useGetEditableFlatMemberFilters(members, {
    managers: selectedManagers,
    teams: selectedTeams,
    departments: selectedDepartments,
    countries: selectedCountries,
  });

  const rows = sortMembers(
    filterMembers(members, {
      selectedDepartments,
      selectedCountries,
      selectedTeams,
      selectedManagers,
      searchTerm,
    }),
    sort
  );

  const stickyColumn = {
    ...keyColumn<MemberWithPoints, "place">("place", {
      component: PlaceColumn,
      title: (
        <TitleCell
          title="Place"
          field="place"
          sortValue={sort}
          sort={onSortChange}
        />
      ),
      columnData: {},
    }),
    headerClassName: "capitalize",
    disabled: true,
    basis: 90,
  };

  const csvProps = {
    columnHeaders: [
      { key: "place", displayLabel: "Place" },
      { key: "name", displayLabel: "Employee" },
      { key: "coinsEarned", displayLabel: "Coins Earned" },
      { key: "coinsSpent", displayLabel: "Coins Spent" },
      { key: "balance", displayLabel: "Current Balance" },
      { key: "lastActivity", displayLabel: "Last Activity" },
    ],
    csvData: rows.map((row) => ({
      place: row.place,
      name: row.name ?? "",
      coinsEarned: row.coinsEarned,
      coinsSpent: row.coinsSpent,
      balance: row.balance,
      lastActivity: row.lastActivity
        ? row.lastActivity.toLocaleDateString()
        : "",
    })),
    fileName: "leaderboard",
  };

  return (
    <>
      <SingleSection childrenClassName="flex w-full flex-col">
        {loading && !fetchedData ? (
          <TableSkeleton />
        ) : (
          <>
            <div className="flex gap-6 rounded-t-[4px] border-b border-gray-300 p-3">
              <CountDisplay
                total={totalMembers}
                visibleTotal={rows.length}
                label="employee"
              />
              <nav
                data-test="table-filters"
                className="flex flex-1 gap-3 text-sm"
              >
                <MultipleFiltersDropdown
                  filters={filterFields}
                  onResetFilters={onResetFilters}
                />
              </nav>
            </div>
            <div className="flex-1">
              <DynamicDataSheetGrid<MemberWithPoints>
                value={rows}
                columns={columns}
                lockRows
                disableExpandSelection
                rowHeight={40}
                headerRowHeight={40}
                className={cn("h-full text-sm", {
                  hidden: !rows.length,
                })}
                gutterColumn={stickyColumn}
                onChange={handleDataUpdate}
              />
            </div>
          </>
        )}
      </SingleSection>
      <ClientOnlyPortal selector="#rewards-header-actions">
        {session?.me.isAdmin ? (
          <Button variant="outline" onClick={() => downloadCsv(csvProps)}>
            Export to CSV
          </Button>
        ) : null}
      </ClientOnlyPortal>
    </>
  );
};

LeaderboardPage.getHeader = getHeader;

export default LeaderboardPage;

export async function getServerSideProps(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<unknown>> {
  const redirect =
    (await redirectIfUnauthenticated(ctx)) ??
    (await redirectIfOnboardingIsUnfinished(ctx)) ??
    (await redirectIfNotAdmin(ctx)) ??
    (await redirectIfWorkspaceIsSuspended(ctx));

  if (redirect) {
    return redirect;
  }

  return {
    props: {
      query: ctx.query,
    },
  };
}
