import cn from "classnames";
import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";
import { useState, useMemo, FC, useCallback } from "react";

import Button from "../../components/Button";
import CountDisplay from "../../components/CountDisplay";
import DropdownMenu, { Menu } from "../../components/DropdownMenu";
import EmptyPageState from "../../components/EmptyPageState";
import MultipleFiltersDropdown, {
  FilterType,
  SelectFilter,
  DateFilter,
} from "../../components/MultipleFiltersDropdown";
import { CoinsValue } from "../../components/Rewards/CoinsValue";
import RejectRewardRequestModal from "../../components/Rewards/RejectRewardRequestModal";
import SingleSection from "../../components/SingleSection";
import {
  VirtualizedTable,
  VirtualizedTableColumn,
} from "../../components/Table/Table";
import TableSkeleton from "../../components/TableSkeleton";
import TreeDotsButton from "../../components/TreeDotsButton";
import UserCell from "../../components/Users/<USER>";
import { NextPageWithLayout } from "../../components/layouts/Layout";
import { getHeader } from "../../components/layouts/RewardsLayout";
import {
  ActiveSidebar,
  useActiveSidebarContext,
} from "../../context/ActiveSidebarContext";
import { useConfirm } from "../../context/ConfirmContext";
import {
  useRewardsRequestsPageQuery,
  RewardRequestStatus,
  RewardsRequestsPageQuery,
  useApproveRewardRequestMutation,
  useRejectRewardRequestMutation,
  useDeleteRewardRequestMutation,
} from "../../graphql/client.generated";
import { buildDateRange } from "../../helpers/date";
import { Choice } from "../../helpers/flattenMember";
import {
  parseDateRange,
  DateIntervalValue,
  getDateInterval,
  getDateIntervals,
} from "../../helpers/getDateIntervals";
import { redirectIfNotAdmin } from "../../helpers/redirectIfNotAdmin";
import { redirectIfOnboardingIsUnfinished } from "../../helpers/redirectIfOnboardingIsUnfinished";
import { redirectIfUnauthenticated } from "../../helpers/redirectIfUnauthenticated";
import { redirectIfWorkspaceIsSuspended } from "../../helpers/redirectIfWorkspaceIsSuspended";
import { useReplaceRouterQuery } from "../../hooks/useReplaceRouterQuery";
import { useToast } from "../../hooks/useToast";

type RewardRequest = RewardsRequestsPageQuery["rewardRequests"][number];

const EmployeeCell: FC<{
  row: RewardRequest;
  activeSidebar: ActiveSidebar | null;
  openSidebar: (sidebar: ActiveSidebar) => void;
}> = ({ row, activeSidebar, openSidebar }) => (
  <div className="flex h-full w-full items-center gap-2 whitespace-nowrap">
    <UserCell
      name={row.member.realName ?? ""}
      photoUrl={row.member.photo72Url ?? undefined}
      showSidebarButton
      isSidebarActive={
        activeSidebar?.type === "member" && row.member.id === activeSidebar.id
      }
      onSidebarButtonClick={() =>
        openSidebar({
          type: "member",
          id: row.member.id,
          tabs: [
            {
              id: "profile",
            },
            {
              id: "timeOffs",
            },
            {
              id: "activityLog",
              isActive: true,
            },
          ],
          mode: "edit",
          showGoToOrgChartBtn: true,
        })
      }
    />
  </div>
);

const RewardTitleCell: FC<RewardRequest> = (row) => (
  <div
    className="flex h-full w-full items-center truncate"
    data-test="reward-name"
  >
    {row.reward.name}
  </div>
);

const RewardCostCell: FC<RewardRequest> = (row) => (
  <div className="flex h-full w-full items-center justify-end">
    <CoinsValue coins={row.reward.points} />
  </div>
);

const CoinBalanceCell: FC<RewardRequest> = (row) => (
  <div className="flex h-full w-full items-center justify-end">
    {row.member.coinsBalance.balance}
  </div>
);

const CommentCell: FC<RewardRequest> = (row) => (
  <div className="flex h-full max-w-[200px] items-center">
    <div className="truncate" title={row.comment ?? ""}>
      {row.comment}
    </div>
  </div>
);

const RejectionReasonCell: FC<RewardRequest> = (row) => (
  <div className="flex h-full max-w-[200px] items-center">
    <div className="truncate" title={row.rejectReason ?? ""}>
      {row.rejectReason}
    </div>
  </div>
);

const DateCell: FC<RewardRequest> = (row) => (
  <div className="flex h-full w-full items-center justify-end">
    {new Date(row.createdAt).toLocaleDateString()}
  </div>
);

const DefaultInterval = DateIntervalValue.thisWeek;

const RewardsRequestsPage: NextPageWithLayout = () => {
  const { router, replaceQuery } = useReplaceRouterQuery();
  const { activeSidebar, openSidebar } = useActiveSidebarContext();
  const [from, to] = useMemo(
    () =>
      !router.query.date_range || Array.isArray(router.query.date_range)
        ? []
        : parseDateRange(router.query.date_range),
    [router.query.date_range]
  );

  const rewardsIntervalByDate = getDateInterval({
    from,
    to,
  });

  const [dateRange, setDateRange] = useState(
    rewardsIntervalByDate?.value ?? DefaultInterval
  );
  const [rejectionTarget, setRejectionTarget] = useState<RewardRequest | null>(
    null
  );

  const status = useMemo(() => {
    return router.query.status
      ? (router.query.status as RewardRequestStatus)
      : undefined;
  }, [router.query.status]);

  const toast = useToast();
  const { confirm } = useConfirm();

  const onResetFilters = useCallback(() => {
    replaceQuery({
      status: undefined,
      date_range: undefined,
    });

    setDateRange(DateIntervalValue.thisWeek);
  }, [replaceQuery]);

  const dateRangeOptions: Choice<string>[] = getDateIntervals().map(
    (interval) => ({
      label: interval.label,
      value: interval.value,
    })
  );

  const filterFields = useMemo(() => {
    const statusFilter: SelectFilter<string> = {
      id: "status",
      filterName: "Status",
      filterType: FilterType.Select as const,
      filterValue: status ?? null,
      columnData: {
        choices: [
          {
            label: "Pending",
            value: RewardRequestStatus.Pending,
          },
          {
            label: "Approved",
            value: RewardRequestStatus.Approved,
          },
          {
            label: "Rejected",
            value: RewardRequestStatus.Rejected,
          },
        ],
        placeholder: "Select status",
      },
      onFilterApply: (value) => {
        replaceQuery({
          status: value?.value,
        });
      },
    };

    const dateFilter: SelectFilter<string> = {
      id: "date",
      filterName: "Date",
      filterType: FilterType.Select as const,
      filterValue: dateRange,
      columnData: {
        choices: dateRangeOptions,
        placeholder: "Select date",
      },
      onFilterApply: (date) => {
        if (!date) {
          return;
        }

        const intervalValue = date.value as DateIntervalValue;
        const interval = getDateInterval({
          value: intervalValue,
        });

        setDateRange(intervalValue);

        if (interval && interval.value !== DateIntervalValue.custom) {
          replaceQuery({
            date_range: buildDateRange(interval),
          });
        }
      },
    };

    const fromFilter: DateFilter = {
      id: "from",
      filterName: "From",
      filterType: FilterType.Date as const,
      filterValue: from ?? null,
      onFilterApply: (value) => {
        replaceQuery({
          date_range: buildDateRange({
            from: value ?? undefined,
            to,
          }),
        });
      },
    };

    const toFilter: DateFilter = {
      id: "to",
      filterName: "To",
      filterType: FilterType.Date as const,
      filterValue: to ?? null,
      onFilterApply: (value) => {
        replaceQuery({
          date_range: buildDateRange({
            from,
            to: value ?? undefined,
          }),
        });
      },
    };

    if (dateRange === DateIntervalValue.custom) {
      return [dateFilter, fromFilter, toFilter, statusFilter];
    }

    return [dateFilter, statusFilter];
  }, [replaceQuery, from, to, dateRange, dateRangeOptions, status]);

  const [approveRequest] = useApproveRewardRequestMutation({
    onCompleted: () => toast("success", "Request approved"),
    onError: (error) => {
      toast("error", error.message);
    },
    refetchQueries: ["RewardsRequestsPage"],
  });
  const [rejectRequest, { loading: rejectLoading }] =
    useRejectRewardRequestMutation({
      onCompleted: () => {
        toast("success", "Request rejected");
        setRejectionTarget(null);
      },
      refetchQueries: ["RewardsRequestsPage"],
    });
  const [deleteRequest] = useDeleteRewardRequestMutation({
    onCompleted: () => toast("success", "Request deleted"),
    refetchQueries: ["RewardsRequestsPage"],
  });

  const {
    data: newData,
    previousData,
    loading,
  } = useRewardsRequestsPageQuery({
    variables: {
      from: from ?? rewardsIntervalByDate?.from,
      to: to ?? rewardsIntervalByDate?.to,
    },
    nextFetchPolicy: "cache-and-network",
  });

  const data = newData ?? previousData;
  const rows =
    data?.rewardRequests.filter((r) => !status || r.status === status) ?? [];

  const isFiltered = !!status || dateRange !== DefaultInterval;
  const showEmptyMessage = !isFiltered && rows.length === 0;
  const showNotFoundMessage = isFiltered && rows.length === 0;
  const showToolbar = !showEmptyMessage;
  const showTable = rows.length > 0;

  const StatusCell: FC<RewardRequest> = useCallback(
    (row) => {
      if (row.status === RewardRequestStatus.Pending) {
        return (
          <div className="flex h-full items-center gap-2">
            <Button
              dataTest={`approve-request-button-${row.id}`}
              variant="outline"
              color="green"
              size="s"
              onClick={() => {
                void approveRequest({ variables: { id: row.id } });
              }}
            >
              Approve
            </Button>
            <Button
              dataTest={`reject-request-button-${row.id}`}
              variant="outline"
              color="danger"
              size="s"
              onClick={() => {
                setRejectionTarget(row);
              }}
            >
              Reject
            </Button>
          </div>
        );
      }

      return (
        <div className="flex h-full items-center">
          <div
            data-test={`request-status-${row.id}`}
            className={cn(
              "inline-block rounded-md px-2 py-1 text-xs font-medium capitalize",
              {
                "bg-green-100 text-green-800":
                  row.status === RewardRequestStatus.Approved,
                "bg-red-100 text-red-800":
                  row.status === RewardRequestStatus.Rejected,
              }
            )}
          >
            {row.status.toLowerCase()}
          </div>
        </div>
      );
    },
    [approveRequest, setRejectionTarget]
  );

  const ActionsCell: FC<RewardRequest> = useCallback(
    (row) => {
      const actions: Menu[] = [
        {
          label: "Delete",
          id: "delete_template",
          type: "danger",
          onClick: () => {
            void confirm({
              message: "Are you sure you want to delete this request?",
              description:
                "The requester will be notified and will receive a full refund of Coins.",
              buttonConfirm: "Delete",
              buttonCancel: "Cancel",
            }).then(() => deleteRequest({ variables: { id: row.id } }));
          },
        },
      ];

      return (
        <div
          className="flex h-full w-full items-center justify-center"
          data-test={`actions-menu-${row.id}`}
        >
          <DropdownMenu menu={actions}>
            <TreeDotsButton itemClassName="bg-black" />
          </DropdownMenu>
        </div>
      );
    },
    [confirm, deleteRequest]
  );

  const renderEmployeeCell = useCallback(
    (row: RewardRequest) => (
      <EmployeeCell
        row={row}
        activeSidebar={activeSidebar}
        openSidebar={openSidebar}
      />
    ),
    [activeSidebar, openSidebar]
  );

  const columns: VirtualizedTableColumn<RewardRequest>[] = useMemo(
    () => [
      {
        id: "employee",
        header: "Employee",
        cell: renderEmployeeCell,
        width: 250,
      },
      {
        id: "rewardTitle",
        header: "Reward",
        cell: RewardTitleCell,
        width: 250,
      },
      {
        id: "rewardCost",
        header: "Cost",
        cell: RewardCostCell,
        width: 100,
      },
      {
        id: "coinBalance",
        header: "Balance",
        cell: CoinBalanceCell,
        width: 100,
      },
      {
        id: "comment",
        header: "Comment",
        cell: CommentCell,
        width: 200,
      },
      {
        id: "rejectionReason",
        header: "Rejection Reason",
        cell: RejectionReasonCell,
        width: 200,
      },
      {
        id: "date",
        header: "Date",
        cell: DateCell,
        width: 150,
      },
      {
        id: "status",
        header: "Status",
        cell: StatusCell,
        width: 220,
      },
      {
        id: "actions",
        header: <div className="px-3" />,
        cell: ActionsCell,
        width: 56,
      },
    ],
    [StatusCell, ActionsCell, renderEmployeeCell]
  );

  return (
    <>
      <SingleSection childrenClassName="flex w-full flex-col overflow-hidden">
        {loading && <TableSkeleton />}

        {!loading && data && (
          <>
            {showToolbar && (
              <div className="flex items-center gap-6 border-b border-main-200 px-6 py-2.5">
                <CountDisplay
                  total={data.rewardRequests.length}
                  visibleTotal={rows.length}
                  label="request"
                />

                <nav data-test="table-filters">
                  <MultipleFiltersDropdown
                    filters={filterFields}
                    onResetFilters={onResetFilters}
                  />
                </nav>
              </div>
            )}

            <div className="flex-1 overflow-hidden">
              {showEmptyMessage && (
                <EmptyPageState>
                  <div className="max-w-[370px]">
                    <h2 className="font-bold">No reward requests yet</h2>
                    <p className="mt-2">
                      Once your team members start requesting rewards, you will
                      see them here.
                    </p>
                  </div>
                </EmptyPageState>
              )}

              {showNotFoundMessage && (
                <EmptyPageState>
                  <div className="max-w-[370px]">
                    <h2 className="font-bold">No requests found</h2>
                    <p className="mt-2">
                      Try changing the filters to see available requests.
                    </p>
                  </div>
                </EmptyPageState>
              )}

              {showTable && (
                <VirtualizedTable
                  dataTest="requests-table"
                  data={rows}
                  columns={columns}
                  rowHeight={48}
                  stickyHeader
                  stickyFirstColumn
                  className="flex-grow"
                />
              )}
            </div>
          </>
        )}
      </SingleSection>
      {rejectionTarget && (
        <RejectRewardRequestModal
          loading={rejectLoading}
          onClose={() => setRejectionTarget(null)}
          onSubmit={(reason) => {
            void rejectRequest({
              variables: { id: rejectionTarget.id, reason },
            });
          }}
        />
      )}
    </>
  );
};

RewardsRequestsPage.getHeader = getHeader;

export default RewardsRequestsPage;

export async function getServerSideProps(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<unknown>> {
  const redirect =
    (await redirectIfUnauthenticated(ctx)) ??
    (await redirectIfOnboardingIsUnfinished(ctx)) ??
    (await redirectIfNotAdmin(ctx)) ??
    (await redirectIfWorkspaceIsSuspended(ctx));

  if (redirect) {
    return redirect;
  }

  return {
    props: {
      query: ctx.query,
    },
  };
}
