import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";
import { useMemo, useCallback, useState } from "react";

import Avatar from "../../components/Avatar";
import Button from "../../components/Button";
import ClientOnlyPortal from "../../components/ClientOnlyPortal";
import Leaderboard, {
  Column,
  LeaderboardMember,
} from "../../components/Leaderboard";
import TopLeaderboardCard from "../../components/Leaderboard/TopLeaderboardCard";
import MultipleFiltersDropdown, {
  DateFilter,
  FilterType,
  SelectFilter,
} from "../../components/MultipleFiltersDropdown";
import { CoinsValue } from "../../components/Rewards/CoinsValue";
import { Tab } from "../../components/Tab";
import { NextPageWithLayout } from "../../components/layouts/Layout";
import { getHeader } from "../../components/layouts/RewardsLayout";
import { useSessionContext } from "../../context/SessionContext";
import { useLeaderboardPageQuery } from "../../graphql/client.generated";
import { buildDateRange } from "../../helpers/date";
import downloadCsv, { CsvData } from "../../helpers/downloadCsv";
import { Choice } from "../../helpers/flattenMember";
import {
  DateIntervalValue,
  getDateInterval,
  getDateIntervals,
  parseDateRange,
} from "../../helpers/getDateIntervals";
import { redirectIfOnboardingIsUnfinished } from "../../helpers/redirectIfOnboardingIsUnfinished";
import { redirectIfUnauthenticated } from "../../helpers/redirectIfUnauthenticated";
import { redirectIfWorkspaceIsSuspended } from "../../helpers/redirectIfWorkspaceIsSuspended";
import { useReplaceRouterQuery } from "../../hooks/useReplaceRouterQuery";

const LEADERBOARD_TABS = [
  {
    id: "EARNERS",
    title: "Top earners",
  },
  {
    id: "SPENDERS",
    title: "Top spenders",
  },
];

const ColumnHeaders = [
  {
    key: "name",
    displayLabel: "Name",
  },
  {
    key: "earned",
    displayLabel: "Coins Earned",
  },
  {
    key: "spent",
    displayLabel: "Coins Spent",
  },
];

const RewardsLeader: React.FC<{ member: LeaderboardMember; order: number }> = ({
  member,
  order,
}) => (
  <TopLeaderboardCard
    member={member}
    order={order}
    renderPoints={() => (
      <div className="mt-2">
        <div className="flex items-end gap-3">
          <CoinsValue coins={member.quantity} />
        </div>
      </div>
    )}
  />
);

const columns: Column[] = [
  {
    key: "rank",
    displayLabel: "#",
    className: "w-[55px] text-right",
    render: (member) => member.rank,
  },
  {
    key: "name",
    displayLabel: "Name",
    render: (member) => (
      <div className="flex items-center gap-2">
        <Avatar photoUrl={member.avatarUrl} size="s" rounded="full" />
        <div>{member.realName}</div>
      </div>
    ),
  },
  {
    key: "quantity",
    displayLabel: "Number of coins",
    className: "w-[180px] text-right",
    render: (member) => (
      <div className="flex w-full justify-end">
        <CoinsValue coins={member.quantity} />
      </div>
    ),
  },
];

const dateFilterOptions: Choice<string>[] = getDateIntervals().map(
  (interval) => ({
    label: interval.label,
    value: interval.value,
  })
);

const DefaultInterval = DateIntervalValue.thisWeek;

const LeaderboardPage: NextPageWithLayout = () => {
  const { replaceQuery, router } = useReplaceRouterQuery();
  const { session } = useSessionContext();

  const [from, to] =
    !router.query.date_range || Array.isArray(router.query.date_range)
      ? []
      : parseDateRange(router.query.date_range);

  const kudosIntervalByDate = getDateInterval({
    from,
    to,
  });

  const [dateFilter, setDateFilter] = useState(
    kudosIntervalByDate?.value ?? DefaultInterval
  );

  const kudosInterval = getDateInterval({
    value: dateFilter,
  })!;

  const { data, loading } = useLeaderboardPageQuery({
    variables: {
      from: from ?? kudosInterval.from,
      to: to ?? kudosInterval.to,
    },
    fetchPolicy: "cache-and-network",
  });

  const onResetFilters = useCallback(() => {
    replaceQuery({
      date_range: undefined,
    });

    setDateFilter(DefaultInterval);
  }, [replaceQuery]);

  const filterFields = useMemo(() => {
    const dateFilterField: SelectFilter<string> = {
      id: "date",
      filterName: "Date",
      filterType: FilterType.Select as const,
      filterValue: dateFilter,
      columnData: {
        choices: dateFilterOptions,
        placeholder: "Select date",
      },
      onFilterApply: (date) => {
        if (!date) {
          return;
        }

        const intervalValue = date.value as DateIntervalValue;
        const interval = getDateInterval({
          value: intervalValue,
        });

        setDateFilter(intervalValue);

        if (interval && interval.value !== DateIntervalValue.custom) {
          replaceQuery({
            date_range: buildDateRange(interval),
          });
        }
      },
    };

    const fromFilter: DateFilter = {
      id: "from",
      filterName: "From",
      filterType: FilterType.Date as const,
      filterValue: from ?? null,
      onFilterApply: (value) => {
        replaceQuery({
          date_range: buildDateRange({
            from: value ?? undefined,
            to,
          }),
        });
      },
    };

    const toFilter: DateFilter = {
      id: "to",
      filterName: "To",
      filterType: FilterType.Date as const,
      filterValue: to ?? null,
      onFilterApply: (value) => {
        replaceQuery({
          date_range: buildDateRange({
            from,
            to: value ?? undefined,
          }),
        });
      },
    };

    if (dateFilter === DateIntervalValue.custom) {
      return [dateFilterField, fromFilter, toFilter];
    }

    return [dateFilterField];
  }, [dateFilter, replaceQuery, from, to]);

  const leaderboardData = useMemo(() => {
    if (!data) {
      return [];
    }

    if (router.query.leaderboard_tab === LEADERBOARD_TABS[1].id) {
      return data.coinSpenders.map((spender) => ({
        id: spender.member.id,
        realName: spender.member.realName ?? "",
        avatarUrl: spender.member.photo72Url,
        quantity: spender.coins,
      }));
    }

    return data.coinEarners.map((earner) => ({
      id: earner.member.id,
      realName: earner.member.realName ?? "",
      avatarUrl: earner.member.photo72Url,
      quantity: earner.coins,
    }));
  }, [data, router.query.leaderboard_tab]);

  const csvData: CsvData[] = useMemo(() => {
    if (!data) {
      return [];
    }

    const members = new Map(
      data.coinEarners.map((earner) => [
        earner.member.id,
        {
          name: earner.member.realName ?? "",
          earned: earner.coins,
          spent: 0,
        },
      ])
    );

    data.coinSpenders.forEach((spender) => {
      const member = members.get(spender.member.id);

      if (member) {
        member.spent = spender.coins;
      } else {
        members.set(spender.member.id, {
          name: spender.member.realName ?? "",
          earned: 0,
          spent: spender.coins,
        });
      }
    });

    return Array.from(members.values())
      .map((member) => ({
        name: member.name,
        earned: member.earned,
        spent: member.spent,
      }))
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [data]);

  const csvProps = useMemo(() => {
    return {
      columnHeaders: ColumnHeaders,
      csvData,
      fileName: "organice-rewards-leaderboard",
    };
  }, [csvData]);

  return (
    <>
      <ClientOnlyPortal selector="#rewards-header-actions">
        {session?.me.isAdmin ? (
          <Button variant="outline" onClick={() => downloadCsv(csvProps)}>
            Export to CSV
          </Button>
        ) : null}
      </ClientOnlyPortal>
      <div className="grid h-full grid-rows-[60px_1fr] divide-y divide-gray-300 overflow-hidden rounded-md border border-main-300 bg-white">
        <div className="flex items-center gap-6 rounded-t-md px-4 text-sm">
          <div className="flex items-center gap-1 border-r pr-6">
            {LEADERBOARD_TABS.map((tab) => (
              <Tab
                key={tab.id}
                isActive={() => {
                  if (!router.query.leaderboard_tab) {
                    return tab.id === LEADERBOARD_TABS[0].id;
                  }

                  return router.query.leaderboard_tab === tab.id;
                }}
                onClick={() =>
                  replaceQuery({
                    leaderboard_tab: tab.id,
                  })
                }
                dataTest={`leaderboard-tab-${tab.id}`}
              >
                {tab.title}
              </Tab>
            ))}
          </div>
          <div>
            <MultipleFiltersDropdown
              filters={filterFields}
              onResetFilters={onResetFilters}
            />
          </div>
        </div>

        <div className="flex flex-col gap-4 overflow-auto">
          <Leaderboard
            dataTest="rewards-leaderboard"
            data={leaderboardData}
            isLoading={loading}
            emptyComponent={
              router.query.leaderboard_tab === LEADERBOARD_TABS[0].id ? (
                <>
                  <div className="mb-2 font-semibold">Leaderboard is empty</div>
                  <div>It looks like no one has earned coins yet.</div>
                </>
              ) : (
                <>
                  <div className="mb-2 font-semibold">Leaderboard is empty</div>
                  <div>It looks like no one has spent coins yet.</div>
                </>
              )
            }
            leaderComponent={RewardsLeader}
            columns={columns}
          />
        </div>
      </div>
    </>
  );
};

LeaderboardPage.getHeader = getHeader;

export default LeaderboardPage;

export async function getServerSideProps(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<unknown>> {
  const redirect =
    (await redirectIfUnauthenticated(ctx)) ??
    (await redirectIfOnboardingIsUnfinished(ctx)) ??
    (await redirectIfWorkspaceIsSuspended(ctx));

  if (redirect) {
    return redirect;
  }

  return {
    props: {
      query: ctx.query,
    },
  };
}
