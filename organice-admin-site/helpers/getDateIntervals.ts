import { isSameDay, startOfWeek, subDays, subMonths } from "date-fns";

export enum DateIntervalValue {
  thisWeek = "THIS_WEEK",
  last30Days = "LAST_30_DAYS",
  lastThreeMonths = "LAST_THREE_MONTHS",
  lastSixMonths = "LAST_SIX_MONTHS",
  lastTwelveMonths = "LAST_TWELVE_MONTHS",
  allTime = "ALL_TIME",
  custom = "CUSTOM",
}

export interface DateInterval {
  label: string;
  value: DateIntervalValue;
  from?: Date;
  to?: Date;
}

export const getDateIntervals = (
  include?: DateIntervalValue[]
): DateInterval[] => {
  const now = new Date();

  const allIntervals: DateInterval[] = [
    {
      label: "This week",
      value: DateIntervalValue.thisWeek,
      from: startOfWeek(now, { weekStartsOn: 1 }),
    },
    {
      label: "Last 30 days",
      value: DateIntervalValue.last30Days,
      from: subDays(now, 30),
    },
    {
      label: "3 months",
      value: DateIntervalValue.lastThreeMonths,
      from: subMonths(now, 3),
    },
    {
      label: "6 months",
      value: DateIntervalValue.lastSixMonths,
      from: subMonths(now, 6),
    },
    {
      label: "12 month",
      value: DateIntervalValue.lastTwelveMonths,
      from: subMonths(now, 12),
    },
    {
      label: "All time",
      value: DateIntervalValue.allTime,
    },
    {
      label: "Custom",
      value: DateIntervalValue.custom,
    },
  ];

  if (!include) {
    return allIntervals;
  }

  return allIntervals.filter((interval) => include.includes(interval.value));
};

export const getDateInterval = ({
  value,
  from,
  to,
}: {
  value?: DateIntervalValue;
  from?: Date;
  to?: Date;
}): DateInterval | null => {
  const intervals = getDateIntervals();
  const custom = intervals.find(
    (interval) => interval.value === DateIntervalValue.custom
  );

  if (to) {
    return custom ?? null;
  }

  if (from) {
    return (
      intervals.find(
        (interval) => interval.from && isSameDay(interval.from, from)
      ) ??
      custom ??
      null
    );
  }

  if (value) {
    return intervals.find((interval) => interval.value === value) ?? null;
  }

  return null;
};

export const parseDateRange = (
  dateRange: string | undefined
): [Date?, Date?] => {
  if (!dateRange) return [];

  const [from, to] = dateRange.split("_");

  return [from ? new Date(from) : undefined, to ? new Date(to) : undefined];
};
