import { useCallback } from "react";

import { useConfirm } from "../context/ConfirmContext";
import {
  RewardRequestStatus,
  useGetPendingRewardRequestsLazyQuery,
} from "../graphql/client.generated";

export const useConfirmDeactivateReward = (): ((
  rewardId: string
) => Promise<boolean>) => {
  const [getPendingRewardRequests] = useGetPendingRewardRequestsLazyQuery();
  const { confirm } = useConfirm();

  return useCallback(
    async (rewardId: string): Promise<boolean> => {
      try {
        const { data: requestsData } = await getPendingRewardRequests({
          fetchPolicy: "network-only",
        });

        const pendingRequestsCount =
          requestsData?.rewardRequests.filter(
            (req) =>
              req.reward.id === rewardId &&
              req.status === RewardRequestStatus.Pending
          ).length ?? 0;

        if (pendingRequestsCount > 0) {
          await confirm({
            message: "Deactivate reward?",
            description: `This reward has ${pendingRequestsCount} pending requests. If you deactivate it, all pending requests will be rejected.`,
            buttonConfirm: "Deactivate",
            buttonCancel: "Cancel",
          });
        }

        return true;
      } catch {
        return false;
      }
    },
    [confirm, getPendingRewardRequests]
  );
};
