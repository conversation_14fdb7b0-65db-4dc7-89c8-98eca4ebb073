# run "docker compose up --build" to build and run
# run "docker compose up --build -d" to run in background
# run "docker compose ps" to see status of containers
# run "docker compose stop" to stop containers
# run "docker compose down" to stop and remove containers and built images
services:
  backend:
    build:
      context: backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    env_file: .env
