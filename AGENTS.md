# Agents Instructions

## Project Documentation

When handling user prompts related to features, requirements, or implementation decisions, always consult the specification documents located in `/docs`:

- `/docs/Specification.md` - Contains detailed UX flows, screen definitions, and business rules for the Easy Beauty MVP
- `/docs/User Stories.csv` - Contains user stories with milestone assignments (<PERSON> vs Post MVP) and effort estimates

**Important**: In case of conflicting requirements between these documents, the CSV file takes precedence.

## Updating Documentation

When the user explicitly requests a change that contradicts the current specification or user stories, you must:

1. Implement the requested change in the codebase
2. Update the relevant documentation in `/docs` to reflect the new requirements
3. Ensure consistency between `Specification.md` and `User Stories.csv`

This ensures the documentation remains the source of truth and stays in sync with the actual implementation.
