{"editor.formatOnSave": true, "eslint.format.enable": true, "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "eslint.validate": ["glimmer-ts", "glimmer-js"], "typescript.tsdk": "./node_modules/typescript/lib", "eslint.workingDirectories": ["astrala-site"]}